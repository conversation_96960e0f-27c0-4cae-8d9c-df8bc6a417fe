<html>
<head>
    <meta charset="utf-8"/>
    <link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect"/>
    <link as="style" href="https://fonts.googleapis.com/css2?display=swap&family=Inter%3Awght%40400%3B500%3B600%3B700&family=Noto+Sans%3Awght%40400%3B500%3B700%3B900" onload="this.rel='stylesheet'" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet"/>
    <title>Calendrier d'├ëquipe</title>
    <link href="data:image/x-icon;base64," rel="icon" type="image/x-icon"/>
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <style>
        /* --- STYLES DE CODE 1 (BASE DESIGN) --- */
        .material-icons-outlined { font-size: inherit; line-height: inherit; }
        .schedule-scroll-indicator::after {
            content: ''; position: absolute; bottom: 0; left: 0; right: 0;
            height: 100px; background: linear-gradient(to bottom, transparent 0%, rgba(255, 255, 255, 0.98) 60%);
            pointer-events: none; opacity: 0; transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
            border-bottom-left-radius: 0.75rem;border-bottom-right-radius: 0.75rem;
            z-index: 25; transform: translateY(10px);
        }
        .schedule-scroll-indicator.scrolling::after { opacity: 1; transform: translateY(0); }
        .shift-card {
            transition: transform 0.25s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.25s cubic-bezier(0.4, 0, 0.2, 1), background-color 0.2s ease-out, border-color 0.2s ease-out, filter 0.2s ease-out;
            will-change: transform, box-shadow; position: relative;
        }
        .shift-card:hover {
            transform: translateY(-6px) scale(1.05) perspective(1000px) rotateX(2deg);
            box-shadow: 0 20px 30px -8px rgba(0, 0, 0, 0.2), 0 8px 12px -5px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0,0,0,0.05);
            filter: brightness(1.05);
        }
        .day-column:hover .day-header { /* Appliqu├® par le parent, l'effet sera sur le day-header sticky */
            background-color: rgba(222, 230, 255, 0.85) !important;box-shadow: 0 6px 15px -3px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px) scale(1.01); 
            /* z-index d├®j├á g├®r├® par day-header, mais on pourrait ajuster si conflit avec employee-info-header */
        }
        .day-column:hover { background-color: rgba(240, 245, 255, 0.6); }
        .employee-row:hover { background-color: #f7f8fc; } /* S'applique ├á la ligne d'info ET aux cellules de shift */
        .employee-row-info:hover .employee-info-cell-content { /* Style sp├®cifique pour le survol de la cellule d'info employ├® */
             background-color: #eef2ff; box-shadow: inset 3px 0 6px -2px rgba(59, 130, 246, 0.15);
        }
        .day-header { /* En-t├¬te de jour (DIM, LUN...) */
            transition: background-color 0.25s ease-out, box-shadow 0.25s ease-out, transform 0.25s ease-out, opacity 0.25s ease-out;
            position: sticky; top: 0; z-index: 20; /* Z-index inf├®rieur ├á employee-info-header */
            background-color: rgba(255, 255, 255, 0.8);backdrop-filter: blur(10px) saturate(120%);
            -webkit-backdrop-filter: blur(10px) saturate(120%);
        }
        .employee-info-header { /* En-t├¬te "Employ├®(e)" */
            position: sticky; top: 0; z-index: 30; /* Z-index sup├®rieur pour passer au-dessus des day-header */
            background-color: rgba(248, 250, 252, 0.85);backdrop-filter: blur(10px) saturate(120%);
            -webkit-backdrop-filter: blur(10px) saturate(120%); box-shadow: 0 2px 5px rgba(0,0,0,0.04);
        }
        .employee-avatar {
            box-shadow: 0 5px 10px rgba(0,0,0,0.07), 0 0 0 2px rgba(255,255,255,0.9), 0 0 0 4px rgba(59, 130, 246, 0.25);
            transition: transform 0.25s ease-out, box-shadow 0.25s ease-out;
        }
        .employee-row-info:hover .employee-avatar { /* S'applique ├á l'avatar dans la ligne d'info */
            transform: scale(1.12);
            box-shadow: 0 8px 15px rgba(0,0,0,0.1), 0 0 0 2px rgba(255,255,255,1), 0 0 0 5px rgba(59, 130, 246, 0.3);
        }
        .shift-card-angled { clip-path: polygon(10% 0%, 100% 0%, 90% 100%, 0% 100%); padding-left: 12%; padding-right: 12%; text-align: center; }
        .shift-card-pill { border-radius: 9999px; }
        .schedule-container-glass {
            background-color: rgba(255, 255, 255, 0.80);backdrop-filter: blur(16px) saturate(180%);
            -webkit-backdrop-filter: blur(16px) saturate(180%);
            border: 1px solid rgba(200, 200, 200, 0.25);
            box-shadow: 0 20px 45px rgba(0, 0, 0, 0.12), 0 8px 20px rgba(0,0,0,0.08);
        }
        .sticky-header-bg { /* Appliqu├® aux deux types d'en-t├¬tes sticky */
            background: linear-gradient(to bottom, rgba(248, 250, 252, 0.9) 70%, rgba(248, 250, 252, 0.7));
            backdrop-filter: blur(10px) saturate(120%); -webkit-backdrop-filter: blur(10px) saturate(120%);
        }
        .day-header.sticky-header-bg { /* Sp├®cificit├® pour day-header */
            background: linear-gradient(to bottom, rgba(255, 255, 255, 0.85) 70%, rgba(255, 255, 255, 0.65));
        }
        /* employee-info-cell n'est plus utilis├® comme classe globale pour les lignes avec la nouvelle structure JS */
        /* .employee-info-cell { position: relative; z-index: 5; } */
        .schedule-grid-container { 
            scrollbar-width: thin; scrollbar-color: rgba(156, 163, 175, 0.5) rgba(229, 231, 235, 0.5);
        }
        .schedule-grid-container::-webkit-scrollbar { width: 8px; height: 8px; }
        .schedule-grid-container::-webkit-scrollbar-track { background: rgba(229, 231, 235, 0.5); border-radius: 10px; }
        .schedule-grid-container::-webkit-scrollbar-thumb {
            background-color: rgba(156, 163, 175, 0.5); border-radius: 10px;
            border: 2px solid transparent; background-clip: content-box;
        }
        .schedule-grid-container::-webkit-scrollbar-thumb:hover { background-color: rgba(107, 114, 128, 0.7); }

        /* --- STYLES DE CODE 2 (M├ëCANISMES), AJUST├ëS --- */
        .schedule-grid-cell-dynamic { display: flex; flex-direction: column; gap: 0.25rem; align-items: center; justify-content: flex-start; }
        .shift-card.dragging { opacity: 0.6; cursor: grabbing; transform: translateY(-2px) scale(1.03); box-shadow: 0 10px 20px rgba(0,0,0,0.2); }
        .drop-target-active { background-color: rgba(191, 219, 254, 0.6) !important; outline: 2px dashed #3b82f6; outline-offset: -2px; }
        .delete-shift {
            position: absolute; top: 2px; right: 2px; padding: 0.1rem 0.30rem; cursor: pointer;
            font-size: 1rem; line-height: 1; color: inherit; opacity: 0.4;
            border-radius: 0 0.35rem 0 0.25rem; background-color: rgba(0,0,0,0.05);
            transition: opacity 0.2s, background-color 0.2s; z-index: 1;
        }
        .shift-card:hover .delete-shift { opacity: 0.7; }
        .delete-shift:hover { opacity: 1; background-color: rgba(0,0,0,0.15); }
        .employee-row-draggable { cursor: grab; } /* Appliqu├® ├á la ligne d'info employ├® draggable */
        .employee-row-draggable.dragging { opacity: 0.7; background-color: #e0f2fe !important; }
        .employee-row-draggable.dragging .employee-info-cell-content { background-color: #e0f2fe !important; box-shadow: none !important; }
        .employee-drop-indicator {
            height: 3px; background-color: #2563eb; width: calc(100% - 1rem); margin: 0 auto;
            border-radius: 2px; position: absolute; left: 0.5rem; right: 0.5rem; z-index: 10;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-slate-100 to-sky-100 text-slate-900 overflow-hidden" style='font-family: Inter, "Noto Sans", sans-serif;'>
<div class="relative flex size-full min-h-screen flex-col group/design-root">
    <div class="layout-container flex h-screen grow flex-col">
        <header class="flex items-center justify-between whitespace-nowrap border-b border-solid border-slate-300/60 px-6 py-4 bg-white/85 backdrop-blur-lg shadow-md fixed top-0 left-0 right-0 z-50">
             <!-- Header content ... (inchang├®) -->
            <div class="flex items-center gap-3 text-slate-900">
                <div class="size-8 text-blue-600"><svg fill="none" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg"><path d="M44 4H30.6666V17.3334H17.3334V30.6666H4V44H44V4Z" fill="currentColor"></path></svg></div>
                <h2 class="text-slate-900 text-xl font-semibold leading-tight tracking-tight">Calendrier d'├ëquipe</h2>
            </div>
            <div class="flex flex-1 justify-end items-center gap-4 md:gap-6">
                <nav class="hidden md:flex items-center gap-2">
                    <a class="text-slate-600 hover:text-blue-600 text-sm font-medium leading-normal px-3 py-2 rounded-lg hover:bg-slate-200/70 transition-colors" href="#">Aujourd'hui</a>
                    <a class="text-slate-600 hover:text-blue-600 text-sm font-medium leading-normal px-3 py-2 rounded-lg hover:bg-slate-200/70 transition-colors" href="#">Mois</a>
                    <a class="text-blue-600 bg-blue-100/80 text-sm font-semibold leading-normal px-3 py-2 rounded-lg transition-colors hover:bg-blue-200/80 shadow-sm" href="#">Semaine</a>
                    <a class="text-slate-600 hover:text-blue-600 text-sm font-medium leading-normal px-3 py-2 rounded-lg hover:bg-slate-200/70 transition-colors" href="#">Jour</a>
                </nav>
                <button class="flex items-center justify-center rounded-lg h-10 w-10 text-slate-500 hover:bg-slate-200/70 hover:text-slate-700 transition-all duration-200 transform hover:scale-110"><span class="material-icons-outlined text-2xl">filter_list</span></button>
                <button class="flex items-center justify-center rounded-lg h-10 w-10 text-slate-500 hover:bg-slate-200/70 hover:text-slate-700 transition-all duration-200 transform hover:scale-110"><span class="material-icons-outlined text-2xl">settings</span></button>
                <div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10 border-2 border-slate-200 shadow-md transform hover:scale-110 transition-transform duration-200" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuDSJThJUPty7Wl9A9rU2FQdRSYJpb2_hVYDOh8hLy1MG9FnqJzZM95qmEx90D1r-0hdyCnJQPzfZHmRMt2AwALJ4o5xD9_eeeyvz_KMBOd_lDYlwQ8kvybXW3vXqnD5x4bex_arARqREWRBzAlLc6u_eqJXwjXYrWLiQp74vfLqvcbKX8THk6ct0QTj2jvsPk8cdhJMv1voPKXnQ6hW7ANU73a7FoWwR4i1uEkogdReG5KtHuxIejDGKuxm6-NzoSsSGKSinmGajvXM");'></div>
                <button class="md:hidden flex items-center justify-center rounded-lg h-10 w-10 text-slate-500 hover:bg-slate-200/70 hover:text-slate-700 transition-all duration-200 transform hover:scale-110"><span class="material-icons-outlined text-2xl">menu</span></button>
            </div>
        </header>
        <main class="flex flex-1 flex-col px-6 py-8 overflow-hidden pt-24">
            <div class="flex items-center justify-between mb-8">
                <!-- Date navigation and action buttons ... (inchang├®) -->
                <div class="flex items-center gap-2">
                    <button class="p-2.5 rounded-full hover:bg-slate-200/80 text-slate-500 hover:text-slate-700 transition-all duration-200 transform hover:scale-110"><span class="material-icons-outlined text-2xl">chevron_left</span></button>
                    <h2 id="current-week-display" class="text-slate-900 text-xl sm:text-2xl font-semibold leading-tight tracking-tight">13 - 19 Octobre, 2024</h2>
                    <button class="p-2.5 rounded-full hover:bg-slate-200/80 text-slate-500 hover:text-slate-700 transition-all duration-200 transform hover:scale-110"><span class="material-icons-outlined text-2xl">chevron_right</span></button>
                </div>
                <div class="flex items-center gap-3">
                    <button class="flex items-center justify-center gap-2 text-slate-600 bg-white/90 hover:bg-slate-50/90 border border-slate-300/80 font-medium py-2.5 px-4 rounded-xl transition-all duration-200 text-sm shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"><span class="material-icons-outlined text-xl">upload</span> Exporter</button>
                    <button id="add-shift-button" class="flex items-center justify-center gap-2 text-white bg-blue-600 hover:bg-blue-700 font-medium py-2.5 px-4 rounded-xl transition-all duration-200 text-sm shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 active:bg-blue-800"><span class="material-icons-outlined text-xl">add</span> Ajouter un shift</button>
                </div>
            </div>
            <div class="flex-1 border border-slate-300/40 rounded-xl shadow-2xl bg-white/70 overflow-hidden relative schedule-scroll-indicator schedule-container-glass" id="schedule-container">
                <div class="grid grid-cols-[220px_1fr] h-full overflow-y-auto schedule-grid-container" id="schedule-grid-scrollable-content">
                    <!-- COLONNE 1: EMPLOY├ëS - Nouvelle structure pour le scroll synchronis├® -->
                    <div class="bg-slate-100/60 border-r border-slate-300/40 shadow-sm"> {/* Pas de sticky ici pour la colonne enti├¿re */}
                        <div class="h-[calc(5.5rem-1px)] flex items-center justify-start pl-6 border-b border-slate-300/40 employee-info-header sticky-header-bg sticky top-0 z-30">
                            <p class="text-slate-700 text-sm font-semibold leading-normal tracking-wider">Employ├®(e)</p>
                        </div>
                        <div id="employee-list-container" class="divide-y divide-slate-200/70">
                            <!-- Les lignes d'employ├®s (avec leur propre min-height dynamique) seront inject├®es ici par JS -->
                        </div>
                    </div>
                    <!-- COLONNE 2: GRILLE DES JOURS -->
                    <div class="grid grid-cols-7 flex-1" id="schedule-grid-content">
                        <!-- Les 7 colonnes de jours (avec en-t├¬tes sticky et cellules de shifts) seront inject├®es ici par JS -->
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
const TeamCalendarApp = {
    config: { /* ... (inchang├®) ... */ 
        days: [ 
            { short: "DIM", long: "Dimanche", number: 13, isToday: false }, { short: "LUN", long: "Lundi", number: 14, isToday: false },
            { short: "MAR", long: "Mardi", number: 15, isToday: false }, { short: "MER", long: "Mercredi", number: 16, isToday: true },
            { short: "JEU", long: "Jeudi", number: 17, isToday: false }, { short: "VEN", long: "Vendredi", number: 18, isToday: false },
            { short: "SAM", long: "Samedi", number: 19, isToday: false },
        ],
        currentWeekDisplayFormat: "13 - 19 Octobre, 2024", 
    },
    data: { /* ... (inchang├®) ... */ 
        employees: [
            { id: "e1", name: "L├®a Dubois", status: "Temps Plein", avatar: "https://lh3.googleusercontent.com/aida-public/AB6AXuBJUv4p2EFaQSJQUhLtv43vX6UIZWhrNYTjabFdpuj7ApK1La3JhTNbyuFynrnCa3jx33rhdkVBoHomgnJ74UG7FlQYhMZVh0gX-RXTUN-Zs2LWuDhsVnZJOU0hxmwSwdyl97HQ4X1DkcSuvII2ij9yZR0O5ap_7EFFo5vl3oCmI0Yc-j9By79Tl1RnFYPKSHVKZDSXjGD7itSkuKoA7WrCaUVl8GWvCw36gl02wgjs3dWYq8kQ5jAybGbD0Ua9u45RaEfXPi9zppc8" },
            { id: "e2", name: "Omar Hassan", status: "Temps Partiel", avatar: "https://lh3.googleusercontent.com/aida-public/AB6AXuBjxaThkcOATFLiCux9TtocfaqWrCE_plIjv_eS2MV511lb-pEIM8WUdr00JFG4P_xQHkk1x3ZAPO6qmcD_Q1lCw4Rl1sS33eQYz9zrX5hOT-y_1BYzjxPBbGPJoqw4WSfkVYGyKf_1OdK-Vlrk047vhhCjrZXu-A2F0ikIM5Y0__Jr7251UlYXREaELioW0z4dZ0UZUX_undobvQVJmihmwW2-hP_yN568w1l9piT9wmSh-VEQGmKNxp0oRemJCpuNvqDDbHbsAtkb" },
            { id: "e3", name: "Chlo├® Moreau", status: "Temps Partiel", avatar: "https://lh3.googleusercontent.com/aida-public/AB6AXuC4XZpQFFHtfuvnDseQckFFru1NEe0r6hLgv88Ob0cFG0-y9OGDKjSHzrow7_fg6JjJus-Llv2PTXFCyY_kERTYn3gbal8HWCdvViX16LizOn2ekDIuD8KI7RJ726fIoSZNaMQf0Pyx8q1qs8rNJp6kMMXUij2HCOwS1NdKKWx9E1tpx0D1bOIYFqd8qngP1HY62wF2SJGzIPqCFINUxH6LLBl6RsF88mv9wU2SfIexc7mzGJhvHWouBe0VR6G8FTIX6P9k0iJ85ND3" },
            { id: "e4", name: "Aisha Khan", status: "Temps Plein", avatar: "https://lh3.googleusercontent.com/aida-public/AB6AXuAIS_REK7tUqyvOcMff5SpdCeykx_B03UVESNknuC3APEiSrDdBDGPe9HsZbiluQneOuHH2VvKtkmDrOv8cYAViljMI8nzr9NER2I_3A0fGcVmIxGF-x0avWb8YL4rW5HNuUIlLyd5esO5YzrVWX-hZwSW0tnEqq4XEzmTDZzSl3yMCWzppV40ohe3h6CHmb6PMF6Muh4lGYTmqZvEJ2Vmij9FhhXut0DvtdYgesrzsDAnfIk6TgbJN1QWBcaXhRoEMB67h1BFvo-p4" },
            { id: "e5", name: "Marc Lefevre", status: "Temps Plein", avatar: "https://lh3.googleusercontent.com/aida-public/AB6AXuAAG7ztV3EAe6dPmBv_4EwQO_e_tnpe6CwEwE3t1bt_y3nWGACqcM3dl0ehRPqRD0Axrl1Aqm_yRqIFUTjKY59FsRkFHN_4tVCg_rnf3HONwJTnKauhEzmi2va1P7M0sZYCQejJP-4wCN1sOvw2i7m2-xFcrtDIa2yGAHLpl8CJf1QylkEWIHStTrpvhLgB1WBnLQde-UVajC4G1rYj5BZnkqDzWBgf6sI495N6sj6rLZ4C5DMdQAJqd8U0i-BeIZPnb3bwQqgMo0H1" },
            { id: "e6", name: "Sophie Martin", status: "Temps Plein", avatar: "https://lh3.googleusercontent.com/aida-public/AB6AXuAFEDibedhAgyFQmbSSYtIbPxb8f4zxVkMyw1fSmn_TFp6rEqwotIX0iIEb12sV_HYN8TU8ZOGEhmvevpCuV1iwmwuN91ENww8JHTMfFVK1djvTfugMoRyls7oC00AWB2hdDZLewvxd-sJAz1GPpi4wMhqWQEPTWIyDJyS0Zawy6x3mXsamY6rUCCNDId2E_Mm2dZv4i0aPV6_TAv8vA_U0mjUea1k5bWgdL9rYbPV9xKyOmFsYek30JZCF8iBMm7gFRSW6F_kmjX6E" },
        ],
        schedule: {
            "e1": { 1: [{ text: "09:00 - 17:00", type: "sky" }], 2: [{ text: "09:00 - 17:00", type: "sky" }], 3: [{ text: "09:00 - 17:00", type: "sky" }], 4: [{ text: "09:00 - 17:00", type: "sky" }], 5: [{ text: "09:00 - 17:00", type: "sky" }] },
            "e2": { 0: [{ text: "08:00 - 16:00", type: "amber" }], 4: [{ text: "16:00 - 00:00", type: "amber" }], 5: [{ text: "16:00 - 00:00", type: "amber" }] },
            "e3": { 0: [{ text: "08:00 - 16:00", type: "amber" }], 4: [{ text: "16:00 - 00:00", type: "amber" }], 5: [{ text: "16:00 - 00:00", type: "amber" }] },
            "e4": { 1: [{ text: "Cong├®", type: "emerald", shape: "pill" }], 2: [{ text: "09:00 - 17:00", type: "sky" }], 3: [{ text: "09:00 - 17:00", type: "sky" }], 4: [{ text: "09:00 - 17:00", type: "sky" }], 6: [{ text: "Cong├®", type: "emerald", shape: "pill" }] },
            "e5": { 0: [{ text: "10:00 - 18:00", type: "sky" }], 1: [{ text: "09:00 - 17:00", type: "sky" }], 2: [{ text: "09:00 - 17:00", type: "sky" }], 3: [{ text: "09:00 - 17:00", type: "sky" }], 4: [{ text: "17:00 - 01:00", type: "amber" }], 5: [{ text: "17:00 - 01:00", type: "amber" }], 6: [{ text: "10:00 - 18:00", type: "sky" }] },
            "e6": { 1: [{ text: "10:00 - 14:00", type: "sky" }], 3: [{ text: "Cong├®", type: "emerald", shape: "pill" }], 5: [{ text: "13:00 - 17:00", type: "sky" }] }
        }
    },
    elements: { /* ... (inchang├®) ... */ 
        employeeListContainer: null, scheduleGridContent: null, currentWeekDisplay: null,
        scheduleContainer: null, scheduleGridScrollableContent: null, addShiftButton: null,
    },
    draggedItem: { /* ... (inchang├®) ... */ 
        type: null, data: null, originalEmployeeId: null, originalDayIndex: null, originalShiftIndex: null, originalEmployeeIndex: null
    },
    
    getEmployeeRowHeightClass: function() {
        const numEmployees = this.data.employees.length > 0 ? this.data.employees.length : 1;
        // Utiliser le diviseur de CODE1 (5) ou le nombre d'employ├®s si sup├®rieur, pour que les lignes ne deviennent pas TROP grandes.
        // Si on veut que les lignes r├®tr├®cissent toujours avec plus d'employ├®s, on utiliserait numEmployees directement.
        // Mais le `min-h` de CODE1 sugg├¿re une base de 5 lignes visibles.
        const divisor = Math.max(numEmployees, 5); 
        const fixedVerticalSpace = "15rem"; // Estimation de 12rem + 48px(3rem) + 1px
        return `min-h-[calc((100vh-${fixedVerticalSpace}-1px)/${divisor})]`;
    },

    init: function() { /* ... (inchang├®, les IDs sont corrects) ... */ 
        this.elements.employeeListContainer = document.getElementById('employee-list-container');
        this.elements.scheduleGridContent = document.getElementById('schedule-grid-content');
        this.elements.currentWeekDisplay = document.getElementById('current-week-display');
        this.elements.scheduleContainer = document.getElementById('schedule-container'); 
        this.elements.scheduleGridScrollableContent = document.getElementById('schedule-grid-scrollable-content'); 
        this.elements.addShiftButton = document.getElementById('add-shift-button');

        if (!this.elements.employeeListContainer || !this.elements.scheduleGridContent || !this.elements.currentWeekDisplay || !this.elements.scheduleContainer || !this.elements.scheduleGridScrollableContent || !this.elements.addShiftButton) {
            console.error("TeamCalendarApp: Un ou plusieurs ├®l├®ments DOM requis n'ont pas ├®t├® trouv├®s. Le rendu est annul├®."); return;
        }
        
        this.elements.currentWeekDisplay.textContent = this.config.currentWeekDisplayFormat;
        this.elements.addShiftButton.addEventListener('click', this.handleAddShiftPrompt.bind(this));
        
        this.render();
        this.initScrollIndicator(); 
    },

    render: function() { /* ... (inchang├®) ... */ 
        this.renderEmployees();
        this.renderScheduleGrid();
        this.attachAllEventListeners();
    },

    renderEmployees: function() { 
        this.elements.employeeListContainer.innerHTML = '';
        const employeeRowHeightClass = this.getEmployeeRowHeightClass();

        this.data.employees.forEach((emp, index) => {
            const empRowOuter = document.createElement('div');
            // La classe employee-row-info est ajout├®e pour diff├®rencier le hover de la ligne d'info vs cellules de shift
            empRowOuter.className = `employee-row-info employee-row employee-row-draggable transition-colors duration-150 ${employeeRowHeightClass}`;
            empRowOuter.draggable = true;
            empRowOuter.dataset.employeeId = emp.id;
            empRowOuter.dataset.employeeIndex = index;
            
            const empDivContent = document.createElement('div');
            // h-full assure que le contenu interne remplit la hauteur de empRowOuter
            // pointer-events-none sur le contenu pour faciliter le drag de la ligne parente
            empDivContent.className = 'employee-info-cell-content flex items-center p-4 gap-4 h-full pointer-events-none'; 

            empDivContent.innerHTML = `
                <img alt="${emp.name}" class="size-12 rounded-full object-cover employee-avatar" src="${emp.avatar}"/>
                <div>
                    <p class="text-slate-800 text-sm font-semibold leading-normal">${emp.name}</p>
                    <p class="text-slate-500 text-xs font-normal leading-normal">${emp.status}</p>
                </div>
            `;
            empRowOuter.appendChild(empDivContent);
            this.elements.employeeListContainer.appendChild(empRowOuter);
        });
    },

    renderScheduleGrid: function() { 
        this.elements.scheduleGridContent.innerHTML = '';
        const employeeRowHeightClass = this.getEmployeeRowHeightClass(); 

        this.config.days.forEach((day, dayIndex) => {
            const dayCol = document.createElement('div');
            // Classes de la colonne de jour de CODE1
            const dayColClasses = ['flex', 'flex-col', 'day-column', 'transition-colors', 'duration-150'];
            if (dayIndex < this.config.days.length - 1) dayColClasses.push('border-r', 'border-slate-300/40');
            if (day.isToday) dayColClasses.push('border-blue-300/50', 'bg-blue-50/40');
            dayCol.className = dayColClasses.join(' ');

            const dayHeader = document.createElement('div');
            // Classes de l'en-t├¬te de jour de CODE1 (sticky, etc.)
            let dayHeaderBaseClasses = 'day-header h-[calc(5.5rem-1px)] flex flex-col items-center justify-center sticky-header-bg';
            if (day.isToday) { 
                dayHeaderBaseClasses += ' border-b border-blue-300/50 bg-blue-100/70 backdrop-blur-sm z-10 shadow-inner'; // z-index:10 de CODE1 pour today
            } else {
                dayHeaderBaseClasses += ' border-b border-slate-300/40'; // z-index:20 par .day-header
            }
            dayHeader.className = dayHeaderBaseClasses;
            dayHeader.innerHTML = `
                <p class="${day.isToday ? 'text-blue-700' : 'text-slate-500'} text-xs font-semibold leading-normal tracking-wider">${day.short}</p>
                <p class="${day.isToday ? 'text-blue-700' : 'text-slate-900'} text-xl font-bold leading-tight">${day.number}</p>
            `;
            dayCol.appendChild(dayHeader);

            const dayCellsContainer = document.createElement('div');
            // flex-1 pour que ce conteneur prenne la hauteur restante dans la colonne de jour
            dayCellsContainer.className = `divide-y ${day.isToday ? 'divide-blue-200/70' : 'divide-slate-200/70'} flex-1`; 
            
            this.data.employees.forEach(employee => {
                const cell = document.createElement('div');
                // `employee-row` est ajout├® pour le hover synchronis├® de CODE1.
                let cellClasses = `p-3 ${employeeRowHeightClass} flex items-center justify-center transition-colors duration-150 employee-row schedule-grid-cell-dynamic`;
                if (day.isToday) cellClasses += ' hover:bg-blue-100/50'; 
                cell.className = cellClasses;
                cell.dataset.employeeId = employee.id;
                cell.dataset.dayIndex = dayIndex;

                const shiftsInCell = this.data.schedule[employee.id]?.[dayIndex] || [];
                shiftsInCell.forEach((shiftData, shiftIndex) => {
                    cell.appendChild(this.createShiftElement(shiftData, employee.id, dayIndex, shiftIndex, day.isToday));
                });
                dayCellsContainer.appendChild(cell);
            });
            dayCol.appendChild(dayCellsContainer);
            this.elements.scheduleGridContent.appendChild(dayCol);
        });
    },

    createShiftElement: function(shiftData, employeeId, dayIndex, shiftIndex, isTodayCell) { /* ... (inchang├®) ... */ 
        const shiftDiv = document.createElement('div');
        let baseClasses = 'shift-card text-xs font-semibold w-full text-center'; 
        let colorClasses = ''; let borderClasses = '';
        if (shiftData.type === 'amber') {
            colorClasses = isTodayCell ? 'bg-amber-100/90 hover:bg-amber-200/90 text-amber-800 hover:text-amber-900' : 'bg-amber-100/80 hover:bg-amber-200/90 text-amber-800 hover:text-amber-900';
            borderClasses = isTodayCell ? 'border border-blue-300/60 hover:border-blue-400/80' : 'border border-amber-300/60 hover:border-amber-400/80'; 
        } else if (shiftData.type === 'emerald') {
            colorClasses = isTodayCell ? 'bg-emerald-200/60 hover:bg-emerald-300/70 text-emerald-800 hover:text-emerald-900' : 'bg-emerald-100/80 hover:bg-emerald-200/90 text-emerald-800 hover:text-emerald-900';
            borderClasses = isTodayCell ? 'border border-blue-300/60 hover:border-blue-400/80' : 'border border-emerald-300/60 hover:border-emerald-400/80';
        } else { 
            colorClasses = isTodayCell ? 'bg-sky-200/60 hover:bg-sky-300/70 text-sky-800 hover:text-sky-900' : 'bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 hover:text-sky-900';
            borderClasses = isTodayCell ? 'border border-blue-300/60 hover:border-blue-400/80' : 'border border-sky-300/60 hover:border-sky-400/80';
        }
        let shapeClasses = 'p-3 rounded-lg'; 
        if (shiftData.shape === 'pill' || shiftData.text.toLowerCase() === 'cong├®') { shapeClasses = 'p-3 rounded-full shift-card-pill'; } 
        else if (shiftData.shape === 'angled') { shapeClasses = 'shift-card-angled p-3'; }
        shiftDiv.className = `${baseClasses} ${colorClasses} ${borderClasses} ${shapeClasses}`;
        shiftDiv.textContent = shiftData.text;
        shiftDiv.draggable = true;
        shiftDiv.dataset.employeeId = employeeId; shiftDiv.dataset.dayIndex = dayIndex;
        shiftDiv.dataset.shiftIndex = shiftIndex; shiftDiv.dataset.shiftPayload = JSON.stringify(shiftData);
        const deleteBtn = document.createElement('span');
        deleteBtn.className = 'delete-shift material-icons-outlined'; deleteBtn.innerHTML = 'close'; 
        deleteBtn.title = "Supprimer ce quart";
        deleteBtn.addEventListener('click', (e) => { e.stopPropagation(); this.handleDeleteShift(employeeId, dayIndex, shiftIndex); });
        shiftDiv.appendChild(deleteBtn);
        return shiftDiv;
    },

    attachAllEventListeners: function() { /* ... (inchang├®) ... */ 
        this.elements.scheduleGridContent.querySelectorAll('.shift-card').forEach(block => {
            block.addEventListener('dragstart', this.handleDragStart.bind(this)); block.addEventListener('dragend', this.handleDragEnd.bind(this));
        });
        this.elements.scheduleGridContent.querySelectorAll('.schedule-grid-cell-dynamic').forEach(cell => {
            cell.addEventListener('dragover', this.handleDragOver.bind(this)); cell.addEventListener('dragleave', this.handleDragLeave.bind(this));
            cell.addEventListener('drop', this.handleDrop.bind(this));
        });
        this.elements.employeeListContainer.querySelectorAll('.employee-row-draggable').forEach(row => {
            row.addEventListener('dragstart', this.handleDragStart.bind(this)); row.addEventListener('dragend', this.handleDragEnd.bind(this));
        });
        this.elements.employeeListContainer.addEventListener('dragover', this.handleEmployeeListDragOver.bind(this));
        this.elements.employeeListContainer.addEventListener('dragleave', this.handleEmployeeListDragLeave.bind(this));
        this.elements.employeeListContainer.addEventListener('drop', this.handleEmployeeDrop.bind(this));
    },
    
    handleAddShiftPrompt: function() { /* ... (inchang├®) ... */ 
        const employeeNames = this.data.employees.map((emp, i) => `${i + 1}: ${emp.name}`).join('\n');
        const empIndexStr = prompt(`Entrez le num├®ro de l'employ├®(e) pour le nouveau shift:\n${employeeNames}`);
        if (empIndexStr === null) return;
        const empIndex = parseInt(empIndexStr) - 1;
        if (isNaN(empIndex) || empIndex < 0 || empIndex >= this.data.employees.length) { alert("Num├®ro d'employ├® invalide."); return; }
        const employeeId = this.data.employees[empIndex].id;
        const dayNames = this.config.days.map((day, i) => `${i + 1}: ${day.long} (${day.number})`).join('\n');
        const dayIndexStr = prompt(`Entrez le num├®ro du jour pour le nouveau shift:\n${dayNames}`);
        if (dayIndexStr === null) return;
        const dayIndex = parseInt(dayIndexStr) - 1;
         if (isNaN(dayIndex) || dayIndex < 0 || dayIndex >= this.config.days.length) { alert("Num├®ro de jour invalide."); return; }
        const shiftText = prompt("Entrez le texte du shift (ex: 09:00 - 17:00 ou Cong├®):");
        if (!shiftText) { alert("Texte du shift requis."); return; }
        const shiftType = prompt("Entrez le type du shift (sky, amber, emerald):", "sky")?.toLowerCase();
        if (!['sky', 'amber', 'emerald'].includes(shiftType)) { alert("Type de shift invalide. Utilisez 'sky', 'amber', ou 'emerald'."); return; }
        let shiftShape = 'default'; 
        if (shiftText.toLowerCase() === 'cong├®') { shiftShape = 'pill'; }
        this.addShift(employeeId, dayIndex, { text: shiftText, type: shiftType, shape: shiftShape });
    },

    addShift: function(employeeId, dayIndex, shiftData) { /* ... (inchang├®) ... */ 
        if (!this.data.schedule[employeeId]) this.data.schedule[employeeId] = {};
        if (!this.data.schedule[employeeId][dayIndex]) this.data.schedule[employeeId][dayIndex] = [];
        this.data.schedule[employeeId][dayIndex].push(shiftData); this.render();
    },
    
    handleDeleteShift: function(employeeId, dayIndex, shiftIndex) { /* ... (inchang├®) ... */ 
        if (this.data.schedule[employeeId]?.[dayIndex]?.[shiftIndex]) {
            this.data.schedule[employeeId][dayIndex].splice(shiftIndex, 1);
            if (this.data.schedule[employeeId][dayIndex].length === 0) {
                delete this.data.schedule[employeeId][dayIndex];
                if (Object.keys(this.data.schedule[employeeId]).length === 0) { delete this.data.schedule[employeeId]; }
            } this.render();
        }
    },

    handleDragStart: function(e) { /* ... (inchang├®) ... */ 
        const target = e.target;
        if (target.classList.contains('shift-card')) {
            target.classList.add('dragging');
            this.draggedItem = { type: 'shift', data: JSON.parse(target.dataset.shiftPayload),
                originalEmployeeId: target.dataset.employeeId, originalDayIndex: parseInt(target.dataset.dayIndex),
                originalShiftIndex: parseInt(target.dataset.shiftIndex) };
        } else if (target.classList.contains('employee-row-draggable')) {
            target.classList.add('dragging');
             this.draggedItem = { type: 'employee', employeeId: target.dataset.employeeId,
                originalEmployeeIndex: parseInt(target.dataset.employeeIndex) };
        }
        if (this.draggedItem.type) { e.dataTransfer.effectAllowed = 'move'; e.dataTransfer.setData('text/plain', this.draggedItem.type); }
    },

    handleDragEnd: function(e) { /* ... (inchang├®) ... */ 
        if (e.target && e.target.classList && e.target.classList.contains('dragging')) { e.target.classList.remove('dragging'); }
        this.elements.scheduleGridContent.querySelectorAll('.drop-target-active').forEach(el => el.classList.remove('drop-target-active'));
        this.elements.employeeListContainer.querySelectorAll('.employee-drop-indicator').forEach(el => el.remove());
        this.draggedItem = { type: null, data: null, originalEmployeeId: null, originalDayIndex: null, originalShiftIndex: null, originalEmployeeIndex: null };
    },

    handleDragOver: function(e) { /* ... (inchang├®) ... */ 
        e.preventDefault(); const targetCell = e.target.closest('.schedule-grid-cell-dynamic');
        this.elements.scheduleGridContent.querySelectorAll('.drop-target-active').forEach(el => el.classList.remove('drop-target-active'));
        if (this.draggedItem.type === 'shift' && targetCell) { targetCell.classList.add('drop-target-active'); e.dataTransfer.dropEffect = 'move';
        } else { e.dataTransfer.dropEffect = 'none'; }
    },
    
    handleDragLeave: function(e) { /* ... (inchang├®) ... */ 
        const targetCell = e.target.closest('.schedule-grid-cell-dynamic');
        if (targetCell && !targetCell.contains(e.relatedTarget)) { targetCell.classList.remove('drop-target-active'); }
    },

    handleEmployeeListDragOver: function(e) { /* ... (inchang├®) ... */ 
        e.preventDefault(); if (this.draggedItem.type !== 'employee') { e.dataTransfer.dropEffect = 'none'; return; }
        e.dataTransfer.dropEffect = 'move'; const container = this.elements.employeeListContainer;
        const directChildren = Array.from(container.children).filter(child => child.classList.contains('employee-row-draggable'));
        const afterElement = directChildren.reduce((closest, child) => {
            const box = child.getBoundingClientRect(); const offset = e.clientY - (box.top + box.height / 2);
            if (offset < 0 && offset > closest.offset) { return { offset: offset, element: child }; } else { return closest; }
        }, { offset: Number.NEGATIVE_INFINITY }).element;
        this.elements.employeeListContainer.querySelectorAll('.employee-drop-indicator').forEach(el => el.remove());
        const indicator = document.createElement('div'); indicator.className = 'employee-drop-indicator';
        if (afterElement) { container.insertBefore(indicator, afterElement); indicator.style.top = (afterElement.offsetTop - (indicator.offsetHeight / 2) - 2) + 'px'; 
        } else { container.appendChild(indicator); const lastChild = container.lastChild.previousSibling; 
            if(lastChild && lastChild.offsetTop && lastChild.offsetHeight){ indicator.style.top = (lastChild.offsetTop + lastChild.offsetHeight - (indicator.offsetHeight/2) + 2) + 'px';
            } else { indicator.style.top = '2px'; }
        }
    },

    handleEmployeeListDragLeave: function(e) { /* ... (inchang├®) ... */ 
        if (!this.elements.employeeListContainer.contains(e.relatedTarget)) {
            this.elements.employeeListContainer.querySelectorAll('.employee-drop-indicator').forEach(el => el.remove());
        }
    },

    handleDrop: function(e) { /* ... (inchang├®) ... */ 
        e.preventDefault(); if (this.draggedItem.type === 'shift') { this.handleShiftDrop(e); } 
    },

    handleShiftDrop: function(e) { /* ... (inchang├®) ... */ 
        const targetCell = e.target.closest('.schedule-grid-cell-dynamic'); if (!targetCell) return;
        targetCell.classList.remove('drop-target-active'); const targetEmployeeId = targetCell.dataset.employeeId;
        const targetDayIndex = parseInt(targetCell.dataset.dayIndex); const shiftDataToMove = { ...this.draggedItem.data };
        const sourceShifts = this.data.schedule[this.draggedItem.originalEmployeeId]?.[this.draggedItem.originalDayIndex];
        if (sourceShifts) {
            sourceShifts.splice(this.draggedItem.originalShiftIndex, 1);
            if (sourceShifts.length === 0) delete this.data.schedule[this.draggedItem.originalEmployeeId][this.draggedItem.originalDayIndex];
            if (Object.keys(this.data.schedule[this.draggedItem.originalEmployeeId] || {}).length === 0) delete this.data.schedule[this.draggedItem.originalEmployeeId];
        }
        if (!this.data.schedule[targetEmployeeId]) this.data.schedule[targetEmployeeId] = {};
        if (!this.data.schedule[targetEmployeeId][targetDayIndex]) this.data.schedule[targetEmployeeId][targetDayIndex] = [];
        this.data.schedule[targetEmployeeId][targetDayIndex].push(shiftDataToMove); this.render();
    },

    handleEmployeeDrop: function(e) { /* ... (inchang├®) ... */ 
        e.preventDefault(); if (this.draggedItem.type !== 'employee') return;
        this.elements.employeeListContainer.querySelectorAll('.employee-drop-indicator').forEach(el => el.remove());
        const targetRow = e.target.closest('.employee-row-draggable'); let targetIndex = this.draggedItem.originalEmployeeIndex; 
        if (targetRow && targetRow.dataset.employeeId !== this.draggedItem.employeeId) {
            const rect = targetRow.getBoundingClientRect(); const isAfter = e.clientY > rect.top + rect.height / 2;
            targetIndex = parseInt(targetRow.dataset.employeeIndex);
            if (isAfter) targetIndex++; if (this.draggedItem.originalEmployeeIndex < targetIndex) { targetIndex--; }
        } else { 
            const container = this.elements.employeeListContainer;
            const directChildren = Array.from(container.children).filter(child => child.classList.contains('employee-row-draggable'));
            let insertAtIndex = directChildren.length; 
            for(let i=0; i < directChildren.length; i++) {
                const child = directChildren[i]; const box = child.getBoundingClientRect();
                if (e.clientY < box.top + box.height / 2) { insertAtIndex = i; break; }
            }
            targetIndex = insertAtIndex; if (this.draggedItem.originalEmployeeIndex < targetIndex) { targetIndex--;  }
        }
        if (this.draggedItem.originalEmployeeIndex === targetIndex) { this.render(); return; }
        const [movedEmployee] = this.data.employees.splice(this.draggedItem.originalEmployeeIndex, 1);
        this.data.employees.splice(targetIndex, 0, movedEmployee); this.render();
    },

    initScrollIndicator: function() { /* ... (inchang├®) ... */ 
        const scheduleContainer = this.elements.scheduleContainer; 
        const scheduleGrid = this.elements.scheduleGridScrollableContent; 
        let scrollTimeout;
        function checkScroll() {
            if (!scheduleGrid || !scheduleContainer) return;
            const isScrollable = scheduleGrid.scrollHeight > scheduleGrid.clientHeight;
            const isAtBottom = scheduleGrid.scrollTop + scheduleGrid.clientHeight >= scheduleGrid.scrollHeight - 20; 
            if (isScrollable && !isAtBottom) { scheduleContainer.classList.add('scrolling'); } 
            else { scheduleContainer.classList.remove('scrolling'); }
        }
        scheduleGrid.addEventListener('scroll', () => {
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => { checkScroll(); }, 150); checkScroll(); 
        });
        checkScroll(); window.addEventListener('resize', checkScroll);
        const observer = new MutationObserver(checkScroll);
        if (scheduleGrid) { observer.observe(scheduleGrid, { childList: true, subtree: true, attributes: true, characterData: true }); }
    }
};
document.addEventListener('DOMContentLoaded', () => { TeamCalendarApp.init(); });
</script>
</body>
</html>
