/* ✅ FONTS AUTO-HÉBERGÉES */
@import './styles/fonts.css';
@import './styles/regular-assignment-drag.css';
@import './styles/regular-assignment-confirmation.css';

/* Tailwind CSS directives - Configuration valide pour PostCSS */
/* stylelint-disable-next-line at-rule-no-unknown */
@tailwind base;
/* stylelint-disable-next-line at-rule-no-unknown */
@tailwind components;
/* stylelint-disable-next-line at-rule-no-unknown */
@tailwind utilities;

body {
  font-family: Inter, "Noto Sans", sans-serif;
}

/* Styles personnalisés pour le calendrier d'équipe */
.employee-row:hover {
  background-color: rgba(248, 250, 252, 0.8); /* un peu plus clair que le hover de post-row */
}

.post-row-info {
  opacity: 0.9;
}

.post-row-info:hover {
  opacity: 1;
  background-color: rgba(148, 163, 184, 0.1);
}

.available-shift-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.shift-card {
  transition: all 0.2s ease-in-out;
}

.shift-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

.employee-row-draggable, .post-row-info {
  transition: all 0.2s ease-in-out;
}

.employee-row-draggable:hover, .post-row-info:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 14px rgba(94, 106, 123, 0.1);
  z-index: 5; /* Assure que l'ombre est visible au-dessus des autres éléments */
  position: relative;
}

/* --- Surbrillance du jour actuel --- */
.shadow-inner-strong {
  box-shadow: inset 0 0 15px 5px rgba(59, 130, 246, 0.1);
}

.day-column.bg-blue-100-50 {
  background-color: rgba(219, 234, 254, 0.5) !important;
}

/* --- Styles pour la modale de configuration --- */
#settings-modal.hidden {
  display: none;
}

/* Permet le défilement dans la modale sur les petits écrans */
#posts-config-container {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

#posts-config-container::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

/* Améliorations pour les statistiques */
#stats-output {
  overflow: hidden;
}

#stats-output .material-icons-outlined {
  font-size: inherit;
  line-height: inherit;
}

/* Correction des alignements - CRITIQUE pour l'alignement parfait */
.schedule-grid-cell-dynamic {
  min-height: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}

.employee-info-cell-content,
.post-info-cell-content {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

.employee-row {
  height: var(--row-height, 75px); /* Applique la hauteur de ligne depuis la variable JS */
  box-sizing: border-box;
  border-bottom: 1px solid #e2e8f0;
}

#employee-list-container .employee-row:last-child {
  border-bottom: none;
}

.day-cells-container .employee-row:last-child {
    border-bottom: none;
}

/* En-têtes sticky qui restent visibles au défilement */
.employee-info-header, .day-header {
  position: sticky;
  top: 0;
  background: rgba(248, 250, 252, 0.85); /* slate-50/85 */
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

.employee-info-header { z-index: 20; }
.day-header { z-index: 10; }

/* Force la grille à maintenir les lignes alignées */
.schedule-grid-container {
  display: grid;
  grid-template-columns: 220px 1fr;
  align-items: start;
}

#employee-list-container {
  display: flex;
  flex-direction: column;
}

#schedule-grid-content {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
}

/* Assure que toutes les colonnes de jours sont alignées */
.day-column {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* Animation de chargement */
.loading-animation {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

/* --- Zone de dépôt pour les postes --- */
#employee-list-container.drag-target {
    background-color: rgba(59, 130, 246, 0.1);
    border: 2px dashed rgba(59, 130, 246, 0.3);
    border-radius: 8px;
}

/* ✅ NOUVEAU : Styles pour les zones de drop actives */
.drop-zone-active {
    background-color: rgba(59, 130, 246, 0.15) !important;
    border: 2px dashed rgba(59, 130, 246, 0.4) !important;
    border-radius: 8px !important;
    transform: scale(1.02) !important;
    transition: all 0.2s ease-in-out !important;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2) !important;
}

/* ===== SYSTÈME DE TUTORIEL INTERACTIF AMÉLIORÉ ===== */

/* Icône d'aide flottante */
.tutorial-help-icon {
    position: fixed !important;
    bottom: 20px !important;
    right: 20px !important;
    width: 60px !important;
    height: 60px !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3) !important;
    z-index: 9999 !important;
    transition: all 0.3s ease !important;
    animation: tutorial-pulse 2s infinite !important;
    border: none !important;
}

.tutorial-help-icon:hover {
    transform: scale(1.1) !important;
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4) !important;
}

.tutorial-help-icon .material-icons {
    color: white !important;
    font-size: 28px !important;
}

@keyframes tutorial-pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Overlay principal du tutoriel */
.tutorial-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    background: rgba(0, 0, 0, 0.8) !important;
    z-index: 10000 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    animation: tutorial-fade-in 0.5s ease !important;
}

@keyframes tutorial-fade-in {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Menu principal du tutoriel */
.tutorial-menu {
    background: white !important;
    border-radius: 20px !important;
    padding: 30px !important;
    max-width: 500px !important;
    width: 90% !important;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3) !important;
    animation: tutorial-slide-up 0.6s ease !important;
    position: relative !important;
}

@keyframes tutorial-slide-up {
    from { 
        opacity: 0;
        transform: translateY(50px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

.tutorial-menu h2 {
    color: #4f46e5 !important;
    font-size: 28px !important;
    font-weight: 700 !important;
    margin-bottom: 10px !important;
    text-align: center !important;
}

.tutorial-menu .subtitle {
    color: #6b7280 !important;
    font-size: 16px !important;
    text-align: center !important;
    margin-bottom: 25px !important;
}

.tutorial-options {
    display: grid !important;
    gap: 15px !important;
}

.tutorial-option {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
    border: 2px solid #e2e8f0 !important;
    border-radius: 12px !important;
    padding: 20px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    display: flex !important;
    align-items: center !important;
    gap: 15px !important;
}

.tutorial-option:hover {
    border-color: #4f46e5 !important;
    background: linear-gradient(135deg, #eef2ff 0%, #e0e7ff 100%) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(79, 70, 229, 0.15) !important;
}

.tutorial-option .icon {
    width: 50px !important;
    height: 50px !important;
    border-radius: 12px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 24px !important;
    color: white !important;
}

.tutorial-option.beginner .icon {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
}

.tutorial-option.advanced .icon {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
}

.tutorial-option.features .icon {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%) !important;
}

.tutorial-option .content h3 {
    font-size: 18px !important;
    font-weight: 600 !important;
    color: #1f2937 !important;
    margin-bottom: 5px !important;
}

.tutorial-option .content p {
    font-size: 14px !important;
    color: #6b7280 !important;
    margin: 0 !important;
}

.tutorial-btn {
    padding: 8px 16px !important;
    border-radius: 8px !important;
    border: none !important;
    cursor: pointer !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
}

.tutorial-btn.primary {
    background: #4f46e5 !important;
    color: white !important;
}

.tutorial-btn.primary:hover {
    background: #4338ca !important;
    transform: translateY(-1px) !important;
}

.tutorial-btn.skip {
    background: transparent !important;
    color: #6b7280 !important;
    text-decoration: underline !important;
}

.tutorial-btn.skip:hover {
    color: #374151 !important;
}

/* ===== STYLES AMÉLIORÉS POUR LE SYSTÈME DE TUTORIEL ===== */

/* Overlay de mise en évidence amélioré */
.tutorial-highlight-overlay-enhanced {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    background: rgba(0, 0, 0, 0.2) !important;
    z-index: 9998 !important;
    pointer-events: none !important;
    animation: tutorial-fade-in 0.5s ease !important;
}

/* Spotlight amélioré avec zone bien éclairée */
.tutorial-spotlight-enhanced {
    position: fixed !important;
    border: 4px solid #4f46e5 !important;
    border-radius: 16px !important;
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(2px) !important;
    box-shadow: 
        0 0 0 9999px rgba(0, 0, 0, 0.2),
        0 0 50px rgba(79, 70, 229, 0.8),
        inset 0 0 30px rgba(79, 70, 229, 0.1) !important;
    animation: tutorial-spotlight-glow 2s infinite !important;
    z-index: 9999 !important;
    pointer-events: none !important;
}

@keyframes tutorial-spotlight-glow {
    0%, 100% { 
        border-color: #4f46e5;
        box-shadow: 
            0 0 0 9999px rgba(0, 0, 0, 0.2),
            0 0 50px rgba(79, 70, 229, 0.8),
            inset 0 0 30px rgba(79, 70, 229, 0.1);
    }
    50% { 
        border-color: #06b6d4;
        box-shadow: 
            0 0 0 9999px rgba(0, 0, 0, 0.2),
            0 0 60px rgba(6, 182, 212, 1),
            inset 0 0 40px rgba(6, 182, 212, 0.3);
    }
}

/* Flèche pointant vers l'élément */
.tutorial-arrow {
    position: fixed !important;
    width: 0 !important;
    height: 0 !important;
    border-left: 20px solid transparent !important;
    border-right: 20px solid transparent !important;
    border-top: 30px solid #4f46e5 !important;
    z-index: 10000 !important;
    animation: tutorial-arrow-bounce 1.5s infinite !important;
    pointer-events: none !important;
}

@keyframes tutorial-arrow-bounce {
    0%, 100% { 
        transform: translateY(0);
        opacity: 1;
    }
    50% { 
        transform: translateY(-10px);
        opacity: 0.7;
    }
}

/* Bulle d'explication améliorée */
.tutorial-bubble-enhanced {
    position: fixed !important;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
    border-radius: 20px !important;
    padding: 25px !important;
    max-width: 400px !important;
    min-width: 320px !important;
    box-shadow: 0 25px 80px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(79, 70, 229, 0.1) !important;
    z-index: 10001 !important;
    animation: tutorial-bubble-appear-enhanced 0.6s ease !important;
    font-family: Inter, system-ui, sans-serif !important;
    color: #374151 !important;
    border: 3px solid #4f46e5 !important;
}

@keyframes tutorial-bubble-appear-enhanced {
    from { 
        opacity: 0;
        transform: scale(0.8) translateY(30px);
    }
    to { 
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* ===== ANIMATIONS POUR LES SIMULATIONS ===== */

/* Animation de clic */
@keyframes tutorial-click-ripple {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    50% {
        transform: scale(1.5);
        opacity: 0.7;
    }
    100% {
        transform: scale(3);
        opacity: 0;
    }
}

/* Animation pour les postes draggables */
.post-row-info[draggable="true"] {
    cursor: grab;
}

.post-row-info[draggable="true"]:active {
    cursor: grabbing;
}

/* --- Styles pour les onglets de configuration --- */
.settings-tab {
    transition: all 0.2s ease-in-out;
    border-bottom: 2px solid transparent;
}

.settings-tab.active {
    border-bottom-color: #2563eb;
}

.tab-content {
    min-height: 200px;
}

/* --- Correction de l'alignement des postes non attribués --- */
#available-posts-container .post-row-info {
    height: var(--row-height, 75px);
    display: flex;
    align-items: center;
    border-bottom: 1px solid #e2e8f0;
}

#available-posts-container .post-row-info:last-child {
    border-bottom: none;
}

/* Assurer que la section des postes non attribués garde la même largeur */
#available-posts-container {
    width: 100%;
    box-sizing: border-box;
}

/* Styles pour les onglets du modal de configuration */
.tab-btn {
    transition: all 0.2s ease-in-out;
    color: #64748b; /* text-slate-500 */
    background-color: transparent;
    border: 1px solid transparent;
}

.tab-btn:hover {
    background-color: #f1f5f9; /* bg-slate-100 */
    color: #475569; /* text-slate-600 */
}

.tab-btn.active {
    background-color: #2563eb; /* bg-blue-600 */
    color: white;
    border-color: #2563eb;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.tab-content {
    transition: opacity 0.2s ease-in-out;
}

.tab-content.hidden {
  display: none;
}

/* ✅ NOUVEAU : Styles pour la surbrillance de drop intelligente */
.drop-zone-ready {
    transition: all 0.3s ease-in-out !important;
    position: relative !important;
}

.drop-zone-ready:after {
    content: '' !important;
    position: absolute !important;
    inset: 0 !important;
    border: 2px dashed transparent !important;
    border-radius: 8px !important;
    pointer-events: none !important;
    transition: all 0.3s ease-in-out !important;
}

.drop-zone-hover {
    transition: all 0.2s ease-in-out !important;
    z-index: 10 !important;
    position: relative !important;
}

/* Effet spécifique pour les postes standards */
.drop-zone-hover.drop-zone-active {
    background-color: #dbeafe !important;
    border: 2px dashed #3b82f6 !important;
    border-radius: 8px !important;
    transform: scale(1.01) !important;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
}

/* Effet spécifique pour les attributions régulières */
.drop-zone-hover.regular-assignment-drop-zone {
    background-color: #fef3c7 !important;
    border: 2px dashed #f59e0b !important;
    border-radius: 8px !important;
    transform: scale(1.01) !important;
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3) !important;
}

/* Animation de sortie fluide */
.drop-zone-ready:not(.drop-zone-hover) {
    transform: scale(1) !important;
    box-shadow: none !important;
    background-color: transparent !important;
    border: transparent !important;
}

/* ✅ NOUVEAU : Styles pour le preview fantôme intégré dans l'agenda */
.ghost-preview-shift {
    position: absolute !important;
    inset: 4px !important;
    opacity: 0 !important;
    transform: scale(0.8) !important;
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1) !important;
    pointer-events: none !important;
    z-index: 5 !important;
}

.ghost-preview-shift.ghost-visible {
    opacity: 1 !important;
    transform: scale(1) !important;
}

.ghost-preview-shift.ghost-removing {
    opacity: 0 !important;
    transform: scale(0.6) !important;
    transition: all 0.15s ease-in !important;
}

.ghost-content {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(99, 102, 241, 0.15) 100%) !important;
    border: 2px dashed #3b82f6 !important;
    border-radius: 8px !important;
    padding: 8px !important;
    height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 4px !important;
    position: relative !important;
    backdrop-filter: blur(2px) !important;
    -webkit-backdrop-filter: blur(2px) !important;
}

.ghost-icon {
    font-size: 16px !important;
    color: #3b82f6 !important;
    opacity: 0.8 !important;
}

.ghost-hours {
    font-size: 11px !important;
    font-weight: 600 !important;
    color: #1e40af !important;
    text-align: center !important;
    line-height: 1.2 !important;
}

.ghost-overlay {
    position: absolute !important;
    top: 2px !important;
    right: 2px !important;
    width: 18px !important;
    height: 18px !important;
    background: #3b82f6 !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    opacity: 0 !important;
    transform: scale(0) !important;
    transition: all 0.2s ease-out !important;
}

.ghost-overlay .material-icons-outlined {
    font-size: 12px !important;
    color: white !important;
}

/* Animation pour les fantômes ciblés */
.ghost-preview-shift.ghost-targeted {
    transform: scale(1.05) !important;
    z-index: 10 !important;
}

.ghost-targeted .ghost-content {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.25) 0%, rgba(99, 102, 241, 0.25) 100%) !important;
    border-color: #1d4ed8 !important;
    border-width: 3px !important;
    box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3) !important;
}

.ghost-targeted .ghost-overlay {
    opacity: 1 !important;
    transform: scale(1) !important;
}

/* Cellule ciblée pour le drop */
.cell-drop-target {
    background-color: rgba(59, 130, 246, 0.1) !important;
    transform: scale(1.02) !important;
    transition: all 0.2s ease-out !important;
    box-shadow: inset 0 0 0 2px rgba(59, 130, 246, 0.3) !important;
    border-radius: 6px !important;
}

/* Animation d'onde pour l'apparition des fantômes */
@keyframes ghost-wave-appear {
    0% {
        opacity: 0;
        transform: scale(0.5) translateY(10px);
    }
    60% {
        opacity: 0.8;
        transform: scale(1.1) translateY(-2px);
    }
    100% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* Fantômes avec délai d'apparition pour effet de vague */
.ghost-preview-shift:nth-child(1) { animation-delay: 0ms !important; }
.ghost-preview-shift:nth-child(2) { animation-delay: 50ms !important; }
.ghost-preview-shift:nth-child(3) { animation-delay: 100ms !important; }
.ghost-preview-shift:nth-child(4) { animation-delay: 150ms !important; }
.ghost-preview-shift:nth-child(5) { animation-delay: 200ms !important; }

/* Effet de pulsation subtile pour attirer l'attention */
@keyframes ghost-pulse {
    0%, 100% {
        opacity: 0.7;
    }
    50% {
        opacity: 1;
    }
}

.ghost-content:hover {
    animation: ghost-pulse 2s infinite !important;
}

/* Optimisation pour les appareils mobiles/tactiles */
@media (hover: none) and (pointer: coarse) {
    .ghost-preview-shift {
        inset: 6px !important;
    }
    
    .ghost-content {
        padding: 6px !important;
    }
    
    .ghost-icon {
        font-size: 14px !important;
    }
    
    .ghost-hours {
        font-size: 10px !important;
    }
    
    .ghost-overlay {
        width: 16px !important;
        height: 16px !important;
    }
    
    .ghost-overlay .material-icons-outlined {
        font-size: 10px !important;
    }
}

/* ✅ NOUVEAU : Styles pour le drag & drop des employés */
.employee-dragging .employee-row {
  transition: transform 0.2s ease;
}

.employee-drag-ghost {
  opacity: 0.4;
  background: linear-gradient(135deg, #e0e7ff, #c7d2fe);
  border: 2px dashed #6366f1;
  transform: rotate(3deg);
}

.employee-drag-chosen {
  transform: scale(1.02);
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
  z-index: 1000;
}

.employee-drag-active {
  opacity: 0.8;
  transform: rotate(-2deg);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
}

/* ✅ NOUVEAU : Styles pour le mode swap */
.swap-selected {
  animation: swap-pulse 2s infinite;
}

.swap-target-available {
  animation: swap-target-glow 3s infinite;
}

@keyframes swap-pulse {
  0%, 100% { 
    background-color: #8b5cf6; 
    transform: scale(1);
  }
  50% { 
    background-color: #7c3aed; 
    transform: scale(1.01);
  }
}

@keyframes swap-target-glow {
  0%, 100% { 
    border-color: #8b5cf6; 
    box-shadow: 0 0 0 0 rgba(139, 92, 246, 0.4);
  }
  50% { 
    border-color: #a855f7; 
    box-shadow: 0 0 0 4px rgba(139, 92, 246, 0.2);
  }
}

/* ✅ Amélioration des handles de drag */
.employee-drag-handle:hover {
  background-color: rgba(156, 163, 175, 0.1);
  transform: scale(1.1);
}

.employee-drag-handle:active {
  transform: scale(0.95);
}

/* ✅ Amélioration des icônes de swap */
.swap-icon:hover {
  transform: scale(1.15) rotate(180deg);
  transition: all 0.3s ease;
}

/* ✅ Styles pour les indicateurs de swap */
.swap-selected-indicator {
  animation: indicator-bounce 1s infinite;
}

.swap-target-indicator {
  animation: indicator-float 2s ease-in-out infinite;
}

@keyframes indicator-bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-2px); }
}

@keyframes indicator-float {
  0%, 100% { transform: translateY(0) scale(1); }
  50% { transform: translateY(-1px) scale(1.05); }
}

/* ✅ Amélioration de la lisibilité en mode swap */
.employee-row.swap-selected * {
  color: white !important;
}

/* Styles pour les avatars d'employés draggables */
.employee-row .employee-avatar {
  cursor: grab !important;
  transition: transform 0.2s, box-shadow 0.2s;
  user-select: none;
  position: relative;
}

.employee-row .employee-avatar::before {
  content: "⋮⋮";
  position: absolute;
  top: 50%;
  left: -5px;
  transform: translateY(-50%);
  font-size: 14px;
  color: #6b7280;
  opacity: 0.7;
}

.employee-row .employee-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.employee-row .employee-avatar:hover::before {
  opacity: 1;
  color: #4f46e5;
}

.employee-row.dragging {
  background-color: #f3f4f6 !important;
  opacity: 0.8;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.sortable-ghost {
  opacity: 0.4;
  background-color: #e5e7eb !important;
}

.sortable-chosen {
  background-color: #f3f4f6 !important;
}

.sortable-drag {
  opacity: 0.8;
  background-color: #f9fafb !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}
