[{
	"resource": "/c:/Users/<USER>/Desktop/interface/interface-3/interface - 1 Backup/backups/backup-2025-07-07T16-46-28-613Z/tsconfig.json",
	"owner": "typescript",
	"severity": 8,
	"message": "File 'c:/Users/<USER>/Desktop/interface/interface-3/interface - 1 Backup/backups/backup-2025-07-07T16-46-28-613Z/tsconfig.node.json' not found.",
	"source": "ts",
	"startLineNumber": 36,
	"startColumn": 18,
	"endLineNumber": 36,
	"endColumn": 52,
	"origin": "extHost1"
}]
[{
	"resource": "/c:/Users/<USER>/Desktop/interface/interface-3/interface - 1 Backup/src/teamCalendarApp.ts",
	"owner": "typescript",
	"code": "2339",
	"severity": 8,
	"message": "Property 'style' does not exist on type 'Element'.",
	"source": "ts",
	"startLineNumber": 1981,
	"startColumn": 31,
	"endLineNumber": 1981,
	"endColumn": 36,
	"origin": "extHost1"
},{
	"resource": "/c:/Users/<USER>/Desktop/interface/interface-3/interface - 1 Backup/src/teamCalendarApp.ts",
	"owner": "typescript",
	"code": "2339",
	"severity": 8,
	"message": "Property 'style' does not exist on type 'Element'.",
	"source": "ts",
	"startLineNumber": 1981,
	"startColumn": 65,
	"endLineNumber": 1981,
	"endColumn": 70,
	"origin": "extHost1"
},{
	"resource": "/c:/Users/<USER>/Desktop/interface/interface-3/interface - 1 Backup/src/teamCalendarApp.ts",
	"owner": "typescript",
	"code": "2339",
	"severity": 8,
	"message": "Property 'style' does not exist on type 'Element'.",
	"source": "ts",
	"startLineNumber": 1982,
	"startColumn": 71,
	"endLineNumber": 1982,
	"endColumn": 76,
	"origin": "extHost1"
},{
	"resource": "/c:/Users/<USER>/Desktop/interface/interface-3/interface - 1 Backup/src/teamCalendarApp.ts",
	"owner": "typescript",
	"code": "2339",
	"severity": 8,
	"message": "Property 'style' does not exist on type 'Element'.",
	"source": "ts",
	"startLineNumber": 1983,
	"startColumn": 35,
	"endLineNumber": 1983,
	"endColumn": 40,
	"origin": "extHost1"
},{
	"resource": "/c:/Users/<USER>/Desktop/interface/interface-3/interface - 1 Backup/src/teamCalendarApp.ts",
	"owner": "typescript",
	"code": "2353",
	"severity": 8,
	"message": "Object literal may only specify known properties, and 'closeSettingsModalHelper' does not exist in type 'TeamCalendarAppType'.",
	"source": "ts",
	"startLineNumber": 13577,
	"startColumn": 5,
	"endLineNumber": 13577,
	"endColumn": 29,
	"origin": "extHost1"
},{
	"resource": "/c:/Users/<USER>/Desktop/interface/interface-3/interface - 1 Backup/src/teamCalendarApp.ts",
	"owner": "typescript",
	"code": "2339",
	"severity": 8,
	"message": "Property 'closeModal' does not exist on type 'ModalFunctionalitiesManager'.",
	"source": "ts",
	"startLineNumber": 13578,
	"startColumn": 81,
	"endLineNumber": 13578,
	"endColumn": 91,
	"origin": "extHost1"
},{
	"resource": "/c:/Users/<USER>/Desktop/interface/interface-3/interface - 1 Backup/src/teamCalendarApp.ts",
	"owner": "typescript",
	"code": "2339",
	"severity": 8,
	"message": "Property 'closeModal' does not exist on type 'ModalFunctionalitiesManager'.",
	"source": "ts",
	"startLineNumber": 13579,
	"startColumn": 34,
	"endLineNumber": 13579,
	"endColumn": 44,
	"origin": "extHost1"
}]
[18/07/2025 09:01:41] [BACKEND] [INFO] [LOGS] Client connecté: 1752843701559
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T13:01:41.561Z"
}
[18/07/2025 09:01:41] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[18/07/2025 09:01:41] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[18/07/2025 08:51:37] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"42c64a69-ab23-451f-a314-c5c87622675d","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-27T04:00:00.000Z","dayDateKey":"2025-07-27","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5]}
[18/07/2025 08:51:37] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution 42c64a69-ab23-451f-a314-c5c87622675d: [{"dateKey":"2025-07-27","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-28","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-29","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-30","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-31","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-08-01","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-08-02","isWeekend":true,"dayOfWeek":6}]
[18/07/2025 08:51:37] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[18/07/2025 08:51:37] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-27 ignoré pour attribution 42c64a69-ab23-451f-a314-c5c87622675d
[18/07/2025 08:51:37] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-28 (1)
[18/07/2025 08:51:37] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"42c64a69-ab23-451f-a314-c5c87622675d","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-28T04:00:00.000Z","dayDateKey":"2025-07-28","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5]}
[18/07/2025 08:51:37] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-28: {"assignmentId":"42c64a69-ab23-451f-a314-c5c87622675d","dayDateStr":"2025-07-28","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 08:51:37] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[18/07/2025 08:51:37] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-28 ignoré pour attribution 42c64a69-ab23-451f-a314-c5c87622675d
[18/07/2025 08:51:37] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-28 avant startDate 2025-07-30 (original: 2025-07-30)
[18/07/2025 08:51:37] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"42c64a69-ab23-451f-a314-c5c87622675d","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-29T04:00:00.000Z","dayDateKey":"2025-07-29","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5]}
[18/07/2025 08:51:37] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-29 (2)
[18/07/2025 08:51:37] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[18/07/2025 08:51:37] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-29: {"assignmentId":"42c64a69-ab23-451f-a314-c5c87622675d","dayDateStr":"2025-07-29","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 08:51:37] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-29 avant startDate 2025-07-30 (original: 2025-07-30)
[18/07/2025 08:51:37] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-29 ignoré pour attribution 42c64a69-ab23-451f-a314-c5c87622675d
[18/07/2025 08:51:37] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-30 (3)
[18/07/2025 08:51:37] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"42c64a69-ab23-451f-a314-c5c87622675d","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-30T04:00:00.000Z","dayDateKey":"2025-07-30","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5]}
[18/07/2025 08:51:37] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[18/07/2025 08:51:37] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-30: {"assignmentId":"42c64a69-ab23-451f-a314-c5c87622675d","dayDateStr":"2025-07-30","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 08:51:37] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-30
[18/07/2025 08:51:37] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-31 (4)
[18/07/2025 08:51:37] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Nuit → 2025-07-30 pour Marie Martin
[18/07/2025 08:51:37] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-30 validé, shifts existants: 0
[18/07/2025 08:51:37] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"42c64a69-ab23-451f-a314-c5c87622675d","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-31T04:00:00.000Z","dayDateKey":"2025-07-31","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5]}
[18/07/2025 08:51:37] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[18/07/2025 08:51:37] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-31: {"assignmentId":"42c64a69-ab23-451f-a314-c5c87622675d","dayDateStr":"2025-07-31","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 08:51:37] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Nuit → 2025-07-31 pour Marie Martin
[18/07/2025 08:51:37] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-31 validé, shifts existants: 0
[18/07/2025 08:51:37] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-31
[18/07/2025 08:51:37] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-08-01 (5)
[18/07/2025 08:51:37] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"42c64a69-ab23-451f-a314-c5c87622675d","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-08-01T04:00:00.000Z","dayDateKey":"2025-08-01","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5]}
[18/07/2025 08:51:37] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[18/07/2025 08:51:37] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-08-01: {"assignmentId":"42c64a69-ab23-451f-a314-c5c87622675d","dayDateStr":"2025-08-01","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 08:51:37] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-08-01 après endDate 2025-07-31 (original: 2025-07-31) (attribution 42c64a69-ab23-451f-a314-c5c87622675d)
[18/07/2025 08:51:37] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-08-01 ignoré pour attribution 42c64a69-ab23-451f-a314-c5c87622675d
[18/07/2025 08:51:37] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-08-02 (6)
[18/07/2025 08:51:37] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"42c64a69-ab23-451f-a314-c5c87622675d","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-08-02T04:00:00.000Z","dayDateKey":"2025-08-02","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5]}
[18/07/2025 08:51:37] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[18/07/2025 08:51:37] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-08-02 ignoré pour attribution 42c64a69-ab23-451f-a314-c5c87622675d
[18/07/2025 08:51:37] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"316031c4-9402-4394-970e-e598022f5118","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"startDate":"2025-07-25","endDate":"2025-07-27","excludedDates":[]}
[18/07/2025 08:51:37] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-27 (0)
[18/07/2025 08:51:37] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[18/07/2025 08:51:37] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution 316031c4-9402-4394-970e-e598022f5118: [{"dateKey":"2025-07-27","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-28","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-29","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-30","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-31","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-08-01","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-08-02","isWeekend":true,"dayOfWeek":6}]
[18/07/2025 08:51:37] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"316031c4-9402-4394-970e-e598022f5118","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-27T04:00:00.000Z","dayDateKey":"2025-07-27","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5]}
[18/07/2025 08:51:37] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-28 (1)
[18/07/2025 08:51:37] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-27 ignoré pour attribution 316031c4-9402-4394-970e-e598022f5118
[18/07/2025 08:51:37] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"316031c4-9402-4394-970e-e598022f5118","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-28T04:00:00.000Z","dayDateKey":"2025-07-28","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5]}
[18/07/2025 08:51:37] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[18/07/2025 08:51:37] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-28: {"assignmentId":"316031c4-9402-4394-970e-e598022f5118","dayDateStr":"2025-07-28","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 08:51:37] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-28 après endDate 2025-07-27 (original: 2025-07-27) (attribution 316031c4-9402-4394-970e-e598022f5118)
[18/07/2025 08:51:37] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-29 (2)
[18/07/2025 08:51:37] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-28 ignoré pour attribution 316031c4-9402-4394-970e-e598022f5118
[18/07/2025 08:51:37] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[18/07/2025 08:51:37] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"316031c4-9402-4394-970e-e598022f5118","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-29T04:00:00.000Z","dayDateKey":"2025-07-29","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5]}
[18/07/2025 08:51:37] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-29: {"assignmentId":"316031c4-9402-4394-970e-e598022f5118","dayDateStr":"2025-07-29","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 08:51:37] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-29 après endDate 2025-07-27 (original: 2025-07-27) (attribution 316031c4-9402-4394-970e-e598022f5118)
[18/07/2025 08:51:38] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-30 (3)
[18/07/2025 08:51:38] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-29 ignoré pour attribution 316031c4-9402-4394-970e-e598022f5118
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-30: {"assignmentId":"316031c4-9402-4394-970e-e598022f5118","dayDateStr":"2025-07-30","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"316031c4-9402-4394-970e-e598022f5118","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-30T04:00:00.000Z","dayDateKey":"2025-07-30","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5]}
[18/07/2025 08:51:38] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[18/07/2025 08:51:38] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-30 après endDate 2025-07-27 (original: 2025-07-27) (attribution 316031c4-9402-4394-970e-e598022f5118)
[18/07/2025 08:51:38] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-30 ignoré pour attribution 316031c4-9402-4394-970e-e598022f5118
[18/07/2025 08:51:38] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-31 (4)
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"316031c4-9402-4394-970e-e598022f5118","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-31T04:00:00.000Z","dayDateKey":"2025-07-31","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5]}
[18/07/2025 08:51:38] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[18/07/2025 08:51:38] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-31 ignoré pour attribution 316031c4-9402-4394-970e-e598022f5118
[18/07/2025 08:51:38] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-31 après endDate 2025-07-27 (original: 2025-07-27) (attribution 316031c4-9402-4394-970e-e598022f5118)
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-31: {"assignmentId":"316031c4-9402-4394-970e-e598022f5118","dayDateStr":"2025-07-31","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 08:51:38] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-08-01 (5)
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"316031c4-9402-4394-970e-e598022f5118","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-08-01T04:00:00.000Z","dayDateKey":"2025-08-01","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5]}
[18/07/2025 08:51:38] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[18/07/2025 08:51:38] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-08-01 ignoré pour attribution 316031c4-9402-4394-970e-e598022f5118
[18/07/2025 08:51:38] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-08-02 (6)
[18/07/2025 08:51:38] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-08-01 après endDate 2025-07-27 (original: 2025-07-27) (attribution 316031c4-9402-4394-970e-e598022f5118)
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-08-01: {"assignmentId":"316031c4-9402-4394-970e-e598022f5118","dayDateStr":"2025-08-01","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"316031c4-9402-4394-970e-e598022f5118","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-08-02T04:00:00.000Z","dayDateKey":"2025-08-02","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5]}
[18/07/2025 08:51:38] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[18/07/2025 08:51:38] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-08-02 ignoré pour attribution 316031c4-9402-4394-970e-e598022f5118
[18/07/2025 08:51:38] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 5 attributions appliquées pour la semaine 2025-W30
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Rendu demandé car 5 shifts créés
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🔄 [reassignRegularAssignmentFromDate] Rendu final...
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [reassignRegularAssignmentFromDate] Attribution divisée avec succès
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Nuit: [1, 2, 3, 4, 5]
[18/07/2025 08:51:38] [FRONTEND] [LOG] 📝 [logModification] Enregistrement: {"id":"23033ddf-d659-4f32-a385-cf6b7488a0a6","type":"regular-assignment","title":"Attribution régulière modifiée (permanent)","description":"Poste Nuit transféré de Marie Martin vers Lucas Bernard à partir du 31/07/2025","timestamp":"2025-07-18T12:51:36.132Z","employeeName":"Lucas Bernard","postLabel":"Poste Nuit","date":"2025-08-01","assignmentId":"42c64a69-ab23-451f-a314-c5c87622675d","sourceEmployeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","targetEmployeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d"}
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 0c74e25d-4692-46d7-b5aa-b84eee797971 -> "00:00-08:00"
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 86f97857-d86a-4e2a-aec3-831bf92012d4
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 0c74e25d-4692-46d7-b5aa-b84eee797971 -> "00:00-08:00"
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 86f97857-d86a-4e2a-aec3-831bf92012d4
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 0c74e25d-4692-46d7-b5aa-b84eee797971 -> "00:00-08:00"
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 6830f981-f642-4364-9b1d-0ff92e12fad2
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 0c74e25d-4692-46d7-b5aa-b84eee797971 -> "00:00-08:00"
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 0c74e25d-4692-46d7-b5aa-b84eee797971 -> "00:00-08:00"
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 42c64a69-ab23-451f-a314-c5c87622675d
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 42c64a69-ab23-451f-a314-c5c87622675d
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 70 zones trouvées
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 70 zones de drop disponibles
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Matin: [1, 2, 3, 4, 5]
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Soir: [1, 2, 3, 4, 5]
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE1: [0, 6]
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE2: [0, 6]
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (70 zones)
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [SETUP] Drag & drop des employés configuré (mode simplifié)
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 70 zones trouvées
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (70 zones)
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [attachGripEvents] Événements attachés pour 86f97857-d86a-4e2a-aec3-831bf92012d4
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [createAndAttachGrip] Grip créé et attaché pour 86f97857-d86a-4e2a-aec3-831bf92012d4
[18/07/2025 08:51:38] [FRONTEND] [LOG] ⚠️ [validateGripFunctionality] Grip 86f97857-d86a-4e2a-aec3-831bf92012d4 invalide: {"hasCorrectId":true,"isDraggable":false,"hasRegularFlag":true,"hasValidDimensions":true,"isVisible":true,"hasCorrectCursor":true}
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Recréation du grip défaillant pour 86f97857-d86a-4e2a-aec3-831bf92012d4
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [attachGripEvents] Événements attachés pour 86f97857-d86a-4e2a-aec3-831bf92012d4
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🔗 [attachGripEvents] Attachement événements pour 86f97857-d86a-4e2a-aec3-831bf92012d4
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [createAndAttachGrip] Grip créé et attaché pour 86f97857-d86a-4e2a-aec3-831bf92012d4
[18/07/2025 08:51:38] [FRONTEND] [LOG] ⚠️ [validateGripFunctionality] Grip 6830f981-f642-4364-9b1d-0ff92e12fad2 invalide: {"hasCorrectId":true,"isDraggable":false,"hasRegularFlag":true,"hasValidDimensions":true,"isVisible":true,"hasCorrectCursor":true}
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Recréation du grip défaillant pour 6830f981-f642-4364-9b1d-0ff92e12fad2
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🔗 [attachGripEvents] Attachement événements pour 6830f981-f642-4364-9b1d-0ff92e12fad2
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [attachGripEvents] Événements attachés pour 6830f981-f642-4364-9b1d-0ff92e12fad2
[18/07/2025 08:51:38] [FRONTEND] [LOG] ✅ [createAndAttachGrip] Grip créé et attaché pour 6830f981-f642-4364-9b1d-0ff92e12fad2
[18/07/2025 08:51:38] [FRONTEND] [LOG] ⚠️ [validateGripFunctionality] Grip 42c64a69-ab23-451f-a314-c5c87622675d invalide: {"hasCorrectId":true,"isDraggable":false,"hasRegularFlag":true,"hasValidDimensions":true,"isVisible":true,"hasCorrectCursor":true}
[18/07/2025 08:51:38] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Recréation du grip défaillant pour 42c64a69-ab23-451f-a314-c5c87622675d
[18/07/2025 08:51:39] [FRONTEND] [LOG] ✅ [attachGripEvents] Événements attachés pour 42c64a69-ab23-451f-a314-c5c87622675d
[18/07/2025 08:51:39] [FRONTEND] [LOG] 🔗 [attachGripEvents] Attachement événements pour 42c64a69-ab23-451f-a314-c5c87622675d
[18/07/2025 08:51:39] [FRONTEND] [LOG] ✅ [createAndAttachGrip] Grip créé et attaché pour 42c64a69-ab23-451f-a314-c5c87622675d
[18/07/2025 08:51:39] [FRONTEND] [LOG] ⚠️ [validateGripFunctionality] Grip 42c64a69-ab23-451f-a314-c5c87622675d invalide: {"hasCorrectId":true,"isDraggable":false,"hasRegularFlag":true,"hasValidDimensions":true,"isVisible":true,"hasCorrectCursor":true}
[18/07/2025 08:51:39] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Recréation du grip défaillant pour 42c64a69-ab23-451f-a314-c5c87622675d
[18/07/2025 08:51:39] [FRONTEND] [LOG] 🔗 [attachGripEvents] Attachement événements pour 42c64a69-ab23-451f-a314-c5c87622675d
[18/07/2025 08:51:39] [FRONTEND] [LOG] ✅ [attachGripEvents] Événements attachés pour 42c64a69-ab23-451f-a314-c5c87622675d
[18/07/2025 08:51:39] [FRONTEND] [LOG] ✅ [createAndAttachGrip] Grip créé et attaché pour 42c64a69-ab23-451f-a314-c5c87622675d
[18/07/2025 08:51:39] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[18/07/2025 08:51:39] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[18/07/2025 08:51:39] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] Résultats: 0 ajoutés, 0 validés, 5 corrigés
[18/07/2025 08:51:39] [FRONTEND] [LOG] 🎯 [ensureRegularAssignmentGrips] Grips réguliers maintenant disponibles pour le drag & drop
[18/07/2025 09:01:41] [BACKEND] [INFO] [LOGS] Client connecté: 1752843701559
[18/07/2025 09:01:41] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T13:01:41.693Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T13:01:41.861Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 302ms: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 302ms:",
    "SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T13:01:41.863Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T13:01:41.716Z"
}
[18/07/2025 09:01:42] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T13:01:41.720Z"
}
[18/07/2025 09:01:42] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[18/07/2025 09:01:42] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T13:01:41.926Z"
}
[18/07/2025 09:01:42] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T13:01:41.925Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T13:01:41.929Z"
}
[18/07/2025 09:01:42] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[18/07/2025 09:01:42] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T13:01:41.945Z"
}
[18/07/2025 09:01:42] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[18/07/2025 09:01:42] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[18/07/2025 09:01:42] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[18/07/2025 09:01:42] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[18/07/2025 09:01:42] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[18/07/2025 09:01:42] [FRONTEND] [LOG] 🧪 [TEST] Script de test de la modale drag & drop chargé
[18/07/2025 09:01:42] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[18/07/2025 09:01:42] [FRONTEND] [LOG] - testButtonListeners() : Tester les listeners des boutons
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T13:01:42.045Z"
}
[18/07/2025 09:01:42] [FRONTEND] [LOG] - testAssignmentModal() : Tester l'ouverture de la modale
[18/07/2025 09:01:42] [FRONTEND] [LOG] - forceReconfigureListeners() : Reconfigurer les listeners
[18/07/2025 09:01:42] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[18/07/2025 09:01:42] [FRONTEND] [LOG] - diagnoseModalIssues() : Diagnostiquer les problèmes
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T13:01:41.872Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T13:01:42.047Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T13:01:41.886Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T13:01:42.080Z"
}
[18/07/2025 09:01:42] [FRONTEND] [LOG] - testButtonClicks() : Tester les clics sur les boutons
[18/07/2025 09:01:42] [FRONTEND] [LOG] 🔍 [VALIDATION] Script de validation de la correction modale chargé
[18/07/2025 09:01:42] [FRONTEND] [LOG] - validateMainFix() : Valider la correction principale
[18/07/2025 09:01:42] [FRONTEND] [LOG] ✅ [VALIDATION] Fonctions de validation disponibles:
[18/07/2025 09:01:42] [FRONTEND] [LOG] - validateListeners() : Valider les listeners
[18/07/2025 09:01:42] [FRONTEND] [LOG] - validateDOMElements() : Valider les éléments DOM
[18/07/2025 09:01:42] [FRONTEND] [LOG] - runCompleteValidation() : Validation complète
[18/07/2025 09:01:42] [FRONTEND] [LOG] - testEndToEnd() : Test end-to-end complet
[18/07/2025 09:01:42] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[18/07/2025 09:01:42] [FRONTEND] [LOG] 🧪 [TEST] Script de test pour la correction du fork modal chargé
[18/07/2025 09:01:42] [FRONTEND] [LOG] - testDropDateDetection() : Tester la détection de date
[18/07/2025 09:01:42] [FRONTEND] [LOG] - testForkModalWithDate() : Tester le modal avec date
[18/07/2025 09:01:42] [FRONTEND] [LOG] - testCompleteForkFlow() : Tester le flux complet
[18/07/2025 09:01:42] [FRONTEND] [LOG] - diagnoseForkModalIssues() : Diagnostic complet
[18/07/2025 09:01:42] [FRONTEND] [LOG] 🧪 [DATE-TEST] Script de validation des corrections de date chargé
[18/07/2025 09:01:42] [FRONTEND] [LOG] ✅ [DATE-TEST] Fonctions de validation des dates disponibles:
[18/07/2025 09:01:42] [FRONTEND] [LOG] - testSafeDateConversion() : Tester la conversion sécurisée
[18/07/2025 09:01:42] [FRONTEND] [LOG] - testForkDateLogic() : Tester la logique de fork
[18/07/2025 09:01:42] [FRONTEND] [LOG] - testDropDateDetectionWithTimezone() : Tester la détection avec timezone
[18/07/2025 09:01:42] [FRONTEND] [LOG] - testTimezoneEnhancements() : Tester les améliorations timezone
[18/07/2025 09:01:42] [FRONTEND] [LOG] - runDateValidationSuite() : Suite complète de validation
[18/07/2025 09:01:42] [FRONTEND] [LOG] 🔧 [GRIP-FIX] Script de diagnostic et correction des GRIP handles chargé
[18/07/2025 09:01:42] [FRONTEND] [LOG] ✅ [GRIP-FIX] Fonctions de diagnostic et correction disponibles:
[18/07/2025 09:01:42] [FRONTEND] [LOG] - testSpecificGrip(assignmentId) : Test spécifique
[18/07/2025 09:01:42] [FRONTEND] [LOG] - diagnoseGripHandles() : Diagnostic complet
[18/07/2025 09:01:42] [FRONTEND] [LOG] - autoFixGripIssues() : Correction automatique
[18/07/2025 09:01:42] [FRONTEND] [LOG] - forceRecreateGripHandles() : Recréation forcée
[18/07/2025 09:01:42] [FRONTEND] [LOG] - startGripMonitoring() : Surveillance continue
[18/07/2025 09:01:42] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[18/07/2025 09:01:42] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[18/07/2025 09:01:42] [FRONTEND] [LOG] [LogsPage] Fermeture de la connexion SSE.
[18/07/2025 09:01:43] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[18/07/2025 09:01:43] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Données contextuelles: {"employees":0,"posts":0}
[18/07/2025 09:01:43] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] TeamCalendarApp: {"exists":true,"hasModalManager":true,"hasOpenFunction":true,"hasHandleFunction":true}
[18/07/2025 09:01:43] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Éléments DOM: {"modal":false,"closeBtn":false,"cancelBtn":false,"confirmBtn":false,"radioButtons":0}
[18/07/2025 09:01:43] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic des problèmes de modale...
[18/07/2025 09:01:44] [FRONTEND] [DEBUG] [vite] connected.
[18/07/2025 09:01:44] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation automatique...
[18/07/2025 09:01:44] [FRONTEND] [ERROR] ❌ [VALIDATION] ModalManager.app non initialisé
[18/07/2025 09:01:44] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation de la correction principale...
[18/07/2025 09:01:44] [FRONTEND] [LOG] ✅ [VALIDATION] ModalManager.setupAssignmentContextModal disponible
[18/07/2025 09:01:44] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation complète...
[18/07/2025 09:01:44] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[18/07/2025 09:01:44] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[18/07/2025 09:01:44] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[18/07/2025 09:01:44] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[18/07/2025 09:01:44] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[18/07/2025 09:01:44] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[18/07/2025 09:01:45] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[18/07/2025 09:01:45] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[18/07/2025 09:01:45] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[18/07/2025 09:01:45] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[18/07/2025 09:01:45] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[18/07/2025 09:01:45] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[18/07/2025 09:01:45] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[18/07/2025 09:01:45] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[18/07/2025 09:01:45] [FRONTEND] [LOG] 🧪 [TEST] Script de test de la modale drag & drop chargé
[18/07/2025 09:01:45] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[18/07/2025 09:01:45] [FRONTEND] [LOG] - testAssignmentModal() : Tester l'ouverture de la modale
[18/07/2025 09:01:45] [FRONTEND] [LOG] - testButtonListeners() : Tester les listeners des boutons
[18/07/2025 09:01:45] [FRONTEND] [LOG] - diagnoseModalIssues() : Diagnostiquer les problèmes
[18/07/2025 09:01:45] [FRONTEND] [LOG] - forceReconfigureListeners() : Reconfigurer les listeners
[18/07/2025 09:01:45] [FRONTEND] [LOG] - testButtonClicks() : Tester les clics sur les boutons
[18/07/2025 09:01:45] [FRONTEND] [LOG] 🔍 [VALIDATION] Script de validation de la correction modale chargé
[18/07/2025 09:01:45] [FRONTEND] [LOG] ✅ [VALIDATION] Fonctions de validation disponibles:
[18/07/2025 09:01:45] [FRONTEND] [LOG] - validateMainFix() : Valider la correction principale
[18/07/2025 09:01:45] [FRONTEND] [LOG] - validateDOMElements() : Valider les éléments DOM
[18/07/2025 09:01:45] [FRONTEND] [LOG] - validateListeners() : Valider les listeners
[18/07/2025 09:01:45] [FRONTEND] [LOG] - runCompleteValidation() : Validation complète
[18/07/2025 09:01:45] [FRONTEND] [LOG] - testEndToEnd() : Test end-to-end complet
[18/07/2025 09:01:45] [FRONTEND] [LOG] 🧪 [TEST] Script de test pour la correction du fork modal chargé
[18/07/2025 09:01:45] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[18/07/2025 09:01:45] [FRONTEND] [LOG] - testDropDateDetection() : Tester la détection de date
[18/07/2025 09:01:45] [FRONTEND] [LOG] - testForkModalWithDate() : Tester le modal avec date
[18/07/2025 09:01:45] [FRONTEND] [LOG] - testCompleteForkFlow() : Tester le flux complet
[18/07/2025 09:01:45] [FRONTEND] [LOG] 🧪 [DATE-TEST] Script de validation des corrections de date chargé
[18/07/2025 09:01:45] [FRONTEND] [LOG] - diagnoseForkModalIssues() : Diagnostic complet
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT e.*, et.name as template_name 
      FROM employees e 
      LEFT JOIN employee_templates et ON e.template_id = et.id 
      ORDER BY e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT e.*, et.name as template_name \n      FROM employees e \n      LEFT JOIN employee_templates et ON e.template_id = et.id \n      ORDER BY e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T13:01:45.127Z"
}
[18/07/2025 09:01:45] [FRONTEND] [LOG] ✅ [DATE-TEST] Fonctions de validation des dates disponibles:
[18/07/2025 09:01:45] [FRONTEND] [LOG] - testSafeDateConversion() : Tester la conversion sécurisée
[18/07/2025 09:01:45] [FRONTEND] [LOG] - testForkDateLogic() : Tester la logique de fork
[18/07/2025 09:01:45] [FRONTEND] [LOG] - testDropDateDetectionWithTimezone() : Tester la détection avec timezone
[18/07/2025 09:01:45] [FRONTEND] [LOG] - testTimezoneEnhancements() : Tester les améliorations timezone
[18/07/2025 09:01:45] [FRONTEND] [LOG] - runDateValidationSuite() : Suite complète de validation
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 47ms: 
      SELECT e.*, et.name as template_name 
      {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 47ms:",
    "\n      SELECT e.*, et.name as template_name \n     ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T13:01:45.174Z"
}
[18/07/2025 09:01:45] [FRONTEND] [LOG] 🔧 [GRIP-FIX] Script de diagnostic et correction des GRIP handles chargé
[18/07/2025 09:01:45] [BACKEND] [INFO] ✅ Requête exécutée en 47ms: 
      SELECT e.*, et.name as template_name 
      {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT s.*, e.name as employee_name, sp.label as post_label
      FROM shifts s
      LEFT JOIN employees e ON s.employee_id = e.id
      LEFT JOIN standard_posts sp ON s.post_id = sp.id
      ORDER BY s.date_key, e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT s.*, e.name as employee_name, sp.label as post_label\n      FROM shifts s\n      LEFT JOIN employees e ON s.employee_id = e.id\n      LEFT JOIN standard_posts sp ON s.post_id = sp.id\n      ORDER BY s.date_key, e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T13:01:45.194Z"
}
[18/07/2025 09:01:45] [FRONTEND] [LOG] - testSpecificGrip(assignmentId) : Test spécifique
[18/07/2025 09:01:45] [FRONTEND] [LOG] - forceRecreateGripHandles() : Recréation forcée
[18/07/2025 09:01:45] [FRONTEND] [LOG] ✅ [GRIP-FIX] Fonctions de diagnostic et correction disponibles:
[18/07/2025 09:01:45] [FRONTEND] [LOG] - diagnoseGripHandles() : Diagnostic complet
[18/07/2025 09:01:45] [FRONTEND] [LOG] - autoFixGripIssues() : Correction automatique
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 61ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 61ms:",
    "\n      SELECT s.*, e.name as employee_name, sp.lab",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T13:01:45.255Z"
}
[18/07/2025 09:01:45] [FRONTEND] [LOG] - startGripMonitoring() : Surveillance continue
[18/07/2025 09:01:45] [FRONTEND] [LOG] 🚀 [Agenda] Initialisation TeamCalendarApp...
[18/07/2025 09:01:45] [FRONTEND] [LOG] 🚀 [init] Initialisation de TeamCalendarApp...
[18/07/2025 09:01:45] [FRONTEND] [LOG] 🧹 [Agenda] Nettoyage composant Agenda
[18/07/2025 09:01:45] [BACKEND] [INFO] ✅ Requête exécutée en 61ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
[18/07/2025 09:01:45] [FRONTEND] [LOG] 🔄 [Agenda] Réinitialisation nécessaire (employés non chargés)
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T13:01:45.282Z"
}
[18/07/2025 09:01:45] [FRONTEND] [LOG] 🧹 [clearRenderCache] Nettoyage du cache de rendu
[18/07/2025 09:01:45] [FRONTEND] [LOG] ✅ [clearRenderCache] Cache de rendu nettoyé
[18/07/2025 09:01:45] [FRONTEND] [LOG] 📂 [TeamCalendarApp] Chargement de l'état...
[18/07/2025 09:01:45] [FRONTEND] [LOG] 🔌 [init] Indicateur de connexion initialisé
[18/07/2025 09:01:45] [FRONTEND] [LOG] 🔌 [ConnectionIndicator] Création de l'indicateur de connexion
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 41ms: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 41ms:",
    "SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T13:01:45.323Z"
}
[18/07/2025 09:01:45] [BACKEND] [INFO] ✅ Requête exécutée en 41ms: SELECT * FROM standard_posts ORDER BY label {}
[18/07/2025 09:01:45] [FRONTEND] [LOG] 🌐 [TeamCalendarApp] Chargement depuis l'API...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T13:01:45.343Z"
}
[18/07/2025 09:01:45] [FRONTEND] [LOG] 📦 [loadState] Chargement de tous les shifts depuis la base de données...
[18/07/2025 09:01:45] [FRONTEND] [LOG] 👥 [TeamCalendarApp] 5 employés chargés
[18/07/2025 09:01:45] [FRONTEND] [LOG] 📅 [loadState] 0 shifts chargés depuis la base de données
[18/07/2025 09:01:45] [FRONTEND] [LOG] 🏢 [TeamCalendarApp] 5 postes standards chargés
[18/07/2025 09:01:45] [FRONTEND] [LOG] ✅ [loadState] Shifts organisés dans this.data.schedule
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 41ms: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 41ms:",
    "SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T13:01:45.384Z"
}
[18/07/2025 09:01:45] [BACKEND] [INFO] ✅ Requête exécutée en 41ms: SELECT * FROM app_settings ORDER BY setting_key {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT 
        ra.id,
        ra.employee_id,
        ra.post_id,
        ra.days_of_week,
        ra.start_date,
        ra.end_date,
        ra.is_active,
        ra.excluded_dates,
        e.name as employee_name,
        sp.label as post_label,
        sp.hours as post_hours,
        sp.type as post_type,
        array_length(ra.days_of_week, 1) as days_count
      FROM regular_assignments ra
      LEFT JOIN employees e ON ra.employee_id = e.id
      LEFT JOIN standard_posts sp ON ra.post_id = sp.id
      WHERE ra.is_active = true
      ORDER BY e.name, sp.label, ra.start_date
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT \n        ra.id,\n        ra.employee_id,\n        ra.post_id,\n        ra.days_of_week,\n        ra.start_date,\n        ra.end_date,\n        ra.is_active,\n        ra.excluded_dates,\n        e.name as employee_name,\n        sp.label as post_label,\n        sp.hours as post_hours,\n        sp.type as post_type,\n        array_length(ra.days_of_week, 1) as days_count\n      FROM regular_assignments ra\n      LEFT JOIN employees e ON ra.employee_id = e.id\n      LEFT JOIN standard_posts sp ON ra.post_id = sp.id\n      WHERE ra.is_active = true\n      ORDER BY e.name, sp.label, ra.start_date\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T13:01:45.399Z"
}
[18/07/2025 09:01:45] [FRONTEND] [LOG] 📋 [VALIDATION] Éléments DOM: {"modal":false,"closeBtn":false,"cancelBtn":false,"confirmBtn":false,"radioButtons":false}
[18/07/2025 09:01:45] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des éléments DOM...
[18/07/2025 09:01:45] [FRONTEND] [LOG] ⚙️ [loadState] Paramètres chargés depuis API: {}
[18/07/2025 09:01:45] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des listeners...
[18/07/2025 09:01:45] [FRONTEND] [ERROR] ❌ [VALIDATION] Éléments DOM manquants
[18/07/2025 09:01:45] [FRONTEND] [ERROR] ❌ [setupAssignmentContextModal] Éléments manquants: {"closeBtn":false,"cancelBtn":false,"confirmBtn":false}
[18/07/2025 09:01:45] [FRONTEND] [LOG] 🔧 [setupAssignmentContextModal] Configuration du modal contextuel...
[18/07/2025 09:01:45] [FRONTEND] [LOG] ✅ [VALIDATION] setupAssignmentContextModal exécuté sans erreur
[18/07/2025 09:01:45] [BACKEND] [INFO] ✅ Requête exécutée en 76ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
[18/07/2025 09:01:45] [FRONTEND] [LOG] 📊 [VALIDATION] Résultats de validation: {"mainFix":false,"domElements":false,"listeners":true}
[18/07/2025 09:01:45] [FRONTEND] [ERROR] ❌ [VALIDATION] Certaines validations ont échoué
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 76ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 76ms:",
    "\n      SELECT \n        ra.id,\n        ra.employee_",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T13:01:45.476Z"
}
[18/07/2025 09:01:45] [FRONTEND] [LOG] 🔧 [VALIDATION] Actions recommandées:
[18/07/2025 09:01:45] [FRONTEND] [LOG] - Vérifier que la modale assignment-context-modal existe dans le DOM
[18/07/2025 09:01:45] [FRONTEND] [LOG] - Vérifier que TeamCalendarApp et ModalManager sont correctement initialisés
[18/07/2025 09:01:45] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"08b7356a-e76a-484d-b599-2d5d4a0d8b6b","employee_id":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","post_id":"0c74e25d-4692-46d7-b5aa-b84eee797971","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-23T04:00:00.000Z","end_date":"2025-07-23T04:00:00.000Z","excluded_dates":[]}
[18/07/2025 09:01:45] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"6830f981-f642-4364-9b1d-0ff92e12fad2","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"isLimited":false,"startDate":"2025-08-01","endDate":null,"excludedDates":[]}
[18/07/2025 09:01:45] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"86f97857-d86a-4e2a-aec3-831bf92012d4","employee_id":"cf78e945-72c7-48f3-a72d-bd0e245a284d","post_id":"0c74e25d-4692-46d7-b5aa-b84eee797971","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-28T04:00:00.000Z","end_date":"2025-07-29T04:00:00.000Z","excluded_dates":[]}
[18/07/2025 09:01:45] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"6830f981-f642-4364-9b1d-0ff92e12fad2","employee_id":"cf78e945-72c7-48f3-a72d-bd0e245a284d","post_id":"0c74e25d-4692-46d7-b5aa-b84eee797971","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-08-01T04:00:00.000Z","end_date":null,"excluded_dates":[]}
[18/07/2025 09:01:45] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"86f97857-d86a-4e2a-aec3-831bf92012d4","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-28","endDate":"2025-07-29","excludedDates":[]}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📋 [GET /api/employee-order] Récupération ordre des employés
  Data: {
  "originalArgs": [
    "📋 [GET /api/employee-order] Récupération ordre des employés"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T13:01:45.634Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'
         {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'\n        ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T13:01:45.637Z"
}
[18/07/2025 09:01:45] [BACKEND] [INFO] 📋 [GET /api/employee-order] Récupération ordre des employés
[18/07/2025 09:01:45] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"08b7356a-e76a-484d-b599-2d5d4a0d8b6b","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-23","endDate":"2025-07-23","excludedDates":[]}
[18/07/2025 09:01:45] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"42c64a69-ab23-451f-a314-c5c87622675d","employee_id":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","post_id":"0c74e25d-4692-46d7-b5aa-b84eee797971","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-30T04:00:00.000Z","end_date":"2025-07-31T04:00:00.000Z","excluded_dates":[]}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 32ms: 
            SELECT setting_value FROM app_setting {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 32ms:",
    "\n            SELECT setting_value FROM app_setting",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T13:01:45.668Z"
}
[18/07/2025 09:01:45] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"5400f0bd-d560-4167-89ea-a6da77478469","employee_id":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","post_id":"0c74e25d-4692-46d7-b5aa-b84eee797971","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-22T04:00:00.000Z","end_date":"2025-07-22T04:00:00.000Z","excluded_dates":[]}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📋 [GET /api/employee-order] Aucun ordre personnalisé trouvé
  Data: {
  "originalArgs": [
    "📋 [GET /api/employee-order] Aucun ordre personnalisé trouvé"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T13:01:45.671Z"
}
[18/07/2025 09:01:45] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"42c64a69-ab23-451f-a314-c5c87622675d","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-30","endDate":"2025-07-31","excludedDates":[]}
[18/07/2025 09:01:45] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"5400f0bd-d560-4167-89ea-a6da77478469","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-22","endDate":"2025-07-22","excludedDates":[]}
[18/07/2025 09:01:45] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"316031c4-9402-4394-970e-e598022f5118","employee_id":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","post_id":"0c74e25d-4692-46d7-b5aa-b84eee797971","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-25T04:00:00.000Z","end_date":"2025-07-27T04:00:00.000Z","excluded_dates":[]}
[18/07/2025 09:01:45] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"ed906322-db11-4d06-aa28-437b06c445c4","employee_id":"59f5df3a-33ef-425c-bfa2-adca818cf94f","post_id":"0c74e25d-4692-46d7-b5aa-b84eee797971","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-18T04:00:00.000Z","end_date":"2025-07-21T04:00:00.000Z","excluded_dates":[]}
[18/07/2025 09:01:45] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"316031c4-9402-4394-970e-e598022f5118","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-25","endDate":"2025-07-27","excludedDates":[]}
[18/07/2025 09:01:45] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"d546a50b-43af-4f53-afd0-4a43c1f3959a","employee_id":"59f5df3a-33ef-425c-bfa2-adca818cf94f","post_id":"0c74e25d-4692-46d7-b5aa-b84eee797971","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-24T04:00:00.000Z","end_date":"2025-07-24T04:00:00.000Z","excluded_dates":[]}
[18/07/2025 09:01:45] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"ed906322-db11-4d06-aa28-437b06c445c4","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-18","endDate":"2025-07-21","excludedDates":[]}
[18/07/2025 09:01:45] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"d546a50b-43af-4f53-afd0-4a43c1f3959a","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-24","endDate":"2025-07-24","excludedDates":[]}
[18/07/2025 09:01:45] [FRONTEND] [LOG] 📋 [loadState] 8/8 assignations régulières valides chargées et normalisées
[18/07/2025 09:01:45] [FRONTEND] [LOG] 🔧 [fixInvalidAssignmentDates] Vérification des dates invalides...
[18/07/2025 09:01:45] [FRONTEND] [LOG] ✅ [fixInvalidAssignmentDates] Aucune date invalide trouvée
[18/07/2025 09:01:46] [FRONTEND] [LOG] 🧹 [cleanupDuplicateRegularShifts] Nettoyage des doublons de shifts réguliers...
[18/07/2025 09:01:46] [FRONTEND] [LOG] ✅ [cleanupDuplicateRegularShifts] Aucun doublon détecté
[18/07/2025 09:01:46] [FRONTEND] [LOG] 📋 [loadState] Assignations régulières chargées (application différée)
[18/07/2025 09:01:46] [FRONTEND] [LOG] 🧹 [cleanupInvalidAssignmentIds] Nettoyage des assignment_id invalides...
[18/07/2025 09:01:46] [FRONTEND] [LOG] ✅ [cleanupInvalidAssignmentIds] Aucun assignment_id invalide détecté
[18/07/2025 09:01:46] [FRONTEND] [LOG] ✅ [loadState] État chargé depuis l'API avec succès
[18/07/2025 09:01:46] [FRONTEND] [LOG] ✅ [init] Icône paramètres trouvée: [Objet non sérialisable]
[18/07/2025 09:01:46] [FRONTEND] [LOG] 🔍 [init] Recherche du bouton des paramètres dans le DOM
[18/07/2025 09:01:46] [FRONTEND] [LOG] 🔍 [init] Bouton des paramètres trouvé ? true
[18/07/2025 09:01:46] [FRONTEND] [LOG] 🔍 [init] Attachement du listener click sur le bouton des paramètres
[18/07/2025 09:01:46] [FRONTEND] [LOG] 🔍 [init] Recherche du bouton des paramètres header dans le DOM
[18/07/2025 09:01:46] [FRONTEND] [LOG] 🔍 [init] Bouton des paramètres header trouvé ? true
[18/07/2025 09:01:46] [FRONTEND] [LOG] 🔍 [init] Attachement du listener click sur le bouton des paramètres header
[18/07/2025 09:01:46] [FRONTEND] [LOG] ✅ [setupNavigationListeners] Écouteurs de navigation attachés
[18/07/2025 09:01:46] [FRONTEND] [LOG] ✅ Boutons de navigation configurés
[18/07/2025 09:01:46] [FRONTEND] [LOG] 🎯 [setupViewNavigationListeners] Configuration des boutons de navigation...
[18/07/2025 09:01:46] [FRONTEND] [LOG] 🔧 [ModalManager] Configuration des gestionnaires de modal
[18/07/2025 09:01:46] [FRONTEND] [LOG] ✅ [ModalManager] Gestionnaires de modal configurés
[18/07/2025 09:01:46] [FRONTEND] [LOG] 🔧 [setupGeneralSettings] Création du conteneur de fuseau horaire
[18/07/2025 09:01:46] [FRONTEND] [LOG] 🔧 [setupGeneralSettings] Configuration des paramètres généraux
[18/07/2025 09:01:46] [FRONTEND] [LOG] ✅ [setupGlobalEscapeHandler] Gestionnaire ESC global configuré
[18/07/2025 09:01:46] [FRONTEND] [LOG] 📋 [loadEmployeeOrder] Chargement ordre des employés
[18/07/2025 09:01:46] [FRONTEND] [WARN] ⚠️ [loadEmployeeOrder] Réponse API sans tableau employeeOrder
[18/07/2025 09:01:46] [FRONTEND] [LOG] 🔧 [init] Forçage du rendu initial...
[18/07/2025 09:01:46] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[18/07/2025 09:01:46] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[18/07/2025 09:01:46] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[18/07/2025 09:01:46] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[18/07/2025 09:01:46] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[18/07/2025 09:01:46] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[18/07/2025 09:01:46] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[18/07/2025 09:01:46] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 60 zones de drop disponibles
[18/07/2025 09:01:46] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 60 zones trouvées
[18/07/2025 09:01:46] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[18/07/2025 09:01:46] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 09:01:46] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Matin: [1, 2, 3, 4, 5]
[18/07/2025 09:01:46] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 09:01:46] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Nuit: [1, 2, 3, 4, 5]
[18/07/2025 09:01:46] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 09:01:46] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Soir: [1, 2, 3, 4, 5]
[18/07/2025 09:01:46] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 09:01:46] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE1: [0, 6]
[18/07/2025 09:01:46] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 09:01:46] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 09:01:46] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 09:01:46] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 09:01:46] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 09:01:46] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE2: [0, 6]
[18/07/2025 09:01:46] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 09:01:46] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 09:01:46] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 09:01:46] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 09:01:46] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 09:01:46] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 09:01:46] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 09:01:46] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 09:01:46] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 09:01:46] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 09:01:46] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 09:01:47] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[18/07/2025 09:01:47] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[18/07/2025 09:01:47] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 09:01:47] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 09:01:47] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 09:01:48] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 09:01:48] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 09:01:48] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[18/07/2025 09:01:48] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[18/07/2025 09:01:48] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic complet du fork modal...
[18/07/2025 09:01:49] [FRONTEND] [LOG] 📊 [DIAGNOSTIC] Résultats: {"teamCalendarApp":true,"detectDropDateFunction":true,"showConfirmationMenuFunction":true,"handleRegularAssignmentDropFunction":true,"regularAssignments":0,"employees":0}
[18/07/2025 09:01:49] [FRONTEND] [LOG] ✅ [DIAGNOSTIC] Toutes les fonctions nécessaires sont disponibles
[18/07/2025 09:01:49] [FRONTEND] [LOG] 🚀 [DATE-TEST] Lancement de la validation automatique des dates...
[18/07/2025 09:01:49] [FRONTEND] [LOG] 🚀 [DATE-TEST] Lancement de la suite de validation des dates...
[18/07/2025 09:01:49] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Conversion de date sécurisée
[18/07/2025 09:01:49] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la conversion de date sécurisée...
[18/07/2025 09:01:49] [FRONTEND] [LOG] ✅ [DATE-TEST] Test 1 (Date locale): 2025-07-24
[18/07/2025 09:01:49] [FRONTEND] [ERROR] ❌ [DATE-TEST] Test 2 (String date): attendu 2025-07-24, obtenu 2025-07-23
[18/07/2025 09:01:49] [FRONTEND] [LOG] ✅ [DATE-TEST] Test 3 (Date ISO avec timezone): 2025-07-24
[18/07/2025 09:01:49] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Logique de fork avec dates
[18/07/2025 09:01:49] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la logique de fork avec dates...
[18/07/2025 09:01:50] [FRONTEND] [ERROR] ❌ [DATE-TEST] Données insuffisantes pour le test
[18/07/2025 09:01:50] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Améliorations timezone
[18/07/2025 09:01:50] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test des améliorations de timezone...
[18/07/2025 09:01:50] [FRONTEND] [WARN] ⚠️ [DATE-TEST] Sélecteur de timezone non trouvé (normal si modal fermé)
[18/07/2025 09:01:50] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Détection de date avec timezone
[18/07/2025 09:01:50] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la détection de date avec timezone...
[18/07/2025 09:01:50] [FRONTEND] [LOG] 🔍 [detectDropDateFromPosition] Analyse position pour employé test-employee
[18/07/2025 09:01:50] [FRONTEND] [LOG] 📍 [detectDropDateFromPosition] Position souris: (500, 300)
[18/07/2025 09:01:50] [FRONTEND] [LOG] ⚠️ [detectDropDateFromPosition] Fallback vers aujourd'hui: 2025-07-18
[18/07/2025 09:01:50] [FRONTEND] [LOG] ✅ [DATE-TEST] Date détectée: 2025-07-18
[18/07/2025 09:01:51] [FRONTEND] [LOG] ✅ [DATE-TEST] Format de date valide
[18/07/2025 09:01:51] [FRONTEND] [LOG] ✅ [DATE-TEST] Date raisonnable (0 jours de différence)
[18/07/2025 09:01:51] [FRONTEND] [LOG] 
📊 [DATE-TEST] Résultats de la validation:
[18/07/2025 09:01:51] [FRONTEND] [LOG] ❌ Conversion de date sécurisée: ÉCHOUÉ
[18/07/2025 09:01:51] [FRONTEND] [LOG] ❌ Logique de fork avec dates: ÉCHOUÉ
[18/07/2025 09:01:51] [FRONTEND] [LOG] ✅ Améliorations timezone: PASSÉ
[18/07/2025 09:01:51] [FRONTEND] [LOG] ✅ Détection de date avec timezone: PASSÉ
[18/07/2025 09:01:51] [FRONTEND] [LOG] 
⚠️ [DATE-TEST] Certaines validations ont échoué
[18/07/2025 09:01:51] [FRONTEND] [LOG] 🔧 [DATE-TEST] Vérifiez les logs ci-dessus pour les détails
[18/07/2025 09:01:51] [FRONTEND] [LOG] 🚀 [GRIP-FIX] Lancement du diagnostic automatique...
[18/07/2025 09:01:51] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (60 zones)
[18/07/2025 09:01:51] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[18/07/2025 09:01:51] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[18/07/2025 09:01:51] [FRONTEND] [LOG] 📊 [GRIP-FIX] Résultats du diagnostic: {"totalShifts":0,"regularShifts":0,"gripsInDOM":0,"gripsWithEvents":0,"gripsResponsive":0,"issues":[]}
[18/07/2025 09:01:52] [FRONTEND] [LOG] 🔍 [GRIP-FIX] Diagnostic complet des GRIP handles...
[18/07/2025 09:01:52] [FRONTEND] [LOG] 📊 [GRIP-FIX] Total shifts trouvés: 0
[18/07/2025 09:01:52] [FRONTEND] [LOG] 🔧 [init] Configuration du drag & drop des postes...
[18/07/2025 09:01:52] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[18/07/2025 09:01:52] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[18/07/2025 09:01:52] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 09:01:52] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[18/07/2025 09:01:52] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 60 zones de drop disponibles
[18/07/2025 09:01:52] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 60 zones trouvées
[18/07/2025 09:01:52] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 09:01:52] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 09:01:52] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 09:01:52] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 09:01:52] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 09:01:52] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 09:01:52] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 09:01:52] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 09:01:52] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 09:01:52] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 09:01:52] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 09:01:52] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 09:01:52] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 09:01:52] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 09:01:52] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 09:01:52] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 09:01:52] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 09:01:52] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 09:01:52] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 09:01:52] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[18/07/2025 09:01:52] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[18/07/2025 09:01:52] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 09:01:52] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 09:01:52] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 09:01:52] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 09:01:52] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 09:01:52] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 09:01:52] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 09:01:52] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 09:01:52] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 09:01:52] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 09:01:52] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[18/07/2025 09:01:52] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (60 zones)
[18/07/2025 09:01:52] [FRONTEND] [LOG] ℹ️ [setupEmployeeDragDrop] Fonction désactivée - Utiliser setupDragAndDrop
[18/07/2025 09:01:52] [FRONTEND] [LOG] 🔧 [addEmergencyFixButton] Ajout du bouton d'historique...
[18/07/2025 09:01:52] [FRONTEND] [LOG] ✅ [addEmergencyFixButton] Boutons ajoutés (emergency fix buttons supprimés)
[18/07/2025 09:01:52] [FRONTEND] [LOG] 📋 [init] Application des attributions régulières après initialisation
[18/07/2025 09:01:52] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] Nettoyage des shifts réguliers existants...
[18/07/2025 09:01:52] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Application pour semaine 2025-W28
[18/07/2025 09:01:52] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 86f97857-d86a-4e2a-aec3-831bf92012d4 commence après cette semaine (startDate: 2025-07-28, semaine se termine: 2025-07-19)
[18/07/2025 09:01:52] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] 0 shifts réguliers supprimés
[18/07/2025 09:01:52] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 6830f981-f642-4364-9b1d-0ff92e12fad2 commence après cette semaine (startDate: 2025-08-01, semaine se termine: 2025-07-19)
[18/07/2025 09:01:52] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 08b7356a-e76a-484d-b599-2d5d4a0d8b6b commence après cette semaine (startDate: 2025-07-23, semaine se termine: 2025-07-19)
[18/07/2025 09:01:52] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 42c64a69-ab23-451f-a314-c5c87622675d commence après cette semaine (startDate: 2025-07-30, semaine se termine: 2025-07-19)
[18/07/2025 09:01:52] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 5400f0bd-d560-4167-89ea-a6da77478469 commence après cette semaine (startDate: 2025-07-22, semaine se termine: 2025-07-19)
[18/07/2025 09:01:52] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 316031c4-9402-4394-970e-e598022f5118 commence après cette semaine (startDate: 2025-07-25, semaine se termine: 2025-07-19)
[18/07/2025 09:01:52] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution ed906322-db11-4d06-aa28-437b06c445c4 a une intersection avec cette semaine (2025-07-18 → 2025-07-21) vs (2025-07-13 → 2025-07-19)
[18/07/2025 09:01:52] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution d546a50b-43af-4f53-afd0-4a43c1f3959a commence après cette semaine (startDate: 2025-07-24, semaine se termine: 2025-07-19)
[18/07/2025 09:01:52] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 1 attributions actives sur 8 total
[18/07/2025 09:01:52] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"ed906322-db11-4d06-aa28-437b06c445c4","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"startDate":"2025-07-18","endDate":"2025-07-21","excludedDates":[]}
[18/07/2025 09:01:52] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution ed906322-db11-4d06-aa28-437b06c445c4: [{"dateKey":"2025-07-13","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-14","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-15","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-16","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-17","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-07-18","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-07-19","isWeekend":true,"dayOfWeek":6}]
[18/07/2025 09:01:52] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-13T04:00:00.000Z","dayDateKey":"2025-07-13","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 09:01:52] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-13 (0)
[18/07/2025 09:01:52] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-13 ignoré pour attribution ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 09:01:52] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[18/07/2025 09:01:52] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-14 (1)
[18/07/2025 09:01:52] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-14T04:00:00.000Z","dayDateKey":"2025-07-14","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 09:01:52] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 09:01:52] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-14: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","dayDateStr":"2025-07-14","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 09:01:52] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-14 avant startDate 2025-07-18 (original: 2025-07-18)
[18/07/2025 09:01:52] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-14 ignoré pour attribution ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 09:01:52] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-15 (2)
[18/07/2025 09:01:52] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-15T04:00:00.000Z","dayDateKey":"2025-07-15","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 09:01:52] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 09:01:52] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-15: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","dayDateStr":"2025-07-15","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 09:01:52] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-15 avant startDate 2025-07-18 (original: 2025-07-18)
[18/07/2025 09:01:52] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-15 ignoré pour attribution ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 09:01:52] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-16 (3)
[18/07/2025 09:01:52] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-16T04:00:00.000Z","dayDateKey":"2025-07-16","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 09:01:52] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 09:01:52] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-16: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","dayDateStr":"2025-07-16","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 09:01:52] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-16 avant startDate 2025-07-18 (original: 2025-07-18)
[18/07/2025 09:01:52] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-16 ignoré pour attribution ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 09:01:52] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-17 (4)
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-17T04:00:00.000Z","dayDateKey":"2025-07-17","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 09:01:53] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-17 avant startDate 2025-07-18 (original: 2025-07-18)
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-17: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","dayDateStr":"2025-07-17","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 09:01:53] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-17 ignoré pour attribution ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 09:01:53] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-07-18 (5)
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-18T04:00:00.000Z","dayDateKey":"2025-07-18","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-18: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","dayDateStr":"2025-07-18","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 09:01:53] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-18
[18/07/2025 09:01:53] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-18 validé, shifts existants: 0
[18/07/2025 09:01:53] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Nuit → 2025-07-18 pour Sophie Leblanc
[18/07/2025 09:01:53] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-07-19 (6)
[18/07/2025 09:01:53] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-19T04:00:00.000Z","dayDateKey":"2025-07-19","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 09:01:53] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-19 ignoré pour attribution ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Rendu demandé car 1 shifts créés
[18/07/2025 09:01:53] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 1 attributions appliquées pour la semaine 2025-W28
[18/07/2025 09:01:53] [FRONTEND] [LOG] ✅ [init] Initialisation terminée avec succès
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🎉 [Agenda] Application fullscreen initialisée avec succès
[18/07/2025 09:01:53] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] Résultats: 0 ajoutés, 0 validés, 0 corrigés
[18/07/2025 09:01:53] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[18/07/2025 09:01:53] [FRONTEND] [WARN] ⚠️ [renderUnifiedCalendar] Conteneur available-posts-container non trouvé, création...
[18/07/2025 09:01:53] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneur available-posts-container créé
[18/07/2025 09:01:53] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[18/07/2025 09:01:53] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 0c74e25d-4692-46d7-b5aa-b84eee797971 -> "00:00-08:00"
[18/07/2025 09:01:53] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[18/07/2025 09:01:53] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[18/07/2025 09:01:53] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 62 zones trouvées
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 62 zones de drop disponibles
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 09:01:53] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 09:01:53] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 09:01:53] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 09:01:53] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 09:01:53] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 09:01:53] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 09:01:53] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 09:01:53] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 09:01:53] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 09:01:53] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[18/07/2025 09:01:53] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 09:01:53] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 09:01:53] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 09:01:53] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 09:01:53] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 09:01:53] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[18/07/2025 09:01:53] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (62 zones)
[18/07/2025 09:01:53] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[18/07/2025 09:01:53] [FRONTEND] [LOG] ✅ [SETUP] Drag & drop des employés configuré (mode simplifié)
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[18/07/2025 09:01:53] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 62 zones trouvées
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 62 zones de drop disponibles
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 09:01:53] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 09:01:53] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 09:01:53] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 09:01:53] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 09:01:53] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 09:01:53] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 09:01:53] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 09:01:53] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 09:01:53] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 09:01:54] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 09:01:54] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 09:01:54] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 09:01:54] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 09:01:54] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[18/07/2025 09:01:54] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[18/07/2025 09:01:54] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 09:01:54] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 09:01:54] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 09:01:54] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 09:01:54] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 09:01:54] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 09:01:54] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 09:01:54] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 09:01:54] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 09:01:54] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 09:01:54] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[18/07/2025 09:01:54] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (62 zones)
[18/07/2025 09:01:54] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] Résultats: 0 ajoutés, 1 validés, 0 corrigés
[18/07/2025 09:01:54] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[18/07/2025 09:01:54] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[18/07/2025 09:01:54] [FRONTEND] [LOG] 🔍 [diagnoseRegularAssignmentGrips] Diagnostic des grips d'attributions régulières
[18/07/2025 09:01:54] [FRONTEND] [LOG] 📊 [diagnoseRegularAssignmentGrips] Statistiques: {"totalShifts":1,"regularShifts":1,"regularAssignments":8}
[18/07/2025 09:01:54] [FRONTEND] [LOG] 🎯 [diagnoseRegularAssignmentGrips] Grips trouvés dans le DOM: 1
[18/07/2025 09:01:54] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[18/07/2025 09:01:54] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 1: {"employee":"Sophie Leblanc","date":"2025-07-18","assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","text":"00:00-08:00"}
[18/07/2025 09:01:54] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[18/07/2025 09:01:54] [FRONTEND] [LOG] 🚀 [DATE-TEST] Lancement de la validation automatique des dates...
[18/07/2025 09:01:54] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Conversion de date sécurisée
[18/07/2025 09:01:54] [FRONTEND] [LOG] 🚀 [DATE-TEST] Lancement de la suite de validation des dates...
[18/07/2025 09:01:54] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la conversion de date sécurisée...
[18/07/2025 09:01:54] [FRONTEND] [LOG] ✅ [DATE-TEST] Test 1 (Date locale): 2025-07-24
[18/07/2025 09:01:54] [FRONTEND] [ERROR] ❌ [DATE-TEST] Test 2 (String date): attendu 2025-07-24, obtenu 2025-07-23
[18/07/2025 09:01:54] [FRONTEND] [LOG] ✅ [DATE-TEST] Test 3 (Date ISO avec timezone): 2025-07-24
[18/07/2025 09:01:54] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Logique de fork avec dates
[18/07/2025 09:01:54] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la logique de fork avec dates...
[18/07/2025 09:01:54] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test avec date: 2025-07-25
[18/07/2025 09:01:54] [FRONTEND] [LOG] 🧪 [DATE-TEST] Simulation du changement permanent...
[18/07/2025 09:01:54] [FRONTEND] [LOG] - Assignment: 86f97857-d86a-4e2a-aec3-831bf92012d4
[18/07/2025 09:01:54] [FRONTEND] [LOG] - De: Lucas Bernard
[18/07/2025 09:01:54] [FRONTEND] [LOG] - Vers: Jean Dupont
[18/07/2025 09:01:54] [FRONTEND] [LOG] - Date: 2025-07-25
[18/07/2025 09:01:54] [FRONTEND] [LOG] ✅ [showRegularAssignmentConfirmationMenu] Date automatiquement détectée: 2025-07-25
[18/07/2025 09:01:54] [FRONTEND] [LOG] 🎯 [showRegularAssignmentConfirmationMenu] Affichage menu pour: {"assignment":"86f97857-d86a-4e2a-aec3-831bf92012d4","from":"Lucas Bernard","to":"Jean Dupont","post":"Poste Nuit","referenceDate":"2025-07-25","minDate":"2025-07-25","todayKey":"2025-07-18"}
[18/07/2025 09:01:54] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Améliorations timezone
[18/07/2025 09:01:54] [FRONTEND] [WARN] ⚠️ [DATE-TEST] Sélecteur de timezone non trouvé (normal si modal fermé)
[18/07/2025 09:01:54] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test des améliorations de timezone...
[18/07/2025 09:01:54] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Détection de date avec timezone
[18/07/2025 09:01:54] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la détection de date avec timezone...
[18/07/2025 09:01:54] [FRONTEND] [LOG] 🔍 [detectDropDateFromPosition] Analyse position pour employé test-employee
[18/07/2025 09:01:54] [FRONTEND] [LOG] 📍 [detectDropDateFromPosition] Position souris: (500, 300)
[18/07/2025 09:01:54] [FRONTEND] [LOG] ✅ [detectDropDateFromPosition] Date calculée via position: 2025-07-14 (jour 1)
[18/07/2025 09:01:54] [FRONTEND] [LOG] ✅ [DATE-TEST] Date détectée: 2025-07-14
[18/07/2025 09:01:54] [FRONTEND] [LOG] ✅ [DATE-TEST] Format de date valide
[18/07/2025 09:01:54] [FRONTEND] [LOG] ✅ [DATE-TEST] Date raisonnable (4 jours de différence)
[18/07/2025 09:01:54] [FRONTEND] [LOG] 
📊 [DATE-TEST] Résultats de la validation:
[18/07/2025 09:01:54] [FRONTEND] [LOG] ❌ Conversion de date sécurisée: ÉCHOUÉ
[18/07/2025 09:01:54] [FRONTEND] [LOG] ✅ Logique de fork avec dates: PASSÉ
[18/07/2025 09:01:54] [FRONTEND] [LOG] ✅ Améliorations timezone: PASSÉ
[18/07/2025 09:01:54] [FRONTEND] [LOG] ✅ Détection de date avec timezone: PASSÉ
[18/07/2025 09:01:54] [FRONTEND] [LOG] 
⚠️ [DATE-TEST] Certaines validations ont échoué
[18/07/2025 09:01:54] [FRONTEND] [LOG] 🔧 [DATE-TEST] Vérifiez les logs ci-dessus pour les détails
[18/07/2025 09:01:54] [FRONTEND] [LOG] 🚀 [GRIP-FIX] Lancement du diagnostic automatique...
[18/07/2025 09:01:54] [FRONTEND] [LOG] 🔍 [GRIP-FIX] Diagnostic complet des GRIP handles...
[18/07/2025 09:01:54] [FRONTEND] [LOG] 📊 [GRIP-FIX] Total shifts trouvés: 1
[18/07/2025 09:01:54] [FRONTEND] [LOG] 🔍 [GRIP-FIX] Test responsivité ed906322-db11-4d06-aa28-437b06c445c4: {"hasMouseEvents":false,"isVisible":true,"hasCorrectCursor":true,"hasValidDimensions":true,"rect":{"width":197.85000610351562,"height":6}}
[18/07/2025 09:01:54] [FRONTEND] [LOG] 📊 [GRIP-FIX] Résultats du diagnostic: {"totalShifts":1,"regularShifts":1,"gripsInDOM":1,"gripsWithEvents":0,"gripsResponsive":1,"issues":[]}
[18/07/2025 09:01:54] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[18/07/2025 09:01:54] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic des problèmes de modale...
[18/07/2025 09:01:54] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] État de la modale: {"hidden":true,"classes":["hidden","fixed","inset-0","bg-black/40","backdrop-blur-sm","z-50","flex","items-center","justify-center","p-4"],"style":""}
[18/07/2025 09:01:54] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Éléments DOM: {"modal":true,"closeBtn":true,"cancelBtn":true,"confirmBtn":true,"radioButtons":4}
[18/07/2025 09:01:54] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] TeamCalendarApp: {"exists":true,"hasModalManager":true,"hasOpenFunction":true,"hasHandleFunction":true}
[18/07/2025 09:01:54] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Données contextuelles: {"employees":5,"posts":5}
[18/07/2025 09:01:54] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation de la correction principale...
[18/07/2025 09:01:54] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation complète...
[18/07/2025 09:01:54] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation automatique...
[18/07/2025 09:01:54] [FRONTEND] [LOG] ✅ [VALIDATION] ModalManager.setupAssignmentContextModal disponible
[18/07/2025 09:01:54] [FRONTEND] [LOG] ✅ [VALIDATION] ModalManager.app initialisé
[18/07/2025 09:01:54] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des éléments DOM...
[18/07/2025 09:01:54] [FRONTEND] [LOG] 📋 [VALIDATION] Éléments DOM: {"modal":true,"closeBtn":true,"cancelBtn":true,"confirmBtn":true,"radioButtons":true}
[18/07/2025 09:01:54] [FRONTEND] [LOG] ✅ [VALIDATION] handleAssignmentContextConfirm disponible
[18/07/2025 09:01:54] [FRONTEND] [LOG] ✅ [VALIDATION] Tous les éléments DOM sont présents
[18/07/2025 09:01:54] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des listeners...
[18/07/2025 09:01:54] [FRONTEND] [LOG] 🔧 [setupAssignmentContextModal] Configuration du modal contextuel...
[18/07/2025 09:01:54] [FRONTEND] [LOG] ✅ [setupAssignmentContextModal] Modal contextuel configuré avec listeners nettoyés
[18/07/2025 09:01:54] [FRONTEND] [LOG] 📊 [VALIDATION] Résultats de validation: {"mainFix":true,"domElements":true,"listeners":true}
[18/07/2025 09:01:54] [FRONTEND] [LOG] ✅ [VALIDATION] setupAssignmentContextModal exécuté sans erreur
[18/07/2025 09:01:54] [FRONTEND] [LOG] 🎉 [VALIDATION] Toutes les validations sont passées avec succès !
[18/07/2025 09:01:54] [FRONTEND] [LOG] ✅ [VALIDATION] La correction de la modale devrait maintenant fonctionner
[18/07/2025 09:01:54] [FRONTEND] [LOG] 4. Ou utilisez testButtonClicks() pour un test automatique
[18/07/2025 09:01:54] [FRONTEND] [LOG] 🧪 [VALIDATION] Pour tester pratiquement:
[18/07/2025 09:01:54] [FRONTEND] [LOG] 2. Vérifiez que la modale s'ouvre
[18/07/2025 09:01:54] [FRONTEND] [LOG] 1. Effectuez un drag & drop d'un poste vers un employé
[18/07/2025 09:01:54] [FRONTEND] [LOG] 3. Cliquez sur "Confirmer" et vérifiez les logs
[18/07/2025 09:01:54] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[18/07/2025 09:01:54] [FRONTEND] [LOG] ✅ [DIAGNOSTIC] Toutes les fonctions nécessaires sont disponibles
[18/07/2025 09:01:54] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic complet du fork modal...
[18/07/2025 09:01:54] [FRONTEND] [LOG] 📊 [DIAGNOSTIC] Résultats: {"teamCalendarApp":true,"detectDropDateFunction":true,"showConfirmationMenuFunction":true,"handleRegularAssignmentDropFunction":true,"regularAssignments":8,"employees":5}
[18/07/2025 09:01:55] [FRONTEND] [LOG] ✅ [DATE-TEST] Date configurée dans le modal: 2025-07-25
[18/07/2025 09:01:55] [FRONTEND] [LOG] 🧪 [DATE-TEST] Simulation du processus de fork...
[18/07/2025 09:01:55] [FRONTEND] [LOG] ✅ [DATE-TEST] Date de test correcte: 2025-07-25