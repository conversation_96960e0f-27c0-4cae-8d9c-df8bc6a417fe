[15/07/2025 11:41:43] [FRONTEND] [LOG] ✅ [DATE-TEST] Fonctions de validation des dates disponibles:
[15/07/2025 11:41:43] [FRONTEND] [LOG] - testSafeDateConversion() : Tester la conversion sécurisée
[15/07/2025 11:41:43] [FRONTEND] [LOG] - testForkDateLogic() : Tester la logique de fork
[15/07/2025 11:41:43] [FRONTEND] [LOG] - testDropDateDetectionWithTimezone() : Tester la détection avec timezone
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:41:43.503Z"
}
[15/07/2025 11:41:43] [BACKEND] [INFO] [LOGS] Client connecté: 1752594103501
[15/07/2025 11:41:43] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[15/07/2025 11:41:43] [FRONTEND] [LOG] [LogsPage] Fermeture de la connexion SSE.
[15/07/2025 11:41:43] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[15/07/2025 11:41:40] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-18 pour Sophie Leblanc
[15/07/2025 11:41:40] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-07-19 (6)
[15/07/2025 11:41:40] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-19T04:00:00.000Z","dayDateKey":"2025-07-19","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:41:40] [FRONTEND] [LOG] 🎉 [Agenda] Application fullscreen initialisée avec succès
[15/07/2025 11:41:40] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 4 attributions appliquées pour la semaine 2025-W28
[15/07/2025 11:41:40] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-19 ignoré pour attribution b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 11:41:40] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 11:41:40] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] 0 grips ajoutés
[15/07/2025 11:41:40] [FRONTEND] [LOG] ✅ [init] Initialisation terminée avec succès
[15/07/2025 11:41:40] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 11:41:40] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneur available-posts-container créé
[15/07/2025 11:41:40] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[15/07/2025 11:41:40] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[15/07/2025 11:41:40] [FRONTEND] [WARN] ⚠️ [renderUnifiedCalendar] Conteneur available-posts-container non trouvé, création...
[15/07/2025 11:41:40] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 11:41:40] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 11:41:40] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 68 zones trouvées
[15/07/2025 11:41:41] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 68 zones de drop disponibles
[15/07/2025 11:41:41] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 11:41:41] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 11:41:41] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [SETUP] Drag & drop des employés configuré (mode simplifié)
[15/07/2025 11:41:41] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 11:41:41] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Éléments DOM: {"modal":true,"closeBtn":true,"cancelBtn":true,"confirmBtn":true,"radioButtons":4}
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [VALIDATION] handleAssignmentContextConfirm disponible
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [VALIDATION] Tous les éléments DOM sont présents
[15/07/2025 11:41:41] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 4: {"employee":"Sophie Leblanc","date":"2025-07-18","assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","text":"08:00-16:00"}
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 11:41:41] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 11:41:41] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 11:41:41] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 11:41:41] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 11:41:41] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 11:41:41] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 11:41:41] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 11:41:41] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (68 zones)
[15/07/2025 11:41:41] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 11:41:41] [FRONTEND] [LOG] 📊 [VALIDATION] Résultats de validation: {"mainFix":true,"domElements":true,"listeners":true}
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [DIAGNOSTIC] Toutes les fonctions nécessaires sont disponibles
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 11:41:41] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 11:41:41] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 11:41:41] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 68 zones de drop disponibles
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 68 zones trouvées
[15/07/2025 11:41:41] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 11:41:41] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 11:41:41] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 11:41:41] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation complète...
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [VALIDATION] La correction de la modale devrait maintenant fonctionner
[15/07/2025 11:41:41] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 11:41:41] [FRONTEND] [LOG] 📊 [diagnoseRegularAssignmentGrips] Statistiques: {"totalShifts":4,"regularShifts":4,"regularAssignments":3}
[15/07/2025 11:41:41] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 11:41:41] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 11:41:41] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 11:41:41] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 11:41:41] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 11:41:41] [FRONTEND] [LOG] 🎯 [diagnoseRegularAssignmentGrips] Grips trouvés dans le DOM: 4
[15/07/2025 11:41:41] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 11:41:41] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 11:41:41] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (68 zones)
[15/07/2025 11:41:41] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 11:41:41] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic des problèmes de modale...
[15/07/2025 11:41:41] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Données contextuelles: {"employees":5,"posts":5}
[15/07/2025 11:41:41] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] État de la modale: {"hidden":true,"classes":["hidden","fixed","inset-0","bg-black/40","backdrop-blur-sm","z-50","flex","items-center","justify-center","p-4"],"style":""}
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 11:41:41] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 1: {"employee":"Sophie Leblanc","date":"2025-07-15","assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","text":"08:00-16:00"}
[15/07/2025 11:41:41] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 11:41:41] [FRONTEND] [LOG] 2. Vérifiez que la modale s'ouvre
[15/07/2025 11:41:41] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 3: {"employee":"Sophie Leblanc","date":"2025-07-17","assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","text":"08:00-16:00"}
[15/07/2025 11:41:41] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 2: {"employee":"Sophie Leblanc","date":"2025-07-16","assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","text":"08:00-16:00"}
[15/07/2025 11:41:41] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation automatique...
[15/07/2025 11:41:41] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation de la correction principale...
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [VALIDATION] ModalManager.app initialisé
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [VALIDATION] ModalManager.setupAssignmentContextModal disponible
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 11:41:41] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des éléments DOM...
[15/07/2025 11:41:41] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des listeners...
[15/07/2025 11:41:41] [FRONTEND] [LOG] 🔧 [setupAssignmentContextModal] Configuration du modal contextuel...
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [setupAssignmentContextModal] Modal contextuel configuré avec listeners nettoyés
[15/07/2025 11:41:41] [FRONTEND] [LOG] 🎉 [VALIDATION] Toutes les validations sont passées avec succès !
[15/07/2025 11:41:41] [FRONTEND] [LOG] ✅ [VALIDATION] setupAssignmentContextModal exécuté sans erreur
[15/07/2025 11:41:41] [FRONTEND] [LOG] 🧪 [VALIDATION] Pour tester pratiquement:
[15/07/2025 11:41:41] [FRONTEND] [LOG] 3. Cliquez sur "Confirmer" et vérifiez les logs
[15/07/2025 11:41:42] [FRONTEND] [LOG] 4. Ou utilisez testButtonClicks() pour un test automatique
[15/07/2025 11:41:42] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[15/07/2025 11:41:42] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic complet du fork modal...
[15/07/2025 11:41:42] [FRONTEND] [LOG] 📊 [DIAGNOSTIC] Résultats: {"teamCalendarApp":true,"detectDropDateFunction":true,"showConfirmationMenuFunction":true,"handleRegularAssignmentDropFunction":true,"regularAssignments":3,"employees":5}
[15/07/2025 11:41:42] [FRONTEND] [LOG] 🚀 [DATE-TEST] Lancement de la suite de validation des dates...
[15/07/2025 11:41:42] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Conversion de date sécurisée
[15/07/2025 11:41:42] [FRONTEND] [LOG] 🚀 [DATE-TEST] Lancement de la validation automatique des dates...
[15/07/2025 11:41:42] [FRONTEND] [LOG] 🔧 [DATE-TEST] Vérifiez les logs ci-dessus pour les détails
[15/07/2025 11:41:42] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la conversion de date sécurisée...
[15/07/2025 11:41:42] [FRONTEND] [ERROR] ❌ [DATE-TEST] Test 2 (String date): attendu 2025-07-24, obtenu 2025-07-23
[15/07/2025 11:41:42] [FRONTEND] [LOG] ✅ [DATE-TEST] Test 1 (Date locale): 2025-07-24
[15/07/2025 11:41:42] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test des améliorations de timezone...
[15/07/2025 11:41:42] [FRONTEND] [LOG] ✅ [DATE-TEST] Test 3 (Date ISO avec timezone): 2025-07-24
[15/07/2025 11:41:42] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la logique de fork avec dates...
[15/07/2025 11:41:42] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Logique de fork avec dates
[15/07/2025 11:41:42] [FRONTEND] [LOG] - De: Marie Martin
[15/07/2025 11:41:42] [FRONTEND] [LOG] ✅ Améliorations timezone: PASSÉ
[15/07/2025 11:41:42] [FRONTEND] [WARN] ⚠️ [DATE-TEST] Sélecteur de timezone non trouvé (normal si modal fermé)
[15/07/2025 11:41:42] [FRONTEND] [LOG] 
📊 [DATE-TEST] Résultats de la validation:
[15/07/2025 11:41:42] [FRONTEND] [LOG] - Vers: Jean Dupont
[15/07/2025 11:41:42] [FRONTEND] [LOG] ✅ Logique de fork avec dates: PASSÉ
[15/07/2025 11:41:42] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la détection de date avec timezone...
[15/07/2025 11:41:42] [FRONTEND] [DEBUG] [vite] connected.
[15/07/2025 11:41:42] [FRONTEND] [LOG] 🔍 [detectDropDateFromPosition] Analyse position pour employé test-employee
[15/07/2025 11:41:42] [FRONTEND] [LOG] ✅ [detectDropDateFromPosition] Date calculée via position: 2025-07-15 (jour 2)
[15/07/2025 11:41:42] [FRONTEND] [LOG] 
⚠️ [DATE-TEST] Certaines validations ont échoué
[15/07/2025 11:41:42] [FRONTEND] [LOG] ❌ Conversion de date sécurisée: ÉCHOUÉ
[15/07/2025 11:41:42] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test avec date: 2025-07-25
[15/07/2025 11:41:42] [FRONTEND] [LOG] - Date: 2025-07-25
[15/07/2025 11:41:42] [FRONTEND] [LOG] ✅ [DATE-TEST] Date raisonnable (0 jours de différence)
[15/07/2025 11:41:42] [FRONTEND] [LOG] 📍 [detectDropDateFromPosition] Position souris: (500, 300)
[15/07/2025 11:41:42] [FRONTEND] [LOG] 🧪 [DATE-TEST] Simulation du changement permanent...
[15/07/2025 11:41:42] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Détection de date avec timezone
[15/07/2025 11:41:42] [FRONTEND] [LOG] 🎯 [showRegularAssignmentConfirmationMenu] Affichage menu pour: {"assignment":"a563e948-18d8-4540-bfbc-7853d6506c05","from":"Marie Martin","to":"Jean Dupont","post":"Poste Matin","referenceDate":"2025-07-25","minDate":"2025-07-25","todayKey":"2025-07-15"}
[15/07/2025 11:41:42] [FRONTEND] [LOG] ✅ [DATE-TEST] Format de date valide
[15/07/2025 11:41:42] [FRONTEND] [LOG] ✅ [showRegularAssignmentConfirmationMenu] Date automatiquement détectée: 2025-07-25
[15/07/2025 11:41:42] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Améliorations timezone
[15/07/2025 11:41:42] [FRONTEND] [LOG] ✅ [DATE-TEST] Date détectée: 2025-07-15
[15/07/2025 11:41:42] [FRONTEND] [LOG] - Assignment: a563e948-18d8-4540-bfbc-7853d6506c05
[15/07/2025 11:41:42] [FRONTEND] [LOG] ✅ Détection de date avec timezone: PASSÉ
[15/07/2025 11:41:42] [FRONTEND] [LOG] 🧪 [DATE-TEST] Simulation du processus de fork...
[15/07/2025 11:41:42] [FRONTEND] [LOG] ✅ [DATE-TEST] Date configurée dans le modal: 2025-07-25
[15/07/2025 11:41:42] [FRONTEND] [LOG] ✅ [DATE-TEST] Date de test correcte: 2025-07-25
[15/07/2025 11:41:43] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[15/07/2025 11:41:43] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[15/07/2025 11:41:43] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[15/07/2025 11:41:43] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[15/07/2025 11:41:43] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[15/07/2025 11:41:43] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[15/07/2025 11:41:43] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[15/07/2025 11:41:43] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[15/07/2025 11:41:43] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[15/07/2025 11:41:43] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[15/07/2025 11:41:43] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[15/07/2025 11:41:43] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[15/07/2025 11:41:43] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[15/07/2025 11:41:43] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[15/07/2025 11:41:43] [FRONTEND] [LOG] 🧪 [TEST] Script de test de la modale drag & drop chargé
[15/07/2025 11:41:43] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[15/07/2025 11:41:43] [FRONTEND] [LOG] - testAssignmentModal() : Tester l'ouverture de la modale
[15/07/2025 11:41:43] [FRONTEND] [LOG] - testButtonListeners() : Tester les listeners des boutons
[15/07/2025 11:41:43] [FRONTEND] [LOG] - diagnoseModalIssues() : Diagnostiquer les problèmes
[15/07/2025 11:41:43] [FRONTEND] [LOG] - forceReconfigureListeners() : Reconfigurer les listeners
[15/07/2025 11:41:43] [FRONTEND] [LOG] - testButtonClicks() : Tester les clics sur les boutons
[15/07/2025 11:41:43] [FRONTEND] [LOG] 🔍 [VALIDATION] Script de validation de la correction modale chargé
[15/07/2025 11:41:43] [FRONTEND] [LOG] ✅ [VALIDATION] Fonctions de validation disponibles:
[15/07/2025 11:41:43] [FRONTEND] [LOG] - validateMainFix() : Valider la correction principale
[15/07/2025 11:41:43] [FRONTEND] [LOG] - validateDOMElements() : Valider les éléments DOM
[15/07/2025 11:41:43] [FRONTEND] [LOG] - validateListeners() : Valider les listeners
[15/07/2025 11:41:43] [FRONTEND] [LOG] - runDateValidationSuite() : Suite complète de validation
[15/07/2025 11:41:43] [FRONTEND] [LOG] - runCompleteValidation() : Validation complète
[15/07/2025 11:41:43] [FRONTEND] [LOG] - testEndToEnd() : Test end-to-end complet
[15/07/2025 11:41:43] [FRONTEND] [LOG] 🧪 [TEST] Script de test pour la correction du fork modal chargé
[15/07/2025 11:41:43] [FRONTEND] [LOG] - testTimezoneEnhancements() : Tester les améliorations timezone
[15/07/2025 11:41:43] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[15/07/2025 11:41:43] [FRONTEND] [LOG] - testDropDateDetection() : Tester la détection de date
[15/07/2025 11:41:43] [FRONTEND] [LOG] - testForkModalWithDate() : Tester le modal avec date
[15/07/2025 11:41:43] [FRONTEND] [LOG] - testCompleteForkFlow() : Tester le flux complet
[15/07/2025 11:41:43] [FRONTEND] [LOG] - diagnoseForkModalIssues() : Diagnostic complet
[15/07/2025 11:41:43] [FRONTEND] [LOG] 🧪 [DATE-TEST] Script de validation des corrections de date chargé
[15/07/2025 11:41:43] [FRONTEND] [LOG] ✅ [DATE-TEST] Fonctions de validation des dates disponibles:
[15/07/2025 11:41:43] [FRONTEND] [LOG] - testSafeDateConversion() : Tester la conversion sécurisée
[15/07/2025 11:41:43] [FRONTEND] [LOG] - testForkDateLogic() : Tester la logique de fork
[15/07/2025 11:41:43] [FRONTEND] [LOG] - testDropDateDetectionWithTimezone() : Tester la détection avec timezone
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 120ms: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 120ms:",
    "SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:41:43.623Z"
}
[15/07/2025 11:41:43] [BACKEND] [INFO] ✅ Requête exécutée en 120ms: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
[15/07/2025 11:41:45] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[15/07/2025 11:41:45] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] TeamCalendarApp: {"exists":true,"hasModalManager":true,"hasOpenFunction":true,"hasHandleFunction":true}
[15/07/2025 11:41:45] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Données contextuelles: {"employees":0,"posts":0}
[15/07/2025 11:41:45] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic des problèmes de modale...
[15/07/2025 11:41:45] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Éléments DOM: {"modal":false,"closeBtn":false,"cancelBtn":false,"confirmBtn":false,"radioButtons":0}
[15/07/2025 11:41:46] [FRONTEND] [DEBUG] [vite] connected.
[15/07/2025 11:41:46] [FRONTEND] [LOG] ✅ [VALIDATION] ModalManager.setupAssignmentContextModal disponible
[15/07/2025 11:41:46] [FRONTEND] [ERROR] ❌ [VALIDATION] ModalManager.app non initialisé
[15/07/2025 11:41:46] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation complète...
[15/07/2025 11:41:46] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation de la correction principale...
[15/07/2025 11:41:46] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation automatique...
[15/07/2025 11:41:46] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des éléments DOM...
[15/07/2025 11:41:46] [FRONTEND] [LOG] 📋 [VALIDATION] Éléments DOM: {"modal":false,"closeBtn":false,"cancelBtn":false,"confirmBtn":false,"radioButtons":false}
[15/07/2025 11:41:46] [FRONTEND] [ERROR] ❌ [VALIDATION] Éléments DOM manquants
[15/07/2025 11:41:46] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des listeners...
[15/07/2025 11:41:46] [FRONTEND] [LOG] 🔧 [setupAssignmentContextModal] Configuration du modal contextuel...
[15/07/2025 11:41:46] [FRONTEND] [ERROR] ❌ [setupAssignmentContextModal] Éléments manquants: {"closeBtn":false,"cancelBtn":false,"confirmBtn":false}
[15/07/2025 11:41:46] [FRONTEND] [LOG] ✅ [VALIDATION] setupAssignmentContextModal exécuté sans erreur
[15/07/2025 11:41:46] [FRONTEND] [LOG] 📊 [VALIDATION] Résultats de validation: {"mainFix":false,"domElements":false,"listeners":true}
[15/07/2025 11:41:46] [FRONTEND] [ERROR] ❌ [VALIDATION] Certaines validations ont échoué
[15/07/2025 11:41:46] [FRONTEND] [LOG] 🔧 [VALIDATION] Actions recommandées:
[15/07/2025 11:41:46] [FRONTEND] [LOG] - Vérifier que TeamCalendarApp et ModalManager sont correctement initialisés
[15/07/2025 11:41:46] [FRONTEND] [LOG] - Vérifier que la modale assignment-context-modal existe dans le DOM
[15/07/2025 11:41:46] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[15/07/2025 11:41:46] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic complet du fork modal...
[15/07/2025 11:41:46] [FRONTEND] [LOG] ✅ [DIAGNOSTIC] Toutes les fonctions nécessaires sont disponibles
[15/07/2025 11:41:46] [FRONTEND] [LOG] 📊 [DIAGNOSTIC] Résultats: {"teamCalendarApp":true,"detectDropDateFunction":true,"showConfirmationMenuFunction":true,"handleRegularAssignmentDropFunction":true,"regularAssignments":0,"employees":0}
[15/07/2025 11:41:47] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[15/07/2025 11:41:47] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[15/07/2025 11:41:47] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[15/07/2025 11:41:47] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[15/07/2025 11:41:47] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[15/07/2025 11:41:47] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[15/07/2025 11:41:47] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[15/07/2025 11:41:47] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[15/07/2025 11:41:47] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[15/07/2025 11:41:47] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[15/07/2025 11:41:47] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[15/07/2025 11:41:47] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[15/07/2025 11:41:47] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[15/07/2025 11:41:47] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[15/07/2025 11:41:47] [FRONTEND] [LOG] 🧪 [TEST] Script de test de la modale drag & drop chargé
[15/07/2025 11:41:47] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[15/07/2025 11:41:47] [FRONTEND] [LOG] - testAssignmentModal() : Tester l'ouverture de la modale
[15/07/2025 11:41:47] [FRONTEND] [LOG] - testButtonListeners() : Tester les listeners des boutons
[15/07/2025 11:41:47] [FRONTEND] [LOG] - diagnoseModalIssues() : Diagnostiquer les problèmes
[15/07/2025 11:41:47] [FRONTEND] [LOG] - forceReconfigureListeners() : Reconfigurer les listeners
[15/07/2025 11:41:47] [FRONTEND] [LOG] - testButtonClicks() : Tester les clics sur les boutons
[15/07/2025 11:41:47] [FRONTEND] [LOG] 🔍 [VALIDATION] Script de validation de la correction modale chargé
[15/07/2025 11:41:47] [FRONTEND] [LOG] ✅ [VALIDATION] Fonctions de validation disponibles:
[15/07/2025 11:41:47] [FRONTEND] [LOG] - validateMainFix() : Valider la correction principale
[15/07/2025 11:41:47] [FRONTEND] [LOG] - validateDOMElements() : Valider les éléments DOM
[15/07/2025 11:41:47] [FRONTEND] [LOG] - validateListeners() : Valider les listeners
[15/07/2025 11:41:47] [FRONTEND] [LOG] - runCompleteValidation() : Validation complète
[15/07/2025 11:41:47] [FRONTEND] [LOG] - testEndToEnd() : Test end-to-end complet
[15/07/2025 11:41:47] [FRONTEND] [LOG] 🧪 [TEST] Script de test pour la correction du fork modal chargé
[15/07/2025 11:41:47] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[15/07/2025 11:41:47] [FRONTEND] [LOG] - testDropDateDetection() : Tester la détection de date
[15/07/2025 11:41:47] [FRONTEND] [LOG] - testForkModalWithDate() : Tester le modal avec date
[15/07/2025 11:41:47] [FRONTEND] [LOG] - diagnoseForkModalIssues() : Diagnostic complet
[15/07/2025 11:41:47] [FRONTEND] [LOG] - testCompleteForkFlow() : Tester le flux complet
[15/07/2025 11:41:47] [FRONTEND] [LOG] 🧪 [DATE-TEST] Script de validation des corrections de date chargé
[15/07/2025 11:41:47] [FRONTEND] [LOG] ✅ [DATE-TEST] Fonctions de validation des dates disponibles:
[15/07/2025 11:41:47] [FRONTEND] [LOG] - testSafeDateConversion() : Tester la conversion sécurisée
[15/07/2025 11:41:47] [FRONTEND] [LOG] - testForkDateLogic() : Tester la logique de fork
[15/07/2025 11:41:47] [FRONTEND] [LOG] - testTimezoneEnhancements() : Tester les améliorations timezone
[15/07/2025 11:41:47] [FRONTEND] [LOG] - testDropDateDetectionWithTimezone() : Tester la détection avec timezone
[15/07/2025 11:41:47] [FRONTEND] [LOG] - runDateValidationSuite() : Suite complète de validation
[15/07/2025 11:41:47] [FRONTEND] [LOG] 🚀 [DATE-TEST] Lancement de la validation automatique des dates...
[15/07/2025 11:41:47] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Conversion de date sécurisée
[15/07/2025 11:41:47] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Logique de fork avec dates
[15/07/2025 11:41:47] [FRONTEND] [LOG] 🚀 [DATE-TEST] Lancement de la suite de validation des dates...
[15/07/2025 11:41:47] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la conversion de date sécurisée...
[15/07/2025 11:41:47] [FRONTEND] [LOG] ✅ [DATE-TEST] Test 1 (Date locale): 2025-07-24
[15/07/2025 11:41:47] [FRONTEND] [ERROR] ❌ [DATE-TEST] Test 2 (String date): attendu 2025-07-24, obtenu 2025-07-23
[15/07/2025 11:41:47] [FRONTEND] [LOG] 🔧 [DATE-TEST] Vérifiez les logs ci-dessus pour les détails
[15/07/2025 11:41:47] [FRONTEND] [LOG] ✅ [DATE-TEST] Test 3 (Date ISO avec timezone): 2025-07-24
[15/07/2025 11:41:47] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la logique de fork avec dates...
[15/07/2025 11:41:47] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Améliorations timezone
[15/07/2025 11:41:47] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test des améliorations de timezone...
[15/07/2025 11:41:47] [FRONTEND] [LOG] 🔍 [detectDropDateFromPosition] Analyse position pour employé test-employee
[15/07/2025 11:41:47] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la détection de date avec timezone...
[15/07/2025 11:41:47] [FRONTEND] [LOG] ❌ Conversion de date sécurisée: ÉCHOUÉ
[15/07/2025 11:41:47] [FRONTEND] [ERROR] ❌ [DATE-TEST] Données insuffisantes pour le test
[15/07/2025 11:41:47] [FRONTEND] [LOG] ✅ [DATE-TEST] Format de date valide
[15/07/2025 11:41:47] [FRONTEND] [LOG] ✅ Améliorations timezone: PASSÉ
[15/07/2025 11:41:47] [FRONTEND] [LOG] ❌ Logique de fork avec dates: ÉCHOUÉ
[15/07/2025 11:41:47] [FRONTEND] [LOG] ✅ [DATE-TEST] Date raisonnable (0 jours de différence)
[15/07/2025 11:41:47] [FRONTEND] [LOG] ✅ Détection de date avec timezone: PASSÉ
[15/07/2025 11:41:48] [FRONTEND] [LOG] 
📊 [DATE-TEST] Résultats de la validation:
[15/07/2025 11:41:48] [FRONTEND] [WARN] ⚠️ [DATE-TEST] Sélecteur de timezone non trouvé (normal si modal fermé)
[15/07/2025 11:41:48] [FRONTEND] [LOG] 📍 [detectDropDateFromPosition] Position souris: (500, 300)
[15/07/2025 11:41:48] [FRONTEND] [LOG] ✅ [DATE-TEST] Date détectée: 2025-07-15
[15/07/2025 11:41:48] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Détection de date avec timezone
[15/07/2025 11:41:48] [FRONTEND] [LOG] ⚠️ [detectDropDateFromPosition] Fallback vers aujourd'hui: 2025-07-15
[15/07/2025 11:41:48] [FRONTEND] [LOG] 🔌 [ConnectionIndicator] Création de l'indicateur de connexion
[15/07/2025 11:41:48] [FRONTEND] [LOG] 
⚠️ [DATE-TEST] Certaines validations ont échoué
[15/07/2025 11:41:48] [FRONTEND] [LOG] 🧹 [Agenda] Nettoyage composant Agenda
[15/07/2025 11:41:48] [FRONTEND] [LOG] 🔄 [Agenda] Réinitialisation nécessaire (employés non chargés)
[15/07/2025 11:41:48] [FRONTEND] [LOG] 🚀 [Agenda] Initialisation TeamCalendarApp...
[15/07/2025 11:41:48] [FRONTEND] [LOG] 🧹 [clearRenderCache] Nettoyage du cache de rendu
[15/07/2025 11:41:48] [FRONTEND] [LOG] 🚀 [init] Initialisation de TeamCalendarApp...
[15/07/2025 11:41:48] [FRONTEND] [LOG] ✅ [clearRenderCache] Cache de rendu nettoyé
[15/07/2025 11:41:48] [FRONTEND] [LOG] 🔌 [init] Indicateur de connexion initialisé
[15/07/2025 11:41:48] [FRONTEND] [LOG] 📂 [TeamCalendarApp] Chargement de l'état...
[15/07/2025 11:41:48] [FRONTEND] [LOG] 🌐 [TeamCalendarApp] Chargement depuis l'API...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT e.*, et.name as template_name 
      FROM employees e 
      LEFT JOIN employee_templates et ON e.template_id = et.id 
      ORDER BY e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT e.*, et.name as template_name \n      FROM employees e \n      LEFT JOIN employee_templates et ON e.template_id = et.id \n      ORDER BY e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:41:48.040Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 39ms: 
      SELECT e.*, et.name as template_name 
      {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 39ms:",
    "\n      SELECT e.*, et.name as template_name \n     ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:41:48.079Z"
}
[15/07/2025 11:41:48] [BACKEND] [INFO] ✅ Requête exécutée en 39ms: 
      SELECT e.*, et.name as template_name 
      {}
[15/07/2025 11:41:48] [FRONTEND] [LOG] 👥 [TeamCalendarApp] 5 employés chargés
[15/07/2025 11:41:48] [FRONTEND] [LOG] 📦 [loadState] Chargement de tous les shifts depuis la base de données...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT s.*, e.name as employee_name, sp.label as post_label
      FROM shifts s
      LEFT JOIN employees e ON s.employee_id = e.id
      LEFT JOIN standard_posts sp ON s.post_id = sp.id
      ORDER BY s.date_key, e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT s.*, e.name as employee_name, sp.label as post_label\n      FROM shifts s\n      LEFT JOIN employees e ON s.employee_id = e.id\n      LEFT JOIN standard_posts sp ON s.post_id = sp.id\n      ORDER BY s.date_key, e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:41:48.101Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 51ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 51ms:",
    "\n      SELECT s.*, e.name as employee_name, sp.lab",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:41:48.153Z"
}
[15/07/2025 11:41:48] [BACKEND] [INFO] ✅ Requête exécutée en 51ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
[15/07/2025 11:41:48] [FRONTEND] [LOG] 📅 [loadState] 0 shifts chargés depuis la base de données
[15/07/2025 11:41:48] [FRONTEND] [LOG] ✅ [loadState] Shifts organisés dans this.data.schedule
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:41:48.203Z"
}
[15/07/2025 11:41:48] [BACKEND] [INFO] ✅ Requête exécutée en 48ms: SELECT * FROM standard_posts ORDER BY label {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 48ms: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 48ms:",
    "SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:41:48.246Z"
}
[15/07/2025 11:41:48] [FRONTEND] [LOG] 🏢 [TeamCalendarApp] 5 postes standards chargés
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:41:48.274Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 37ms: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 37ms:",
    "SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:41:48.311Z"
}
[15/07/2025 11:41:48] [BACKEND] [INFO] ✅ Requête exécutée en 37ms: SELECT * FROM app_settings ORDER BY setting_key {}
[15/07/2025 11:41:48] [FRONTEND] [LOG] ⚙️ [loadState] Paramètres chargés depuis API: {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT 
        ra.id,
        ra.employee_id,
        ra.post_id,
        ra.days_of_week,
        ra.start_date,
        ra.end_date,
        ra.is_active,
        ra.excluded_dates,
        e.name as employee_name,
        sp.label as post_label,
        sp.hours as post_hours,
        sp.type as post_type,
        array_length(ra.days_of_week, 1) as days_count
      FROM regular_assignments ra
      LEFT JOIN employees e ON ra.employee_id = e.id
      LEFT JOIN standard_posts sp ON ra.post_id = sp.id
      WHERE ra.is_active = true
      ORDER BY e.name, sp.label, ra.start_date
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT \n        ra.id,\n        ra.employee_id,\n        ra.post_id,\n        ra.days_of_week,\n        ra.start_date,\n        ra.end_date,\n        ra.is_active,\n        ra.excluded_dates,\n        e.name as employee_name,\n        sp.label as post_label,\n        sp.hours as post_hours,\n        sp.type as post_type,\n        array_length(ra.days_of_week, 1) as days_count\n      FROM regular_assignments ra\n      LEFT JOIN employees e ON ra.employee_id = e.id\n      LEFT JOIN standard_posts sp ON ra.post_id = sp.id\n      WHERE ra.is_active = true\n      ORDER BY e.name, sp.label, ra.start_date\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:41:48.331Z"
}
[15/07/2025 11:41:48] [BACKEND] [INFO] ✅ Requête exécutée en 60ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 60ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 60ms:",
    "\n      SELECT \n        ra.id,\n        ra.employee_",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:41:48.390Z"
}
[15/07/2025 11:41:48] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"a563e948-18d8-4540-bfbc-7853d6506c05","employee_id":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","post_id":"ef3246ff-8398-4f11-a498-648d207563a0","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-30T04:00:00.000Z","end_date":null,"excluded_dates":[]}
[15/07/2025 11:41:48] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","employee_id":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","post_id":"ef3246ff-8398-4f11-a498-648d207563a0","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-23T04:00:00.000Z","end_date":"2025-07-29T04:00:00.000Z","excluded_dates":[]}
[15/07/2025 11:41:48] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"a563e948-18d8-4540-bfbc-7853d6506c05","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"isLimited":false,"startDate":"2025-07-30","endDate":null,"excludedDates":[]}
[15/07/2025 11:41:48] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-23","endDate":"2025-07-29","excludedDates":[]}
[15/07/2025 11:41:48] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"b77227a7-67b7-4feb-b082-890cfeecab1f","employee_id":"59f5df3a-33ef-425c-bfa2-adca818cf94f","post_id":"ef3246ff-8398-4f11-a498-648d207563a0","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-15T04:00:00.000Z","end_date":"2025-07-22T04:00:00.000Z","excluded_dates":[]}
[15/07/2025 11:41:48] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"b77227a7-67b7-4feb-b082-890cfeecab1f","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-15","endDate":"2025-07-22","excludedDates":[]}
[15/07/2025 11:41:48] [FRONTEND] [LOG] 📋 [loadState] 3/3 assignations régulières valides chargées et normalisées
[15/07/2025 11:41:48] [FRONTEND] [LOG] 🔧 [fixInvalidAssignmentDates] Vérification des dates invalides...
[15/07/2025 11:41:48] [FRONTEND] [LOG] ✅ [fixInvalidAssignmentDates] Aucune date invalide trouvée
[15/07/2025 11:41:48] [FRONTEND] [LOG] 📋 [loadState] Assignations régulières chargées (application différée)
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'
         {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'\n        ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:41:48.528Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📋 [GET /api/employee-order] Récupération ordre des employés
  Data: {
  "originalArgs": [
    "📋 [GET /api/employee-order] Récupération ordre des employés"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:41:48.527Z"
}
[15/07/2025 11:41:48] [FRONTEND] [LOG] 🧹 [cleanupDuplicateRegularShifts] Nettoyage des doublons de shifts réguliers...
[15/07/2025 11:41:48] [FRONTEND] [LOG] ✅ [cleanupInvalidAssignmentIds] Aucun assignment_id invalide détecté
[15/07/2025 11:41:48] [FRONTEND] [LOG] ✅ [cleanupDuplicateRegularShifts] Aucun doublon détecté
[15/07/2025 11:41:48] [FRONTEND] [LOG] ✅ [loadState] État chargé depuis l'API avec succès
[15/07/2025 11:41:48] [FRONTEND] [LOG] 🧹 [cleanupInvalidAssignmentIds] Nettoyage des assignment_id invalides...
[15/07/2025 11:41:48] [FRONTEND] [LOG] ✅ [init] Icône paramètres trouvée: [Objet non sérialisable]
[15/07/2025 11:41:48] [FRONTEND] [LOG] 🔍 [init] Recherche du bouton des paramètres dans le DOM
[15/07/2025 11:41:48] [FRONTEND] [LOG] 🔍 [init] Attachement du listener click sur le bouton des paramètres
[15/07/2025 11:41:48] [FRONTEND] [LOG] 🔍 [init] Bouton des paramètres trouvé ? true
[15/07/2025 11:41:48] [FRONTEND] [LOG] 🔍 [init] Recherche du bouton des paramètres header dans le DOM
[15/07/2025 11:41:48] [FRONTEND] [LOG] 🔍 [init] Bouton des paramètres header trouvé ? true
[15/07/2025 11:41:48] [FRONTEND] [LOG] 🔍 [init] Attachement du listener click sur le bouton des paramètres header
[15/07/2025 11:41:48] [FRONTEND] [LOG] 🎯 [setupViewNavigationListeners] Configuration des boutons de navigation...
[15/07/2025 11:41:48] [FRONTEND] [LOG] ✅ [setupNavigationListeners] Écouteurs de navigation attachés
[15/07/2025 11:41:48] [FRONTEND] [LOG] ✅ Boutons de navigation configurés
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:41:48.663Z"
}
[15/07/2025 11:41:48] [FRONTEND] [LOG] 🔧 [ModalManager] Configuration des gestionnaires de modal
[15/07/2025 11:41:48] [FRONTEND] [LOG] ✅ [ModalManager] Gestionnaires de modal configurés
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:41:48.687Z"
}
[15/07/2025 11:41:48] [BACKEND] [INFO] 📋 [GET /api/employee-order] Récupération ordre des employés
[15/07/2025 11:41:48] [FRONTEND] [LOG] 🔧 [setupGeneralSettings] Configuration des paramètres généraux
[15/07/2025 11:41:48] [FRONTEND] [LOG] 🔧 [setupGeneralSettings] Création du conteneur de fuseau horaire
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 176ms: 
            SELECT setting_value FROM app_setting {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 176ms:",
    "\n            SELECT setting_value FROM app_setting",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:41:48.706Z"
}
[15/07/2025 11:41:48] [FRONTEND] [LOG] 📋 [loadEmployeeOrder] Chargement ordre des employés
[15/07/2025 11:41:48] [FRONTEND] [LOG] ✅ [setupGlobalEscapeHandler] Gestionnaire ESC global configuré
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:41:48.834Z"
}
[15/07/2025 11:41:49] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📋 [GET /api/employee-order] Aucun ordre personnalisé trouvé
  Data: {
  "originalArgs": [
    "📋 [GET /api/employee-order] Aucun ordre personnalisé trouvé"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:41:48.707Z"
}
[15/07/2025 11:41:49] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[15/07/2025 11:41:49] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[15/07/2025 11:41:49] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[15/07/2025 11:41:49] [FRONTEND] [LOG] 🔧 [init] Forçage du rendu initial...
[15/07/2025 11:41:49] [FRONTEND] [WARN] ⚠️ [loadEmployeeOrder] Réponse API sans tableau employeeOrder
[15/07/2025 11:41:49] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[15/07/2025 11:41:49] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 11:41:49] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 11:41:49] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[15/07/2025 11:41:49] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 60 zones trouvées
[15/07/2025 11:41:49] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 60 zones de drop disponibles
[15/07/2025 11:41:49] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 11:41:49] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 11:41:49] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Matin: [1, 2, 3, 4, 5]
[15/07/2025 11:41:49] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 11:41:49] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Nuit: [1, 2, 3, 4, 5]
[15/07/2025 11:41:49] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 11:41:49] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 11:41:49] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 11:41:49] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Soir: [1, 2, 3, 4, 5]
[15/07/2025 11:41:49] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 11:41:49] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE1: [0, 6]
[15/07/2025 11:41:49] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 11:41:49] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 11:41:49] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE2: [0, 6]
[15/07/2025 11:41:49] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 11:41:49] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 11:41:49] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 11:41:49] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 11:41:49] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 11:41:49] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 11:41:49] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 11:41:49] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 11:41:49] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 11:41:49] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 11:41:49] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 11:41:49] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 11:41:49] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 11:41:49] [FRONTEND] [LOG] ℹ️ [setupEmployeeDragDrop] Fonction désactivée - Utiliser setupDragAndDrop
[15/07/2025 11:41:49] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-14 ignoré pour attribution b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 11:41:49] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-17T04:00:00.000Z","dayDateKey":"2025-07-17","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:41:49] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-18
[15/07/2025 11:41:49] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneur available-posts-container créé
[15/07/2025 11:41:49] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[15/07/2025 11:41:49] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 11:41:49] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 11:41:49] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 11:41:49] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 11:41:49] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 11:41:49] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 11:41:49] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 11:41:49] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 11:41:49] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 11:41:49] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 11:41:50] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 11:41:50] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 11:41:50] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 11:41:50] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[15/07/2025 11:41:50] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (60 zones)
[15/07/2025 11:41:50] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🔧 [init] Configuration du drag & drop des postes...
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 60 zones de drop disponibles
[15/07/2025 11:41:50] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 60 zones trouvées
[15/07/2025 11:41:50] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 11:41:50] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 11:41:50] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 11:41:50] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 11:41:50] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 11:41:50] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 11:41:50] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 11:41:50] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 11:41:50] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 11:41:50] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 11:41:50] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 11:41:50] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 11:41:50] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 11:41:50] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 11:41:50] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (60 zones)
[15/07/2025 11:41:50] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 11:41:50] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 11:41:50] [FRONTEND] [LOG] 📋 [init] Application des attributions régulières après initialisation
[15/07/2025 11:41:50] [FRONTEND] [LOG] ✅ [addEmergencyFixButton] Bouton de correction ajouté
[15/07/2025 11:41:50] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Application pour semaine 2025-W28
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🔧 [addEmergencyFixButton] Ajout du bouton de correction...
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] 0 shifts réguliers supprimés
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] Nettoyage des shifts réguliers existants...
[15/07/2025 11:41:50] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution b77227a7-67b7-4feb-b082-890cfeecab1f a une intersection avec cette semaine (2025-07-15 → 2025-07-22) vs (2025-07-13 → 2025-07-19)
[15/07/2025 11:41:50] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution a563e948-18d8-4540-bfbc-7853d6506c05 commence après cette semaine (startDate: 2025-07-30, semaine se termine: 2025-07-19)
[15/07/2025 11:41:50] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888 commence après cette semaine (startDate: 2025-07-23, semaine se termine: 2025-07-19)
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution b77227a7-67b7-4feb-b082-890cfeecab1f: [{"dateKey":"2025-07-13","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-14","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-15","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-16","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-17","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-07-18","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-07-19","isWeekend":true,"dayOfWeek":6}]
[15/07/2025 11:41:50] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"b77227a7-67b7-4feb-b082-890cfeecab1f","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"startDate":"2025-07-15","endDate":"2025-07-22","excludedDates":[]}
[15/07/2025 11:41:50] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 1 attributions actives sur 3 total
[15/07/2025 11:41:50] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-14T04:00:00.000Z","dayDateKey":"2025-07-14","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-13T04:00:00.000Z","dayDateKey":"2025-07-13","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:41:50] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-13 (0)
[15/07/2025 11:41:50] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-14 (1)
[15/07/2025 11:41:50] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-13 ignoré pour attribution b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-14: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","dayDateStr":"2025-07-14","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 11:41:50] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-14 avant startDate 2025-07-15 (original: 2025-07-15)
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-15T04:00:00.000Z","dayDateKey":"2025-07-15","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:41:50] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-15 (2)
[15/07/2025 11:41:50] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-15 validé, shifts existants: 0
[15/07/2025 11:41:50] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-15
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-15: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","dayDateStr":"2025-07-15","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-16T04:00:00.000Z","dayDateKey":"2025-07-16","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:41:50] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-16 (3)
[15/07/2025 11:41:50] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-15 pour Sophie Leblanc
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-17: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","dayDateStr":"2025-07-17","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 11:41:50] [FRONTEND] [WARN] ⚠️ [renderUnifiedCalendar] Conteneur available-posts-container non trouvé, création...
[15/07/2025 11:41:50] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 68 zones trouvées
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 11:41:50] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 11:41:50] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 11:41:50] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-16
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-16: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","dayDateStr":"2025-07-16","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 11:41:50] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-16 pour Sophie Leblanc
[15/07/2025 11:41:50] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-17 (4)
[15/07/2025 11:41:50] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-16 validé, shifts existants: 0
[15/07/2025 11:41:50] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-17
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 11:41:50] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-17 pour Sophie Leblanc
[15/07/2025 11:41:50] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-07-18 (5)
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-18: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","dayDateStr":"2025-07-18","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 11:41:50] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-17 validé, shifts existants: 0
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-18T04:00:00.000Z","dayDateKey":"2025-07-18","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:41:50] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 11:41:50] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-18 validé, shifts existants: 0
[15/07/2025 11:41:50] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-18 pour Sophie Leblanc
[15/07/2025 11:41:50] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-07-19 (6)
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-19T04:00:00.000Z","dayDateKey":"2025-07-19","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:41:51] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 11:41:51] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-19 ignoré pour attribution b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 11:41:51] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 4 attributions appliquées pour la semaine 2025-W28
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Rendu demandé car 4 shifts créés
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🎉 [Agenda] Application fullscreen initialisée avec succès
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] 0 grips ajoutés
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [init] Initialisation terminée avec succès
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 68 zones de drop disponibles
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 11:41:51] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 3: {"employee":"Sophie Leblanc","date":"2025-07-17","assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","text":"08:00-16:00"}
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (68 zones)
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [SETUP] Drag & drop des employés configuré (mode simplifié)
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 68 zones de drop disponibles
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 68 zones trouvées
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 11:41:51] [FRONTEND] [LOG] 📊 [diagnoseRegularAssignmentGrips] Statistiques: {"totalShifts":4,"regularShifts":4,"regularAssignments":3}
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 11:41:51] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 1: {"employee":"Sophie Leblanc","date":"2025-07-15","assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","text":"08:00-16:00"}
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] 0 grips ajoutés
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (68 zones)
[15/07/2025 11:41:51] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic des problèmes de modale...
[15/07/2025 11:41:51] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[15/07/2025 11:41:51] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Éléments DOM: {"modal":true,"closeBtn":true,"cancelBtn":true,"confirmBtn":true,"radioButtons":4}
[15/07/2025 11:41:52] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] TeamCalendarApp: {"exists":true,"hasModalManager":true,"hasOpenFunction":true,"hasHandleFunction":true}
[15/07/2025 11:41:52] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] État de la modale: {"hidden":true,"classes":["hidden","fixed","inset-0","bg-black/40","backdrop-blur-sm","z-50","flex","items-center","justify-center","p-4"],"style":""}
[15/07/2025 11:41:52] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Données contextuelles: {"employees":5,"posts":5}
[15/07/2025 11:41:52] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 11:41:52] [FRONTEND] [LOG] 🔍 [diagnoseRegularAssignmentGrips] Diagnostic des grips d'attributions régulières
[15/07/2025 11:41:52] [FRONTEND] [LOG] 🎯 [diagnoseRegularAssignmentGrips] Grips trouvés dans le DOM: 4
[15/07/2025 11:41:52] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 11:41:52] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 2: {"employee":"Sophie Leblanc","date":"2025-07-16","assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","text":"08:00-16:00"}
[15/07/2025 11:41:52] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 11:41:52] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 4: {"employee":"Sophie Leblanc","date":"2025-07-18","assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","text":"08:00-16:00"}
[15/07/2025 11:41:52] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 11:41:52] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation automatique...
[15/07/2025 11:41:52] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation complète...
[15/07/2025 11:41:52] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation de la correction principale...
[15/07/2025 11:41:52] [FRONTEND] [LOG] ✅ [VALIDATION] ModalManager.setupAssignmentContextModal disponible
[15/07/2025 11:41:52] [FRONTEND] [LOG] 🔍 [detectDropDateFromPosition] Analyse position pour employé test-employee
[15/07/2025 11:41:52] [FRONTEND] [LOG] 🧪 [VALIDATION] Pour tester pratiquement:
[15/07/2025 11:41:52] [FRONTEND] [LOG] ✅ [VALIDATION] handleAssignmentContextConfirm disponible
[15/07/2025 11:41:52] [FRONTEND] [LOG] 🧪 [DATE-TEST] Simulation du processus de fork...
[15/07/2025 11:41:52] [FRONTEND] [LOG] ✅ [DATE-TEST] Test 3 (Date ISO avec timezone): 2025-07-24
[15/07/2025 11:41:52] [FRONTEND] [LOG] - Vers: Jean Dupont
[15/07/2025 11:41:52] [FRONTEND] [LOG] ✅ Détection de date avec timezone: PASSÉ
[15/07/2025 11:41:52] [FRONTEND] [LOG] ✅ [DATE-TEST] Date détectée: 2025-07-15
[15/07/2025 11:41:52] [FRONTEND] [LOG] ✅ [VALIDATION] Tous les éléments DOM sont présents
[15/07/2025 11:41:52] [FRONTEND] [LOG] 📋 [VALIDATION] Éléments DOM: {"modal":true,"closeBtn":true,"cancelBtn":true,"confirmBtn":true,"radioButtons":true}
[15/07/2025 11:41:52] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des éléments DOM...
[15/07/2025 11:41:52] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des listeners...
[15/07/2025 11:41:52] [FRONTEND] [LOG] 🔧 [setupAssignmentContextModal] Configuration du modal contextuel...
[15/07/2025 11:41:52] [FRONTEND] [LOG] ✅ [setupAssignmentContextModal] Modal contextuel configuré avec listeners nettoyés
[15/07/2025 11:41:52] [FRONTEND] [LOG] 📊 [VALIDATION] Résultats de validation: {"mainFix":true,"domElements":true,"listeners":true}
[15/07/2025 11:41:52] [FRONTEND] [LOG] ✅ [VALIDATION] setupAssignmentContextModal exécuté sans erreur
[15/07/2025 11:41:52] [FRONTEND] [LOG] ✅ [VALIDATION] La correction de la modale devrait maintenant fonctionner
[15/07/2025 11:41:52] [FRONTEND] [LOG] ✅ [VALIDATION] ModalManager.app initialisé
[15/07/2025 11:41:52] [FRONTEND] [LOG] 🎉 [VALIDATION] Toutes les validations sont passées avec succès !
[15/07/2025 11:41:52] [FRONTEND] [LOG] 1. Effectuez un drag & drop d'un poste vers un employé
[15/07/2025 11:41:52] [FRONTEND] [LOG] 2. Vérifiez que la modale s'ouvre
[15/07/2025 11:41:52] [FRONTEND] [LOG] 4. Ou utilisez testButtonClicks() pour un test automatique
[15/07/2025 11:41:52] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic complet du fork modal...
[15/07/2025 11:41:52] [FRONTEND] [LOG] 3. Cliquez sur "Confirmer" et vérifiez les logs
[15/07/2025 11:41:52] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[15/07/2025 11:41:52] [FRONTEND] [LOG] ✅ [DIAGNOSTIC] Toutes les fonctions nécessaires sont disponibles
[15/07/2025 11:41:52] [FRONTEND] [LOG] 📊 [DIAGNOSTIC] Résultats: {"teamCalendarApp":true,"detectDropDateFunction":true,"showConfirmationMenuFunction":true,"handleRegularAssignmentDropFunction":true,"regularAssignments":3,"employees":5}
[15/07/2025 11:41:52] [FRONTEND] [LOG] 🚀 [DATE-TEST] Lancement de la suite de validation des dates...
[15/07/2025 11:41:52] [FRONTEND] [LOG] 🚀 [DATE-TEST] Lancement de la validation automatique des dates...
[15/07/2025 11:41:52] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la conversion de date sécurisée...
[15/07/2025 11:41:52] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Conversion de date sécurisée
[15/07/2025 11:41:52] [FRONTEND] [ERROR] ❌ [DATE-TEST] Test 2 (String date): attendu 2025-07-24, obtenu 2025-07-23
[15/07/2025 11:41:52] [FRONTEND] [LOG] ✅ [DATE-TEST] Test 1 (Date locale): 2025-07-24
[15/07/2025 11:41:52] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Logique de fork avec dates
[15/07/2025 11:41:52] [FRONTEND] [LOG] ✅ [DATE-TEST] Date raisonnable (0 jours de différence)
[15/07/2025 11:41:52] [FRONTEND] [LOG] 
⚠️ [DATE-TEST] Certaines validations ont échoué
[15/07/2025 11:41:52] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la logique de fork avec dates...
[15/07/2025 11:41:52] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test avec date: 2025-07-25
[15/07/2025 11:41:52] [FRONTEND] [LOG] 🧪 [DATE-TEST] Simulation du changement permanent...
[15/07/2025 11:41:52] [FRONTEND] [LOG] - De: Marie Martin
[15/07/2025 11:41:52] [FRONTEND] [LOG] - Assignment: a563e948-18d8-4540-bfbc-7853d6506c05
[15/07/2025 11:41:52] [FRONTEND] [LOG] ✅ [showRegularAssignmentConfirmationMenu] Date automatiquement détectée: 2025-07-25
[15/07/2025 11:41:52] [FRONTEND] [LOG] - Date: 2025-07-25
[15/07/2025 11:41:52] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Améliorations timezone
[15/07/2025 11:41:52] [FRONTEND] [LOG] 🎯 [showRegularAssignmentConfirmationMenu] Affichage menu pour: {"assignment":"a563e948-18d8-4540-bfbc-7853d6506c05","from":"Marie Martin","to":"Jean Dupont","post":"Poste Matin","referenceDate":"2025-07-25","minDate":"2025-07-25","todayKey":"2025-07-15"}
[15/07/2025 11:41:52] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test des améliorations de timezone...
[15/07/2025 11:41:52] [FRONTEND] [WARN] ⚠️ [DATE-TEST] Sélecteur de timezone non trouvé (normal si modal fermé)
[15/07/2025 11:41:52] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Détection de date avec timezone
[15/07/2025 11:41:52] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la détection de date avec timezone...
[15/07/2025 11:41:52] [FRONTEND] [LOG] 📍 [detectDropDateFromPosition] Position souris: (500, 300)
[15/07/2025 11:41:52] [FRONTEND] [LOG] ✅ [detectDropDateFromPosition] Date calculée via position: 2025-07-15 (jour 2)
[15/07/2025 11:41:52] [FRONTEND] [LOG] ✅ [DATE-TEST] Format de date valide
[15/07/2025 11:41:52] [FRONTEND] [LOG] ❌ Conversion de date sécurisée: ÉCHOUÉ
[15/07/2025 11:41:52] [FRONTEND] [LOG] 
📊 [DATE-TEST] Résultats de la validation:
[15/07/2025 11:41:52] [FRONTEND] [LOG] ✅ Logique de fork avec dates: PASSÉ
[15/07/2025 11:41:52] [FRONTEND] [LOG] ✅ Améliorations timezone: PASSÉ
[15/07/2025 11:41:52] [FRONTEND] [LOG] 🔧 [DATE-TEST] Vérifiez les logs ci-dessus pour les détails
[15/07/2025 11:41:52] [FRONTEND] [LOG] ✅ [DATE-TEST] Date configurée dans le modal: 2025-07-25
[15/07/2025 11:41:52] [FRONTEND] [LOG] ✅ [DATE-TEST] Date de test correcte: 2025-07-25
[15/07/2025 11:41:56] [FRONTEND] [LOG] ➡️ Clic sur suivant
[15/07/2025 11:41:56] [FRONTEND] [LOG] 🧭 [navigateWeek] Direction: 1, Offset actuel: 0
[15/07/2025 11:41:56] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[15/07/2025 11:41:56] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 11:41:56] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (70 zones)
[15/07/2025 11:41:56] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 11:41:56] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 11:41:56] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 11:41:56] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 11:41:56] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 70 zones de drop disponibles
[15/07/2025 11:41:56] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] 0 grips ajoutés
[15/07/2025 11:41:56] [FRONTEND] [LOG] 📋 [navigateWeek] Application des attributions pour la nouvelle semaine 2025-W29
[15/07/2025 11:41:56] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] Nettoyage des shifts réguliers existants...
[15/07/2025 11:41:56] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-22 avant startDate 2025-07-23 (original: 2025-07-23)
[15/07/2025 11:41:56] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-20 ignoré pour attribution b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 11:41:56] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-22T04:00:00.000Z","dayDateKey":"2025-07-22","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:41:56] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 11:41:56] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-22 (2)
[15/07/2025 11:41:56] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] 0 shifts réguliers supprimés
[15/07/2025 11:41:56] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-20T04:00:00.000Z","dayDateKey":"2025-07-20","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:41:56] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[15/07/2025 11:41:56] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 11:41:56] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 11:41:56] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 11:41:56] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-21T04:00:00.000Z","dayDateKey":"2025-07-21","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:41:56] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 11:41:56] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 11:41:56] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 11:41:56] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 11:41:56] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 11:41:56] [FRONTEND] [LOG] 🧭 [navigateWeek] Direction: 1, Offset actuel: 1
[15/07/2025 11:41:56] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-22: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","dayDateStr":"2025-07-22","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 11:41:56] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[15/07/2025 11:41:56] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 11:41:56] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[15/07/2025 11:41:56] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[15/07/2025 11:41:56] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-21 avant startDate 2025-07-23 (original: 2025-07-23)
[15/07/2025 11:41:56] [FRONTEND] [LOG] ➡️ Clic sur suivant
[15/07/2025 11:41:56] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 70 zones trouvées
[15/07/2025 11:41:56] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 11:41:56] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-21: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","dayDateStr":"2025-07-21","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 11:41:56] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Cache invalidé pour forcer la régénération
[15/07/2025 11:41:56] [FRONTEND] [LOG] 📋 [navigateWeek] Application des attributions pour la nouvelle semaine 2025-W30
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 11:41:57] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] Nettoyage des shifts réguliers existants...
[15/07/2025 11:41:57] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 11:41:57] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Cache invalidé pour forcer la régénération
[15/07/2025 11:41:57] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution a563e948-18d8-4540-bfbc-7853d6506c05 a une intersection avec cette semaine (2025-07-30 → ∞) vs (2025-07-27 → 2025-08-02)
[15/07/2025 11:41:57] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution b77227a7-67b7-4feb-b082-890cfeecab1f terminée avant cette semaine (endDate: 2025-07-22, semaine commence: 2025-07-27)
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 11:41:57] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 2 attributions actives sur 3 total
[15/07/2025 11:41:57] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution a563e948-18d8-4540-bfbc-7853d6506c05: [{"dateKey":"2025-07-27","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-28","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-29","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-30","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-31","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-08-01","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-08-02","isWeekend":true,"dayOfWeek":6}]
[15/07/2025 11:41:57] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"a563e948-18d8-4540-bfbc-7853d6506c05","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-27T04:00:00.000Z","dayDateKey":"2025-07-27","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 11:41:57] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 11:41:57] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-28: {"assignmentId":"a563e948-18d8-4540-bfbc-7853d6506c05","dayDateStr":"2025-07-28","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-29: {"assignmentId":"a563e948-18d8-4540-bfbc-7853d6506c05","dayDateStr":"2025-07-29","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 11:41:57] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-30 (3)
[15/07/2025 11:41:57] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-30
[15/07/2025 11:41:57] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-31: {"assignmentId":"a563e948-18d8-4540-bfbc-7853d6506c05","dayDateStr":"2025-07-31","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 11:41:57] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-08-01 (5)
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"a563e948-18d8-4540-bfbc-7853d6506c05","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-08-02T04:00:00.000Z","dayDateKey":"2025-08-02","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:41:57] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 11:41:57] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-29 pour Pierre Durand
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-08-01: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","dayDateStr":"2025-08-01","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 11:41:57] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 5 attributions appliquées pour la semaine 2025-W30
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-29: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","dayDateStr":"2025-07-29","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Rendu demandé car 5 shifts créés
[15/07/2025 11:41:57] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-08-02 ignoré pour attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 11:41:57] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[15/07/2025 11:41:57] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 11:41:57] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"a563e948-18d8-4540-bfbc-7853d6506c05","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-31T04:00:00.000Z","dayDateKey":"2025-07-31","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:41:57] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 11:41:57] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-31 (4)
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-08-02T04:00:00.000Z","dayDateKey":"2025-08-02","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:41:57] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-30 pour Marie Martin
[15/07/2025 11:41:57] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-08-02 (6)
[15/07/2025 11:41:57] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-28 avant startDate 2025-07-30 (original: 2025-07-30)
[15/07/2025 11:41:57] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 11:41:57] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 11:41:57] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 11:41:57] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 11:41:57] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 11:41:57] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 11:41:57] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 11:41:57] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[15/07/2025 11:41:57] [FRONTEND] [WARN] ⚠️ [SETUP] Erreur lors de la destruction: Error: can't access property "Sortable1752594106961", el is null
destroy@http://localhost:5173/node_modules/.vite/deps/sortablejs.js?v=189de042:1812:5
setupDragAndDrop@http://localhost:5173/src/teamCalendarApp.ts?t=1752586718689:5808:33
attachAllEventListeners@http://localhost:5173/src/teamCalendarApp.ts?t=1752586718689:5358:10
actualRender@http://localhost:5173/src/teamCalendarApp.ts?t=1752586718689:2466:10
render/this._debouncedRender<@http://localhost:5173/src/teamCalendarApp.ts?t=1752586718689:2451:12

[15/07/2025 11:41:57] [FRONTEND] [LOG] ✅ [SETUP] Drag & drop des employés configuré (mode simplifié)
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 11:41:57] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 70 zones trouvées
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 70 zones de drop disponibles
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 11:41:57] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 11:41:57] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 11:41:57] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 11:41:57] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 11:41:57] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 11:41:57] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 11:41:57] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 11:41:57] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 11:41:57] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 11:41:57] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 11:41:58] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 11:41:58] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 11:41:58] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 11:41:58] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 11:41:58] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 11:41:58] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 11:41:58] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 11:41:58] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 11:41:58] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 11:41:58] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 11:41:58] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 11:41:58] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (70 zones)
[15/07/2025 11:41:58] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 11:41:58] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-23: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","dayDateStr":"2025-07-23","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 11:41:58] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-23 validé, shifts existants: 0
[15/07/2025 11:41:58] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"a563e948-18d8-4540-bfbc-7853d6506c05","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-28T04:00:00.000Z","dayDateKey":"2025-07-28","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:41:58] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 11:41:58] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-28 (1)
[15/07/2025 11:41:58] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-24: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","dayDateStr":"2025-07-24","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 11:41:58] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-27 ignoré pour attribution a563e948-18d8-4540-bfbc-7853d6506c05
[15/07/2025 11:41:58] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-24 (4)
[15/07/2025 11:41:58] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 11:41:58] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-24T04:00:00.000Z","dayDateKey":"2025-07-24","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:41:58] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"a563e948-18d8-4540-bfbc-7853d6506c05","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"startDate":"2025-07-30","endDate":null,"excludedDates":[]}
[15/07/2025 11:41:58] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-23 pour Pierre Durand
[15/07/2025 11:41:58] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-23T04:00:00.000Z","dayDateKey":"2025-07-23","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:41:58] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-24
[15/07/2025 11:41:58] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-25 pour Pierre Durand
[15/07/2025 11:41:58] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 11:41:58] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-23
[15/07/2025 11:41:58] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 11:41:58] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-08-01 ignoré pour attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 11:41:58] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-21 (1)
[15/07/2025 11:41:58] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-08-01 après endDate 2025-07-29 (original: 2025-07-29) (attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888)
[15/07/2025 11:41:58] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-20T04:00:00.000Z","dayDateKey":"2025-07-20","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:41:58] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-08-01T04:00:00.000Z","dayDateKey":"2025-08-01","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:41:58] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-21T04:00:00.000Z","dayDateKey":"2025-07-21","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:41:58] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-08-01 (5)
[15/07/2025 11:41:58] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution b77227a7-67b7-4feb-b082-890cfeecab1f: [{"dateKey":"2025-07-20","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-21","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-22","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-23","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-24","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-07-25","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-07-26","isWeekend":true,"dayOfWeek":6}]
[15/07/2025 11:41:58] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-31 ignoré pour attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 11:41:58] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-26 ignoré pour attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 11:41:58] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-31 après endDate 2025-07-29 (original: 2025-07-29) (attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888)
[15/07/2025 11:41:58] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-20 (0)
[15/07/2025 11:41:58] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-31: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","dayDateStr":"2025-07-31","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 11:41:58] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-26T04:00:00.000Z","dayDateKey":"2025-07-26","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:41:58] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 11:41:58] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-31T04:00:00.000Z","dayDateKey":"2025-07-31","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:41:58] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"b77227a7-67b7-4feb-b082-890cfeecab1f","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"startDate":"2025-07-15","endDate":"2025-07-22","excludedDates":[]}
[15/07/2025 11:41:58] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-22 (2)
[15/07/2025 11:41:58] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-21: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","dayDateStr":"2025-07-21","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 11:41:58] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-24 ignoré pour attribution b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 11:41:58] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-31 (4)
[15/07/2025 11:41:58] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-07-26 (6)
[15/07/2025 11:41:58] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Application pour semaine 2025-W29
[15/07/2025 11:41:58] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution a563e948-18d8-4540-bfbc-7853d6506c05 commence après cette semaine (startDate: 2025-07-30, semaine se termine: 2025-07-26)
[15/07/2025 11:41:58] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888 a une intersection avec cette semaine (2025-07-23 → 2025-07-29) vs (2025-07-20 → 2025-07-26)
[15/07/2025 11:41:58] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution b77227a7-67b7-4feb-b082-890cfeecab1f a une intersection avec cette semaine (2025-07-15 → 2025-07-22) vs (2025-07-20 → 2025-07-26)
[15/07/2025 11:41:58] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"startDate":"2025-07-23","endDate":"2025-07-29","excludedDates":[]}
[15/07/2025 11:41:58] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 2 attributions actives sur 3 total
[15/07/2025 11:41:59] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888: [{"dateKey":"2025-07-20","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-21","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-22","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-23","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-24","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-07-25","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-07-26","isWeekend":true,"dayOfWeek":6}]
[15/07/2025 11:41:59] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-21 (1)
[15/07/2025 11:41:59] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-20 ignoré pour attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 11:41:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 11:41:59] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-23 (3)
[15/07/2025 11:41:59] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 11:41:59] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-20 (0)
[15/07/2025 11:41:59] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-22 ignoré pour attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 11:41:59] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-21 ignoré pour attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 11:41:59] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] 0 shifts réguliers supprimés
[15/07/2025 11:41:59] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Application pour semaine 2025-W30
[15/07/2025 11:41:59] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888 a une intersection avec cette semaine (2025-07-23 → 2025-07-29) vs (2025-07-27 → 2025-08-02)
[15/07/2025 11:41:59] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-27 (0)
[15/07/2025 11:41:59] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-28 ignoré pour attribution a563e948-18d8-4540-bfbc-7853d6506c05
[15/07/2025 11:41:59] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-29 (2)
[15/07/2025 11:41:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"a563e948-18d8-4540-bfbc-7853d6506c05","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-29T04:00:00.000Z","dayDateKey":"2025-07-29","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:41:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 11:41:59] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-29 avant startDate 2025-07-30 (original: 2025-07-30)
[15/07/2025 11:41:59] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-29 ignoré pour attribution a563e948-18d8-4540-bfbc-7853d6506c05
[15/07/2025 11:41:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"a563e948-18d8-4540-bfbc-7853d6506c05","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-30T04:00:00.000Z","dayDateKey":"2025-07-30","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:41:59] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 11:41:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-30: {"assignmentId":"a563e948-18d8-4540-bfbc-7853d6506c05","dayDateStr":"2025-07-30","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 11:41:59] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 11:41:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 11:41:59] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-30 validé, shifts existants: 0
[15/07/2025 11:41:59] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-31
[15/07/2025 11:41:59] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-31 validé, shifts existants: 0
[15/07/2025 11:41:59] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-31 pour Marie Martin
[15/07/2025 11:41:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"a563e948-18d8-4540-bfbc-7853d6506c05","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-08-01T04:00:00.000Z","dayDateKey":"2025-08-01","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:41:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 11:41:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-08-01: {"assignmentId":"a563e948-18d8-4540-bfbc-7853d6506c05","dayDateStr":"2025-08-01","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 11:41:59] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-08-01
[15/07/2025 11:41:59] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-08-01 pour Marie Martin
[15/07/2025 11:41:59] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-08-01 validé, shifts existants: 0
[15/07/2025 11:41:59] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-08-02 (6)
[15/07/2025 11:41:59] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 11:41:59] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-08-02 ignoré pour attribution a563e948-18d8-4540-bfbc-7853d6506c05
[15/07/2025 11:41:59] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"startDate":"2025-07-23","endDate":"2025-07-29","excludedDates":[]}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:41:59.303Z"
}
[15/07/2025 11:41:59] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888: [{"dateKey":"2025-07-27","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-28","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-29","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-30","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-31","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-08-01","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-08-02","isWeekend":true,"dayOfWeek":6}]
[15/07/2025 11:41:59] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-26 ignoré pour attribution b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 11:41:59] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[15/07/2025 11:41:59] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-27 (0)
[15/07/2025 11:41:59] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 11:41:59] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-27 ignoré pour attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 11:41:59] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-28 (1)
[15/07/2025 11:41:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-28T04:00:00.000Z","dayDateKey":"2025-07-28","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:41:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 11:41:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-28: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","dayDateStr":"2025-07-28","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 11:41:59] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-28
[15/07/2025 11:41:59] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-28 validé, shifts existants: 0
[15/07/2025 11:41:59] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-28 pour Pierre Durand
[15/07/2025 11:41:59] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-29 (2)
[15/07/2025 11:41:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-29T04:00:00.000Z","dayDateKey":"2025-07-29","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:41:59] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-29
[15/07/2025 11:41:59] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[15/07/2025 11:41:59] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-29 validé, shifts existants: 0
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:41:59.496Z"
}
[15/07/2025 11:41:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-27T04:00:00.000Z","dayDateKey":"2025-07-27","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:41:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-30T04:00:00.000Z","dayDateKey":"2025-07-30","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:41:59] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-30 (3)
[15/07/2025 11:41:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 11:41:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-30: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","dayDateStr":"2025-07-30","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 11:41:59] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-30 après endDate 2025-07-29 (original: 2025-07-29) (attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888)
[15/07/2025 11:41:59] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-30 ignoré pour attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 11:41:59] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Rendu demandé car 5 shifts créés
[15/07/2025 11:41:59] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-24 validé, shifts existants: 0
[15/07/2025 11:41:59] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 5 attributions appliquées pour la semaine 2025-W29
[15/07/2025 11:41:59] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-24 pour Pierre Durand
[15/07/2025 11:41:59] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[15/07/2025 11:41:59] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[15/07/2025 11:41:59] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 11:41:59] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière a563e948-18d8-4540-bfbc-7853d6506c05
[15/07/2025 11:41:59] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 11:41:59] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière a563e948-18d8-4540-bfbc-7853d6506c05
[15/07/2025 11:41:59] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 11:41:59] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière a563e948-18d8-4540-bfbc-7853d6506c05
[15/07/2025 11:41:59] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 11:41:59] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 11:41:59] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 11:41:59] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 11:41:59] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[15/07/2025 11:41:59] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[15/07/2025 11:41:59] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 11:41:59] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 11:41:59] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 70 zones de drop disponibles
[15/07/2025 11:41:59] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 70 zones trouvées
[15/07/2025 11:41:59] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 11:42:00] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 11:42:00] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-07-25 (5)
[15/07/2025 11:42:00] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 11:42:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-25: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","dayDateStr":"2025-07-25","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 11:42:00] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-25 après endDate 2025-07-22 (original: 2025-07-22) (attribution b77227a7-67b7-4feb-b082-890cfeecab1f)
[15/07/2025 11:42:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-25T04:00:00.000Z","dayDateKey":"2025-07-25","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:42:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-26T04:00:00.000Z","dayDateKey":"2025-07-26","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:42:00] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-25 validé, shifts existants: 0
[15/07/2025 11:42:00] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-25 ignoré pour attribution b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 11:42:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 11:42:00] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 11:42:00] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 11:42:00] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 11:42:00] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 11:42:00] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 11:42:00] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 11:42:00] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 11:42:00] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 11:42:00] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 11:42:00] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 11:42:00] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (70 zones)
[15/07/2025 11:42:00] [FRONTEND] [WARN] ⚠️ [SETUP] Erreur lors de la destruction: Error: can't access property "Sortable1752594106961", el is null
destroy@http://localhost:5173/node_modules/.vite/deps/sortablejs.js?v=189de042:1812:5
setupDragAndDrop@http://localhost:5173/src/teamCalendarApp.ts?t=1752586718689:5808:33
attachAllEventListeners@http://localhost:5173/src/teamCalendarApp.ts?t=1752586718689:5358:10
actualRender@http://localhost:5173/src/teamCalendarApp.ts?t=1752586718689:2466:10
render/this._debouncedRender<@http://localhost:5173/src/teamCalendarApp.ts?t=1752586718689:2451:12

[15/07/2025 11:42:00] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[15/07/2025 11:42:00] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 70 zones trouvées
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [SETUP] Drag & drop des employés configuré (mode simplifié)
[15/07/2025 11:42:00] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 11:42:00] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 11:42:00] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 70 zones de drop disponibles
[15/07/2025 11:42:00] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 11:42:00] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 11:42:00] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 11:42:00] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 11:42:00] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 11:42:00] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 11:42:00] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 11:42:00] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 11:42:00] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 11:42:00] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 11:42:00] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 11:42:00] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 11:42:00] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 11:42:00] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 11:42:00] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (70 zones)
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] 0 grips ajoutés
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 11:42:00] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 11:42:00] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-07-26 (6)
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-25
[15/07/2025 11:42:00] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-21
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-21 pour Sophie Leblanc
[15/07/2025 11:42:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-22T04:00:00.000Z","dayDateKey":"2025-07-22","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:42:00] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-21 validé, shifts existants: 0
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-22
[15/07/2025 11:42:00] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-22 pour Sophie Leblanc
[15/07/2025 11:42:01] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-23 (3)
[15/07/2025 11:42:01] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 11:42:01] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-22: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","dayDateStr":"2025-07-22","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 11:42:01] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 11:42:01] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-22 validé, shifts existants: 0
[15/07/2025 11:42:01] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-23T04:00:00.000Z","dayDateKey":"2025-07-23","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:42:01] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-23 après endDate 2025-07-22 (original: 2025-07-22) (attribution b77227a7-67b7-4feb-b082-890cfeecab1f)
[15/07/2025 11:42:01] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-24 (4)
[15/07/2025 11:42:01] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-23: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","dayDateStr":"2025-07-23","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 11:42:01] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-24T04:00:00.000Z","dayDateKey":"2025-07-24","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:42:01] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-23 ignoré pour attribution b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 11:42:01] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 11:42:01] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-24: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","dayDateStr":"2025-07-24","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 11:42:01] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-24 après endDate 2025-07-22 (original: 2025-07-22) (attribution b77227a7-67b7-4feb-b082-890cfeecab1f)
[15/07/2025 11:42:01] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-25T04:00:00.000Z","dayDateKey":"2025-07-25","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:42:01] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-25: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","dayDateStr":"2025-07-25","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 11:42:01] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-07-25 (5)
[15/07/2025 11:42:01] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 11:42:16] [FRONTEND] [LOG] 🧭 [navigateWeek] Direction: 1, Offset actuel: 2
[15/07/2025 11:42:16] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] Nettoyage des shifts réguliers existants...
[15/07/2025 11:42:16] [FRONTEND] [LOG] ➡️ Clic sur suivant
[15/07/2025 11:42:16] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] 0 shifts réguliers supprimés
[15/07/2025 11:42:16] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Application pour semaine 2025-W31
[15/07/2025 11:42:16] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Cache invalidé pour forcer la régénération
[15/07/2025 11:42:16] [FRONTEND] [LOG] 📋 [navigateWeek] Application des attributions pour la nouvelle semaine 2025-W31
[15/07/2025 11:42:16] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution a563e948-18d8-4540-bfbc-7853d6506c05 a une intersection avec cette semaine (2025-07-30 → ∞) vs (2025-08-03 → 2025-08-09)
[15/07/2025 11:42:16] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888 terminée avant cette semaine (endDate: 2025-07-29, semaine commence: 2025-08-03)
[15/07/2025 11:42:16] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-08-06 (3)
[15/07/2025 11:42:16] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-08-04 (1)
[15/07/2025 11:42:16] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 1 attributions actives sur 3 total
[15/07/2025 11:42:16] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution b77227a7-67b7-4feb-b082-890cfeecab1f terminée avant cette semaine (endDate: 2025-07-22, semaine commence: 2025-08-03)
[15/07/2025 11:42:16] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution a563e948-18d8-4540-bfbc-7853d6506c05: [{"dateKey":"2025-08-03","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-08-04","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-08-05","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-08-06","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-08-07","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-08-08","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-08-09","isWeekend":true,"dayOfWeek":6}]
[15/07/2025 11:42:16] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"a563e948-18d8-4540-bfbc-7853d6506c05","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"startDate":"2025-07-30","endDate":null,"excludedDates":[]}
[15/07/2025 11:42:16] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-08-03 (0)
[15/07/2025 11:42:16] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-08-03 ignoré pour attribution a563e948-18d8-4540-bfbc-7853d6506c05
[15/07/2025 11:42:16] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"a563e948-18d8-4540-bfbc-7853d6506c05","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-08-04T04:00:00.000Z","dayDateKey":"2025-08-04","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:42:16] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 11:42:16] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 11:42:16] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"a563e948-18d8-4540-bfbc-7853d6506c05","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-08-03T04:00:00.000Z","dayDateKey":"2025-08-03","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:42:17] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[15/07/2025 11:42:17] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"a563e948-18d8-4540-bfbc-7853d6506c05","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-08-06T04:00:00.000Z","dayDateKey":"2025-08-06","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:16.674Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:16.683Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:16.676Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:16.857Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:16.862Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:16.860Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:16.858Z"
}
[15/07/2025 11:42:17] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:16.680Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:16.867Z"
}
[15/07/2025 11:42:17] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-08-04: {"assignmentId":"a563e948-18d8-4540-bfbc-7853d6506c05","dayDateStr":"2025-08-04","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 11:42:17] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Rendu demandé car 5 shifts créés
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:16.691Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:16.685Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:16.894Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:16.688Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:16.689Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:16.922Z"
}
[15/07/2025 11:42:17] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-08-04
[15/07/2025 11:42:17] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-08-07 validé, shifts existants: 0
[15/07/2025 11:42:17] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-08-04 validé, shifts existants: 0
[15/07/2025 11:42:17] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-08-09 (6)
[15/07/2025 11:42:17] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-08-04 pour Marie Martin
[15/07/2025 11:42:17] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-08-06
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:16.896Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:17.106Z"
}
[15/07/2025 11:42:17] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[15/07/2025 11:42:17] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-08-05 (2)
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:17.129Z"
}
[15/07/2025 11:42:17] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-08-06: {"assignmentId":"a563e948-18d8-4540-bfbc-7853d6506c05","dayDateStr":"2025-08-06","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:16.900Z"
}
[15/07/2025 11:42:17] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"a563e948-18d8-4540-bfbc-7853d6506c05","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-08-05T04:00:00.000Z","dayDateKey":"2025-08-05","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:42:17] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-08-08 pour Marie Martin
[15/07/2025 11:42:17] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 11:42:17] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-08-08 validé, shifts existants: 0
[15/07/2025 11:42:17] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-08-05: {"assignmentId":"a563e948-18d8-4540-bfbc-7853d6506c05","dayDateStr":"2025-08-05","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 11:42:17] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-08-07
[15/07/2025 11:42:17] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-08-05
[15/07/2025 11:42:17] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"a563e948-18d8-4540-bfbc-7853d6506c05","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-08-09T04:00:00.000Z","dayDateKey":"2025-08-09","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:42:17] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 11:42:17] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-08-08: {"assignmentId":"a563e948-18d8-4540-bfbc-7853d6506c05","dayDateStr":"2025-08-08","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 11:42:17] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 11:42:17] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-08-06 pour Marie Martin
[15/07/2025 11:42:17] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-08-06 validé, shifts existants: 0
[15/07/2025 11:42:17] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 11:42:17] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 5 attributions appliquées pour la semaine 2025-W31
[15/07/2025 11:42:17] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-08-05 pour Marie Martin
[15/07/2025 11:42:17] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 11:42:17] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-08-07 pour Marie Martin
[15/07/2025 11:42:17] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-08-07: {"assignmentId":"a563e948-18d8-4540-bfbc-7853d6506c05","dayDateStr":"2025-08-07","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 11:42:17] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-08-08 (5)
[15/07/2025 11:42:17] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-08-07 (4)
[15/07/2025 11:42:17] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 11:42:17] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 11:42:17] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"a563e948-18d8-4540-bfbc-7853d6506c05","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-08-07T04:00:00.000Z","dayDateKey":"2025-08-07","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:42:17] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 11:42:17] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-08-08
[15/07/2025 11:42:17] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-08-05 validé, shifts existants: 0
[15/07/2025 11:42:17] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"a563e948-18d8-4540-bfbc-7853d6506c05","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-08-08T04:00:00.000Z","dayDateKey":"2025-08-08","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 11:42:17] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-08-09 ignoré pour attribution a563e948-18d8-4540-bfbc-7853d6506c05
[15/07/2025 11:42:17] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 11:42:17] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 11:42:17] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[15/07/2025 11:42:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière a563e948-18d8-4540-bfbc-7853d6506c05
[15/07/2025 11:42:18] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[15/07/2025 11:42:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 11:42:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 11:42:18] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 11:42:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 11:42:18] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[15/07/2025 11:42:18] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière a563e948-18d8-4540-bfbc-7853d6506c05
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 11:42:18] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 11:42:18] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière a563e948-18d8-4540-bfbc-7853d6506c05
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière a563e948-18d8-4540-bfbc-7853d6506c05
[15/07/2025 11:42:18] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 11:42:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 11:42:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 11:42:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière a563e948-18d8-4540-bfbc-7853d6506c05
[15/07/2025 11:42:18] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 11:42:18] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[15/07/2025 11:42:18] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 70 zones trouvées
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 70 zones de drop disponibles
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 11:42:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 11:42:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 11:42:18] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 11:42:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 11:42:18] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 11:42:18] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 11:42:18] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 11:42:18] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[15/07/2025 11:42:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (70 zones)
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 11:42:18] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 70 zones trouvées
[15/07/2025 11:42:18] [FRONTEND] [WARN] ⚠️ [SETUP] Erreur lors de la destruction: Error: can't access property "Sortable1752594106961", el is null
destroy@http://localhost:5173/node_modules/.vite/deps/sortablejs.js?v=189de042:1812:5
setupDragAndDrop@http://localhost:5173/src/teamCalendarApp.ts?t=1752586718689:5808:33
attachAllEventListeners@http://localhost:5173/src/teamCalendarApp.ts?t=1752586718689:5358:10
actualRender@http://localhost:5173/src/teamCalendarApp.ts?t=1752586718689:2466:10
render/this._debouncedRender<@http://localhost:5173/src/teamCalendarApp.ts?t=1752586718689:2451:12

[15/07/2025 11:42:18] [FRONTEND] [LOG] ✅ [SETUP] Drag & drop des employés configuré (mode simplifié)
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 70 zones de drop disponibles
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 11:42:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 11:42:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 11:42:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 11:42:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 11:42:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 11:42:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 11:42:18] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 11:42:18] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 11:42:18] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] 0 grips ajoutés
[15/07/2025 11:42:18] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 11:42:18] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 11:42:19] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 11:42:19] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 11:42:19] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 11:42:19] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (70 zones)
[15/07/2025 11:42:19] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 11:42:19] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 11:42:19] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 11:42:19] [FRONTEND] [LOG] 🎯 [GRIP] Début drag grip pour attribution a563e948-18d8-4540-bfbc-7853d6506c05
[15/07/2025 11:42:19] [FRONTEND] [LOG] 🎯 [GRIP] DataTransfer types après: ["text/_moz_htmlcontext","text/_moz_htmlinfo","text/html","text/plain","regularassignmentid"]
[15/07/2025 11:42:19] [FRONTEND] [LOG] 🎯 [GRIP] DataTransfer types avant: ["text/_moz_htmlcontext","text/_moz_htmlinfo","text/html","text/plain"]
[15/07/2025 11:42:19] [FRONTEND] [LOG] 🎯 [GRIP] regularAssignmentId stocké: a563e948-18d8-4540-bfbc-7853d6506c05
[15/07/2025 11:42:19] [FRONTEND] [LOG] 🎨 [highlightEmployeeDropZones] Zones trouvées: [{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30"},{"element":"BUTTON","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors"},{"element":"BUTTON","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors"},{"element":"BUTTON","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30"},{"element":"BUTTON","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors"},{"element":"BUTTON","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors"},{"element":"BUTTON","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30"},{"element":"BUTTON","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors"},{"element":"BUTTON","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors"},{"element":"BUTTON","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30"},{"element":"BUTTON","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors"},{"element":"BUTTON","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors"},{"element":"BUTTON","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30"},{"element":"BUTTON","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors"},{"element":"BUTTON","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors"},{"element":"BUTTON","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60"}]
[15/07/2025 11:42:19] [FRONTEND] [LOG] 🎨 [highlightEmployeeDropZones] Ajout surbrillance sur 70 zones
[15/07/2025 11:42:19] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 18fe9cd1-e8a5-44e3-90b3-a856086ce27e, types: text/_moz_htmlcontext, text/_moz_htmlinfo, text/html, text/plain, regularassignmentid
[15/07/2025 11:42:19] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 11:42:19] [FRONTEND] [LOG] 🎯 [DROP] ✅ Drop reçu sur employé: Pierre Durand
[15/07/2025 11:42:19] [FRONTEND] [LOG] 🔍 [detectDropDateFromPosition] Analyse position pour employé 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 11:42:19] [FRONTEND] [LOG] 🔄 [DROP] Attribution régulière: a563e948-18d8-4540-bfbc-7853d6506c05 → 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 11:42:19] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 11:42:19] [FRONTEND] [LOG] ✅ [detectDropDateFromPosition] Date trouvée via cellule: 2025-08-05
[15/07/2025 11:42:19] [FRONTEND] [LOG] 📍 [detectDropDateFromPosition] Position souris: (738, 535)
[15/07/2025 11:42:19] [FRONTEND] [LOG] 📅 [DROP] Date détectée pour le drop: 2025-08-05
[15/07/2025 11:42:19] [FRONTEND] [LOG] 🎯 [handleRegularAssignmentDrop] Drop assignment a563e948-18d8-4540-bfbc-7853d6506c05 sur employé Pierre Durand
[15/07/2025 11:42:19] [FRONTEND] [LOG] ✅ [showRegularAssignmentConfirmationMenu] Date automatiquement détectée: 2025-08-05
[15/07/2025 11:42:19] [FRONTEND] [LOG] 📅 [handleRegularAssignmentDrop] Date cible: 2025-08-05
[15/07/2025 11:42:19] [FRONTEND] [LOG] 🎯 [GRIP] Fin drag grip pour attribution a563e948-18d8-4540-bfbc-7853d6506c05
[15/07/2025 11:42:19] [FRONTEND] [LOG] 🎯 [showRegularAssignmentConfirmationMenu] Affichage menu pour: {"assignment":"a563e948-18d8-4540-bfbc-7853d6506c05","from":"Marie Martin","to":"Pierre Durand","post":"Poste Matin","referenceDate":"2025-08-05","minDate":"2025-08-05","todayKey":"2025-07-15"}
[15/07/2025 11:42:19] [FRONTEND] [LOG] 🎨 [highlightEmployeeDropZones] Suppression surbrillance sur 70 zones
[15/07/2025 11:42:19] [FRONTEND] [LOG] 🎨 [highlightEmployeeDropZones] Zones trouvées: [{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200 regular-assignment-drop-zone"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30 regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors regular-assignment-drop-zone"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200 regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30 regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200 regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30 regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30 regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200 regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30 regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 regular-assignment-drop-zone"}]
[15/07/2025 11:42:21] [FRONTEND] [LOG] 🔄 [handlePermanentRegularAssignmentChange] Changement permanent a563e948-18d8-4540-bfbc-7853d6506c05 → 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 11:42:21] [FRONTEND] [LOG] 🔄 [handlePermanentRegularAssignmentChange] Désactiver l'original: false
[15/07/2025 11:42:21] [FRONTEND] [LOG] 📅 [handlePermanentRegularAssignmentChange] Date fournie directement: 2025-08-05
[15/07/2025 11:42:21] [FRONTEND] [LOG] 📅 [handlePermanentRegularAssignmentChange] Date finale utilisée: 2025-08-05
[15/07/2025 11:42:21] [FRONTEND] [LOG] 🔄 [reassignRegularAssignmentFromDate] Désactiver l'original: false
[15/07/2025 11:42:21] [FRONTEND] [LOG] 🔄 [reassignRegularAssignmentFromDate] a563e948-18d8-4540-bfbc-7853d6506c05 → 18fe9cd1-e8a5-44e3-90b3-a856086ce27e à partir de 2025-08-05
[15/07/2025 11:42:21] [FRONTEND] [LOG] 🔄 [updateShiftsForDateBasedReassignment] Mise à jour des shifts à partir de 2025-08-05
[15/07/2025 11:42:21] [FRONTEND] [LOG] 🔄 [reassignRegularAssignmentFromDate] Fork créé, historique préservé
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📝 [RegularAssignmentV2.update] Données reçues: {"id":"a563e948-18d8-4540-bfbc-7853d6506c05","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","postId":"ef3246ff-8398-4f11-a498-648d207563a0","startDate":"2025-07-30","endDate":"2025-08-04","isLimited":false,"isActive":true,"selectedDays":[1,2,3,4,5],"daysOfWeek":[1,2,3,4,5],"employeeName":"Marie Martin","postLabel":"Poste Matin","postHours":"08:00-16:00","postType":"standard","daysCount":5,"excludedDates":[]}
  Data: {
  "originalArgs": [
    "📝 [RegularAssignmentV2.update] Données reçues:",
    {
      "id": "a563e948-18d8-4540-bfbc-7853d6506c05",
      "employeeId": "604a1d0c-2141-4543-b7f9-7fc9adfa7b9a",
      "postId": "ef3246ff-8398-4f11-a498-648d207563a0",
      "startDate": "2025-07-30",
      "endDate": "2025-08-04",
      "isLimited": false,
      "isActive": true,
      "selectedDays": [
        1,
        2,
        3,
        4,
        5
      ],
      "daysOfWeek": [
        1,
        2,
        3,
        4,
        5
      ],
      "employeeName": "Marie Martin",
      "postLabel": "Poste Matin",
      "postHours": "08:00-16:00",
      "postType": "standard",
      "daysCount": 5,
      "excludedDates": []
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:21.812Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✏️ [RegularAssignments] Mise à jour attribution a563e948-18d8-4540-bfbc-7853d6506c05
  Data: {
  "originalArgs": [
    "✏️ [RegularAssignments] Mise à jour attribution a563e948-18d8-4540-bfbc-7853d6506c05"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:21.809Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ [validateAndConvertUuid] UUID valide détecté pour post_id: ef3246ff-8398-4f11-a498-648d207563a0
  Data: {
  "originalArgs": [
    "✅ [validateAndConvertUuid] UUID valide détecté pour post_id: ef3246ff-8398-4f11-a498-648d207563a0"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:21.816Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔍 [validateAndConvertUuid] Entrée: fieldName="post_id", value="ef3246ff-8398-4f11-a498-648d207563a0", type=string
  Data: {
  "originalArgs": [
    "🔍 [validateAndConvertUuid] Entrée: fieldName=\"post_id\", value=\"ef3246ff-8398-4f11-a498-648d207563a0\", type=string"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:21.814Z"
}
[15/07/2025 11:42:22] [FRONTEND] [LOG] ✅ [updateShiftsForDateBasedReassignment] 4 shifts mis à jour
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      UPDATE regular_assignments 
      SET employee_id = $1, post_id = $2, days_of_week = $3, start_date = $4, 
          end_date = $5, is_active = $6, excluded_dates = $7, updated_at = CURRENT_TIMESTAMP
      WHERE id = $8
      RETURNING *
     {"params":["604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","ef3246ff-8398-4f11-a498-648d207563a0",[1,2,3,4,5],"2025-07-30","2025-08-04",true,[],"a563e948-18d8-4540-bfbc-7853d6506c05"]}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      UPDATE regular_assignments \n      SET employee_id = $1, post_id = $2, days_of_week = $3, start_date = $4, \n          end_date = $5, is_active = $6, excluded_dates = $7, updated_at = CURRENT_TIMESTAMP\n      WHERE id = $8\n      RETURNING *\n    ",
    {
      "params": [
        "604a1d0c-2141-4543-b7f9-7fc9adfa7b9a",
        "ef3246ff-8398-4f11-a498-648d207563a0",
        [
          1,
          2,
          3,
          4,
          5
        ],
        "2025-07-30",
        "2025-08-04",
        true,
        [],
        "a563e948-18d8-4540-bfbc-7853d6506c05"
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:21.821Z"
}
[15/07/2025 11:42:22] [BACKEND] [INFO] ✏️ [RegularAssignments] Mise à jour attribution a563e948-18d8-4540-bfbc-7853d6506c05
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ [RegularAssignments] Attribution mise à jour: a563e948-18d8-4540-bfbc-7853d6506c05
  Data: {
  "originalArgs": [
    "✅ [RegularAssignments] Attribution mise à jour:",
    "a563e948-18d8-4540-bfbc-7853d6506c05"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:21.890Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 68ms: 
      UPDATE regular_assignments 
      SET emplo {"params":["604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","ef3246ff-8398-4f11-a498-648d207563a0",[1,2,3,4,5],"2025-07-30","2025-08-04",true,[],"a563e948-18d8-4540-bfbc-7853d6506c05"]}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 68ms:",
    "\n      UPDATE regular_assignments \n      SET emplo",
    {
      "params": [
        "604a1d0c-2141-4543-b7f9-7fc9adfa7b9a",
        "ef3246ff-8398-4f11-a498-648d207563a0",
        [
          1,
          2,
          3,
          4,
          5
        ],
        "2025-07-30",
        "2025-08-04",
        true,
        [],
        "a563e948-18d8-4540-bfbc-7853d6506c05"
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:21.889Z"
}
[15/07/2025 11:42:22] [FRONTEND] [LOG] ✅ [reassignRegularAssignmentFromDate] Attribution existante mise à jour avec date de fin
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ [validateAndConvertUuid] UUID valide détecté pour post_id: ef3246ff-8398-4f11-a498-648d207563a0
  Data: {
  "originalArgs": [
    "✅ [validateAndConvertUuid] UUID valide détecté pour post_id: ef3246ff-8398-4f11-a498-648d207563a0"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:21.930Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔍 [validateAndConvertUuid] Entrée: fieldName="post_id", value="ef3246ff-8398-4f11-a498-648d207563a0", type=string
  Data: {
  "originalArgs": [
    "🔍 [validateAndConvertUuid] Entrée: fieldName=\"post_id\", value=\"ef3246ff-8398-4f11-a498-648d207563a0\", type=string"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:21.928Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📝 [RegularAssignmentV2.create] Données reçues: {"employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"ef3246ff-8398-4f11-a498-648d207563a0","isLimited":false,"startDate":"2025-08-05","endDate":null,"selectedDays":[1,2,3,4,5],"daysOfWeek":[1,2,3,4,5],"isActive":true,"id":"a7e2ca5a-c5fa-40b3-a01f-fa8967d876ba"}
  Data: {
  "originalArgs": [
    "📝 [RegularAssignmentV2.create] Données reçues:",
    {
      "employeeId": "18fe9cd1-e8a5-44e3-90b3-a856086ce27e",
      "postId": "ef3246ff-8398-4f11-a498-648d207563a0",
      "isLimited": false,
      "startDate": "2025-08-05",
      "endDate": null,
      "selectedDays": [
        1,
        2,
        3,
        4,
        5
      ],
      "daysOfWeek": [
        1,
        2,
        3,
        4,
        5
      ],
      "isActive": true,
      "id": "a7e2ca5a-c5fa-40b3-a01f-fa8967d876ba"
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:21.927Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ➕ [RegularAssignments] Création d'une attribution individuelle
  Data: {
  "originalArgs": [
    "➕ [RegularAssignments] Création d'une attribution individuelle"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:21.924Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔍 [RegularAssignmentV2.create] Post ID original: ef3246ff-8398-4f11-a498-648d207563a0 Post ID validé: ef3246ff-8398-4f11-a498-648d207563a0
  Data: {
  "originalArgs": [
    "🔍 [RegularAssignmentV2.create] Post ID original:",
    "ef3246ff-8398-4f11-a498-648d207563a0",
    "Post ID validé:",
    "ef3246ff-8398-4f11-a498-648d207563a0"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:21.933Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📤 [RegularAssignments] Données reçues: {
  "employeeId": "18fe9cd1-e8a5-44e3-90b3-a856086ce27e",
  "postId": "ef3246ff-8398-4f11-a498-648d207563a0",
  "isLimited": false,
  "startDate": "2025-08-05",
  "endDate": null,
  "selectedDays": [
    1,
    2,
    3,
    4,
    5
  ],
  "daysOfWeek": [
    1,
    2,
    3,
    4,
    5
  ],
  "isActive": true,
  "id": "a7e2ca5a-c5fa-40b3-a01f-fa8967d876ba"
}
  Data: {
  "originalArgs": [
    "📤 [RegularAssignments] Données reçues:",
    "{\n  \"employeeId\": \"18fe9cd1-e8a5-44e3-90b3-a856086ce27e\",\n  \"postId\": \"ef3246ff-8398-4f11-a498-648d207563a0\",\n  \"isLimited\": false,\n  \"startDate\": \"2025-08-05\",\n  \"endDate\": null,\n  \"selectedDays\": [\n    1,\n    2,\n    3,\n    4,\n    5\n  ],\n  \"daysOfWeek\": [\n    1,\n    2,\n    3,\n    4,\n    5\n  ],\n  \"isActive\": true,\n  \"id\": \"a7e2ca5a-c5fa-40b3-a01f-fa8967d876ba\"\n}"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:21.926Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔍 [RegularAssignmentV2.create] Création attribution unique pour jours: [1, 2, 3, 4, 5]
  Data: {
  "originalArgs": [
    "🔍 [RegularAssignmentV2.create] Création attribution unique pour jours: [1, 2, 3, 4, 5]"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:21.937Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      INSERT INTO regular_assignments (id, employee_id, post_id, days_of_week, start_date, end_date, is_active)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
     {"params":["255ae26a-03c7-499f-ba7d-10568e4a8be1","18fe9cd1-e8a5-44e3-90b3-a856086ce27e","ef3246ff-8398-4f11-a498-648d207563a0",[1,2,3,4,5],"2025-08-05",null,true]}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      INSERT INTO regular_assignments (id, employee_id, post_id, days_of_week, start_date, end_date, is_active)\n      VALUES ($1, $2, $3, $4, $5, $6, $7)\n      RETURNING *\n    ",
    {
      "params": [
        "255ae26a-03c7-499f-ba7d-10568e4a8be1",
        "18fe9cd1-e8a5-44e3-90b3-a856086ce27e",
        "ef3246ff-8398-4f11-a498-648d207563a0",
        [
          1,
          2,
          3,
          4,
          5
        ],
        "2025-08-05",
        null,
        true
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:21.939Z"
}
[15/07/2025 11:42:22] [BACKEND] [INFO] ➕ [RegularAssignments] Création d'une attribution individuelle
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ [RegularAssignmentV2.create] Attribution créée avec 5 jours
  Data: {
  "originalArgs": [
    "✅ [RegularAssignmentV2.create] Attribution créée avec 5 jours"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:22.006Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 59ms: 
      INSERT INTO regular_assignments (id, employ {"params":["255ae26a-03c7-499f-ba7d-10568e4a8be1","18fe9cd1-e8a5-44e3-90b3-a856086ce27e","ef3246ff-8398-4f11-a498-648d207563a0",[1,2,3,4,5],"2025-08-05",null,true]}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 59ms:",
    "\n      INSERT INTO regular_assignments (id, employ",
    {
      "params": [
        "255ae26a-03c7-499f-ba7d-10568e4a8be1",
        "18fe9cd1-e8a5-44e3-90b3-a856086ce27e",
        "ef3246ff-8398-4f11-a498-648d207563a0",
        [
          1,
          2,
          3,
          4,
          5
        ],
        "2025-08-05",
        null,
        true
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:22.004Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ [RegularAssignments] Attribution créée avec succès: 255ae26a-03c7-499f-ba7d-10568e4a8be1
  Data: {
  "originalArgs": [
    "✅ [RegularAssignments] Attribution créée avec succès:",
    "255ae26a-03c7-499f-ba7d-10568e4a8be1"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:22.008Z"
}
[15/07/2025 11:42:22] [FRONTEND] [LOG] 🔄 [reassignRegularAssignmentFromDate] Rechargement complet des données...
[15/07/2025 11:42:22] [FRONTEND] [LOG] ✅ [reassignRegularAssignmentFromDate] Nouvelle attribution créée avec succès
[15/07/2025 11:42:22] [FRONTEND] [LOG] 📂 [TeamCalendarApp] Chargement de l'état...
[15/07/2025 11:42:22] [FRONTEND] [LOG] 🌐 [TeamCalendarApp] Chargement depuis l'API...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT e.*, et.name as template_name 
      FROM employees e 
      LEFT JOIN employee_templates et ON e.template_id = et.id 
      ORDER BY e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT e.*, et.name as template_name \n      FROM employees e \n      LEFT JOIN employee_templates et ON e.template_id = et.id \n      ORDER BY e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:22.106Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 51ms: 
      SELECT e.*, et.name as template_name 
      {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 51ms:",
    "\n      SELECT e.*, et.name as template_name \n     ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:22.158Z"
}
[15/07/2025 11:42:22] [BACKEND] [INFO] ✅ Requête exécutée en 51ms: 
      SELECT e.*, et.name as template_name 
      {}
[15/07/2025 11:42:22] [FRONTEND] [LOG] 👥 [TeamCalendarApp] 5 employés chargés
[15/07/2025 11:42:22] [FRONTEND] [LOG] 📦 [loadState] Chargement de tous les shifts depuis la base de données...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT s.*, e.name as employee_name, sp.label as post_label
      FROM shifts s
      LEFT JOIN employees e ON s.employee_id = e.id
      LEFT JOIN standard_posts sp ON s.post_id = sp.id
      ORDER BY s.date_key, e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT s.*, e.name as employee_name, sp.label as post_label\n      FROM shifts s\n      LEFT JOIN employees e ON s.employee_id = e.id\n      LEFT JOIN standard_posts sp ON s.post_id = sp.id\n      ORDER BY s.date_key, e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:22.195Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 45ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 45ms:",
    "\n      SELECT s.*, e.name as employee_name, sp.lab",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:22.239Z"
}
[15/07/2025 11:42:22] [BACKEND] [INFO] ✅ Requête exécutée en 45ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
[15/07/2025 11:42:22] [FRONTEND] [LOG] ✅ [loadState] Shifts organisés dans this.data.schedule
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:22.263Z"
}
[15/07/2025 11:42:22] [FRONTEND] [LOG] 📅 [loadState] 0 shifts chargés depuis la base de données
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 57ms: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 57ms:",
    "SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:22.319Z"
}
[15/07/2025 11:42:22] [BACKEND] [INFO] ✅ Requête exécutée en 57ms: SELECT * FROM standard_posts ORDER BY label {}
[15/07/2025 11:42:22] [FRONTEND] [LOG] 🏢 [TeamCalendarApp] 5 postes standards chargés
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:22.343Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 47ms: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 47ms:",
    "SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:22.391Z"
}
[15/07/2025 11:42:22] [BACKEND] [INFO] ✅ Requête exécutée en 47ms: SELECT * FROM app_settings ORDER BY setting_key {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT 
        ra.id,
        ra.employee_id,
        ra.post_id,
        ra.days_of_week,
        ra.start_date,
        ra.end_date,
        ra.is_active,
        ra.excluded_dates,
        e.name as employee_name,
        sp.label as post_label,
        sp.hours as post_hours,
        sp.type as post_type,
        array_length(ra.days_of_week, 1) as days_count
      FROM regular_assignments ra
      LEFT JOIN employees e ON ra.employee_id = e.id
      LEFT JOIN standard_posts sp ON ra.post_id = sp.id
      WHERE ra.is_active = true
      ORDER BY e.name, sp.label, ra.start_date
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT \n        ra.id,\n        ra.employee_id,\n        ra.post_id,\n        ra.days_of_week,\n        ra.start_date,\n        ra.end_date,\n        ra.is_active,\n        ra.excluded_dates,\n        e.name as employee_name,\n        sp.label as post_label,\n        sp.hours as post_hours,\n        sp.type as post_type,\n        array_length(ra.days_of_week, 1) as days_count\n      FROM regular_assignments ra\n      LEFT JOIN employees e ON ra.employee_id = e.id\n      LEFT JOIN standard_posts sp ON ra.post_id = sp.id\n      WHERE ra.is_active = true\n      ORDER BY e.name, sp.label, ra.start_date\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:22.407Z"
}
[15/07/2025 11:42:22] [FRONTEND] [LOG] ⚙️ [loadState] Paramètres chargés depuis API: {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 57ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 57ms:",
    "\n      SELECT \n        ra.id,\n        ra.employee_",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T15:42:22.463Z"
}
[15/07/2025 11:42:22] [BACKEND] [INFO] ✅ Requête exécutée en 57ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
[15/07/2025 11:42:23] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"a563e948-18d8-4540-bfbc-7853d6506c05","employee_id":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","post_id":"ef3246ff-8398-4f11-a498-648d207563a0","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-30T04:00:00.000Z","end_date":"2025-08-04T04:00:00.000Z","excluded_dates":[]}
[15/07/2025 11:42:23] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","employee_id":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","post_id":"ef3246ff-8398-4f11-a498-648d207563a0","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-23T04:00:00.000Z","end_date":"2025-07-29T04:00:00.000Z","excluded_dates":[]}
[15/07/2025 11:42:23] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"a563e948-18d8-4540-bfbc-7853d6506c05","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-30","endDate":"2025-08-04","excludedDates":[]}
[15/07/2025 11:42:23] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"255ae26a-03c7-499f-ba7d-10568e4a8be1","employee_id":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","post_id":"ef3246ff-8398-4f11-a498-648d207563a0","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-08-05T04:00:00.000Z","end_date":null,"excluded_dates":[]}
[15/07/2025 11:42:23] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-23","endDate":"2025-07-29","excludedDates":[]}
[15/07/2025 11:42:23] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"255ae26a-03c7-499f-ba7d-10568e4a8be1","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"isLimited":false,"startDate":"2025-08-05","endDate":null,"excludedDates":[]}
[15/07/2025 11:42:23] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"b77227a7-67b7-4feb-b082-890cfeecab1f","employee_id":"59f5df3a-33ef-425c-bfa2-adca818cf94f","post_id":"ef3246ff-8398-4f11-a498-648d207563a0","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-15T04:00:00.000Z","end_date":"2025-07-22T04:00:00.000Z","excluded_dates":[]}
[15/07/2025 11:42:23] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"b77227a7-67b7-4feb-b082-890cfeecab1f","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-15","endDate":"2025-07-22","excludedDates":[]}
[15/07/2025 11:42:23] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 11:42:23] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 11:42:23] [FRONTEND] [LOG] 📋 [loadState] 4/4 assignations régulières valides chargées et normalisées
[15/07/2025 11:42:23] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 11:42:23] [FRONTEND] [LOG] ✅ [fixInvalidAssignmentDates] Aucune date invalide trouvée
[15/07/2025 11:42:23] [FRONTEND] [LOG] 🔧 [fixInvalidAssignmentDates] Vérification des dates invalides...
[15/07/2025 11:42:23] [FRONTEND] [LOG] 📋 [loadState] Assignations régulières chargées (application différée)
[15/07/2025 11:42:23] [FRONTEND] [LOG] 🧹 [cleanupDuplicateRegularShifts] Nettoyage des doublons de shifts réguliers...
[15/07/2025 11:42:23] [FRONTEND] [LOG] ✅ [cleanupDuplicateRegularShifts] Aucun doublon détecté
[15/07/2025 11:42:23] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 2 attributions actives sur 4 total
[15/07/2025 11:42:23] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-08-03 (0)
[15/07/2025 11:42:23] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[15/07/2025 11:42:23] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-08-07 après endDate 2025-08-04 (original: 2025-08-04) (attribution a563e948-18d8-4540-bfbc-7853d6506c05)
[15/07/2025 11:42:23] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-08-06 ignoré pour attribution a563e948-18d8-4540-bfbc-7853d6506c05
[15/07/2025 11:42:23] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"255ae26a-03c7-499f-ba7d-10568e4a8be1","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"startDate":"2025-08-05","endDate":null,"excludedDates":[]}
[15/07/2025 11:42:23] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"255ae26a-03c7-499f-ba7d-10568e4a8be1","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-08-03T04:00:00.000Z","dayDateKey":"2025-08-03","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5]}
[15/07/2025 11:42:23] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-08-06 (3)
[15/07/2025 11:42:23] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[15/07/2025 11:42:23] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-08-08 pour Pierre Durand
[15/07/2025 11:42:23] [FRONTEND] [LOG] ✅ [reassignRegularAssignmentFromDate] Attribution divisée avec succès
[15/07/2025 11:42:23] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 255ae26a-03c7-499f-ba7d-10568e4a8be1
[15/07/2025 11:42:23] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 70 zones de drop disponibles
[15/07/2025 11:42:23] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 11:42:23] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 11:42:23] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 11:42:23] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 70 zones trouvées
[15/07/2025 11:42:23] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 11:42:23] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 11:42:23] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 11:42:23] [FRONTEND] [LOG] 🧹 [cleanupInvalidAssignmentIds] Nettoyage des assignment_id invalides...
[15/07/2025 11:42:23] [FRONTEND] [LOG] ✅ [loadState] État chargé depuis l'API avec succès
[15/07/2025 11:42:23] [FRONTEND] [LOG] ✅ [cleanupInvalidAssignmentIds] Aucun assignment_id invalide détecté
[15/07/2025 11:42:23] [FRONTEND] [LOG] 🧹 [reassignRegularAssignmentFromDate] Nettoyage complet des shifts réguliers...
[15/07/2025 11:42:23] [FRONTEND] [LOG] 🗑️ [reassignRegularAssignmentFromDate] Invalidation des caches...
[15/07/2025 11:42:23] [FRONTEND] [LOG] 🔄 [reassignRegularAssignmentFromDate] Application des attributions avec la nouvelle logique...
[15/07/2025 11:42:23] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] Nettoyage des shifts réguliers existants...
[15/07/2025 11:42:23] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Cache invalidé pour forcer la régénération
[15/07/2025 11:42:23] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] 0 shifts réguliers supprimés
[15/07/2025 11:42:23] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888 terminée avant cette semaine (endDate: 2025-07-29, semaine commence: 2025-08-03)
[15/07/2025 11:42:23] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Application pour semaine 2025-W31
[15/07/2025 11:42:23] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution a563e948-18d8-4540-bfbc-7853d6506c05 a une intersection avec cette semaine (2025-07-30 → 2025-08-04) vs (2025-08-03 → 2025-08-09)
[15/07/2025 11:42:23] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution b77227a7-67b7-4feb-b082-890cfeecab1f terminée avant cette semaine (endDate: 2025-07-22, semaine commence: 2025-08-03)
[15/07/2025 11:42:23] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution 255ae26a-03c7-499f-ba7d-10568e4a8be1 a une intersection avec cette semaine (2025-08-05 → ∞) vs (2025-08-03 → 2025-08-09)
[15/07/2025 11:42:23] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution a563e948-18d8-4540-bfbc-7853d6506c05: [{"dateKey":"2025-08-03","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-08-04","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-08-05","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-08-06","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-08-07","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-08-08","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-08-09","isWeekend":true,"dayOfWeek":6}]
[15/07/2025 11:42:23] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"a563e948-18d8-4540-bfbc-7853d6506c05","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"startDate":"2025-07-30","endDate":"2025-08-04","excludedDates":[]}
[15/07/2025 11:42:23] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-08-04 (1)
[15/07/2025 11:42:23] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"a563e948-18d8-4540-bfbc-7853d6506c05","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-08-03T04:00:00.000Z","dayDateKey":"2025-08-03","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5]}
[15/07/2025 11:42:23] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 11:42:23] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[15/07/2025 11:42:23] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-08-03 ignoré pour attribution a563e948-18d8-4540-bfbc-7853d6506c05
[15/07/2025 11:42:23] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"a563e948-18d8-4540-bfbc-7853d6506c05","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-08-04T04:00:00.000Z","dayDateKey":"2025-08-04","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5]}
[15/07/2025 11:42:23] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-08-04
[15/07/2025 11:42:23] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-08-04: {"assignmentId":"a563e948-18d8-4540-bfbc-7853d6506c05","dayDateStr":"2025-08-04","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 11:42:23] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-08-04 pour Marie Martin
[15/07/2025 11:42:23] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-08-04 validé, shifts existants: 0
[15/07/2025 11:42:23] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"a563e948-18d8-4540-bfbc-7853d6506c05","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-08-05T04:00:00.000Z","dayDateKey":"2025-08-05","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5]}
[15/07/2025 11:42:23] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-08-05 (2)
[15/07/2025 11:42:23] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-08-05: {"assignmentId":"a563e948-18d8-4540-bfbc-7853d6506c05","dayDateStr":"2025-08-05","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 11:42:23] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[15/07/2025 11:42:23] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-08-05 ignoré pour attribution a563e948-18d8-4540-bfbc-7853d6506c05
[15/07/2025 11:42:23] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-08-05 après endDate 2025-08-04 (original: 2025-08-04) (attribution a563e948-18d8-4540-bfbc-7853d6506c05)
[15/07/2025 11:42:23] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"a563e948-18d8-4540-bfbc-7853d6506c05","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-08-06T04:00:00.000Z","dayDateKey":"2025-08-06","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5]}
[15/07/2025 11:42:23] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-08-06 (3)
[15/07/2025 11:42:23] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-08-06 après endDate 2025-08-04 (original: 2025-08-04) (attribution a563e948-18d8-4540-bfbc-7853d6506c05)
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-08-06: {"assignmentId":"a563e948-18d8-4540-bfbc-7853d6506c05","dayDateStr":"2025-08-06","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"a563e948-18d8-4540-bfbc-7853d6506c05","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-08-07T04:00:00.000Z","dayDateKey":"2025-08-07","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5]}
[15/07/2025 11:42:24] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-08-07 (4)
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-08-07: {"assignmentId":"a563e948-18d8-4540-bfbc-7853d6506c05","dayDateStr":"2025-08-07","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 11:42:24] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[15/07/2025 11:42:24] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-08-07 ignoré pour attribution a563e948-18d8-4540-bfbc-7853d6506c05
[15/07/2025 11:42:24] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-08-08 (5)
[15/07/2025 11:42:24] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"a563e948-18d8-4540-bfbc-7853d6506c05","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-08-08T04:00:00.000Z","dayDateKey":"2025-08-08","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5]}
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-08-08: {"assignmentId":"a563e948-18d8-4540-bfbc-7853d6506c05","dayDateStr":"2025-08-08","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 11:42:24] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-08-08 ignoré pour attribution a563e948-18d8-4540-bfbc-7853d6506c05
[15/07/2025 11:42:24] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-08-08 après endDate 2025-08-04 (original: 2025-08-04) (attribution a563e948-18d8-4540-bfbc-7853d6506c05)
[15/07/2025 11:42:24] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-08-09 (6)
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"a563e948-18d8-4540-bfbc-7853d6506c05","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-08-09T04:00:00.000Z","dayDateKey":"2025-08-09","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5]}
[15/07/2025 11:42:24] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-08-09 ignoré pour attribution a563e948-18d8-4540-bfbc-7853d6506c05
[15/07/2025 11:42:24] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 11:42:24] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-08-03 (0)
[15/07/2025 11:42:24] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-08-03 ignoré pour attribution 255ae26a-03c7-499f-ba7d-10568e4a8be1
[15/07/2025 11:42:24] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution 255ae26a-03c7-499f-ba7d-10568e4a8be1: [{"dateKey":"2025-08-03","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-08-04","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-08-05","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-08-06","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-08-07","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-08-08","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-08-09","isWeekend":true,"dayOfWeek":6}]
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"255ae26a-03c7-499f-ba7d-10568e4a8be1","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-08-04T04:00:00.000Z","dayDateKey":"2025-08-04","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5]}
[15/07/2025 11:42:24] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-08-04 (1)
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-08-04: {"assignmentId":"255ae26a-03c7-499f-ba7d-10568e4a8be1","dayDateStr":"2025-08-04","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 11:42:24] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-08-04 ignoré pour attribution 255ae26a-03c7-499f-ba7d-10568e4a8be1
[15/07/2025 11:42:24] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[15/07/2025 11:42:24] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-08-04 avant startDate 2025-08-05 (original: 2025-08-05)
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"255ae26a-03c7-499f-ba7d-10568e4a8be1","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-08-05T04:00:00.000Z","dayDateKey":"2025-08-05","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5]}
[15/07/2025 11:42:24] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-08-05 (2)
[15/07/2025 11:42:24] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-08-05: {"assignmentId":"255ae26a-03c7-499f-ba7d-10568e4a8be1","dayDateStr":"2025-08-05","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 11:42:24] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-08-05 validé, shifts existants: 0
[15/07/2025 11:42:24] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-08-05
[15/07/2025 11:42:24] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-08-05 pour Pierre Durand
[15/07/2025 11:42:24] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"255ae26a-03c7-499f-ba7d-10568e4a8be1","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-08-06T04:00:00.000Z","dayDateKey":"2025-08-06","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5]}
[15/07/2025 11:42:24] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-08-06
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-08-06: {"assignmentId":"255ae26a-03c7-499f-ba7d-10568e4a8be1","dayDateStr":"2025-08-06","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 11:42:24] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-08-06 pour Pierre Durand
[15/07/2025 11:42:24] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-08-06 validé, shifts existants: 0
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"255ae26a-03c7-499f-ba7d-10568e4a8be1","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-08-07T04:00:00.000Z","dayDateKey":"2025-08-07","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5]}
[15/07/2025 11:42:24] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-08-07 (4)
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-08-07: {"assignmentId":"255ae26a-03c7-499f-ba7d-10568e4a8be1","dayDateStr":"2025-08-07","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 11:42:24] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-08-07
[15/07/2025 11:42:24] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-08-07 pour Pierre Durand
[15/07/2025 11:42:24] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-08-07 validé, shifts existants: 0
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"255ae26a-03c7-499f-ba7d-10568e4a8be1","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-08-08T04:00:00.000Z","dayDateKey":"2025-08-08","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5]}
[15/07/2025 11:42:24] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-08-08 (5)
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-08-08: {"assignmentId":"255ae26a-03c7-499f-ba7d-10568e4a8be1","dayDateStr":"2025-08-08","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 11:42:24] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[15/07/2025 11:42:24] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-08-08 validé, shifts existants: 0
[15/07/2025 11:42:24] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-08-08
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"255ae26a-03c7-499f-ba7d-10568e4a8be1","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-08-09T04:00:00.000Z","dayDateKey":"2025-08-09","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5]}
[15/07/2025 11:42:24] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-08-09 (6)
[15/07/2025 11:42:24] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-08-09 ignoré pour attribution 255ae26a-03c7-499f-ba7d-10568e4a8be1
[15/07/2025 11:42:24] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Rendu demandé car 5 shifts créés
[15/07/2025 11:42:24] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 5 attributions appliquées pour la semaine 2025-W31
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Matin: [1, 2, 3, 4, 5]
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🔄 [reassignRegularAssignmentFromDate] Rendu final...
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[15/07/2025 11:42:24] [FRONTEND] [LOG] 📝 [logModification] Enregistrement: {"id":"e6bf34c5-f0c4-4189-b900-8ba654635cc4","type":"regular-assignment","title":"Attribution régulière modifiée (permanent)","description":"Poste Matin transféré de Marie Martin vers Pierre Durand à partir du 04/08/2025","timestamp":"2025-07-15T15:42:22.515Z","employeeName":"Pierre Durand","postLabel":"Poste Matin","date":"2025-08-05","assignmentId":"a563e948-18d8-4540-bfbc-7853d6506c05","sourceEmployeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","targetEmployeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e"}
[15/07/2025 11:42:24] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 11:42:24] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[15/07/2025 11:42:24] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière a563e948-18d8-4540-bfbc-7853d6506c05
[15/07/2025 11:42:24] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 255ae26a-03c7-499f-ba7d-10568e4a8be1
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 255ae26a-03c7-499f-ba7d-10568e4a8be1
[15/07/2025 11:42:24] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 255ae26a-03c7-499f-ba7d-10568e4a8be1
[15/07/2025 11:42:24] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 11:42:24] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[15/07/2025 11:42:24] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 11:42:24] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 70 zones trouvées
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Nuit: [1, 2, 3, 4, 5]
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Soir: [1, 2, 3, 4, 5]
[15/07/2025 11:42:24] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 11:42:24] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE1: [0, 6]
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE2: [0, 6]
[15/07/2025 11:42:24] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 11:42:24] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 11:42:24] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 11:42:24] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 11:42:24] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 11:42:24] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 11:42:24] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 11:42:24] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 11:42:24] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 11:42:24] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 11:42:24] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 11:42:24] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 11:42:24] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 11:42:24] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (70 zones)
[15/07/2025 11:42:25] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 11:42:25] [FRONTEND] [LOG] ✅ [SETUP] Drag & drop des employés configuré (mode simplifié)
[15/07/2025 11:42:25] [FRONTEND] [WARN] ⚠️ [SETUP] Erreur lors de la destruction: Error: can't access property "Sortable1752594106961", el is null
destroy@http://localhost:5173/node_modules/.vite/deps/sortablejs.js?v=189de042:1812:5
setupDragAndDrop@http://localhost:5173/src/teamCalendarApp.ts?t=1752586718689:5808:33
attachAllEventListeners@http://localhost:5173/src/teamCalendarApp.ts?t=1752586718689:5358:10
actualRender@http://localhost:5173/src/teamCalendarApp.ts?t=1752586718689:2466:10
render/this._debouncedRender<@http://localhost:5173/src/teamCalendarApp.ts?t=1752586718689:2451:12

[15/07/2025 11:42:25] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[15/07/2025 11:42:25] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[15/07/2025 11:42:25] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 11:42:25] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 11:42:25] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 70 zones de drop disponibles
[15/07/2025 11:42:25] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 11:42:25] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 11:42:25] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 11:42:25] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 11:42:25] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 11:42:25] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 11:42:25] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 11:42:25] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 11:42:25] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 11:42:25] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 11:42:25] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 11:42:25] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 11:42:25] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 11:42:25] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 11:42:25] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 11:42:25] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 11:42:25] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 11:42:25] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 11:42:25] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 11:42:25] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 11:42:25] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 11:42:25] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 11:42:25] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 11:42:25] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 11:42:25] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 11:42:25] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 11:42:25] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 11:42:25] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 11:42:25] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 11:42:25] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 11:42:25] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] 0 grips ajoutés
[15/07/2025 11:42:25] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (70 zones)
[15/07/2025 11:42:25] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés