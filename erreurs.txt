[{
	"resource": "/c:/Users/<USER>/Desktop/interface/interface-3/interface - 1 Backup/backups/backup-2025-07-07T16-46-28-613Z/tsconfig.json",
	"owner": "typescript",
	"severity": 8,
	"message": "File 'c:/Users/<USER>/Desktop/interface/interface-3/interface - 1 Backup/backups/backup-2025-07-07T16-46-28-613Z/tsconfig.node.json' not found.",
	"source": "ts",
	"startLineNumber": 36,
	"startColumn": 18,
	"endLineNumber": 36,
	"endColumn": 52,
	"origin": "extHost1"
}]
[{
	"resource": "/c:/Users/<USER>/Desktop/interface/interface-3/interface - 1 Backup/src/teamCalendarApp.ts",
	"owner": "typescript",
	"code": "2740",
	"severity": 8,
	"message": "Type '{ _transactionLock: false; _savingState: false; _renderingInProgress: false; safeScheduleUpdate: (employeeId: string, dateKey: string, updateFn: (shifts: any[]) => any, operation: string) => any; ... 227 more ...; addEmergencyFixButton: () => void; }' is missing the following properties from type 'TeamCalendarAppType': ModalManager, loadWeekData, preloadAdjacentWeeks, loadWeekDataSilent, and 56 more.",
	"source": "ts",
	"startLineNumber": 691,
	"startColumn": 7,
	"endLineNumber": 691,
	"endColumn": 22,
	"origin": "extHost1"
}]
GET
http://localhost:5173/#employees
[HTTP/1.1 304 Not Modified 4ms]

GET
https://fonts.googleapis.com/icon?family=Material+Icons+Outlined
[HTTP/2 200 OK 0ms]

GET
https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Noto+Sans:wght@400;500;700;900&display=swap
[HTTP/2 200 OK 0ms]

GET
http://localhost:5173/@vite/client
[HTTP/1.1 304 Not Modified 0ms]

GET
http://localhost:5173/src/main.tsx?t=1752846119915
[HTTP/1.1 304 Not Modified 2ms]

GET
http://localhost:5173/capture-logs-unified.js
[HTTP/1.1 304 Not Modified 2ms]

GET
http://localhost:5173/src/interactiveTutorial.js
[HTTP/1.1 304 Not Modified 1ms]

GET
http://localhost:5173/test-modal-drag-drop.js
[HTTP/1.1 304 Not Modified 0ms]

GET
http://localhost:5173/validate-modal-fix.js
[HTTP/1.1 304 Not Modified 0ms]

GET
http://localhost:5173/test-date-fix-validation.js
[HTTP/1.1 304 Not Modified 1ms]

GET
http://localhost:5173/test-fork-modal-fix.js
[HTTP/1.1 304 Not Modified 1ms]

GET
http://localhost:5173/test-grip-handles-fix.js
[HTTP/1.1 304 Not Modified 1ms]

GET
http://localhost:5173/@react-refresh
[HTTP/1.1 304 Not Modified 0ms]

GET
http://localhost:5173/node_modules/vite/dist/client/env.mjs
[HTTP/1.1 304 Not Modified 0ms]

GET
http://localhost:5173/node_modules/.vite/deps/react_jsx-dev-runtime.js?v=189de042
[HTTP/1.1 200 OK 0ms]

GET
http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042
[HTTP/1.1 200 OK 0ms]

GET
http://localhost:5173/node_modules/.vite/deps/react.js?v=189de042
[HTTP/1.1 200 OK 0ms]

GET
http://localhost:5173/src/styles/loading-animation.css
[HTTP/1.1 304 Not Modified 1ms]

GET
http://localhost:5173/src/index.css?t=1752846119915
[HTTP/1.1 304 Not Modified 1ms]

GET
http://localhost:5173/src/styles/employee-names-fix.css
[HTTP/1.1 304 Not Modified 0ms]

GET
http://localhost:5173/src/App.tsx?t=1752846119915
[HTTP/1.1 304 Not Modified 1ms]

GET
http://localhost:5173/src/toastSystem.js
[HTTP/1.1 304 Not Modified 0ms]

GET
http://localhost:5173/scripts/cleanup-logs-system.js
[HTTP/1.1 304 Not Modified 1ms]

GET
http://localhost:5173/scripts/log-level-control.js
[HTTP/1.1 304 Not Modified 0ms]

[vite] connecting... client:789:9
[CAPTURE-SIMPLIFIÉE] Démarrage. Session ID: 1752594096509-9szda7dfq capture-logs-unified.js:18:13
GET
ws://localhost:5173/?token=psq1A6tWWh9b
[HTTP/1.1 101 Switching Protocols 1ms]

[vite] connected. capture-logs-unified.js:69:36
XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 39ms]

GET
http://localhost:5173/node_modules/.vite/deps/chunk-6L6DU33K.js?v=189de042
[HTTP/1.1 200 OK 0ms]

GET
http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042
[HTTP/1.1 200 OK 0ms]

GET
http://localhost:5173/node_modules/.vite/deps/react-router-dom.js?v=189de042
[HTTP/1.1 200 OK 0ms]

GET
http://localhost:5173/src/pages/Logs.tsx
[HTTP/1.1 304 Not Modified 1ms]

GET
http://localhost:5173/src/Agenda.tsx?t=1752846119915
[HTTP/1.1 304 Not Modified 1ms]

GET
http://localhost:5173/node_modules/.vite/deps/chunk-ALF5RGTR.js?v=189de042
[HTTP/1.1 200 OK 0ms]

GET
http://localhost:5173/src/sidebar.css
[HTTP/1.1 304 Not Modified 1ms]

GET
http://localhost:5173/src/teamCalendarApp.ts?t=1752846119915
[HTTP/1.1 304 Not Modified 1ms]

GET
http://localhost:5173/src/styles/fullscreen.css
[HTTP/1.1 304 Not Modified 1ms]

unreachable code after return statement teamCalendarApp.ts:3353:5
GET
http://localhost:5173/node_modules/.vite/deps/sortablejs.js?v=189de042
[HTTP/1.1 200 OK 0ms]

GET
http://localhost:5173/src/logger.ts
[HTTP/1.1 304 Not Modified 0ms]

GET
http://localhost:5173/src/modalFunctionalities.ts
[HTTP/1.1 304 Not Modified 1ms]

Uncaught SyntaxError: The requested module 'http://localhost:5173/src/teamCalendarApp.ts?t=1752846119915' doesn't provide an export named: 'default' Agenda.tsx:19:8
🎓 [Tutorial] Système de tutoriel interactif V2 initialisé capture-logs-unified.js:69:36
🧪 [TEST] Script de test de la modale drag & drop chargé capture-logs-unified.js:69:36
✅ [TEST] Fonctions de test disponibles: capture-logs-unified.js:69:36
- testAssignmentModal() : Tester l'ouverture de la modale capture-logs-unified.js:69:36
- testButtonListeners() : Tester les listeners des boutons capture-logs-unified.js:69:36
- diagnoseModalIssues() : Diagnostiquer les problèmes capture-logs-unified.js:69:36
- forceReconfigureListeners() : Reconfigurer les listeners capture-logs-unified.js:69:36
- testButtonClicks() : Tester les clics sur les boutons capture-logs-unified.js:69:36
🔍 [VALIDATION] Script de validation de la correction modale chargé capture-logs-unified.js:69:36
✅ [VALIDATION] Fonctions de validation disponibles: capture-logs-unified.js:69:36
- validateMainFix() : Valider la correction principale capture-logs-unified.js:69:36
- validateDOMElements() : Valider les éléments DOM capture-logs-unified.js:69:36
- validateListeners() : Valider les listeners capture-logs-unified.js:69:36
- runCompleteValidation() : Validation complète capture-logs-unified.js:69:36
- testEndToEnd() : Test end-to-end complet capture-logs-unified.js:69:36
🧪 [TEST] Script de test pour la correction du fork modal chargé capture-logs-unified.js:69:36
✅ [TEST] Fonctions de test disponibles: capture-logs-unified.js:69:36
- testDropDateDetection() : Tester la détection de date capture-logs-unified.js:69:36
- testForkModalWithDate() : Tester le modal avec date capture-logs-unified.js:69:36
- testCompleteForkFlow() : Tester le flux complet capture-logs-unified.js:69:36
- diagnoseForkModalIssues() : Diagnostic complet capture-logs-unified.js:69:36
🧪 [DATE-TEST] Script de validation des corrections de date chargé capture-logs-unified.js:69:36
✅ [DATE-TEST] Fonctions de validation des dates disponibles: capture-logs-unified.js:69:36
- testSafeDateConversion() : Tester la conversion sécurisée capture-logs-unified.js:69:36
- testForkDateLogic() : Tester la logique de fork capture-logs-unified.js:69:36
- testTimezoneEnhancements() : Tester les améliorations timezone capture-logs-unified.js:69:36
- testDropDateDetectionWithTimezone() : Tester la détection avec timezone capture-logs-unified.js:69:36
- runDateValidationSuite() : Suite complète de validation capture-logs-unified.js:69:36
🔧 [GRIP-FIX] Script de diagnostic et correction des GRIP handles chargé capture-logs-unified.js:69:36
✅ [GRIP-FIX] Fonctions de diagnostic et correction disponibles: capture-logs-unified.js:69:36
- diagnoseGripHandles() : Diagnostic complet capture-logs-unified.js:69:36
- forceRecreateGripHandles() : Recréation forcée capture-logs-unified.js:69:36
- testSpecificGrip(assignmentId) : Test spécifique capture-logs-unified.js:69:36
- autoFixGripIssues() : Correction automatique capture-logs-unified.js:69:36
- startGripMonitoring() : Surveillance continue capture-logs-unified.js:69:36
XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 54ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 43ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 55ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 52ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 85ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 52ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 55ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 93ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 87ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 95ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 93ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 92ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 125ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 122ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 122ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 137ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 138ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 135ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 163ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 165ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 165ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 179ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 176ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 172ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 203ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 200ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 219ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 203ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 200ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 242ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 238ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 222ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 243ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 242ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 262ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 256ms]

GET
http://localhost:5173/favicon.svg
[HTTP/1.1 200 OK 0ms]

🚀 [TEST] Lancement du diagnostic automatique... capture-logs-unified.js:69:36
🔍 [DIAGNOSTIC] Diagnostic des problèmes de modale... capture-logs-unified.js:69:36
📋 [DIAGNOSTIC] Éléments DOM: 
Object { modal: false, closeBtn: false, cancelBtn: false, confirmBtn: false, radioButtons: 0 }
capture-logs-unified.js:69:36
📋 [DIAGNOSTIC] TeamCalendarApp: 
Object { exists: false, hasModalManager: false, hasOpenFunction: false, hasHandleFunction: false }
capture-logs-unified.js:69:36
XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 45ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 76ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 33ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 65ms]

🚀 [VALIDATION] Lancement de la validation automatique... capture-logs-unified.js:69:36
🚀 [VALIDATION] Lancement de la validation complète... capture-logs-unified.js:69:36
🔍 [VALIDATION] Validation de la correction principale... capture-logs-unified.js:69:36
❌ [VALIDATION] TeamCalendarApp non disponible capture-logs-unified.js:69:36
🔍 [VALIDATION] Validation des éléments DOM... capture-logs-unified.js:69:36
📋 [VALIDATION] Éléments DOM: 
Object { modal: false, closeBtn: false, cancelBtn: false, confirmBtn: false, radioButtons: false }
capture-logs-unified.js:69:36
❌ [VALIDATION] Éléments DOM manquants capture-logs-unified.js:69:36
🔍 [VALIDATION] Validation des listeners... capture-logs-unified.js:69:36
❌ [VALIDATION] Erreur lors de setupAssignmentContextModal: TypeError: can't access property "ModalManager", window.TeamCalendarApp is undefined
    validateListeners validate-modal-fix.js:87
    runCompleteValidation validate-modal-fix.js:103
    <anonymous> validate-modal-fix.js:186
    setTimeout handler* validate-modal-fix.js:184
capture-logs-unified.js:69:36
📊 [VALIDATION] Résultats de validation: 
Object { mainFix: false, domElements: false, listeners: false }
capture-logs-unified.js:69:36
❌ [VALIDATION] Certaines validations ont échoué capture-logs-unified.js:69:36
🔧 [VALIDATION] Actions recommandées: capture-logs-unified.js:69:36
- Vérifier que TeamCalendarApp et ModalManager sont correctement initialisés capture-logs-unified.js:69:36
- Vérifier que la modale assignment-context-modal existe dans le DOM capture-logs-unified.js:69:36
- Vérifier les erreurs dans setupAssignmentContextModal capture-logs-unified.js:69:36
🚀 [TEST] Lancement du diagnostic automatique... capture-logs-unified.js:69:36
🔍 [DIAGNOSTIC] Diagnostic complet du fork modal... capture-logs-unified.js:69:36
📊 [DIAGNOSTIC] Résultats: 
Object { teamCalendarApp: false, detectDropDateFunction: false, showConfirmationMenuFunction: false, handleRegularAssignmentDropFunction: false, regularAssignments: 0, employees: 0 }
capture-logs-unified.js:69:36
❌ [DIAGNOSTIC] Fonctions manquantes détectées capture-logs-unified.js:69:36
XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 53ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 63ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 54ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 53ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 55ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 39ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 42ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 54ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 97ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 98ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 95ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 97ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 61ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 61ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 34ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 53ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 44ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 41ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 57ms]

🚀 [DATE-TEST] Lancement de la validation automatique des dates... capture-logs-unified.js:69:36
🚀 [DATE-TEST] Lancement de la suite de validation des dates... capture-logs-unified.js:69:36

🧪 [DATE-TEST] Exécution: Conversion de date sécurisée capture-logs-unified.js:69:36
🧪 [DATE-TEST] Test de la conversion de date sécurisée... capture-logs-unified.js:69:36
❌ [DATE-TEST] formatDateToKey non disponible capture-logs-unified.js:69:36

🧪 [DATE-TEST] Exécution: Logique de fork avec dates capture-logs-unified.js:69:36
🧪 [DATE-TEST] Test de la logique de fork avec dates... capture-logs-unified.js:69:36
❌ [DATE-TEST] Données insuffisantes pour le test capture-logs-unified.js:69:36

🧪 [DATE-TEST] Exécution: Améliorations timezone capture-logs-unified.js:69:36
🧪 [DATE-TEST] Test des améliorations de timezone... capture-logs-unified.js:69:36
⚠️ [DATE-TEST] Sélecteur de timezone non trouvé (normal si modal fermé) capture-logs-unified.js:69:36

🧪 [DATE-TEST] Exécution: Détection de date avec timezone capture-logs-unified.js:69:36
🧪 [DATE-TEST] Test de la détection de date avec timezone... capture-logs-unified.js:69:36
❌ [DATE-TEST] detectDropDateFromPosition non disponible capture-logs-unified.js:69:36

📊 [DATE-TEST] Résultats de la validation: capture-logs-unified.js:69:36
❌ Conversion de date sécurisée: ÉCHOUÉ capture-logs-unified.js:69:36
❌ Logique de fork avec dates: ÉCHOUÉ capture-logs-unified.js:69:36
✅ Améliorations timezone: PASSÉ capture-logs-unified.js:69:36
❌ Détection de date avec timezone: ÉCHOUÉ capture-logs-unified.js:69:36

⚠️ [DATE-TEST] Certaines validations ont échoué capture-logs-unified.js:69:36
🔧 [DATE-TEST] Vérifiez les logs ci-dessus pour les détails capture-logs-unified.js:69:36
XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 57ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 59ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 60ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 82ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 63ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 58ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 94ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 96ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 96ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 88ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 89ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 132ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 127ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 112ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 146ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 145ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 138ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 169ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 166ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 145ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 169ms]

🚀 [GRIP-FIX] Lancement du diagnostic automatique... capture-logs-unified.js:69:36
🔍 [GRIP-FIX] Diagnostic complet des GRIP handles... capture-logs-unified.js:69:36
📊 [GRIP-FIX] Total shifts trouvés: 0 capture-logs-unified.js:69:36
📊 [GRIP-FIX] Résultats du diagnostic: 
Object { totalShifts: 0, regularShifts: 0, gripsInDOM: 0, gripsWithEvents: 0, gripsResponsive: 0, issues: [] }
capture-logs-unified.js:69:36
XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 50ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 51ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 59ms]

XHRPOST
http://localhost:3001/api/logs
[HTTP/1.1 201 Created 50ms]

