[15/07/2025 14:56:21] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[15/07/2025 14:56:21] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[15/07/2025 14:56:21] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[15/07/2025 14:56:21] [FRONTEND] [LOG] [LogsPage] Fermeture de la connexion SSE.
[15/07/2025 14:56:21] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:56:21.213Z"
}
[15/07/2025 14:56:21] [BACKEND] [INFO] [LOGS] Client connecté: 1752605781212
[15/07/2025 14:56:16] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[15/07/2025 14:56:16] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[15/07/2025 14:56:16] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[15/07/2025 14:56:17] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[15/07/2025 14:56:17] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[15/07/2025 14:56:17] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[15/07/2025 14:56:17] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[15/07/2025 14:56:17] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[15/07/2025 14:56:17] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[15/07/2025 14:56:17] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[15/07/2025 14:56:17] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[15/07/2025 14:56:17] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[15/07/2025 14:56:17] [FRONTEND] [LOG] [LogsPage] Fermeture de la connexion SSE.
[15/07/2025 14:56:17] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:56:16.752Z",
  "originalArgs": [
    "[query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ]
}
[15/07/2025 14:56:17] [BACKEND] [INFO] [LOGS] Client connecté: 1752605776750
[15/07/2025 14:56:17] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:56:16.676Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 14:56:17] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[15/07/2025 14:56:17] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:56:16.815Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 14:56:17] [BACKEND] [INFO] ✅ Requête exécutée en 129ms: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
[15/07/2025 14:56:17] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 129ms: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:56:16.881Z",
  "originalArgs": [
    "✅ Requête exécutée en 129ms:",
    "SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ]
}
[15/07/2025 14:56:17] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:56:16.893Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 14:56:17] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[15/07/2025 14:56:17] [FRONTEND] [LOG] 🚀 [Agenda] Initialisation TeamCalendarApp...
[15/07/2025 14:56:17] [FRONTEND] [LOG] ✅ [clearRenderCache] Cache de rendu nettoyé
[15/07/2025 14:56:17] [FRONTEND] [LOG] 🚀 [init] Initialisation de TeamCalendarApp...
[15/07/2025 14:56:17] [FRONTEND] [LOG] 🧹 [clearRenderCache] Nettoyage du cache de rendu
[15/07/2025 14:56:17] [FRONTEND] [LOG] 🔌 [ConnectionIndicator] Création de l'indicateur de connexion
[15/07/2025 14:56:17] [FRONTEND] [LOG] 🔌 [init] Indicateur de connexion initialisé
[15/07/2025 14:56:17] [FRONTEND] [LOG] 📂 [TeamCalendarApp] Chargement de l'état...
[15/07/2025 14:56:17] [FRONTEND] [LOG] 🌐 [TeamCalendarApp] Chargement depuis l'API...
[15/07/2025 14:56:17] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT e.*, et.name as template_name 
      FROM employees e 
      LEFT JOIN employee_templates et ON e.template_id = et.id 
      ORDER BY e.name
     {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:56:17.368Z",
  "originalArgs": [
    "[query] Executing query: \n      SELECT e.*, et.name as template_name \n      FROM employees e \n      LEFT JOIN employee_templates et ON e.template_id = et.id \n      ORDER BY e.name\n    ",
    {}
  ]
}
[15/07/2025 14:56:17] [BACKEND] [INFO] ✅ Requête exécutée en 49ms: 
      SELECT e.*, et.name as template_name 
      {}
[15/07/2025 14:56:17] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 49ms: 
      SELECT e.*, et.name as template_name 
      {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:56:17.417Z",
  "originalArgs": [
    "✅ Requête exécutée en 49ms:",
    "\n      SELECT e.*, et.name as template_name \n     ",
    {}
  ]
}
[15/07/2025 14:56:17] [FRONTEND] [LOG] 👥 [TeamCalendarApp] 5 employés chargés
[15/07/2025 14:56:17] [FRONTEND] [LOG] 📦 [loadState] Chargement de tous les shifts depuis la base de données...
[15/07/2025 14:56:17] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT s.*, e.name as employee_name, sp.label as post_label
      FROM shifts s
      LEFT JOIN employees e ON s.employee_id = e.id
      LEFT JOIN standard_posts sp ON s.post_id = sp.id
      ORDER BY s.date_key, e.name
     {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:56:17.445Z",
  "originalArgs": [
    "[query] Executing query: \n      SELECT s.*, e.name as employee_name, sp.label as post_label\n      FROM shifts s\n      LEFT JOIN employees e ON s.employee_id = e.id\n      LEFT JOIN standard_posts sp ON s.post_id = sp.id\n      ORDER BY s.date_key, e.name\n    ",
    {}
  ]
}
[15/07/2025 14:56:17] [BACKEND] [INFO] ✅ Requête exécutée en 55ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
[15/07/2025 14:56:17] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 55ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:56:17.500Z",
  "originalArgs": [
    "✅ Requête exécutée en 55ms:",
    "\n      SELECT s.*, e.name as employee_name, sp.lab",
    {}
  ]
}
[15/07/2025 14:56:17] [FRONTEND] [LOG] 📅 [loadState] 0 shifts chargés depuis la base de données
[15/07/2025 14:56:17] [FRONTEND] [LOG] ✅ [loadState] Shifts organisés dans this.data.schedule
[15/07/2025 14:56:17] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:56:17.523Z",
  "originalArgs": [
    "[query] Executing query: SELECT * FROM standard_posts ORDER BY label",
    {}
  ]
}
[15/07/2025 14:56:17] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 47ms: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:56:17.569Z",
  "originalArgs": [
    "✅ Requête exécutée en 47ms:",
    "SELECT * FROM standard_posts ORDER BY label",
    {}
  ]
}
[15/07/2025 14:56:17] [BACKEND] [INFO] ✅ Requête exécutée en 47ms: SELECT * FROM standard_posts ORDER BY label {}
[15/07/2025 14:56:17] [FRONTEND] [LOG] 🏢 [TeamCalendarApp] 5 postes standards chargés
[15/07/2025 14:56:17] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:56:17.598Z",
  "originalArgs": [
    "[query] Executing query: SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ]
}
[15/07/2025 14:56:17] [BACKEND] [INFO] ✅ Requête exécutée en 49ms: SELECT * FROM app_settings ORDER BY setting_key {}
[15/07/2025 14:56:17] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 49ms: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:56:17.648Z",
  "originalArgs": [
    "✅ Requête exécutée en 49ms:",
    "SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ]
}
[15/07/2025 14:56:17] [FRONTEND] [LOG] ⚙️ [loadState] Paramètres chargés depuis API: {}
[15/07/2025 14:56:18] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT 
        ra.id,
        ra.employee_id,
        ra.post_id,
        ra.days_of_week,
        ra.start_date,
        ra.end_date,
        ra.is_active,
        ra.excluded_dates,
        e.name as employee_name,
        sp.label as post_label,
        sp.hours as post_hours,
        sp.type as post_type,
        array_length(ra.days_of_week, 1) as days_count
      FROM regular_assignments ra
      LEFT JOIN employees e ON ra.employee_id = e.id
      LEFT JOIN standard_posts sp ON ra.post_id = sp.id
      WHERE ra.is_active = true
      ORDER BY e.name, sp.label, ra.start_date
     {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:56:17.675Z",
  "originalArgs": [
    "[query] Executing query: \n      SELECT \n        ra.id,\n        ra.employee_id,\n        ra.post_id,\n        ra.days_of_week,\n        ra.start_date,\n        ra.end_date,\n        ra.is_active,\n        ra.excluded_dates,\n        e.name as employee_name,\n        sp.label as post_label,\n        sp.hours as post_hours,\n        sp.type as post_type,\n        array_length(ra.days_of_week, 1) as days_count\n      FROM regular_assignments ra\n      LEFT JOIN employees e ON ra.employee_id = e.id\n      LEFT JOIN standard_posts sp ON ra.post_id = sp.id\n      WHERE ra.is_active = true\n      ORDER BY e.name, sp.label, ra.start_date\n    ",
    {}
  ]
}
[15/07/2025 14:56:18] [BACKEND] [INFO] ✅ Requête exécutée en 52ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
[15/07/2025 14:56:18] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 52ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:56:17.727Z",
  "originalArgs": [
    "✅ Requête exécutée en 52ms:",
    "\n      SELECT \n        ra.id,\n        ra.employee_",
    {}
  ]
}
[15/07/2025 14:56:18] [FRONTEND] [LOG] 📋 [loadState] 0/0 assignations régulières valides chargées et normalisées
[15/07/2025 14:56:18] [FRONTEND] [LOG] ✅ [fixInvalidAssignmentDates] Aucune date invalide trouvée
[15/07/2025 14:56:18] [FRONTEND] [LOG] 🔧 [fixInvalidAssignmentDates] Vérification des dates invalides...
[15/07/2025 14:56:18] [FRONTEND] [LOG] 🧹 [cleanupDuplicateRegularShifts] Nettoyage des doublons de shifts réguliers...
[15/07/2025 14:56:18] [FRONTEND] [LOG] 📋 [loadState] Assignations régulières chargées (application différée)
[15/07/2025 14:56:18] [FRONTEND] [LOG] ✅ [cleanupDuplicateRegularShifts] Aucun doublon détecté
[15/07/2025 14:56:18] [FRONTEND] [LOG] 🧹 [cleanupInvalidAssignmentIds] Nettoyage des assignment_id invalides...
[15/07/2025 14:56:18] [FRONTEND] [LOG] ✅ [cleanupInvalidAssignmentIds] Aucun assignment_id invalide détecté
[15/07/2025 14:56:18] [FRONTEND] [LOG] ✅ [loadState] État chargé depuis l'API avec succès
[15/07/2025 14:56:18] [FRONTEND] [LOG] 🔍 [init] Recherche du bouton des paramètres dans le DOM
[15/07/2025 14:56:18] [FRONTEND] [LOG] ✅ [init] Icône paramètres trouvée: [Objet non sérialisable]
[15/07/2025 14:56:18] [FRONTEND] [LOG] 🔍 [init] Bouton des paramètres trouvé ? true
[15/07/2025 14:56:18] [FRONTEND] [LOG] 🔍 [init] Attachement du listener click sur le bouton des paramètres
[15/07/2025 14:56:18] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'
         {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:56:17.919Z",
  "originalArgs": [
    "[query] Executing query: \n            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'\n        ",
    {}
  ]
}
[15/07/2025 14:56:18] [BACKEND] [INFO] [LOGGER] 📋 [GET /api/employee-order] Récupération ordre des employés
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:56:17.918Z",
  "originalArgs": [
    "📋 [GET /api/employee-order] Récupération ordre des employés"
  ]
}
[15/07/2025 14:56:18] [BACKEND] [INFO] 📋 [GET /api/employee-order] Récupération ordre des employés
[15/07/2025 14:56:18] [FRONTEND] [LOG] ✅ [setupNavigationListeners] Écouteurs de navigation attachés
[15/07/2025 14:56:18] [FRONTEND] [LOG] ✅ Boutons de navigation configurés
[15/07/2025 14:56:18] [FRONTEND] [LOG] 🎯 [setupViewNavigationListeners] Configuration des boutons de navigation...
[15/07/2025 14:56:18] [FRONTEND] [LOG] 🔧 [ModalManager] Configuration des gestionnaires de modal
[15/07/2025 14:56:18] [BACKEND] [INFO] [LOGGER] 📋 [GET /api/employee-order] Aucun ordre personnalisé trouvé
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:56:17.967Z",
  "originalArgs": [
    "📋 [GET /api/employee-order] Aucun ordre personnalisé trouvé"
  ]
}
[15/07/2025 14:56:18] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 47ms: 
            SELECT setting_value FROM app_setting {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:56:17.966Z",
  "originalArgs": [
    "✅ Requête exécutée en 47ms:",
    "\n            SELECT setting_value FROM app_setting",
    {}
  ]
}
[15/07/2025 14:56:18] [FRONTEND] [LOG] 🔧 [setupGeneralSettings] Configuration des paramètres généraux
[15/07/2025 14:56:18] [FRONTEND] [LOG] ✅ [ModalManager] Gestionnaires de modal configurés
[15/07/2025 14:56:18] [FRONTEND] [LOG] 🔧 [setupGeneralSettings] Création du conteneur de fuseau horaire
[15/07/2025 14:56:18] [FRONTEND] [LOG] 📋 [loadEmployeeOrder] Chargement ordre des employés
[15/07/2025 14:56:18] [FRONTEND] [LOG] ✅ [setupGlobalEscapeHandler] Gestionnaire ESC global configuré
[15/07/2025 14:56:18] [FRONTEND] [WARN] ⚠️ [loadEmployeeOrder] Réponse API sans tableau employeeOrder
[15/07/2025 14:56:18] [FRONTEND] [LOG] 🔧 [init] Forçage du rendu initial...
[15/07/2025 14:56:18] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[15/07/2025 14:56:18] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[15/07/2025 14:56:18] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[15/07/2025 14:56:18] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[15/07/2025 14:56:18] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[15/07/2025 14:56:18] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 14:56:18] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 14:56:18] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 60 zones trouvées
[15/07/2025 14:56:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 60 zones de drop disponibles
[15/07/2025 14:56:18] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 14:56:18] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Matin: [1, 2, 3, 4, 5]
[15/07/2025 14:56:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste de49cbe3-b7ae-42ea-8838-2a1ad58c9cdc configuré comme draggable
[15/07/2025 14:56:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste de49cbe3-b7ae-42ea-8838-2a1ad58c9cdc
[15/07/2025 14:56:18] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Nuit: [1, 2, 3, 4, 5]
[15/07/2025 14:56:18] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Soir: [1, 2, 3, 4, 5]
[15/07/2025 14:56:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ad3c79b4-ffab-4765-a4be-c07e8ee69614
[15/07/2025 14:56:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ad3c79b4-ffab-4765-a4be-c07e8ee69614 configuré comme draggable
[15/07/2025 14:56:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ed075ee3-b8ec-41fb-bc32-60c24f2bf968 configuré comme draggable
[15/07/2025 14:56:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ed075ee3-b8ec-41fb-bc32-60c24f2bf968
[15/07/2025 14:56:18] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE1: [0, 6]
[15/07/2025 14:56:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 5c402570-6010-4a05-a997-c3decfa046c9 configuré comme draggable
[15/07/2025 14:56:18] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE2: [0, 6]
[15/07/2025 14:56:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 5c402570-6010-4a05-a997-c3decfa046c9
[15/07/2025 14:56:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste e1a23d95-75e6-430e-9ad1-7116552fc9e8 configuré comme draggable
[15/07/2025 14:56:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste e1a23d95-75e6-430e-9ad1-7116552fc9e8
[15/07/2025 14:56:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste de49cbe3-b7ae-42ea-8838-2a1ad58c9cdc configuré comme draggable
[15/07/2025 14:56:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste de49cbe3-b7ae-42ea-8838-2a1ad58c9cdc
[15/07/2025 14:56:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ad3c79b4-ffab-4765-a4be-c07e8ee69614 configuré comme draggable
[15/07/2025 14:56:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ad3c79b4-ffab-4765-a4be-c07e8ee69614
[15/07/2025 14:56:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ed075ee3-b8ec-41fb-bc32-60c24f2bf968 configuré comme draggable
[15/07/2025 14:56:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ed075ee3-b8ec-41fb-bc32-60c24f2bf968
[15/07/2025 14:56:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 5c402570-6010-4a05-a997-c3decfa046c9 configuré comme draggable
[15/07/2025 14:56:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 5c402570-6010-4a05-a997-c3decfa046c9
[15/07/2025 14:56:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste e1a23d95-75e6-430e-9ad1-7116552fc9e8
[15/07/2025 14:56:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste e1a23d95-75e6-430e-9ad1-7116552fc9e8 configuré comme draggable
[15/07/2025 14:56:18] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 14:56:18] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 14:56:18] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 14:56:18] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 14:56:18] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 14:56:18] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 14:56:18] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 14:56:19] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 14:56:19] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (60 zones)
[15/07/2025 14:56:19] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[15/07/2025 14:56:19] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[15/07/2025 14:56:19] [FRONTEND] [LOG] 🔧 [init] Configuration du drag & drop des postes...
[15/07/2025 14:56:19] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 14:56:19] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 14:56:19] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 60 zones trouvées
[15/07/2025 14:56:19] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 60 zones de drop disponibles
[15/07/2025 14:56:19] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 14:56:19] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste de49cbe3-b7ae-42ea-8838-2a1ad58c9cdc configuré comme draggable
[15/07/2025 14:56:19] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste de49cbe3-b7ae-42ea-8838-2a1ad58c9cdc
[15/07/2025 14:56:19] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ad3c79b4-ffab-4765-a4be-c07e8ee69614 configuré comme draggable
[15/07/2025 14:56:19] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ad3c79b4-ffab-4765-a4be-c07e8ee69614
[15/07/2025 14:56:19] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ed075ee3-b8ec-41fb-bc32-60c24f2bf968 configuré comme draggable
[15/07/2025 14:56:19] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ed075ee3-b8ec-41fb-bc32-60c24f2bf968
[15/07/2025 14:56:19] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 5c402570-6010-4a05-a997-c3decfa046c9 configuré comme draggable
[15/07/2025 14:56:19] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 5c402570-6010-4a05-a997-c3decfa046c9
[15/07/2025 14:56:19] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste e1a23d95-75e6-430e-9ad1-7116552fc9e8 configuré comme draggable
[15/07/2025 14:56:19] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste e1a23d95-75e6-430e-9ad1-7116552fc9e8
[15/07/2025 14:56:19] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste de49cbe3-b7ae-42ea-8838-2a1ad58c9cdc configuré comme draggable
[15/07/2025 14:56:19] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ad3c79b4-ffab-4765-a4be-c07e8ee69614 configuré comme draggable
[15/07/2025 14:56:19] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste de49cbe3-b7ae-42ea-8838-2a1ad58c9cdc
[15/07/2025 14:56:19] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ad3c79b4-ffab-4765-a4be-c07e8ee69614
[15/07/2025 14:56:19] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ed075ee3-b8ec-41fb-bc32-60c24f2bf968 configuré comme draggable
[15/07/2025 14:56:19] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ed075ee3-b8ec-41fb-bc32-60c24f2bf968
[15/07/2025 14:56:19] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 5c402570-6010-4a05-a997-c3decfa046c9 configuré comme draggable
[15/07/2025 14:56:19] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste e1a23d95-75e6-430e-9ad1-7116552fc9e8 configuré comme draggable
[15/07/2025 14:56:19] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 5c402570-6010-4a05-a997-c3decfa046c9
[15/07/2025 14:56:19] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste e1a23d95-75e6-430e-9ad1-7116552fc9e8
[15/07/2025 14:56:19] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 14:56:19] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 14:56:19] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 14:56:19] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 14:56:19] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 14:56:19] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 14:56:19] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 14:56:19] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 14:56:19] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 14:56:19] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 14:56:19] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 14:56:19] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 14:56:19] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 14:56:19] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (60 zones)
[15/07/2025 14:56:19] [FRONTEND] [LOG] ℹ️ [setupEmployeeDragDrop] Fonction désactivée - Utiliser setupDragAndDrop
[15/07/2025 14:56:19] [FRONTEND] [LOG] ✅ [init] Initialisation terminée avec succès
[15/07/2025 14:56:19] [FRONTEND] [LOG] 🎉 [Agenda] Application fullscreen initialisée avec succès
[15/07/2025 14:56:19] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] Résultats: 0 ajoutés, 0 validés, 0 corrigés
[15/07/2025 14:56:19] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 14:56:19] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 14:56:19] [FRONTEND] [LOG] 🔍 [diagnoseRegularAssignmentGrips] Diagnostic des grips d'attributions régulières
[15/07/2025 14:56:19] [FRONTEND] [LOG] 📊 [diagnoseRegularAssignmentGrips] Statistiques: {"totalShifts":0,"regularShifts":0,"regularAssignments":0}
[15/07/2025 14:56:19] [FRONTEND] [LOG] 🎯 [diagnoseRegularAssignmentGrips] Grips trouvés dans le DOM: 0
[15/07/2025 14:56:19] [FRONTEND] [LOG] ⚠️ [diagnoseRegularAssignmentGrips] Aucun shift régulier trouvé - Ré-application des attributions...
[15/07/2025 14:56:19] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 14:56:19] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 14:56:20] [BACKEND] [INFO] [LOGS] Client déconnecté: 1752605776750
[15/07/2025 14:56:20] [FRONTEND] [ERROR] [LogsPage] Erreur SSE: {"isTrusted":true}
[15/07/2025 14:56:20] [FRONTEND] [DEBUG] [vite] connected.
[15/07/2025 14:56:21] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[15/07/2025 14:56:21] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[15/07/2025 14:56:21] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[15/07/2025 14:56:21] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[15/07/2025 14:56:21] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[15/07/2025 14:56:21] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[15/07/2025 14:56:21] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[15/07/2025 14:56:21] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[15/07/2025 14:56:21] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[15/07/2025 14:56:21] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[15/07/2025 14:56:21] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[15/07/2025 14:56:21] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[15/07/2025 14:56:21] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[15/07/2025 14:56:21] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[15/07/2025 14:56:21] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[15/07/2025 14:56:21] [FRONTEND] [LOG] [LogsPage] Fermeture de la connexion SSE.
[15/07/2025 14:56:21] [BACKEND] [INFO] [LOGS] Client connecté: 1752605781212
[15/07/2025 14:56:21] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[15/07/2025 14:56:21] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:56:21.213Z",
  "originalArgs": [
    "[query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ]
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 129ms: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 129ms:",
    "SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:56:21.342Z"
}
[15/07/2025 14:56:21] [BACKEND] [INFO] ✅ Requête exécutée en 129ms: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
[15/07/2025 14:56:26] [FRONTEND] [DEBUG] [vite] connected.
[15/07/2025 14:56:26] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[15/07/2025 14:56:26] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[15/07/2025 14:56:26] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[15/07/2025 14:56:26] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[15/07/2025 14:56:26] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[15/07/2025 14:56:26] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[15/07/2025 14:56:26] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[15/07/2025 14:56:26] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[15/07/2025 14:56:27] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[15/07/2025 14:56:27] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[15/07/2025 14:56:27] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[15/07/2025 14:56:27] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[15/07/2025 14:56:27] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[15/07/2025 14:56:27] [FRONTEND] [LOG] 🧹 [Agenda] Nettoyage composant Agenda
[15/07/2025 14:56:27] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[15/07/2025 14:56:27] [FRONTEND] [LOG] 🔄 [Agenda] Réinitialisation nécessaire (employés non chargés)
[15/07/2025 14:56:27] [FRONTEND] [LOG] 🚀 [Agenda] Initialisation TeamCalendarApp...
[15/07/2025 14:56:27] [FRONTEND] [LOG] ✅ [clearRenderCache] Cache de rendu nettoyé
[15/07/2025 14:56:27] [FRONTEND] [LOG] 🧹 [clearRenderCache] Nettoyage du cache de rendu
[15/07/2025 14:56:27] [FRONTEND] [LOG] 🚀 [init] Initialisation de TeamCalendarApp...
[15/07/2025 14:56:27] [FRONTEND] [LOG] 🔌 [ConnectionIndicator] Création de l'indicateur de connexion
[15/07/2025 14:56:27] [FRONTEND] [LOG] 📂 [TeamCalendarApp] Chargement de l'état...
[15/07/2025 14:56:27] [FRONTEND] [LOG] 🔌 [init] Indicateur de connexion initialisé
[15/07/2025 14:56:27] [FRONTEND] [LOG] 🌐 [TeamCalendarApp] Chargement depuis l'API...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT e.*, et.name as template_name 
      FROM employees e 
      LEFT JOIN employee_templates et ON e.template_id = et.id 
      ORDER BY e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT e.*, et.name as template_name \n      FROM employees e \n      LEFT JOIN employee_templates et ON e.template_id = et.id \n      ORDER BY e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:56:27.045Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 59ms: 
      SELECT e.*, et.name as template_name 
      {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 59ms:",
    "\n      SELECT e.*, et.name as template_name \n     ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:56:27.103Z"
}
[15/07/2025 14:56:27] [BACKEND] [INFO] ✅ Requête exécutée en 59ms: 
      SELECT e.*, et.name as template_name 
      {}
[15/07/2025 14:56:27] [FRONTEND] [LOG] 👥 [TeamCalendarApp] 5 employés chargés
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT s.*, e.name as employee_name, sp.label as post_label
      FROM shifts s
      LEFT JOIN employees e ON s.employee_id = e.id
      LEFT JOIN standard_posts sp ON s.post_id = sp.id
      ORDER BY s.date_key, e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT s.*, e.name as employee_name, sp.label as post_label\n      FROM shifts s\n      LEFT JOIN employees e ON s.employee_id = e.id\n      LEFT JOIN standard_posts sp ON s.post_id = sp.id\n      ORDER BY s.date_key, e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:56:27.149Z"
}
[15/07/2025 14:56:27] [FRONTEND] [LOG] 📦 [loadState] Chargement de tous les shifts depuis la base de données...
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 39ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 39ms:",
    "\n      SELECT s.*, e.name as employee_name, sp.lab",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:56:27.188Z"
}
[15/07/2025 14:56:27] [BACKEND] [INFO] ✅ Requête exécutée en 39ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
[15/07/2025 14:56:27] [FRONTEND] [LOG] 📅 [loadState] 0 shifts chargés depuis la base de données
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:56:27.252Z"
}
[15/07/2025 14:56:27] [FRONTEND] [LOG] ✅ [loadState] Shifts organisés dans this.data.schedule
[15/07/2025 14:56:27] [BACKEND] [INFO] ✅ Requête exécutée en 40ms: SELECT * FROM standard_posts ORDER BY label {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 40ms: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 40ms:",
    "SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:56:27.293Z"
}
[15/07/2025 14:56:27] [FRONTEND] [LOG] 🏢 [TeamCalendarApp] 5 postes standards chargés
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:56:27.346Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 37ms: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 37ms:",
    "SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:56:27.383Z"
}
[15/07/2025 14:56:27] [BACKEND] [INFO] ✅ Requête exécutée en 37ms: SELECT * FROM app_settings ORDER BY setting_key {}
[15/07/2025 14:56:27] [FRONTEND] [LOG] ⚙️ [loadState] Paramètres chargés depuis API: {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT 
        ra.id,
        ra.employee_id,
        ra.post_id,
        ra.days_of_week,
        ra.start_date,
        ra.end_date,
        ra.is_active,
        ra.excluded_dates,
        e.name as employee_name,
        sp.label as post_label,
        sp.hours as post_hours,
        sp.type as post_type,
        array_length(ra.days_of_week, 1) as days_count
      FROM regular_assignments ra
      LEFT JOIN employees e ON ra.employee_id = e.id
      LEFT JOIN standard_posts sp ON ra.post_id = sp.id
      WHERE ra.is_active = true
      ORDER BY e.name, sp.label, ra.start_date
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT \n        ra.id,\n        ra.employee_id,\n        ra.post_id,\n        ra.days_of_week,\n        ra.start_date,\n        ra.end_date,\n        ra.is_active,\n        ra.excluded_dates,\n        e.name as employee_name,\n        sp.label as post_label,\n        sp.hours as post_hours,\n        sp.type as post_type,\n        array_length(ra.days_of_week, 1) as days_count\n      FROM regular_assignments ra\n      LEFT JOIN employees e ON ra.employee_id = e.id\n      LEFT JOIN standard_posts sp ON ra.post_id = sp.id\n      WHERE ra.is_active = true\n      ORDER BY e.name, sp.label, ra.start_date\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:56:27.420Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 47ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 47ms:",
    "\n      SELECT \n        ra.id,\n        ra.employee_",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:56:27.467Z"
}
[15/07/2025 14:56:27] [BACKEND] [INFO] ✅ Requête exécutée en 47ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
[15/07/2025 14:56:27] [FRONTEND] [LOG] 📋 [loadState] 0/0 assignations régulières valides chargées et normalisées
[15/07/2025 14:56:27] [FRONTEND] [LOG] 🔧 [fixInvalidAssignmentDates] Vérification des dates invalides...
[15/07/2025 14:56:27] [FRONTEND] [LOG] 🧹 [cleanupDuplicateRegularShifts] Nettoyage des doublons de shifts réguliers...
[15/07/2025 14:56:27] [BACKEND] [INFO] 📋 [GET /api/employee-order] Récupération ordre des employés
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'
         {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'\n        ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:56:27.532Z"
}
[15/07/2025 14:56:27] [FRONTEND] [LOG] ✅ [fixInvalidAssignmentDates] Aucune date invalide trouvée
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📋 [GET /api/employee-order] Récupération ordre des employés
  Data: {
  "originalArgs": [
    "📋 [GET /api/employee-order] Récupération ordre des employés"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:56:27.531Z"
}
[15/07/2025 14:56:27] [FRONTEND] [LOG] 📋 [loadState] Assignations régulières chargées (application différée)
[15/07/2025 14:56:27] [FRONTEND] [LOG] ✅ [cleanupDuplicateRegularShifts] Aucun doublon détecté
[15/07/2025 14:56:27] [FRONTEND] [LOG] 🧹 [cleanupInvalidAssignmentIds] Nettoyage des assignment_id invalides...
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 47ms: 
            SELECT setting_value FROM app_setting {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 47ms:",
    "\n            SELECT setting_value FROM app_setting",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:56:27.579Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📋 [GET /api/employee-order] Aucun ordre personnalisé trouvé
  Data: {
  "originalArgs": [
    "📋 [GET /api/employee-order] Aucun ordre personnalisé trouvé"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:56:27.580Z"
}
[15/07/2025 14:56:27] [FRONTEND] [LOG] ✅ [cleanupInvalidAssignmentIds] Aucun assignment_id invalide détecté
[15/07/2025 14:56:27] [FRONTEND] [LOG] ✅ [init] Icône paramètres trouvée: [Objet non sérialisable]
[15/07/2025 14:56:27] [FRONTEND] [LOG] ✅ [loadState] État chargé depuis l'API avec succès
[15/07/2025 14:56:27] [FRONTEND] [LOG] 🔍 [init] Recherche du bouton des paramètres dans le DOM
[15/07/2025 14:56:27] [FRONTEND] [LOG] 🔍 [init] Bouton des paramètres trouvé ? true
[15/07/2025 14:56:27] [FRONTEND] [LOG] 🔍 [init] Attachement du listener click sur le bouton des paramètres
[15/07/2025 14:56:27] [FRONTEND] [LOG] ✅ [setupNavigationListeners] Écouteurs de navigation attachés
[15/07/2025 14:56:27] [FRONTEND] [LOG] 🎯 [setupViewNavigationListeners] Configuration des boutons de navigation...
[15/07/2025 14:56:27] [FRONTEND] [LOG] ✅ Boutons de navigation configurés
[15/07/2025 14:56:27] [FRONTEND] [LOG] 🔧 [ModalManager] Configuration des gestionnaires de modal
[15/07/2025 14:56:27] [FRONTEND] [LOG] ✅ [ModalManager] Gestionnaires de modal configurés
[15/07/2025 14:56:27] [FRONTEND] [LOG] 🔧 [setupGeneralSettings] Configuration des paramètres généraux
[15/07/2025 14:56:27] [FRONTEND] [LOG] 🔧 [setupGeneralSettings] Création du conteneur de fuseau horaire
[15/07/2025 14:56:28] [FRONTEND] [LOG] ✅ [setupGlobalEscapeHandler] Gestionnaire ESC global configuré
[15/07/2025 14:56:28] [FRONTEND] [LOG] 📋 [loadEmployeeOrder] Chargement ordre des employés
[15/07/2025 14:56:28] [FRONTEND] [WARN] ⚠️ [loadEmployeeOrder] Réponse API sans tableau employeeOrder
[15/07/2025 14:56:28] [FRONTEND] [LOG] 🔧 [init] Forçage du rendu initial...
[15/07/2025 14:56:28] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[15/07/2025 14:56:28] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[15/07/2025 14:56:28] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[15/07/2025 14:56:28] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[15/07/2025 14:56:28] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[15/07/2025 14:56:28] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 14:56:28] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 14:56:28] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 60 zones trouvées
[15/07/2025 14:56:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 60 zones de drop disponibles
[15/07/2025 14:56:28] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 14:56:28] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Matin: [1, 2, 3, 4, 5]
[15/07/2025 14:56:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste de49cbe3-b7ae-42ea-8838-2a1ad58c9cdc configuré comme draggable
[15/07/2025 14:56:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste de49cbe3-b7ae-42ea-8838-2a1ad58c9cdc
[15/07/2025 14:56:28] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Nuit: [1, 2, 3, 4, 5]
[15/07/2025 14:56:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ad3c79b4-ffab-4765-a4be-c07e8ee69614 configuré comme draggable
[15/07/2025 14:56:28] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Soir: [1, 2, 3, 4, 5]
[15/07/2025 14:56:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ad3c79b4-ffab-4765-a4be-c07e8ee69614
[15/07/2025 14:56:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ed075ee3-b8ec-41fb-bc32-60c24f2bf968 configuré comme draggable
[15/07/2025 14:56:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ed075ee3-b8ec-41fb-bc32-60c24f2bf968
[15/07/2025 14:56:28] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE1: [0, 6]
[15/07/2025 14:56:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 5c402570-6010-4a05-a997-c3decfa046c9 configuré comme draggable
[15/07/2025 14:56:28] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE2: [0, 6]
[15/07/2025 14:56:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 5c402570-6010-4a05-a997-c3decfa046c9
[15/07/2025 14:56:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste e1a23d95-75e6-430e-9ad1-7116552fc9e8 configuré comme draggable
[15/07/2025 14:56:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste e1a23d95-75e6-430e-9ad1-7116552fc9e8
[15/07/2025 14:56:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste de49cbe3-b7ae-42ea-8838-2a1ad58c9cdc configuré comme draggable
[15/07/2025 14:56:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ad3c79b4-ffab-4765-a4be-c07e8ee69614 configuré comme draggable
[15/07/2025 14:56:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste de49cbe3-b7ae-42ea-8838-2a1ad58c9cdc
[15/07/2025 14:56:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ad3c79b4-ffab-4765-a4be-c07e8ee69614
[15/07/2025 14:56:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ed075ee3-b8ec-41fb-bc32-60c24f2bf968 configuré comme draggable
[15/07/2025 14:56:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ed075ee3-b8ec-41fb-bc32-60c24f2bf968
[15/07/2025 14:56:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 5c402570-6010-4a05-a997-c3decfa046c9 configuré comme draggable
[15/07/2025 14:56:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste e1a23d95-75e6-430e-9ad1-7116552fc9e8
[15/07/2025 14:56:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste e1a23d95-75e6-430e-9ad1-7116552fc9e8 configuré comme draggable
[15/07/2025 14:56:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 5c402570-6010-4a05-a997-c3decfa046c9
[15/07/2025 14:56:28] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 14:56:28] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 14:56:28] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 14:56:28] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 14:56:28] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 14:56:28] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 14:56:28] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 14:56:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (60 zones)
[15/07/2025 14:56:28] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 14:56:28] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[15/07/2025 14:56:28] [FRONTEND] [LOG] 🔧 [init] Configuration du drag & drop des postes...
[15/07/2025 14:56:28] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[15/07/2025 14:56:28] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 60 zones trouvées
[15/07/2025 14:56:28] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 14:56:28] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 14:56:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 60 zones de drop disponibles
[15/07/2025 14:56:28] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 14:56:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste de49cbe3-b7ae-42ea-8838-2a1ad58c9cdc configuré comme draggable
[15/07/2025 14:56:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste de49cbe3-b7ae-42ea-8838-2a1ad58c9cdc
[15/07/2025 14:56:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ad3c79b4-ffab-4765-a4be-c07e8ee69614 configuré comme draggable
[15/07/2025 14:56:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ad3c79b4-ffab-4765-a4be-c07e8ee69614
[15/07/2025 14:56:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ed075ee3-b8ec-41fb-bc32-60c24f2bf968
[15/07/2025 14:56:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 5c402570-6010-4a05-a997-c3decfa046c9 configuré comme draggable
[15/07/2025 14:56:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ed075ee3-b8ec-41fb-bc32-60c24f2bf968 configuré comme draggable
[15/07/2025 14:56:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 5c402570-6010-4a05-a997-c3decfa046c9
[15/07/2025 14:56:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste e1a23d95-75e6-430e-9ad1-7116552fc9e8 configuré comme draggable
[15/07/2025 14:56:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste e1a23d95-75e6-430e-9ad1-7116552fc9e8
[15/07/2025 14:56:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ad3c79b4-ffab-4765-a4be-c07e8ee69614 configuré comme draggable
[15/07/2025 14:56:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste de49cbe3-b7ae-42ea-8838-2a1ad58c9cdc configuré comme draggable
[15/07/2025 14:56:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste de49cbe3-b7ae-42ea-8838-2a1ad58c9cdc
[15/07/2025 14:56:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ad3c79b4-ffab-4765-a4be-c07e8ee69614
[15/07/2025 14:56:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ed075ee3-b8ec-41fb-bc32-60c24f2bf968 configuré comme draggable
[15/07/2025 14:56:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ed075ee3-b8ec-41fb-bc32-60c24f2bf968
[15/07/2025 14:56:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste e1a23d95-75e6-430e-9ad1-7116552fc9e8 configuré comme draggable
[15/07/2025 14:56:28] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 5c402570-6010-4a05-a997-c3decfa046c9 configuré comme draggable
[15/07/2025 14:56:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 5c402570-6010-4a05-a997-c3decfa046c9
[15/07/2025 14:56:29] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste e1a23d95-75e6-430e-9ad1-7116552fc9e8
[15/07/2025 14:56:29] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 14:56:29] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 14:56:29] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 14:56:29] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 14:56:29] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 14:56:29] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 14:56:29] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 14:56:29] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 14:56:29] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 14:56:29] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 14:56:29] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 14:56:29] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 14:56:29] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 14:56:29] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (60 zones)
[15/07/2025 14:56:29] [FRONTEND] [LOG] ℹ️ [setupEmployeeDragDrop] Fonction désactivée - Utiliser setupDragAndDrop
[15/07/2025 14:56:29] [FRONTEND] [LOG] 🎉 [Agenda] Application fullscreen initialisée avec succès
[15/07/2025 14:56:29] [FRONTEND] [LOG] ✅ [init] Initialisation terminée avec succès
[15/07/2025 14:56:29] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] Résultats: 0 ajoutés, 0 validés, 0 corrigés
[15/07/2025 14:56:29] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 14:56:29] [FRONTEND] [LOG] 🔍 [diagnoseRegularAssignmentGrips] Diagnostic des grips d'attributions régulières
[15/07/2025 14:56:29] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 14:56:29] [FRONTEND] [LOG] 📊 [diagnoseRegularAssignmentGrips] Statistiques: {"totalShifts":0,"regularShifts":0,"regularAssignments":0}
[15/07/2025 14:56:29] [FRONTEND] [LOG] 🎯 [diagnoseRegularAssignmentGrips] Grips trouvés dans le DOM: 0
[15/07/2025 14:56:29] [FRONTEND] [LOG] ⚠️ [diagnoseRegularAssignmentGrips] Aucun shift régulier trouvé - Ré-application des attributions...
[15/07/2025 14:56:29] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 14:56:29] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 14:56:34] [FRONTEND] [LOG] 🎯 [POST DRAG] Début drag poste: de49cbe3-b7ae-42ea-8838-2a1ad58c9cdc (Poste Matin)
[15/07/2025 14:56:34] [FRONTEND] [LOG] 🎯 [POST DRAG] DataTransfer configuré: {"postId":"de49cbe3-b7ae-42ea-8838-2a1ad58c9cdc","label":"Poste Matin","types":["text/plain","application/x-post-id","postid","postlabel"]}
[15/07/2025 14:56:34] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 14:56:34] [FRONTEND] [LOG] 🎨 [enableAgendaPreviewMode] Activation preview agenda pour poste de49cbe3-b7ae-42ea-8838-2a1ad58c9cdc
[15/07/2025 14:56:34] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 14:56:34] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] Ajout des fantômes pour Poste Matin
[15/07/2025 14:56:34] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] 25 fantômes créés
[15/07/2025 14:56:34] [FRONTEND] [LOG] 🖱️ [enableCellHoverTracking] Suivi activé sur 25 cellules
[15/07/2025 14:56:34] [FRONTEND] [LOG] 🎯 [POST DRAG] 60 zones préparées pour le drop
[15/07/2025 14:56:34] [FRONTEND] [LOG] ✅ [enableAgendaPreviewMode] Mode preview activé pour Poste Matin
[15/07/2025 14:56:34] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 59f5df3a-33ef-425c-bfa2-adca818cf94f, types: text/plain, application/x-post-id, postid, postlabel
[15/07/2025 14:56:34] [FRONTEND] [LOG] 🎯 [POST DRAG] Début drag poste: de49cbe3-b7ae-42ea-8838-2a1ad58c9cdc (Poste Matin)
[15/07/2025 14:56:34] [FRONTEND] [LOG] 🎯 [POST DRAG] DataTransfer configuré: {"postId":"de49cbe3-b7ae-42ea-8838-2a1ad58c9cdc","label":"Poste Matin","types":["text/plain","application/x-post-id","postid","postlabel"]}
[15/07/2025 14:56:34] [FRONTEND] [LOG] 🎨 [enableAgendaPreviewMode] Activation preview agenda pour poste de49cbe3-b7ae-42ea-8838-2a1ad58c9cdc
[15/07/2025 14:56:34] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 14:56:34] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 14:56:34] [FRONTEND] [LOG] 🖱️ [enableCellHoverTracking] Suivi activé sur 50 cellules
[15/07/2025 14:56:35] [FRONTEND] [LOG] 🎯 [POST DRAG] 60 zones préparées pour le drop
[15/07/2025 14:56:35] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] Ajout des fantômes pour Poste Matin
[15/07/2025 14:56:35] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] 25 fantômes créés
[15/07/2025 14:56:35] [FRONTEND] [LOG] ✅ [enableAgendaPreviewMode] Mode preview activé pour Poste Matin
[15/07/2025 14:56:35] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 59f5df3a-33ef-425c-bfa2-adca818cf94f, types: text/plain, application/x-post-id, postid, postlabel
[15/07/2025 14:56:35] [FRONTEND] [LOG] 🎯 [DROP] ✅ Drop reçu sur employé: Sophie Leblanc
[15/07/2025 14:56:35] [FRONTEND] [LOG] 📦 [DROP] Nouveau poste: de49cbe3-b7ae-42ea-8838-2a1ad58c9cdc → 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 14:56:35] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 14:56:35] [FRONTEND] [LOG] ✅ [DROP] PostId trouvé via application/x-post-id: "de49cbe3-b7ae-42ea-8838-2a1ad58c9cdc"
[15/07/2025 14:56:35] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 14:56:35] [FRONTEND] [LOG] 🎯 [openAssignmentContextModal] Poste brut: de49cbe3-b7ae-42ea-8838-2a1ad58c9cdc, Employé: 59f5df3a-33ef-425c-bfa2-adca818cf94f, Contexte: null
[15/07/2025 14:56:35] [FRONTEND] [LOG] ℹ️ [openAssignmentContextModal] Option "Attribution unique (cellule)" masquée - contexte inapproprié
[15/07/2025 14:56:35] [FRONTEND] [LOG] 📅 [openAssignmentContextModal] Date initialisée: 2025-07-15
[15/07/2025 14:56:35] [FRONTEND] [LOG] 🎯 [POST DRAG] Fin drag poste: de49cbe3-b7ae-42ea-8838-2a1ad58c9cdc
[15/07/2025 14:56:35] [FRONTEND] [LOG] ✅ [DROP] Modal d'assignation ouvert
[15/07/2025 14:56:35] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 14:56:35] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 14:56:35] [FRONTEND] [LOG] 🎯 [POST DRAG] Nettoyage des 60 zones
[15/07/2025 14:56:35] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 14:56:35] [FRONTEND] [LOG] 🎯 [POST DRAG] Fin drag poste: de49cbe3-b7ae-42ea-8838-2a1ad58c9cdc
[15/07/2025 14:56:35] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 14:56:35] [FRONTEND] [LOG] 🎯 [POST DRAG] Nettoyage des 60 zones
[15/07/2025 14:56:38] [FRONTEND] [LOG] 🔚 [ESC] Fermeture de toutes les modales ouvertes
[15/07/2025 14:56:38] [FRONTEND] [LOG] ✅ Toutes les modales fermées avec ESC
[15/07/2025 14:56:38] [FRONTEND] [LOG] 🗙 Modal fermé: assignment-context-modal
[15/07/2025 14:57:10] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[15/07/2025 14:57:10] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[15/07/2025 14:57:10] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[15/07/2025 14:57:10] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[15/07/2025 14:57:10] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[15/07/2025 14:57:10] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[15/07/2025 14:57:10] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[15/07/2025 14:57:10] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[15/07/2025 14:57:10] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[15/07/2025 14:57:10] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[15/07/2025 14:57:10] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[15/07/2025 14:57:10] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[15/07/2025 14:57:10] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[15/07/2025 14:57:10] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[15/07/2025 14:57:10] [FRONTEND] [LOG] 🧹 [Agenda] Nettoyage composant Agenda
[15/07/2025 14:57:10] [FRONTEND] [LOG] 🔄 [Agenda] Réinitialisation nécessaire (employés non chargés)
[15/07/2025 14:57:10] [FRONTEND] [LOG] 🚀 [Agenda] Initialisation TeamCalendarApp...
[15/07/2025 14:57:10] [FRONTEND] [LOG] 🚀 [init] Initialisation de TeamCalendarApp...
[15/07/2025 14:57:10] [FRONTEND] [LOG] 🧹 [clearRenderCache] Nettoyage du cache de rendu
[15/07/2025 14:57:10] [FRONTEND] [LOG] ✅ [clearRenderCache] Cache de rendu nettoyé
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:57:10.067Z"
}
[15/07/2025 14:57:10] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:57:10.042Z"
}
[15/07/2025 14:57:10] [FRONTEND] [LOG] 🔌 [ConnectionIndicator] Création de l'indicateur de connexion
[15/07/2025 14:57:10] [FRONTEND] [LOG] 🔌 [init] Indicateur de connexion initialisé
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:57:10.053Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:57:10.069Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:57:10.068Z"
}
[15/07/2025 14:57:10] [FRONTEND] [LOG] 📂 [TeamCalendarApp] Chargement de l'état...
[15/07/2025 14:57:10] [FRONTEND] [LOG] 🌐 [TeamCalendarApp] Chargement depuis l'API...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT e.*, et.name as template_name 
      FROM employees e 
      LEFT JOIN employee_templates et ON e.template_id = et.id 
      ORDER BY e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT e.*, et.name as template_name \n      FROM employees e \n      LEFT JOIN employee_templates et ON e.template_id = et.id \n      ORDER BY e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:57:10.121Z"
}
[15/07/2025 14:57:10] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:57:10.289Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:57:10.287Z"
}
[15/07/2025 14:57:10] [BACKEND] [INFO] ✅ Requête exécutée en 218ms: 
      SELECT e.*, et.name as template_name 
      {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 218ms: 
      SELECT e.*, et.name as template_name 
      {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 218ms:",
    "\n      SELECT e.*, et.name as template_name \n     ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:57:10.340Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:57:10.211Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:57:10.368Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:57:10.210Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:57:10.364Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:57:10.228Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:57:10.379Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:57:10.225Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:57:10.387Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:57:10.227Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:57:10.385Z"
}
[15/07/2025 14:57:10] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:57:10.397Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:57:10.231Z"
}
[15/07/2025 14:57:10] [FRONTEND] [LOG] 👥 [TeamCalendarApp] 5 employés chargés
[15/07/2025 14:57:10] [FRONTEND] [LOG] 📦 [loadState] Chargement de tous les shifts depuis la base de données...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT s.*, e.name as employee_name, sp.label as post_label
      FROM shifts s
      LEFT JOIN employees e ON s.employee_id = e.id
      LEFT JOIN standard_posts sp ON s.post_id = sp.id
      ORDER BY s.date_key, e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT s.*, e.name as employee_name, sp.label as post_label\n      FROM shifts s\n      LEFT JOIN employees e ON s.employee_id = e.id\n      LEFT JOIN standard_posts sp ON s.post_id = sp.id\n      ORDER BY s.date_key, e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:57:10.472Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 49ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 49ms:",
    "\n      SELECT s.*, e.name as employee_name, sp.lab",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:57:10.520Z"
}
[15/07/2025 14:57:10] [BACKEND] [INFO] ✅ Requête exécutée en 49ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
[15/07/2025 14:57:10] [FRONTEND] [LOG] 📅 [loadState] 0 shifts chargés depuis la base de données
[15/07/2025 14:57:10] [FRONTEND] [LOG] ✅ [loadState] Shifts organisés dans this.data.schedule
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:57:10.562Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 46ms: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 46ms:",
    "SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:57:10.609Z"
}
[15/07/2025 14:57:10] [BACKEND] [INFO] ✅ Requête exécutée en 46ms: SELECT * FROM standard_posts ORDER BY label {}
[15/07/2025 14:57:10] [FRONTEND] [LOG] 🏢 [TeamCalendarApp] 5 postes standards chargés
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:57:10.648Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 54ms: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 54ms:",
    "SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:57:10.702Z"
}
[15/07/2025 14:57:11] [BACKEND] [INFO] ✅ Requête exécutée en 54ms: SELECT * FROM app_settings ORDER BY setting_key {}
[15/07/2025 14:57:11] [FRONTEND] [LOG] ⚙️ [loadState] Paramètres chargés depuis API: {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT 
        ra.id,
        ra.employee_id,
        ra.post_id,
        ra.days_of_week,
        ra.start_date,
        ra.end_date,
        ra.is_active,
        ra.excluded_dates,
        e.name as employee_name,
        sp.label as post_label,
        sp.hours as post_hours,
        sp.type as post_type,
        array_length(ra.days_of_week, 1) as days_count
      FROM regular_assignments ra
      LEFT JOIN employees e ON ra.employee_id = e.id
      LEFT JOIN standard_posts sp ON ra.post_id = sp.id
      WHERE ra.is_active = true
      ORDER BY e.name, sp.label, ra.start_date
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT \n        ra.id,\n        ra.employee_id,\n        ra.post_id,\n        ra.days_of_week,\n        ra.start_date,\n        ra.end_date,\n        ra.is_active,\n        ra.excluded_dates,\n        e.name as employee_name,\n        sp.label as post_label,\n        sp.hours as post_hours,\n        sp.type as post_type,\n        array_length(ra.days_of_week, 1) as days_count\n      FROM regular_assignments ra\n      LEFT JOIN employees e ON ra.employee_id = e.id\n      LEFT JOIN standard_posts sp ON ra.post_id = sp.id\n      WHERE ra.is_active = true\n      ORDER BY e.name, sp.label, ra.start_date\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:57:10.729Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 50ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 50ms:",
    "\n      SELECT \n        ra.id,\n        ra.employee_",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:57:10.779Z"
}
[15/07/2025 14:57:11] [BACKEND] [INFO] ✅ Requête exécutée en 50ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
[15/07/2025 14:57:11] [FRONTEND] [LOG] ✅ [fixInvalidAssignmentDates] Aucune date invalide trouvée
[15/07/2025 14:57:11] [FRONTEND] [LOG] 🔧 [fixInvalidAssignmentDates] Vérification des dates invalides...
[15/07/2025 14:57:11] [FRONTEND] [LOG] 📋 [loadState] 0/0 assignations régulières valides chargées et normalisées
[15/07/2025 14:57:11] [FRONTEND] [LOG] 📋 [loadState] Assignations régulières chargées (application différée)
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'
         {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'\n        ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:57:10.907Z"
}
[15/07/2025 14:57:11] [FRONTEND] [LOG] 🧹 [cleanupDuplicateRegularShifts] Nettoyage des doublons de shifts réguliers...
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📋 [GET /api/employee-order] Récupération ordre des employés
  Data: {
  "originalArgs": [
    "📋 [GET /api/employee-order] Récupération ordre des employés"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:57:10.906Z"
}
[15/07/2025 14:57:11] [BACKEND] [INFO] 📋 [GET /api/employee-order] Récupération ordre des employés
[15/07/2025 14:57:11] [FRONTEND] [LOG] ✅ [cleanupDuplicateRegularShifts] Aucun doublon détecté
[15/07/2025 14:57:11] [FRONTEND] [LOG] 🧹 [cleanupInvalidAssignmentIds] Nettoyage des assignment_id invalides...
[15/07/2025 14:57:11] [FRONTEND] [LOG] ✅ [cleanupInvalidAssignmentIds] Aucun assignment_id invalide détecté
[15/07/2025 14:57:11] [FRONTEND] [LOG] ✅ [loadState] État chargé depuis l'API avec succès
[15/07/2025 14:57:11] [FRONTEND] [LOG] ✅ [init] Icône paramètres trouvée: [Objet non sérialisable]
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📋 [GET /api/employee-order] Aucun ordre personnalisé trouvé
  Data: {
  "originalArgs": [
    "📋 [GET /api/employee-order] Aucun ordre personnalisé trouvé"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:57:10.958Z"
}
[15/07/2025 14:57:11] [FRONTEND] [LOG] 🔍 [init] Recherche du bouton des paramètres dans le DOM
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 51ms: 
            SELECT setting_value FROM app_setting {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 51ms:",
    "\n            SELECT setting_value FROM app_setting",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:57:10.957Z"
}
[15/07/2025 14:57:11] [FRONTEND] [LOG] 🔍 [init] Bouton des paramètres trouvé ? true
[15/07/2025 14:57:11] [FRONTEND] [LOG] 🔍 [init] Attachement du listener click sur le bouton des paramètres
[15/07/2025 14:57:11] [FRONTEND] [LOG] ✅ [setupNavigationListeners] Écouteurs de navigation attachés
[15/07/2025 14:57:11] [FRONTEND] [LOG] 🎯 [setupViewNavigationListeners] Configuration des boutons de navigation...
[15/07/2025 14:57:11] [FRONTEND] [LOG] ✅ Boutons de navigation configurés
[15/07/2025 14:57:11] [FRONTEND] [LOG] ✅ [ModalManager] Gestionnaires de modal configurés
[15/07/2025 14:57:11] [FRONTEND] [LOG] 🔧 [ModalManager] Configuration des gestionnaires de modal
[15/07/2025 14:57:11] [FRONTEND] [LOG] 🔧 [setupGeneralSettings] Configuration des paramètres généraux
[15/07/2025 14:57:11] [FRONTEND] [LOG] ✅ [setupGlobalEscapeHandler] Gestionnaire ESC global configuré
[15/07/2025 14:57:11] [FRONTEND] [LOG] 🔧 [setupGeneralSettings] Création du conteneur de fuseau horaire
[15/07/2025 14:57:11] [FRONTEND] [WARN] ⚠️ [loadEmployeeOrder] Réponse API sans tableau employeeOrder
[15/07/2025 14:57:11] [FRONTEND] [LOG] 📋 [loadEmployeeOrder] Chargement ordre des employés
[15/07/2025 14:57:11] [FRONTEND] [LOG] 🔧 [init] Forçage du rendu initial...
[15/07/2025 14:57:11] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[15/07/2025 14:57:11] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[15/07/2025 14:57:11] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[15/07/2025 14:57:11] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[15/07/2025 14:57:11] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[15/07/2025 14:57:11] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 14:57:11] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 14:57:11] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 60 zones trouvées
[15/07/2025 14:57:11] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 60 zones de drop disponibles
[15/07/2025 14:57:11] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 14:57:11] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 2eef9a3b-2bcf-464a-ba8c-9a155b9f4f57 configuré comme draggable
[15/07/2025 14:57:11] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Matin: [1, 2, 3, 4, 5]
[15/07/2025 14:57:11] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 2eef9a3b-2bcf-464a-ba8c-9a155b9f4f57
[15/07/2025 14:57:11] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Nuit: [1, 2, 3, 4, 5]
[15/07/2025 14:57:11] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 506d2962-767a-4224-8ff8-28fe528bf607
[15/07/2025 14:57:11] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste b8b7567b-4bd7-457f-bec4-283a742cce52
[15/07/2025 14:57:11] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Soir: [1, 2, 3, 4, 5]
[15/07/2025 14:57:11] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste b8b7567b-4bd7-457f-bec4-283a742cce52 configuré comme draggable
[15/07/2025 14:57:11] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 506d2962-767a-4224-8ff8-28fe528bf607 configuré comme draggable
[15/07/2025 14:57:11] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE2: [0, 6]
[15/07/2025 14:57:11] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste c02f439c-de02-43ff-a55e-967133d5111f configuré comme draggable
[15/07/2025 14:57:11] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7d88cba3-aa6a-418a-b574-5b5d7d2097b6 configuré comme draggable
[15/07/2025 14:57:11] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE1: [0, 6]
[15/07/2025 14:57:11] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste c02f439c-de02-43ff-a55e-967133d5111f
[15/07/2025 14:57:11] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 506d2962-767a-4224-8ff8-28fe528bf607
[15/07/2025 14:57:11] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 506d2962-767a-4224-8ff8-28fe528bf607 configuré comme draggable
[15/07/2025 14:57:11] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 2eef9a3b-2bcf-464a-ba8c-9a155b9f4f57
[15/07/2025 14:57:11] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 2eef9a3b-2bcf-464a-ba8c-9a155b9f4f57 configuré comme draggable
[15/07/2025 14:57:11] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7d88cba3-aa6a-418a-b574-5b5d7d2097b6
[15/07/2025 14:57:11] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste b8b7567b-4bd7-457f-bec4-283a742cce52 configuré comme draggable
[15/07/2025 14:57:11] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7d88cba3-aa6a-418a-b574-5b5d7d2097b6 configuré comme draggable
[15/07/2025 14:57:11] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste c02f439c-de02-43ff-a55e-967133d5111f
[15/07/2025 14:57:11] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste c02f439c-de02-43ff-a55e-967133d5111f configuré comme draggable
[15/07/2025 14:57:11] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste b8b7567b-4bd7-457f-bec4-283a742cce52
[15/07/2025 14:57:11] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7d88cba3-aa6a-418a-b574-5b5d7d2097b6
[15/07/2025 14:57:11] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 14:57:11] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 14:57:11] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 14:57:11] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 14:57:11] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 14:57:12] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 14:57:12] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 14:57:12] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 14:57:12] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (60 zones)
[15/07/2025 14:57:12] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[15/07/2025 14:57:12] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[15/07/2025 14:57:12] [FRONTEND] [LOG] 🔧 [init] Configuration du drag & drop des postes...
[15/07/2025 14:57:12] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 14:57:12] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 14:57:12] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 60 zones trouvées
[15/07/2025 14:57:12] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 60 zones de drop disponibles
[15/07/2025 14:57:12] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 2eef9a3b-2bcf-464a-ba8c-9a155b9f4f57
[15/07/2025 14:57:12] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 2eef9a3b-2bcf-464a-ba8c-9a155b9f4f57 configuré comme draggable
[15/07/2025 14:57:12] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 14:57:12] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 506d2962-767a-4224-8ff8-28fe528bf607 configuré comme draggable
[15/07/2025 14:57:12] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 506d2962-767a-4224-8ff8-28fe528bf607
[15/07/2025 14:57:12] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste b8b7567b-4bd7-457f-bec4-283a742cce52
[15/07/2025 14:57:12] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste b8b7567b-4bd7-457f-bec4-283a742cce52 configuré comme draggable
[15/07/2025 14:57:12] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste c02f439c-de02-43ff-a55e-967133d5111f configuré comme draggable
[15/07/2025 14:57:12] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste c02f439c-de02-43ff-a55e-967133d5111f
[15/07/2025 14:57:12] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7d88cba3-aa6a-418a-b574-5b5d7d2097b6 configuré comme draggable
[15/07/2025 14:57:12] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 2eef9a3b-2bcf-464a-ba8c-9a155b9f4f57 configuré comme draggable
[15/07/2025 14:57:12] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7d88cba3-aa6a-418a-b574-5b5d7d2097b6
[15/07/2025 14:57:12] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 2eef9a3b-2bcf-464a-ba8c-9a155b9f4f57
[15/07/2025 14:57:12] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 506d2962-767a-4224-8ff8-28fe528bf607
[15/07/2025 14:57:12] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 506d2962-767a-4224-8ff8-28fe528bf607 configuré comme draggable
[15/07/2025 14:57:12] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste b8b7567b-4bd7-457f-bec4-283a742cce52 configuré comme draggable
[15/07/2025 14:57:12] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste c02f439c-de02-43ff-a55e-967133d5111f configuré comme draggable
[15/07/2025 14:57:12] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste b8b7567b-4bd7-457f-bec4-283a742cce52
[15/07/2025 14:57:12] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste c02f439c-de02-43ff-a55e-967133d5111f
[15/07/2025 14:57:12] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7d88cba3-aa6a-418a-b574-5b5d7d2097b6 configuré comme draggable
[15/07/2025 14:57:12] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7d88cba3-aa6a-418a-b574-5b5d7d2097b6
[15/07/2025 14:57:12] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 14:57:12] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 14:57:12] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 14:57:12] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 14:57:12] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 14:57:12] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 14:57:12] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 14:57:12] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 14:57:12] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 14:57:12] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 14:57:12] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 14:57:12] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 14:57:12] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 14:57:12] [FRONTEND] [LOG] ℹ️ [setupEmployeeDragDrop] Fonction désactivée - Utiliser setupDragAndDrop
[15/07/2025 14:57:12] [FRONTEND] [LOG] ✅ [init] Initialisation terminée avec succès
[15/07/2025 14:57:12] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (60 zones)
[15/07/2025 14:57:12] [FRONTEND] [LOG] 🎉 [Agenda] Application fullscreen initialisée avec succès
[15/07/2025 14:57:12] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] Résultats: 0 ajoutés, 0 validés, 0 corrigés
[15/07/2025 14:57:12] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 14:57:12] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 14:57:12] [FRONTEND] [LOG] 🔍 [diagnoseRegularAssignmentGrips] Diagnostic des grips d'attributions régulières
[15/07/2025 14:57:12] [FRONTEND] [LOG] 📊 [diagnoseRegularAssignmentGrips] Statistiques: {"totalShifts":0,"regularShifts":0,"regularAssignments":0}
[15/07/2025 14:57:12] [FRONTEND] [LOG] 🎯 [diagnoseRegularAssignmentGrips] Grips trouvés dans le DOM: 0
[15/07/2025 14:57:12] [FRONTEND] [LOG] ⚠️ [diagnoseRegularAssignmentGrips] Aucun shift régulier trouvé - Ré-application des attributions...
[15/07/2025 14:57:12] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 14:57:12] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 14:57:15] [FRONTEND] [LOG] 🎯 [POST DRAG] Début drag poste: 2eef9a3b-2bcf-464a-ba8c-9a155b9f4f57 (Poste Matin)
[15/07/2025 14:57:15] [FRONTEND] [LOG] 🎯 [POST DRAG] DataTransfer configuré: {"postId":"2eef9a3b-2bcf-464a-ba8c-9a155b9f4f57","label":"Poste Matin","types":["text/plain","application/x-post-id","postid","postlabel"]}
[15/07/2025 14:57:15] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 14:57:15] [FRONTEND] [LOG] 🎨 [enableAgendaPreviewMode] Activation preview agenda pour poste 2eef9a3b-2bcf-464a-ba8c-9a155b9f4f57
[15/07/2025 14:57:15] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 14:57:15] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] Ajout des fantômes pour Poste Matin
[15/07/2025 14:57:15] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] 25 fantômes créés
[15/07/2025 14:57:15] [FRONTEND] [LOG] ✅ [enableAgendaPreviewMode] Mode preview activé pour Poste Matin
[15/07/2025 14:57:15] [FRONTEND] [LOG] 🖱️ [enableCellHoverTracking] Suivi activé sur 25 cellules
[15/07/2025 14:57:15] [FRONTEND] [LOG] 🎯 [POST DRAG] 60 zones préparées pour le drop
[15/07/2025 14:57:15] [FRONTEND] [LOG] 🎯 [POST DRAG] Début drag poste: 2eef9a3b-2bcf-464a-ba8c-9a155b9f4f57 (Poste Matin)
[15/07/2025 14:57:15] [FRONTEND] [LOG] 🎨 [enableAgendaPreviewMode] Activation preview agenda pour poste 2eef9a3b-2bcf-464a-ba8c-9a155b9f4f57
[15/07/2025 14:57:15] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 14:57:15] [FRONTEND] [LOG] 🎯 [POST DRAG] DataTransfer configuré: {"postId":"2eef9a3b-2bcf-464a-ba8c-9a155b9f4f57","label":"Poste Matin","types":["text/plain","application/x-post-id","postid","postlabel"]}
[15/07/2025 14:57:15] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 14:57:15] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] Ajout des fantômes pour Poste Matin
[15/07/2025 14:57:15] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] 25 fantômes créés
[15/07/2025 14:57:15] [FRONTEND] [LOG] ✅ [enableAgendaPreviewMode] Mode preview activé pour Poste Matin
[15/07/2025 14:57:15] [FRONTEND] [LOG] 🎯 [POST DRAG] 60 zones préparées pour le drop
[15/07/2025 14:57:15] [FRONTEND] [LOG] 🖱️ [enableCellHoverTracking] Suivi activé sur 50 cellules
[15/07/2025 14:57:15] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 59f5df3a-33ef-425c-bfa2-adca818cf94f, types: text/plain, application/x-post-id, postid, postlabel
[15/07/2025 14:57:16] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 59f5df3a-33ef-425c-bfa2-adca818cf94f, types: text/plain, application/x-post-id, postid, postlabel
[15/07/2025 14:57:16] [FRONTEND] [LOG] 🎯 [DROP] ✅ Drop reçu sur employé: Sophie Leblanc
[15/07/2025 14:57:16] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 14:57:16] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 14:57:16] [FRONTEND] [LOG] ✅ [DROP] PostId trouvé via application/x-post-id: "2eef9a3b-2bcf-464a-ba8c-9a155b9f4f57"
[15/07/2025 14:57:16] [FRONTEND] [LOG] 📦 [DROP] Nouveau poste: 2eef9a3b-2bcf-464a-ba8c-9a155b9f4f57 → 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 14:57:16] [FRONTEND] [LOG] 🎯 [openAssignmentContextModal] Poste brut: 2eef9a3b-2bcf-464a-ba8c-9a155b9f4f57, Employé: 59f5df3a-33ef-425c-bfa2-adca818cf94f, Contexte: null
[15/07/2025 14:57:16] [FRONTEND] [LOG] ℹ️ [openAssignmentContextModal] Option "Attribution unique (cellule)" masquée - contexte inapproprié
[15/07/2025 14:57:16] [FRONTEND] [LOG] 📅 [openAssignmentContextModal] Date initialisée: 2025-07-15
[15/07/2025 14:57:16] [FRONTEND] [LOG] ✅ [DROP] Modal d'assignation ouvert
[15/07/2025 14:57:16] [FRONTEND] [LOG] 🎯 [POST DRAG] Fin drag poste: 2eef9a3b-2bcf-464a-ba8c-9a155b9f4f57
[15/07/2025 14:57:16] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 14:57:16] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 14:57:16] [FRONTEND] [LOG] 🎯 [POST DRAG] Nettoyage des 60 zones
[15/07/2025 14:57:16] [FRONTEND] [LOG] 🎯 [POST DRAG] Fin drag poste: 2eef9a3b-2bcf-464a-ba8c-9a155b9f4f57
[15/07/2025 14:57:16] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 14:57:16] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 14:57:16] [FRONTEND] [LOG] 🎯 [POST DRAG] Nettoyage des 60 zones