[14/07/2025 15:15:25] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[14/07/2025 15:15:25] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[14/07/2025 15:15:25] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[14/07/2025 15:15:25] [FRONTEND] [LOG] [LogsPage] Fermeture de la connexion SSE.
[14/07/2025 15:15:25] [BACKEND] [INFO] [LOGS] Client connecté: 1752520525811
[14/07/2025 15:15:25] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:25.812Z"
}
[14/07/2025 15:14:50] [FRONTEND] [LOG] 📦 [loadState] Chargement de tous les shifts depuis la base de données...
[14/07/2025 15:14:50] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT s.*, e.name as employee_name, sp.label as post_label
      FROM shifts s
      LEFT JOIN employees e ON s.employee_id = e.id
      LEFT JOIN standard_posts sp ON s.post_id = sp.id
      ORDER BY s.date_key, e.name
     {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:14:51.609Z",
  "originalArgs": [
    "[query] Executing query: \n      SELECT s.*, e.name as employee_name, sp.label as post_label\n      FROM shifts s\n      LEFT JOIN employees e ON s.employee_id = e.id\n      LEFT JOIN standard_posts sp ON s.post_id = sp.id\n      ORDER BY s.date_key, e.name\n    ",
    {}
  ]
}
[14/07/2025 15:14:50] [BACKEND] [INFO] ✅ Requête exécutée en 45ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
[14/07/2025 15:14:50] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 45ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:14:51.654Z",
  "originalArgs": [
    "✅ Requête exécutée en 45ms:",
    "\n      SELECT s.*, e.name as employee_name, sp.lab",
    {}
  ]
}
[14/07/2025 15:14:50] [FRONTEND] [LOG] ✅ [loadState] Shifts organisés dans this.data.schedule
[14/07/2025 15:14:50] [FRONTEND] [LOG] 📅 [loadState] 0 shifts chargés depuis la base de données
[14/07/2025 15:14:50] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:14:51.677Z",
  "originalArgs": [
    "[query] Executing query: SELECT * FROM standard_posts ORDER BY label",
    {}
  ]
}
[14/07/2025 15:14:50] [BACKEND] [INFO] ✅ Requête exécutée en 34ms: SELECT * FROM standard_posts ORDER BY label {}
[14/07/2025 15:14:50] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 34ms: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:14:51.712Z",
  "originalArgs": [
    "✅ Requête exécutée en 34ms:",
    "SELECT * FROM standard_posts ORDER BY label",
    {}
  ]
}
[14/07/2025 15:14:51] [FRONTEND] [LOG] 🏢 [TeamCalendarApp] 5 postes standards chargés
[14/07/2025 15:14:51] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:14:51.732Z",
  "originalArgs": [
    "[query] Executing query: SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ]
}
[14/07/2025 15:14:51] [FRONTEND] [LOG] ⚙️ [loadState] Paramètres chargés depuis API: {}
[14/07/2025 15:14:51] [BACKEND] [INFO] ✅ Requête exécutée en 24ms: SELECT * FROM app_settings ORDER BY setting_key {}
[14/07/2025 15:14:51] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 24ms: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:14:51.756Z",
  "originalArgs": [
    "✅ Requête exécutée en 24ms:",
    "SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ]
}
[14/07/2025 15:14:51] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT 
        ra.id,
        ra.employee_id,
        ra.post_id,
        ra.days_of_week,
        ra.start_date,
        ra.end_date,
        ra.is_active,
        ra.excluded_dates,
        e.name as employee_name,
        sp.label as post_label,
        sp.hours as post_hours,
        sp.type as post_type,
        array_length(ra.days_of_week, 1) as days_count
      FROM regular_assignments ra
      LEFT JOIN employees e ON ra.employee_id = e.id
      LEFT JOIN standard_posts sp ON ra.post_id = sp.id
      WHERE ra.is_active = true
      ORDER BY e.name, sp.label, ra.start_date
     {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:14:51.777Z",
  "originalArgs": [
    "[query] Executing query: \n      SELECT \n        ra.id,\n        ra.employee_id,\n        ra.post_id,\n        ra.days_of_week,\n        ra.start_date,\n        ra.end_date,\n        ra.is_active,\n        ra.excluded_dates,\n        e.name as employee_name,\n        sp.label as post_label,\n        sp.hours as post_hours,\n        sp.type as post_type,\n        array_length(ra.days_of_week, 1) as days_count\n      FROM regular_assignments ra\n      LEFT JOIN employees e ON ra.employee_id = e.id\n      LEFT JOIN standard_posts sp ON ra.post_id = sp.id\n      WHERE ra.is_active = true\n      ORDER BY e.name, sp.label, ra.start_date\n    ",
    {}
  ]
}
[14/07/2025 15:14:51] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 45ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:14:51.822Z",
  "originalArgs": [
    "✅ Requête exécutée en 45ms:",
    "\n      SELECT \n        ra.id,\n        ra.employee_",
    {}
  ]
}
[14/07/2025 15:14:51] [BACKEND] [INFO] ✅ Requête exécutée en 45ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
[14/07/2025 15:14:51] [FRONTEND] [LOG] 📋 [loadState] 0/0 assignations régulières valides chargées et normalisées
[14/07/2025 15:14:51] [FRONTEND] [LOG] ✅ [fixInvalidAssignmentDates] Aucune date invalide trouvée
[14/07/2025 15:14:51] [FRONTEND] [LOG] 🔧 [fixInvalidAssignmentDates] Vérification des dates invalides...
[14/07/2025 15:14:51] [FRONTEND] [LOG] ✅ [cleanupDuplicateRegularShifts] Aucun doublon détecté
[14/07/2025 15:14:51] [FRONTEND] [LOG] 🧹 [cleanupDuplicateRegularShifts] Nettoyage des doublons de shifts réguliers...
[14/07/2025 15:14:51] [FRONTEND] [LOG] 📋 [loadState] Assignations régulières chargées (application différée)
[14/07/2025 15:14:51] [BACKEND] [INFO] [LOGGER] 📋 [GET /api/employee-order] Récupération ordre des employés
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:14:51.871Z",
  "originalArgs": [
    "📋 [GET /api/employee-order] Récupération ordre des employés"
  ]
}
[14/07/2025 15:14:51] [BACKEND] [INFO] 📋 [GET /api/employee-order] Récupération ordre des employés
[14/07/2025 15:14:51] [FRONTEND] [LOG] 🧹 [cleanupInvalidAssignmentIds] Nettoyage des assignment_id invalides...
[14/07/2025 15:14:51] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'
         {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:14:51.872Z",
  "originalArgs": [
    "[query] Executing query: \n            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'\n        ",
    {}
  ]
}
[14/07/2025 15:14:51] [FRONTEND] [LOG] ✅ [cleanupInvalidAssignmentIds] Aucun assignment_id invalide détecté
[14/07/2025 15:14:51] [FRONTEND] [LOG] ✅ [loadState] État chargé depuis l'API avec succès
[14/07/2025 15:14:51] [FRONTEND] [LOG] ✅ [init] Icône paramètres trouvée: [Objet non sérialisable]
[14/07/2025 15:14:51] [FRONTEND] [LOG] 🔍 [init] Recherche du bouton des paramètres dans le DOM
[14/07/2025 15:14:51] [FRONTEND] [LOG] 🔍 [init] Bouton des paramètres trouvé ? true
[14/07/2025 15:14:51] [BACKEND] [INFO] [LOGGER] 📋 [GET /api/employee-order] Aucun ordre personnalisé trouvé
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:14:51.933Z",
  "originalArgs": [
    "📋 [GET /api/employee-order] Aucun ordre personnalisé trouvé"
  ]
}
[14/07/2025 15:14:51] [FRONTEND] [LOG] 🔍 [init] Attachement du listener click sur le bouton des paramètres
[14/07/2025 15:14:51] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 60ms: 
            SELECT setting_value FROM app_setting {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:14:51.933Z",
  "originalArgs": [
    "✅ Requête exécutée en 60ms:",
    "\n            SELECT setting_value FROM app_setting",
    {}
  ]
}
[14/07/2025 15:14:51] [FRONTEND] [LOG] 🔍 [init] Recherche du bouton des paramètres header dans le DOM
[14/07/2025 15:14:51] [FRONTEND] [LOG] 🔍 [init] Bouton des paramètres header trouvé ? true
[14/07/2025 15:14:51] [FRONTEND] [LOG] 🔍 [init] Attachement du listener click sur le bouton des paramètres header
[14/07/2025 15:14:51] [FRONTEND] [LOG] ✅ [setupNavigationListeners] Écouteurs de navigation attachés
[14/07/2025 15:14:51] [FRONTEND] [LOG] 🎯 [setupViewNavigationListeners] Configuration des boutons de navigation...
[14/07/2025 15:14:51] [FRONTEND] [LOG] ✅ Boutons de navigation configurés
[14/07/2025 15:14:51] [FRONTEND] [LOG] 🔧 [ModalManager] Configuration des gestionnaires de modal
[14/07/2025 15:14:51] [FRONTEND] [LOG] ✅ [ModalManager] Gestionnaires de modal configurés
[14/07/2025 15:14:51] [FRONTEND] [LOG] 🔧 [setupGeneralSettings] Configuration des paramètres généraux
[14/07/2025 15:14:51] [FRONTEND] [LOG] 🔧 [setupGeneralSettings] Création du conteneur de fuseau horaire
[14/07/2025 15:14:51] [FRONTEND] [LOG] 📋 [loadEmployeeOrder] Chargement ordre des employés
[14/07/2025 15:14:51] [FRONTEND] [LOG] ✅ [setupGlobalEscapeHandler] Gestionnaire ESC global configuré
[14/07/2025 15:14:51] [FRONTEND] [WARN] ⚠️ [loadEmployeeOrder] Réponse API sans tableau employeeOrder
[14/07/2025 15:14:51] [FRONTEND] [LOG] ℹ️ [setupEmployeeDragDrop] Fonction désactivée - Utiliser setupDragAndDrop
[14/07/2025 15:14:51] [FRONTEND] [LOG] 🔧 [addEmergencyFixButton] Ajout du bouton de correction...
[14/07/2025 15:14:51] [FRONTEND] [LOG] ✅ [addEmergencyFixButton] Bouton de correction ajouté
[14/07/2025 15:14:51] [FRONTEND] [LOG] ✅ [init] Initialisation terminée avec succès
[14/07/2025 15:14:51] [FRONTEND] [LOG] 🎉 [Agenda] Application fullscreen initialisée avec succès
[14/07/2025 15:14:52] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[14/07/2025 15:14:52] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[14/07/2025 15:14:52] [FRONTEND] [LOG] ⚠️ [diagnoseRegularAssignmentGrips] Aucun shift régulier trouvé - Ré-application des attributions...
[14/07/2025 15:14:52] [FRONTEND] [LOG] 📊 [diagnoseRegularAssignmentGrips] Statistiques: {"totalShifts":0,"regularShifts":0,"regularAssignments":0}
[14/07/2025 15:14:52] [FRONTEND] [LOG] 🎯 [diagnoseRegularAssignmentGrips] Grips trouvés dans le DOM: 0
[14/07/2025 15:14:52] [FRONTEND] [LOG] 🔍 [diagnoseRegularAssignmentGrips] Diagnostic des grips d'attributions régulières
[14/07/2025 15:15:16] [FRONTEND] [DEBUG] [vite] connected.
[14/07/2025 15:15:16] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[14/07/2025 15:15:16] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:17.700Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:15:16] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:17.571Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:15:17] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:17.711Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:15:17] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:17.842Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:15:17] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[14/07/2025 15:15:17] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[14/07/2025 15:15:17] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[14/07/2025 15:15:17] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[14/07/2025 15:15:17] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[14/07/2025 15:15:17] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[14/07/2025 15:15:17] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[14/07/2025 15:15:17] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[14/07/2025 15:15:17] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[14/07/2025 15:15:17] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[14/07/2025 15:15:17] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[14/07/2025 15:15:17] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[14/07/2025 15:15:17] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[14/07/2025 15:15:17] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[14/07/2025 15:15:17] [FRONTEND] [LOG] [LogsPage] Fermeture de la connexion SSE.
[14/07/2025 15:15:17] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:18.144Z",
  "originalArgs": [
    "[query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ]
}
[14/07/2025 15:15:17] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[14/07/2025 15:15:17] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:18.162Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:15:17] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[14/07/2025 15:15:17] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[14/07/2025 15:15:17] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:18.284Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:15:17] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:18.286Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:15:17] [BACKEND] [INFO] [LOGS] Client connecté: 1752520518142
[14/07/2025 15:15:17] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:18.340Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:15:17] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:18.164Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:15:17] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 271ms: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:18.415Z",
  "originalArgs": [
    "✅ Requête exécutée en 271ms:",
    "SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ]
}
[14/07/2025 15:15:17] [BACKEND] [INFO] ✅ Requête exécutée en 271ms: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
[14/07/2025 15:15:18] [FRONTEND] [DEBUG] [vite] connected.
[14/07/2025 15:15:19] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[14/07/2025 15:15:19] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[14/07/2025 15:15:19] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[14/07/2025 15:15:19] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[14/07/2025 15:15:19] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[14/07/2025 15:15:19] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[14/07/2025 15:15:19] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[14/07/2025 15:15:19] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[14/07/2025 15:15:19] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[14/07/2025 15:15:19] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[14/07/2025 15:15:19] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[14/07/2025 15:15:19] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[14/07/2025 15:15:19] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[14/07/2025 15:15:19] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[14/07/2025 15:15:19] [FRONTEND] [LOG] 🧹 [Agenda] Nettoyage composant Agenda
[14/07/2025 15:15:19] [FRONTEND] [LOG] 🔄 [Agenda] Réinitialisation nécessaire (employés non chargés)
[14/07/2025 15:15:19] [FRONTEND] [LOG] 🚀 [Agenda] Initialisation TeamCalendarApp...
[14/07/2025 15:15:19] [FRONTEND] [LOG] 🚀 [init] Initialisation de TeamCalendarApp...
[14/07/2025 15:15:19] [FRONTEND] [LOG] 🧹 [clearRenderCache] Nettoyage du cache de rendu
[14/07/2025 15:15:19] [FRONTEND] [LOG] ✅ [clearRenderCache] Cache de rendu nettoyé
[14/07/2025 15:15:19] [FRONTEND] [LOG] 🔌 [ConnectionIndicator] Création de l'indicateur de connexion
[14/07/2025 15:15:19] [FRONTEND] [LOG] 🌐 [TeamCalendarApp] Chargement depuis l'API...
[14/07/2025 15:15:19] [FRONTEND] [LOG] 📂 [TeamCalendarApp] Chargement de l'état...
[14/07/2025 15:15:19] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT e.*, et.name as template_name 
      FROM employees e 
      LEFT JOIN employee_templates et ON e.template_id = et.id 
      ORDER BY e.name
     {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:20.378Z",
  "originalArgs": [
    "[query] Executing query: \n      SELECT e.*, et.name as template_name \n      FROM employees e \n      LEFT JOIN employee_templates et ON e.template_id = et.id \n      ORDER BY e.name\n    ",
    {}
  ]
}
[14/07/2025 15:15:19] [FRONTEND] [LOG] 🔌 [init] Indicateur de connexion initialisé
[14/07/2025 15:15:19] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 49ms: 
      SELECT e.*, et.name as template_name 
      {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:20.427Z",
  "originalArgs": [
    "✅ Requête exécutée en 49ms:",
    "\n      SELECT e.*, et.name as template_name \n     ",
    {}
  ]
}
[14/07/2025 15:15:19] [BACKEND] [INFO] ✅ Requête exécutée en 49ms: 
      SELECT e.*, et.name as template_name 
      {}
[14/07/2025 15:15:19] [FRONTEND] [LOG] 👥 [TeamCalendarApp] 5 employés chargés
[14/07/2025 15:15:19] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT s.*, e.name as employee_name, sp.label as post_label
      FROM shifts s
      LEFT JOIN employees e ON s.employee_id = e.id
      LEFT JOIN standard_posts sp ON s.post_id = sp.id
      ORDER BY s.date_key, e.name
     {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:20.463Z",
  "originalArgs": [
    "[query] Executing query: \n      SELECT s.*, e.name as employee_name, sp.label as post_label\n      FROM shifts s\n      LEFT JOIN employees e ON s.employee_id = e.id\n      LEFT JOIN standard_posts sp ON s.post_id = sp.id\n      ORDER BY s.date_key, e.name\n    ",
    {}
  ]
}
[14/07/2025 15:15:19] [FRONTEND] [LOG] 📦 [loadState] Chargement de tous les shifts depuis la base de données...
[14/07/2025 15:15:19] [BACKEND] [INFO] ✅ Requête exécutée en 46ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
[14/07/2025 15:15:19] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 46ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:20.509Z",
  "originalArgs": [
    "✅ Requête exécutée en 46ms:",
    "\n      SELECT s.*, e.name as employee_name, sp.lab",
    {}
  ]
}
[14/07/2025 15:15:19] [FRONTEND] [LOG] ✅ [loadState] Shifts organisés dans this.data.schedule
[14/07/2025 15:15:19] [FRONTEND] [LOG] 📅 [loadState] 0 shifts chargés depuis la base de données
[14/07/2025 15:15:19] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:20.528Z",
  "originalArgs": [
    "[query] Executing query: SELECT * FROM standard_posts ORDER BY label",
    {}
  ]
}
[14/07/2025 15:15:19] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 45ms: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:20.573Z",
  "originalArgs": [
    "✅ Requête exécutée en 45ms:",
    "SELECT * FROM standard_posts ORDER BY label",
    {}
  ]
}
[14/07/2025 15:15:19] [BACKEND] [INFO] ✅ Requête exécutée en 45ms: SELECT * FROM standard_posts ORDER BY label {}
[14/07/2025 15:15:19] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:20.590Z",
  "originalArgs": [
    "[query] Executing query: SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ]
}
[14/07/2025 15:15:19] [FRONTEND] [LOG] 🏢 [TeamCalendarApp] 5 postes standards chargés
[14/07/2025 15:15:19] [BACKEND] [INFO] ✅ Requête exécutée en 36ms: SELECT * FROM app_settings ORDER BY setting_key {}
[14/07/2025 15:15:19] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 36ms: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:20.626Z",
  "originalArgs": [
    "✅ Requête exécutée en 36ms:",
    "SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ]
}
[14/07/2025 15:15:19] [FRONTEND] [LOG] ⚙️ [loadState] Paramètres chargés depuis API: {}
[14/07/2025 15:15:19] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT 
        ra.id,
        ra.employee_id,
        ra.post_id,
        ra.days_of_week,
        ra.start_date,
        ra.end_date,
        ra.is_active,
        ra.excluded_dates,
        e.name as employee_name,
        sp.label as post_label,
        sp.hours as post_hours,
        sp.type as post_type,
        array_length(ra.days_of_week, 1) as days_count
      FROM regular_assignments ra
      LEFT JOIN employees e ON ra.employee_id = e.id
      LEFT JOIN standard_posts sp ON ra.post_id = sp.id
      WHERE ra.is_active = true
      ORDER BY e.name, sp.label, ra.start_date
     {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:20.655Z",
  "originalArgs": [
    "[query] Executing query: \n      SELECT \n        ra.id,\n        ra.employee_id,\n        ra.post_id,\n        ra.days_of_week,\n        ra.start_date,\n        ra.end_date,\n        ra.is_active,\n        ra.excluded_dates,\n        e.name as employee_name,\n        sp.label as post_label,\n        sp.hours as post_hours,\n        sp.type as post_type,\n        array_length(ra.days_of_week, 1) as days_count\n      FROM regular_assignments ra\n      LEFT JOIN employees e ON ra.employee_id = e.id\n      LEFT JOIN standard_posts sp ON ra.post_id = sp.id\n      WHERE ra.is_active = true\n      ORDER BY e.name, sp.label, ra.start_date\n    ",
    {}
  ]
}
[14/07/2025 15:15:19] [BACKEND] [INFO] ✅ Requête exécutée en 40ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
[14/07/2025 15:15:19] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 40ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:20.696Z",
  "originalArgs": [
    "✅ Requête exécutée en 40ms:",
    "\n      SELECT \n        ra.id,\n        ra.employee_",
    {}
  ]
}
[14/07/2025 15:15:20] [FRONTEND] [LOG] 📋 [loadState] 0/0 assignations régulières valides chargées et normalisées
[14/07/2025 15:15:20] [FRONTEND] [LOG] ✅ [fixInvalidAssignmentDates] Aucune date invalide trouvée
[14/07/2025 15:15:20] [FRONTEND] [LOG] 📋 [loadState] Assignations régulières chargées (application différée)
[14/07/2025 15:15:20] [FRONTEND] [LOG] 🔧 [fixInvalidAssignmentDates] Vérification des dates invalides...
[14/07/2025 15:15:20] [FRONTEND] [LOG] 🧹 [cleanupDuplicateRegularShifts] Nettoyage des doublons de shifts réguliers...
[14/07/2025 15:15:20] [BACKEND] [INFO] [LOGGER] 📋 [GET /api/employee-order] Récupération ordre des employés
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:20.739Z",
  "originalArgs": [
    "📋 [GET /api/employee-order] Récupération ordre des employés"
  ]
}
[14/07/2025 15:15:20] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'
         {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:20.740Z",
  "originalArgs": [
    "[query] Executing query: \n            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'\n        ",
    {}
  ]
}
[14/07/2025 15:15:20] [FRONTEND] [LOG] ✅ [cleanupDuplicateRegularShifts] Aucun doublon détecté
[14/07/2025 15:15:20] [FRONTEND] [LOG] 🧹 [cleanupInvalidAssignmentIds] Nettoyage des assignment_id invalides...
[14/07/2025 15:15:20] [FRONTEND] [LOG] ✅ [cleanupInvalidAssignmentIds] Aucun assignment_id invalide détecté
[14/07/2025 15:15:20] [FRONTEND] [LOG] ✅ [loadState] État chargé depuis l'API avec succès
[14/07/2025 15:15:20] [FRONTEND] [LOG] ✅ [init] Icône paramètres trouvée: [Objet non sérialisable]
[14/07/2025 15:15:20] [FRONTEND] [LOG] 🔍 [init] Recherche du bouton des paramètres dans le DOM
[14/07/2025 15:15:20] [FRONTEND] [LOG] 🔍 [init] Bouton des paramètres trouvé ? true
[14/07/2025 15:15:20] [FRONTEND] [LOG] 🔍 [init] Attachement du listener click sur le bouton des paramètres
[14/07/2025 15:15:20] [FRONTEND] [LOG] 🔍 [init] Recherche du bouton des paramètres header dans le DOM
[14/07/2025 15:15:20] [FRONTEND] [LOG] 🔍 [init] Bouton des paramètres header trouvé ? true
[14/07/2025 15:15:20] [FRONTEND] [LOG] ✅ [setupNavigationListeners] Écouteurs de navigation attachés
[14/07/2025 15:15:20] [FRONTEND] [LOG] 🔍 [init] Attachement du listener click sur le bouton des paramètres header
[14/07/2025 15:15:20] [FRONTEND] [LOG] 🎯 [setupViewNavigationListeners] Configuration des boutons de navigation...
[14/07/2025 15:15:20] [FRONTEND] [LOG] ✅ Boutons de navigation configurés
[14/07/2025 15:15:20] [FRONTEND] [LOG] 🔧 [ModalManager] Configuration des gestionnaires de modal
[14/07/2025 15:15:20] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[14/07/2025 15:15:20] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:20.886Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:15:20] [FRONTEND] [LOG] ✅ [ModalManager] Gestionnaires de modal configurés
[14/07/2025 15:15:20] [FRONTEND] [LOG] 🔧 [setupGeneralSettings] Configuration des paramètres généraux
[14/07/2025 15:15:20] [FRONTEND] [LOG] 🔧 [setupGeneralSettings] Création du conteneur de fuseau horaire
[14/07/2025 15:15:20] [FRONTEND] [LOG] ✅ [setupGlobalEscapeHandler] Gestionnaire ESC global configuré
[14/07/2025 15:15:20] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 181ms: 
            SELECT setting_value FROM app_setting {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:20.921Z",
  "originalArgs": [
    "✅ Requête exécutée en 181ms:",
    "\n            SELECT setting_value FROM app_setting",
    {}
  ]
}
[14/07/2025 15:15:20] [BACKEND] [INFO] [LOGGER] 📋 [GET /api/employee-order] Aucun ordre personnalisé trouvé
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:20.922Z",
  "originalArgs": [
    "📋 [GET /api/employee-order] Aucun ordre personnalisé trouvé"
  ]
}
[14/07/2025 15:15:20] [FRONTEND] [LOG] 📋 [loadEmployeeOrder] Chargement ordre des employés
[14/07/2025 15:15:20] [FRONTEND] [WARN] ⚠️ [loadEmployeeOrder] Réponse API sans tableau employeeOrder
[14/07/2025 15:15:20] [FRONTEND] [LOG] 🔧 [addEmergencyFixButton] Ajout du bouton de correction...
[14/07/2025 15:15:20] [FRONTEND] [LOG] ✅ [addEmergencyFixButton] Bouton de correction ajouté
[14/07/2025 15:15:20] [FRONTEND] [LOG] ✅ [init] Initialisation terminée avec succès
[14/07/2025 15:15:20] [FRONTEND] [LOG] ℹ️ [setupEmployeeDragDrop] Fonction désactivée - Utiliser setupDragAndDrop
[14/07/2025 15:15:20] [FRONTEND] [LOG] 🎉 [Agenda] Application fullscreen initialisée avec succès
[14/07/2025 15:15:20] [FRONTEND] [LOG] 🔍 [diagnoseRegularAssignmentGrips] Diagnostic des grips d'attributions régulières
[14/07/2025 15:15:20] [FRONTEND] [LOG] ⚠️ [diagnoseRegularAssignmentGrips] Aucun shift régulier trouvé - Ré-application des attributions...
[14/07/2025 15:15:20] [FRONTEND] [LOG] 🎯 [diagnoseRegularAssignmentGrips] Grips trouvés dans le DOM: 0
[14/07/2025 15:15:20] [FRONTEND] [LOG] 📊 [diagnoseRegularAssignmentGrips] Statistiques: {"totalShifts":0,"regularShifts":0,"regularAssignments":0}
[14/07/2025 15:15:20] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[14/07/2025 15:15:20] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[14/07/2025 15:15:24] [BACKEND] [INFO] [LOGS] Client déconnecté: 1752520518142
[14/07/2025 15:15:24] [FRONTEND] [ERROR] [LogsPage] Erreur SSE: {"isTrusted":true}
[14/07/2025 15:15:24] [FRONTEND] [DEBUG] [vite] connected.
[14/07/2025 15:15:24] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[14/07/2025 15:15:24] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[14/07/2025 15:15:25] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[14/07/2025 15:15:25] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[14/07/2025 15:15:25] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[14/07/2025 15:15:25] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[14/07/2025 15:15:25] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[14/07/2025 15:15:25] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[14/07/2025 15:15:25] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[14/07/2025 15:15:25] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[14/07/2025 15:15:25] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[14/07/2025 15:15:25] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[14/07/2025 15:15:25] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[14/07/2025 15:15:25] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[14/07/2025 15:15:25] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[14/07/2025 15:15:25] [FRONTEND] [LOG] [LogsPage] Fermeture de la connexion SSE.
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 128ms: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 128ms:",
    "SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:25.941Z"
}
[14/07/2025 15:15:25] [BACKEND] [INFO] ✅ Requête exécutée en 128ms: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
[14/07/2025 15:15:28] [FRONTEND] [DEBUG] [vite] connected.
[14/07/2025 15:15:29] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[14/07/2025 15:15:29] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[14/07/2025 15:15:29] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[14/07/2025 15:15:29] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[14/07/2025 15:15:29] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[14/07/2025 15:15:29] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[14/07/2025 15:15:29] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[14/07/2025 15:15:29] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[14/07/2025 15:15:29] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[14/07/2025 15:15:29] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[14/07/2025 15:15:29] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[14/07/2025 15:15:29] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[14/07/2025 15:15:29] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[14/07/2025 15:15:29] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[14/07/2025 15:15:29] [FRONTEND] [LOG] 🔄 [Agenda] Réinitialisation nécessaire (employés non chargés)
[14/07/2025 15:15:29] [FRONTEND] [LOG] 🧹 [Agenda] Nettoyage composant Agenda
[14/07/2025 15:15:29] [FRONTEND] [LOG] 🚀 [Agenda] Initialisation TeamCalendarApp...
[14/07/2025 15:15:29] [FRONTEND] [LOG] ✅ [clearRenderCache] Cache de rendu nettoyé
[14/07/2025 15:15:29] [FRONTEND] [LOG] 🧹 [clearRenderCache] Nettoyage du cache de rendu
[14/07/2025 15:15:29] [FRONTEND] [LOG] 🚀 [init] Initialisation de TeamCalendarApp...
[14/07/2025 15:15:29] [FRONTEND] [LOG] 🔌 [init] Indicateur de connexion initialisé
[14/07/2025 15:15:29] [FRONTEND] [LOG] 🔌 [ConnectionIndicator] Création de l'indicateur de connexion
[14/07/2025 15:15:29] [FRONTEND] [LOG] 📂 [TeamCalendarApp] Chargement de l'état...
[14/07/2025 15:15:29] [FRONTEND] [LOG] 🌐 [TeamCalendarApp] Chargement depuis l'API...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT e.*, et.name as template_name 
      FROM employees e 
      LEFT JOIN employee_templates et ON e.template_id = et.id 
      ORDER BY e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT e.*, et.name as template_name \n      FROM employees e \n      LEFT JOIN employee_templates et ON e.template_id = et.id \n      ORDER BY e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:30.289Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 50ms: 
      SELECT e.*, et.name as template_name 
      {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 50ms:",
    "\n      SELECT e.*, et.name as template_name \n     ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:30.340Z"
}
[14/07/2025 15:15:29] [BACKEND] [INFO] ✅ Requête exécutée en 50ms: 
      SELECT e.*, et.name as template_name 
      {}
[14/07/2025 15:15:29] [FRONTEND] [LOG] 👥 [TeamCalendarApp] 5 employés chargés
[14/07/2025 15:15:29] [FRONTEND] [LOG] 📦 [loadState] Chargement de tous les shifts depuis la base de données...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT s.*, e.name as employee_name, sp.label as post_label
      FROM shifts s
      LEFT JOIN employees e ON s.employee_id = e.id
      LEFT JOIN standard_posts sp ON s.post_id = sp.id
      ORDER BY s.date_key, e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT s.*, e.name as employee_name, sp.label as post_label\n      FROM shifts s\n      LEFT JOIN employees e ON s.employee_id = e.id\n      LEFT JOIN standard_posts sp ON s.post_id = sp.id\n      ORDER BY s.date_key, e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:30.362Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 51ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 51ms:",
    "\n      SELECT s.*, e.name as employee_name, sp.lab",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:30.413Z"
}
[14/07/2025 15:15:29] [BACKEND] [INFO] ✅ Requête exécutée en 51ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
[14/07/2025 15:15:29] [FRONTEND] [LOG] 📅 [loadState] 0 shifts chargés depuis la base de données
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:30.428Z"
}
[14/07/2025 15:15:29] [FRONTEND] [LOG] ✅ [loadState] Shifts organisés dans this.data.schedule
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 42ms: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 42ms:",
    "SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:30.469Z"
}
[14/07/2025 15:15:29] [BACKEND] [INFO] ✅ Requête exécutée en 42ms: SELECT * FROM standard_posts ORDER BY label {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:30.483Z"
}
[14/07/2025 15:15:29] [FRONTEND] [LOG] 🏢 [TeamCalendarApp] 5 postes standards chargés
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 37ms: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 37ms:",
    "SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:30.521Z"
}
[14/07/2025 15:15:29] [BACKEND] [INFO] ✅ Requête exécutée en 37ms: SELECT * FROM app_settings ORDER BY setting_key {}
[14/07/2025 15:15:29] [FRONTEND] [LOG] ⚙️ [loadState] Paramètres chargés depuis API: {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT 
        ra.id,
        ra.employee_id,
        ra.post_id,
        ra.days_of_week,
        ra.start_date,
        ra.end_date,
        ra.is_active,
        ra.excluded_dates,
        e.name as employee_name,
        sp.label as post_label,
        sp.hours as post_hours,
        sp.type as post_type,
        array_length(ra.days_of_week, 1) as days_count
      FROM regular_assignments ra
      LEFT JOIN employees e ON ra.employee_id = e.id
      LEFT JOIN standard_posts sp ON ra.post_id = sp.id
      WHERE ra.is_active = true
      ORDER BY e.name, sp.label, ra.start_date
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT \n        ra.id,\n        ra.employee_id,\n        ra.post_id,\n        ra.days_of_week,\n        ra.start_date,\n        ra.end_date,\n        ra.is_active,\n        ra.excluded_dates,\n        e.name as employee_name,\n        sp.label as post_label,\n        sp.hours as post_hours,\n        sp.type as post_type,\n        array_length(ra.days_of_week, 1) as days_count\n      FROM regular_assignments ra\n      LEFT JOIN employees e ON ra.employee_id = e.id\n      LEFT JOIN standard_posts sp ON ra.post_id = sp.id\n      WHERE ra.is_active = true\n      ORDER BY e.name, sp.label, ra.start_date\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:30.533Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 56ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 56ms:",
    "\n      SELECT \n        ra.id,\n        ra.employee_",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:30.589Z"
}
[14/07/2025 15:15:29] [BACKEND] [INFO] ✅ Requête exécutée en 56ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
[14/07/2025 15:15:29] [FRONTEND] [LOG] 📋 [loadState] 0/0 assignations régulières valides chargées et normalisées
[14/07/2025 15:15:29] [FRONTEND] [LOG] 🧹 [cleanupDuplicateRegularShifts] Nettoyage des doublons de shifts réguliers...
[14/07/2025 15:15:29] [FRONTEND] [LOG] 🔧 [fixInvalidAssignmentDates] Vérification des dates invalides...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'
         {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'\n        ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:30.629Z"
}
[14/07/2025 15:15:29] [FRONTEND] [LOG] 📋 [loadState] Assignations régulières chargées (application différée)
[14/07/2025 15:15:29] [FRONTEND] [LOG] ✅ [fixInvalidAssignmentDates] Aucune date invalide trouvée
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📋 [GET /api/employee-order] Récupération ordre des employés
  Data: {
  "originalArgs": [
    "📋 [GET /api/employee-order] Récupération ordre des employés"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:30.629Z"
}
[14/07/2025 15:15:29] [FRONTEND] [LOG] ✅ [cleanupDuplicateRegularShifts] Aucun doublon détecté
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 41ms: 
            SELECT setting_value FROM app_setting {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 41ms:",
    "\n            SELECT setting_value FROM app_setting",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:30.671Z"
}
[14/07/2025 15:15:29] [BACKEND] [INFO] ✅ Requête exécutée en 41ms: 
            SELECT setting_value FROM app_setting {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📋 [GET /api/employee-order] Aucun ordre personnalisé trouvé
  Data: {
  "originalArgs": [
    "📋 [GET /api/employee-order] Aucun ordre personnalisé trouvé"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:15:30.672Z"
}
[14/07/2025 15:15:29] [FRONTEND] [LOG] ✅ [loadState] État chargé depuis l'API avec succès
[14/07/2025 15:15:29] [FRONTEND] [LOG] ✅ [init] Icône paramètres trouvée: [Objet non sérialisable]
[14/07/2025 15:15:29] [FRONTEND] [LOG] ✅ [cleanupInvalidAssignmentIds] Aucun assignment_id invalide détecté
[14/07/2025 15:15:29] [FRONTEND] [LOG] 🧹 [cleanupInvalidAssignmentIds] Nettoyage des assignment_id invalides...
[14/07/2025 15:15:29] [FRONTEND] [LOG] 🔍 [init] Recherche du bouton des paramètres dans le DOM
[14/07/2025 15:15:30] [FRONTEND] [LOG] 🔍 [init] Bouton des paramètres trouvé ? true
[14/07/2025 15:15:30] [FRONTEND] [LOG] 🔍 [init] Attachement du listener click sur le bouton des paramètres
[14/07/2025 15:15:30] [FRONTEND] [LOG] 🔍 [init] Recherche du bouton des paramètres header dans le DOM
[14/07/2025 15:15:30] [FRONTEND] [LOG] 🔍 [init] Bouton des paramètres header trouvé ? true
[14/07/2025 15:15:30] [FRONTEND] [LOG] 🔍 [init] Attachement du listener click sur le bouton des paramètres header
[14/07/2025 15:15:30] [FRONTEND] [LOG] ✅ Boutons de navigation configurés
[14/07/2025 15:15:30] [FRONTEND] [LOG] ✅ [setupNavigationListeners] Écouteurs de navigation attachés
[14/07/2025 15:15:30] [FRONTEND] [LOG] 🎯 [setupViewNavigationListeners] Configuration des boutons de navigation...
[14/07/2025 15:15:30] [FRONTEND] [LOG] 🔧 [ModalManager] Configuration des gestionnaires de modal
[14/07/2025 15:15:30] [FRONTEND] [LOG] ✅ [ModalManager] Gestionnaires de modal configurés
[14/07/2025 15:15:30] [FRONTEND] [LOG] 🔧 [setupGeneralSettings] Configuration des paramètres généraux
[14/07/2025 15:15:30] [FRONTEND] [LOG] ✅ [setupGlobalEscapeHandler] Gestionnaire ESC global configuré
[14/07/2025 15:15:30] [FRONTEND] [LOG] 🔧 [setupGeneralSettings] Création du conteneur de fuseau horaire
[14/07/2025 15:15:30] [FRONTEND] [LOG] 📋 [loadEmployeeOrder] Chargement ordre des employés
[14/07/2025 15:15:30] [FRONTEND] [WARN] ⚠️ [loadEmployeeOrder] Réponse API sans tableau employeeOrder
[14/07/2025 15:15:30] [FRONTEND] [LOG] ℹ️ [setupEmployeeDragDrop] Fonction désactivée - Utiliser setupDragAndDrop
[14/07/2025 15:15:30] [FRONTEND] [LOG] 🔧 [addEmergencyFixButton] Ajout du bouton de correction...
[14/07/2025 15:15:30] [FRONTEND] [LOG] ✅ [addEmergencyFixButton] Bouton de correction ajouté
[14/07/2025 15:15:30] [FRONTEND] [LOG] ✅ [init] Initialisation terminée avec succès
[14/07/2025 15:15:30] [FRONTEND] [LOG] 🎉 [Agenda] Application fullscreen initialisée avec succès
[14/07/2025 15:15:30] [FRONTEND] [LOG] 🔍 [diagnoseRegularAssignmentGrips] Diagnostic des grips d'attributions régulières
[14/07/2025 15:15:30] [FRONTEND] [LOG] 🎯 [diagnoseRegularAssignmentGrips] Grips trouvés dans le DOM: 0
[14/07/2025 15:15:30] [FRONTEND] [LOG] 📊 [diagnoseRegularAssignmentGrips] Statistiques: {"totalShifts":0,"regularShifts":0,"regularAssignments":0}
[14/07/2025 15:15:30] [FRONTEND] [LOG] ⚠️ [diagnoseRegularAssignmentGrips] Aucun shift régulier trouvé - Ré-application des attributions...
[14/07/2025 15:15:30] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[14/07/2025 15:15:30] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[14/07/2025 15:15:34] [FRONTEND] [LOG] ➡️ Clic sur suivant
[14/07/2025 15:15:34] [FRONTEND] [LOG] 🧭 [navigateWeek] Direction: 1, Offset actuel: 0
[14/07/2025 15:15:34] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[14/07/2025 15:15:34] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[14/07/2025 15:15:34] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[14/07/2025 15:15:34] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[14/07/2025 15:15:34] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[14/07/2025 15:15:34] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[14/07/2025 15:15:34] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[14/07/2025 15:15:34] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 60 zones trouvées
[14/07/2025 15:15:34] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 60 zones de drop disponibles
[14/07/2025 15:15:34] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste e0332f5d-23e2-40fa-bf2d-d9271f03100a configuré comme draggable
[14/07/2025 15:15:34] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Matin: [1, 2, 3, 4, 5]
[14/07/2025 15:15:34] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[14/07/2025 15:15:34] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste e0332f5d-23e2-40fa-bf2d-d9271f03100a
[14/07/2025 15:15:35] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Nuit: [1, 2, 3, 4, 5]
[14/07/2025 15:15:35] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 9ebe6752-7cf6-47de-bf62-8eb912bd439d
[14/07/2025 15:15:35] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Soir: [1, 2, 3, 4, 5]
[14/07/2025 15:15:35] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 9ebe6752-7cf6-47de-bf62-8eb912bd439d configuré comme draggable
[14/07/2025 15:15:35] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7224fea3-1bc9-4ff2-ab34-35cb0cf19f3e configuré comme draggable
[14/07/2025 15:15:35] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7224fea3-1bc9-4ff2-ab34-35cb0cf19f3e
[14/07/2025 15:15:35] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE1: [0, 6]
[14/07/2025 15:15:35] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 5480db79-77a5-4f23-aa37-614936e8514b configuré comme draggable
[14/07/2025 15:15:35] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 5480db79-77a5-4f23-aa37-614936e8514b
[14/07/2025 15:15:35] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE2: [0, 6]
[14/07/2025 15:15:35] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 1ed512ff-9b54-4a63-90da-790b0594198b configuré comme draggable
[14/07/2025 15:15:35] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 1ed512ff-9b54-4a63-90da-790b0594198b
[14/07/2025 15:15:35] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 9ebe6752-7cf6-47de-bf62-8eb912bd439d configuré comme draggable
[14/07/2025 15:15:35] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste e0332f5d-23e2-40fa-bf2d-d9271f03100a configuré comme draggable
[14/07/2025 15:15:35] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste e0332f5d-23e2-40fa-bf2d-d9271f03100a
[14/07/2025 15:15:35] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 9ebe6752-7cf6-47de-bf62-8eb912bd439d
[14/07/2025 15:15:35] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7224fea3-1bc9-4ff2-ab34-35cb0cf19f3e configuré comme draggable
[14/07/2025 15:15:35] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 5480db79-77a5-4f23-aa37-614936e8514b configuré comme draggable
[14/07/2025 15:15:35] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 5480db79-77a5-4f23-aa37-614936e8514b
[14/07/2025 15:15:35] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7224fea3-1bc9-4ff2-ab34-35cb0cf19f3e
[14/07/2025 15:15:35] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 1ed512ff-9b54-4a63-90da-790b0594198b configuré comme draggable
[14/07/2025 15:15:35] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 1ed512ff-9b54-4a63-90da-790b0594198b
[14/07/2025 15:15:35] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[14/07/2025 15:15:35] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[14/07/2025 15:15:35] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[14/07/2025 15:15:35] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[14/07/2025 15:15:35] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[14/07/2025 15:15:35] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[14/07/2025 15:15:35] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[14/07/2025 15:15:35] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[14/07/2025 15:15:35] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (60 zones)
[14/07/2025 15:15:35] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[14/07/2025 15:15:35] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[14/07/2025 15:15:35] [FRONTEND] [LOG] ⚠️ [setupPostDragDrop] Drag & drop déjà initialisé, ignoré
[14/07/2025 15:15:35] [FRONTEND] [LOG] ✅ [SETUP] Drag & drop des employés configuré (mode simplifié)
[14/07/2025 15:15:35] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] 0 grips ajoutés
[14/07/2025 15:15:35] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[14/07/2025 15:15:35] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[14/07/2025 15:15:36] [FRONTEND] [LOG] 🧭 [navigateWeek] Direction: -1, Offset actuel: 1
[14/07/2025 15:15:36] [FRONTEND] [LOG] ⬅️ Clic sur précédent
[14/07/2025 15:15:37] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[14/07/2025 15:15:37] [FRONTEND] [WARN] ⚠️ [renderUnifiedCalendar] Conteneur available-posts-container non trouvé, création...
[14/07/2025 15:15:37] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[14/07/2025 15:15:37] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneur available-posts-container créé
[14/07/2025 15:15:37] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[14/07/2025 15:15:37] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[14/07/2025 15:15:37] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[14/07/2025 15:15:37] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[14/07/2025 15:15:37] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[14/07/2025 15:15:37] [FRONTEND] [LOG] ⚠️ [setupPostDragDrop] Drag & drop déjà initialisé, ignoré
[14/07/2025 15:15:37] [FRONTEND] [WARN] ⚠️ [SETUP] Erreur lors de la destruction: Error: can't access property "Sortable1752520529943", el is null
destroy@http://localhost:5173/node_modules/.vite/deps/sortablejs.js?v=189de042:1812:5
setupDragAndDrop@http://localhost:5173/src/teamCalendarApp.ts:5805:33
attachAllEventListeners@http://localhost:5173/src/teamCalendarApp.ts:5355:10
actualRender@http://localhost:5173/src/teamCalendarApp.ts:2463:10
render/this._debouncedRender<@http://localhost:5173/src/teamCalendarApp.ts:2448:12

[14/07/2025 15:15:37] [FRONTEND] [LOG] ✅ [SETUP] Drag & drop des employés configuré (mode simplifié)
[14/07/2025 15:15:37] [FRONTEND] [LOG] ⚠️ [setupPostDragDrop] Drag & drop déjà initialisé, ignoré
[14/07/2025 15:15:37] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[14/07/2025 15:15:37] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] 0 grips ajoutés
[14/07/2025 15:15:37] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées