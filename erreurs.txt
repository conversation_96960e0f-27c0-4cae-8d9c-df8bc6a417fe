[{
	"resource": "/c:/Users/<USER>/Desktop/interface/interface-3/interface - 1 Backup/backups/backup-2025-07-07T16-46-28-613Z/tsconfig.json",
	"owner": "typescript",
	"severity": 8,
	"message": "File 'c:/Users/<USER>/Desktop/interface/interface-3/interface - 1 Backup/backups/backup-2025-07-07T16-46-28-613Z/tsconfig.node.json' not found.",
	"source": "ts",
	"startLineNumber": 36,
	"startColumn": 18,
	"endLineNumber": 36,
	"endColumn": 52,
	"origin": "extHost1"
}]
[18/07/2025 10:26:49] [FRONTEND] [LOG] - autoFixGripIssues() : Correction automatique
[18/07/2025 10:26:49] [FRONTEND] [LOG] - startGripMonitoring() : Surveillance continue
[18/07/2025 10:26:49] [FRONTEND] [LOG] [LogsPage] Fermeture de la connexion SSE.
[18/07/2025 10:26:49] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[18/07/2025 10:26:49] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T14:26:49.149Z"
}
[18/07/2025 10:26:49] [BACKEND] [INFO] [LOGS] Client connecté: 1752848809148
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T14:26:49.001Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T14:26:49.170Z"
}
[18/07/2025 10:26:49] [FRONTEND] [LOG] - testSafeDateConversion() : Tester la conversion sécurisée
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T14:26:49.177Z"
}
[18/07/2025 10:26:01] [FRONTEND] [LOG] ✅ [ModalManager] Fonctionnalités activées pour l'onglet employees
[18/07/2025 10:26:01] [FRONTEND] [LOG] ✅ [ModalManager] Fonctionnalités activées pour l'onglet employee-templates
[18/07/2025 10:26:01] [FRONTEND] [LOG] 🔧 [ModalManager] Activation des fonctionnalités pour l'onglet general
[18/07/2025 10:26:01] [FRONTEND] [LOG] ✅ [ModalManager] Onglet employees déjà peuplé, activation des fonctionnalités
[18/07/2025 10:26:01] [FRONTEND] [LOG] ✅ [ModalManager] Fonctionnalités activées pour l'onglet assignments
[18/07/2025 10:26:01] [FRONTEND] [LOG] 🔧 [ModalManager] Activation des fonctionnalités pour l'onglet employee-templates
[18/07/2025 10:26:01] [FRONTEND] [LOG] ✅ [ModalManager] Conteneur onglet assignments trouvé, peuplement du contenu
[18/07/2025 10:26:01] [FRONTEND] [LOG] ✅ [ModalManager] Onglet employee-templates déjà peuplé, activation des fonctionnalités
[18/07/2025 10:26:01] [FRONTEND] [LOG] ✅ [ModalManager] Onglet assignments déjà peuplé, activation des fonctionnalités
[18/07/2025 10:26:01] [FRONTEND] [LOG] ✅ [ModalManager] Fonctionnalités activées pour l'onglet vacations
[18/07/2025 10:26:01] [FRONTEND] [LOG] ✅ [MODAL] Gestion des attributions activée
[18/07/2025 10:26:01] [FRONTEND] [LOG] 🔧 [ModalManager] Activation des fonctionnalités pour l'onglet vacations
[18/07/2025 10:26:02] [FRONTEND] [LOG] ✅ [MODAL] Onglet activé et contenu affiché: employees
[18/07/2025 10:26:02] [FRONTEND] [LOG] 🔍 [MODAL] Clic sur onglet: tab-employees -> tabId: employees
[18/07/2025 10:26:05] [FRONTEND] [LOG] ✅ [MODAL] Onglet activé et contenu affiché: employee-templates
[18/07/2025 10:26:05] [FRONTEND] [LOG] 🔍 [MODAL] Clic sur onglet: tab-employee-templates -> tabId: employee-templates
[18/07/2025 10:26:06] [FRONTEND] [WARN] ⚠️ [MODAL] Fonctionnalité de modèles d'employés non encore implémentée
[18/07/2025 10:26:06] [FRONTEND] [WARN] ⚠️ [MODAL] Fonctionnalité de modèles d'employés non encore implémentée
[18/07/2025 10:26:08] [FRONTEND] [LOG] ✅ [MODAL] Onglet activé et contenu affiché: assignments
[18/07/2025 10:26:08] [FRONTEND] [LOG] 🔍 [MODAL] Clic sur onglet: tab-assignments -> tabId: assignments
[18/07/2025 10:26:09] [FRONTEND] [LOG] ✅ [MODAL] Onglet activé et contenu affiché: vacations
[18/07/2025 10:26:09] [FRONTEND] [LOG] 🔍 [MODAL] Clic sur onglet: tab-vacations -> tabId: vacations
[18/07/2025 10:26:10] [FRONTEND] [WARN] ⚠️ [MODAL] TeamCalendarApp.handleAddVacationPeriod non disponible
[18/07/2025 10:26:10] [FRONTEND] [LOG] 🔍 [MODAL] Clic sur onglet: tab-posts -> tabId: posts
[18/07/2025 10:26:10] [FRONTEND] [LOG] ✅ [MODAL] Onglet activé et contenu affiché: posts
[18/07/2025 10:26:11] [FRONTEND] [ERROR] TypeError: can't access property "openPostModal", this.ModalManager is undefined http://localhost:5173/src/teamCalendarApp.ts 7141 5 Error: can't access property "openPostModal", this.ModalManager is undefined
handlePostEdit@http://localhost:5173/src/teamCalendarApp.ts:7141:5
init/<@http://localhost:5173/src/teamCalendarApp.ts:1095:70
EventListener.handleEvent*init@http://localhost:5173/src/teamCalendarApp.ts:1095:33
async*initApp@http://localhost:5173/src/Agenda.tsx:40:31
setTimeout handler*AgendaFullscreen/<@http://localhost:5173/src/Agenda.tsx:50:29
react-stack-bottom-frame@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:17478:20
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
commitHookEffectListMount@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8460:122
commitHookPassiveMountEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8518:60
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10016:42
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
doubleInvokeEffectsOnFiber@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11461:207
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
recursivelyTraverseAndDoubleInvokeEffectsInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11432:78
commitDoubleInvokeEffectsInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11469:55
flushPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11309:41
node_modules/react-dom/cjs/react-dom-client.development.js/commitRoot/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11060:32
performWorkUntilDeadline@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:36:58
EventHandlerNonNull*node_modules/scheduler/cjs/scheduler.development.js/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:156:9
node_modules/scheduler/cjs/scheduler.development.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:266:7
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/scheduler/index.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:277:24
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/react-dom/cjs/react-dom-client.development.js/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:15216:23
node_modules/react-dom/cjs/react-dom-client.development.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18060:7
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/react-dom/client.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18071:24
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18075:16

[18/07/2025 10:26:11] [FRONTEND] [WARN] ⚠️ [MODAL] TeamCalendarApp.handlePostEdit non disponible
[18/07/2025 10:26:12] [FRONTEND] [LOG] 🔍 [MODAL] Clic sur onglet: tab-general -> tabId: general
[18/07/2025 10:26:12] [FRONTEND] [LOG] ✅ [MODAL] Onglet activé et contenu affiché: general
[18/07/2025 10:26:16] [FRONTEND] [LOG] ✅ [MODAL] Modal fermé via bouton
[18/07/2025 10:26:20] [FRONTEND] [LOG] ➡️ Clic sur suivant
[18/07/2025 10:26:20] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-21 avant startDate 2025-07-23 (original: 2025-07-23)
[18/07/2025 10:26:20] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-22: {"assignmentId":"08b7356a-e76a-484d-b599-2d5d4a0d8b6b","dayDateStr":"2025-07-22","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 10:26:20] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-22 ignoré pour attribution 08b7356a-e76a-484d-b599-2d5d4a0d8b6b
[18/07/2025 10:26:20] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-23 (3)
[18/07/2025 10:26:20] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-23
[18/07/2025 10:26:20] [FRONTEND] [LOG] 📋 [navigateWeek] Application des attributions pour la nouvelle semaine 2025-W29
[18/07/2025 10:26:20] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-21 ignoré pour attribution 08b7356a-e76a-484d-b599-2d5d4a0d8b6b
[18/07/2025 10:26:20] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T14:26:20.747Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 10:26:20] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T14:26:20.767Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 10:26:20] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-22 (2)
[18/07/2025 10:26:21] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"08b7356a-e76a-484d-b599-2d5d4a0d8b6b","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-22T04:00:00.000Z","dayDateKey":"2025-07-22","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5]}
[18/07/2025 10:26:21] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[18/07/2025 10:26:21] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[18/07/2025 10:26:21] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[18/07/2025 10:26:21] [FRONTEND] [LOG] 📋 [navigateWeek] Application des attributions pour la nouvelle semaine 2025-W28
[18/07/2025 10:26:21] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"08b7356a-e76a-484d-b599-2d5d4a0d8b6b","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-23T04:00:00.000Z","dayDateKey":"2025-07-23","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5]}
[18/07/2025 10:26:21] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 5400f0bd-d560-4167-89ea-a6da77478469 commence après cette semaine (startDate: 2025-07-22, semaine se termine: 2025-07-19)
[18/07/2025 10:26:21] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 1 attributions actives sur 8 total
[18/07/2025 10:26:21] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T14:26:21.516Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 10:26:21] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-13 ignoré pour attribution ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 10:26:21] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-22 avant startDate 2025-07-23 (original: 2025-07-23)
[18/07/2025 10:26:21] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[18/07/2025 10:26:21] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T14:26:21.566Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 10:26:21] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-23 validé, shifts existants: 0
[18/07/2025 10:26:21] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[18/07/2025 10:26:21] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-15T04:00:00.000Z","dayDateKey":"2025-07-15","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5]}
[18/07/2025 10:26:22] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-15 (2)
[18/07/2025 10:26:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-16T04:00:00.000Z","dayDateKey":"2025-07-16","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5]}
[18/07/2025 10:26:22] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-14 ignoré pour attribution ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 10:26:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-18T04:00:00.000Z","dayDateKey":"2025-07-18","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5]}
[18/07/2025 10:26:22] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[18/07/2025 10:26:22] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-18 validé, shifts existants: 0
[18/07/2025 10:26:22] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-14 avant startDate 2025-07-18 (original: 2025-07-18)
[18/07/2025 10:26:22] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-18
[18/07/2025 10:26:22] [FRONTEND] [LOG] 🧭 [navigateWeek] Direction: 1, Offset actuel: 0
[18/07/2025 10:26:22] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T14:26:22.313Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 10:26:22] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T14:26:22.317Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 10:26:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-23: {"assignmentId":"08b7356a-e76a-484d-b599-2d5d4a0d8b6b","dayDateStr":"2025-07-23","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 10:26:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-14: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","dayDateStr":"2025-07-14","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 10:26:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-18: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","dayDateStr":"2025-07-18","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 10:26:22] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[18/07/2025 10:26:22] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[18/07/2025 10:26:22] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T14:26:20.828Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 10:26:22] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T14:26:22.435Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 10:26:22] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T14:26:22.465Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 10:26:22] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T14:26:20.814Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 10:26:22] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-07-18 (5)
[18/07/2025 10:26:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-13T04:00:00.000Z","dayDateKey":"2025-07-13","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5]}
[18/07/2025 10:26:22] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-17 ignoré pour attribution ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 10:26:22] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-13 (0)
[18/07/2025 10:26:22] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-17 avant startDate 2025-07-18 (original: 2025-07-18)
[18/07/2025 10:26:22] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution ed906322-db11-4d06-aa28-437b06c445c4: [{"dateKey":"2025-07-13","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-14","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-15","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-16","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-17","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-07-18","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-07-19","isWeekend":true,"dayOfWeek":6}]
[18/07/2025 10:26:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-17: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","dayDateStr":"2025-07-17","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 10:26:22] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"ed906322-db11-4d06-aa28-437b06c445c4","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"startDate":"2025-07-18","endDate":"2025-07-21","excludedDates":[]}
[18/07/2025 10:26:22] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[18/07/2025 10:26:22] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 42c64a69-ab23-451f-a314-c5c87622675d commence après cette semaine (startDate: 2025-07-30, semaine se termine: 2025-07-19)
[18/07/2025 10:26:23] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-17T04:00:00.000Z","dayDateKey":"2025-07-17","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5]}
[18/07/2025 10:26:23] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-17 (4)
[18/07/2025 10:26:23] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 08b7356a-e76a-484d-b599-2d5d4a0d8b6b commence après cette semaine (startDate: 2025-07-23, semaine se termine: 2025-07-19)
[18/07/2025 10:26:23] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T14:26:22.763Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 10:26:23] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-15 avant startDate 2025-07-18 (original: 2025-07-18)
[18/07/2025 10:26:23] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 6830f981-f642-4364-9b1d-0ff92e12fad2 commence après cette semaine (startDate: 2025-08-01, semaine se termine: 2025-07-19)
[18/07/2025 10:26:23] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[18/07/2025 10:26:23] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-16 ignoré pour attribution ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 10:26:23] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 86f97857-d86a-4e2a-aec3-831bf92012d4 commence après cette semaine (startDate: 2025-07-28, semaine se termine: 2025-07-19)
[18/07/2025 10:26:23] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-16 avant startDate 2025-07-18 (original: 2025-07-18)
[18/07/2025 10:26:23] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Application pour semaine 2025-W28
[18/07/2025 10:26:23] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-16: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","dayDateStr":"2025-07-16","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 10:26:23] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Cache invalidé pour forcer la régénération
[18/07/2025 10:26:23] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[18/07/2025 10:26:23] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-16 (3)
[18/07/2025 10:26:23] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] 0 shifts réguliers supprimés
[18/07/2025 10:26:23] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] Nettoyage des shifts réguliers existants...
[18/07/2025 10:26:23] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-15 ignoré pour attribution ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 10:26:23] [FRONTEND] [LOG] 🧭 [navigateWeek] Direction: -1, Offset actuel: 1
[18/07/2025 10:26:23] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-15: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","dayDateStr":"2025-07-15","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 10:26:23] [FRONTEND] [LOG] ⬅️ Clic sur précédent
[18/07/2025 10:26:23] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[18/07/2025 10:26:23] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-14T04:00:00.000Z","dayDateKey":"2025-07-14","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5]}
[18/07/2025 10:26:23] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-14 (1)
[18/07/2025 10:26:23] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 316031c4-9402-4394-970e-e598022f5118 commence après cette semaine (startDate: 2025-07-25, semaine se termine: 2025-07-19)
[18/07/2025 10:26:23] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution ed906322-db11-4d06-aa28-437b06c445c4 a une intersection avec cette semaine (2025-07-18 → 2025-07-21) vs (2025-07-13 → 2025-07-19)
[18/07/2025 10:26:23] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution d546a50b-43af-4f53-afd0-4a43c1f3959a commence après cette semaine (startDate: 2025-07-24, semaine se termine: 2025-07-19)
[18/07/2025 10:26:23] [FRONTEND] [ERROR] Promise non gérée Error: this.generateUUID is not a function
applyRegularAssignmentsForCurrentWeek/</<@http://localhost:5173/src/teamCalendarApp.ts:8165:22
applyRegularAssignmentsForCurrentWeek/<@http://localhost:5173/src/teamCalendarApp.ts:8141:26
applyRegularAssignmentsForCurrentWeek@http://localhost:5173/src/teamCalendarApp.ts:8120:25
navigateWeek@http://localhost:5173/src/teamCalendarApp.ts:682:14
setupNavigationListeners/<@http://localhost:5173/src/teamCalendarApp.ts:2489:14
EventListener.handleEvent*setupNavigationListeners@http://localhost:5173/src/teamCalendarApp.ts:2487:15
init@http://localhost:5173/src/teamCalendarApp.ts:1100:10
async*initApp@http://localhost:5173/src/Agenda.tsx:40:31
setTimeout handler*AgendaFullscreen/<@http://localhost:5173/src/Agenda.tsx:50:29
react-stack-bottom-frame@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:17478:20
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
commitHookEffectListMount@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8460:122
commitHookPassiveMountEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8518:60
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10016:42
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
doubleInvokeEffectsOnFiber@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11461:207
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
recursivelyTraverseAndDoubleInvokeEffectsInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11432:78
commitDoubleInvokeEffectsInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11469:55
flushPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11309:41
node_modules/react-dom/cjs/react-dom-client.development.js/commitRoot/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11060:32
performWorkUntilDeadline@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:36:58
EventHandlerNonNull*node_modules/scheduler/cjs/scheduler.development.js/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:156:9
node_modules/scheduler/cjs/scheduler.development.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:266:7
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/scheduler/index.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:277:24
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/react-dom/cjs/react-dom-client.development.js/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:15216:23
node_modules/react-dom/cjs/react-dom-client.development.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18060:7
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/react-dom/client.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18071:24
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18075:16

[18/07/2025 10:26:23] [FRONTEND] [ERROR] Promise non gérée Error: this.generateUUID is not a function
applyRegularAssignmentsForCurrentWeek/</<@http://localhost:5173/src/teamCalendarApp.ts:8165:22
applyRegularAssignmentsForCurrentWeek/<@http://localhost:5173/src/teamCalendarApp.ts:8141:26
applyRegularAssignmentsForCurrentWeek@http://localhost:5173/src/teamCalendarApp.ts:8120:25
navigateWeek@http://localhost:5173/src/teamCalendarApp.ts:682:14
setupNavigationListeners/<@http://localhost:5173/src/teamCalendarApp.ts:2485:14
EventListener.handleEvent*setupNavigationListeners@http://localhost:5173/src/teamCalendarApp.ts:2483:15
init@http://localhost:5173/src/teamCalendarApp.ts:1100:10
async*initApp@http://localhost:5173/src/Agenda.tsx:40:31
setTimeout handler*AgendaFullscreen/<@http://localhost:5173/src/Agenda.tsx:50:29
react-stack-bottom-frame@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:17478:20
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
commitHookEffectListMount@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8460:122
commitHookPassiveMountEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8518:60
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10016:42
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
doubleInvokeEffectsOnFiber@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11461:207
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
recursivelyTraverseAndDoubleInvokeEffectsInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11432:78
commitDoubleInvokeEffectsInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11469:55
flushPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11309:41
node_modules/react-dom/cjs/react-dom-client.development.js/commitRoot/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11060:32
performWorkUntilDeadline@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:36:58
EventHandlerNonNull*node_modules/scheduler/cjs/scheduler.development.js/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:156:9
node_modules/scheduler/cjs/scheduler.development.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:266:7
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/scheduler/index.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:277:24
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/react-dom/cjs/react-dom-client.development.js/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:15216:23
node_modules/react-dom/cjs/react-dom-client.development.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18060:7
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/react-dom/client.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18071:24
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18075:16

[18/07/2025 10:26:23] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] Nettoyage des shifts réguliers existants...
[18/07/2025 10:26:23] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] 0 shifts réguliers supprimés
[18/07/2025 10:26:23] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Application pour semaine 2025-W29
[18/07/2025 10:26:23] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 86f97857-d86a-4e2a-aec3-831bf92012d4 commence après cette semaine (startDate: 2025-07-28, semaine se termine: 2025-07-26)
[18/07/2025 10:26:23] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 6830f981-f642-4364-9b1d-0ff92e12fad2 commence après cette semaine (startDate: 2025-08-01, semaine se termine: 2025-07-26)
[18/07/2025 10:26:23] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution 08b7356a-e76a-484d-b599-2d5d4a0d8b6b a une intersection avec cette semaine (2025-07-23 → 2025-07-23) vs (2025-07-20 → 2025-07-26)
[18/07/2025 10:26:23] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 42c64a69-ab23-451f-a314-c5c87622675d commence après cette semaine (startDate: 2025-07-30, semaine se termine: 2025-07-26)
[18/07/2025 10:26:23] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution ed906322-db11-4d06-aa28-437b06c445c4 a une intersection avec cette semaine (2025-07-18 → 2025-07-21) vs (2025-07-20 → 2025-07-26)
[18/07/2025 10:26:23] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-21: {"assignmentId":"08b7356a-e76a-484d-b599-2d5d4a0d8b6b","dayDateStr":"2025-07-21","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 10:26:23] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution 5400f0bd-d560-4167-89ea-a6da77478469 a une intersection avec cette semaine (2025-07-22 → 2025-07-22) vs (2025-07-20 → 2025-07-26)
[18/07/2025 10:26:23] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution 316031c4-9402-4394-970e-e598022f5118 a une intersection avec cette semaine (2025-07-25 → 2025-07-27) vs (2025-07-20 → 2025-07-26)
[18/07/2025 10:26:23] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[18/07/2025 10:26:23] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution d546a50b-43af-4f53-afd0-4a43c1f3959a a une intersection avec cette semaine (2025-07-24 → 2025-07-24) vs (2025-07-20 → 2025-07-26)
[18/07/2025 10:26:23] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-21 (1)
[18/07/2025 10:26:23] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 5 attributions actives sur 8 total
[18/07/2025 10:26:23] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-20 ignoré pour attribution 08b7356a-e76a-484d-b599-2d5d4a0d8b6b
[18/07/2025 10:26:24] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[18/07/2025 10:26:24] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"08b7356a-e76a-484d-b599-2d5d4a0d8b6b","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"startDate":"2025-07-23","endDate":"2025-07-23","excludedDates":[]}
[18/07/2025 10:26:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"08b7356a-e76a-484d-b599-2d5d4a0d8b6b","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-21T04:00:00.000Z","dayDateKey":"2025-07-21","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5]}
[18/07/2025 10:26:24] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution 08b7356a-e76a-484d-b599-2d5d4a0d8b6b: [{"dateKey":"2025-07-20","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-21","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-22","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-23","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-24","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-07-25","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-07-26","isWeekend":true,"dayOfWeek":6}]
[18/07/2025 10:26:24] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-20 (0)
[18/07/2025 10:26:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"08b7356a-e76a-484d-b599-2d5d4a0d8b6b","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-20T04:00:00.000Z","dayDateKey":"2025-07-20","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5]}
[18/07/2025 10:26:47] [BACKEND] [INFO] [LOGS] Client déconnecté: 1752848744121
[18/07/2025 10:26:47] [FRONTEND] [ERROR] [LogsPage] Erreur SSE: {"isTrusted":true}
[18/07/2025 10:26:47] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T14:26:46.950Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 10:26:47] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T14:26:46.844Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 10:26:47] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T14:26:47.013Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 10:26:47] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[18/07/2025 10:26:47] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T14:26:47.133Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 10:26:47] [FRONTEND] [DEBUG] [vite] connected.
[18/07/2025 10:26:48] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[18/07/2025 10:26:48] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[18/07/2025 10:26:48] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[18/07/2025 10:26:48] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[18/07/2025 10:26:48] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[18/07/2025 10:26:48] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[18/07/2025 10:26:48] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[18/07/2025 10:26:48] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[18/07/2025 10:26:48] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[18/07/2025 10:26:48] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[18/07/2025 10:26:48] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[18/07/2025 10:26:48] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[18/07/2025 10:26:48] [FRONTEND] [LOG] 🧪 [TEST] Script de test de la modale drag & drop chargé
[18/07/2025 10:26:48] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[18/07/2025 10:26:48] [FRONTEND] [LOG] - testAssignmentModal() : Tester l'ouverture de la modale
[18/07/2025 10:26:49] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T14:26:48.784Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 10:26:49] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[18/07/2025 10:26:49] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[18/07/2025 10:26:49] [FRONTEND] [LOG] - diagnoseModalIssues() : Diagnostiquer les problèmes
[18/07/2025 10:26:49] [FRONTEND] [LOG] - forceReconfigureListeners() : Reconfigurer les listeners
[18/07/2025 10:26:49] [FRONTEND] [LOG] - testButtonClicks() : Tester les clics sur les boutons
[18/07/2025 10:26:49] [FRONTEND] [LOG] 🔍 [VALIDATION] Script de validation de la correction modale chargé
[18/07/2025 10:26:49] [FRONTEND] [LOG] ✅ [VALIDATION] Fonctions de validation disponibles:
[18/07/2025 10:26:49] [FRONTEND] [LOG] - validateMainFix() : Valider la correction principale
[18/07/2025 10:26:49] [FRONTEND] [LOG] - validateDOMElements() : Valider les éléments DOM
[18/07/2025 10:26:49] [FRONTEND] [LOG] - validateListeners() : Valider les listeners
[18/07/2025 10:26:49] [FRONTEND] [LOG] - runCompleteValidation() : Validation complète
[18/07/2025 10:26:49] [FRONTEND] [LOG] - testEndToEnd() : Test end-to-end complet
[18/07/2025 10:26:49] [FRONTEND] [LOG] 🧪 [TEST] Script de test pour la correction du fork modal chargé
[18/07/2025 10:26:49] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[18/07/2025 10:26:49] [FRONTEND] [LOG] - testDropDateDetection() : Tester la détection de date
[18/07/2025 10:26:49] [FRONTEND] [LOG] - testForkModalWithDate() : Tester le modal avec date
[18/07/2025 10:26:49] [FRONTEND] [LOG] - testCompleteForkFlow() : Tester le flux complet
[18/07/2025 10:26:49] [FRONTEND] [LOG] - diagnoseForkModalIssues() : Diagnostic complet
[18/07/2025 10:26:49] [FRONTEND] [LOG] 🧪 [DATE-TEST] Script de validation des corrections de date chargé
[18/07/2025 10:26:49] [FRONTEND] [LOG] ✅ [DATE-TEST] Fonctions de validation des dates disponibles:
[18/07/2025 10:26:49] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[18/07/2025 10:26:49] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T14:26:48.993Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 10:26:49] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T14:26:48.997Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 10:26:49] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T14:26:48.787Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 10:26:49] [FRONTEND] [LOG] - testButtonListeners() : Tester les listeners des boutons
[18/07/2025 10:26:49] [FRONTEND] [LOG] - testForkDateLogic() : Tester la logique de fork
[18/07/2025 10:26:49] [FRONTEND] [LOG] - testTimezoneEnhancements() : Tester les améliorations timezone
[18/07/2025 10:26:49] [FRONTEND] [LOG] - testDropDateDetectionWithTimezone() : Tester la détection avec timezone
[18/07/2025 10:26:49] [FRONTEND] [LOG] - runDateValidationSuite() : Suite complète de validation
[18/07/2025 10:26:49] [FRONTEND] [LOG] 🔧 [GRIP-FIX] Script de diagnostic et correction des GRIP handles chargé
[18/07/2025 10:26:49] [FRONTEND] [LOG] ✅ [GRIP-FIX] Fonctions de diagnostic et correction disponibles:
[18/07/2025 10:26:49] [FRONTEND] [LOG] - diagnoseGripHandles() : Diagnostic complet
[18/07/2025 10:26:49] [FRONTEND] [LOG] - forceRecreateGripHandles() : Recréation forcée
[18/07/2025 10:26:49] [FRONTEND] [LOG] - testSpecificGrip(assignmentId) : Test spécifique
[18/07/2025 10:26:49] [FRONTEND] [LOG] - autoFixGripIssues() : Correction automatique
[18/07/2025 10:26:49] [FRONTEND] [LOG] [LogsPage] Fermeture de la connexion SSE.
[18/07/2025 10:26:49] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[18/07/2025 10:26:49] [FRONTEND] [LOG] - startGripMonitoring() : Surveillance continue
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 136ms: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 136ms:",
    "SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T14:26:49.284Z"
}
[18/07/2025 10:26:49] [BACKEND] [INFO] ✅ Requête exécutée en 136ms: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
[18/07/2025 10:26:51] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Éléments DOM: {"modal":false,"closeBtn":false,"cancelBtn":false,"confirmBtn":false,"radioButtons":0}
[18/07/2025 10:26:51] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic des problèmes de modale...
[18/07/2025 10:26:51] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[18/07/2025 10:26:51] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] TeamCalendarApp: {"exists":false,"hasModalManager":false,"hasOpenFunction":false,"hasHandleFunction":false}
[18/07/2025 10:26:51] [FRONTEND] [DEBUG] [vite] connected.
[18/07/2025 10:26:51] [FRONTEND] [ERROR] ❌ [VALIDATION] TeamCalendarApp non disponible
[18/07/2025 10:26:51] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des éléments DOM...
[18/07/2025 10:26:51] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation de la correction principale...
[18/07/2025 10:26:51] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation complète...
[18/07/2025 10:26:51] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation automatique...
[18/07/2025 10:26:51] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des listeners...
[18/07/2025 10:26:51] [FRONTEND] [ERROR] ❌ [VALIDATION] Éléments DOM manquants
[18/07/2025 10:26:51] [FRONTEND] [LOG] 📋 [VALIDATION] Éléments DOM: {"modal":false,"closeBtn":false,"cancelBtn":false,"confirmBtn":false,"radioButtons":false}
[18/07/2025 10:26:51] [FRONTEND] [ERROR] ❌ [VALIDATION] Erreur lors de setupAssignmentContextModal: Error: can't access property "ModalManager", window.TeamCalendarApp is undefined
validateListeners@http://localhost:5173/validate-modal-fix.js:87:9
runCompleteValidation@http://localhost:5173/validate-modal-fix.js:103:20
@http://localhost:5173/validate-modal-fix.js:186:5

[18/07/2025 10:26:51] [FRONTEND] [LOG] 📊 [VALIDATION] Résultats de validation: {"mainFix":false,"domElements":false,"listeners":false}
[18/07/2025 10:26:51] [FRONTEND] [ERROR] ❌ [VALIDATION] Certaines validations ont échoué
[18/07/2025 10:26:51] [FRONTEND] [LOG] 🔧 [VALIDATION] Actions recommandées:
[18/07/2025 10:26:51] [FRONTEND] [LOG] - Vérifier que la modale assignment-context-modal existe dans le DOM
[18/07/2025 10:26:51] [FRONTEND] [LOG] - Vérifier les erreurs dans setupAssignmentContextModal
[18/07/2025 10:26:51] [FRONTEND] [LOG] - Vérifier que TeamCalendarApp et ModalManager sont correctement initialisés
[18/07/2025 10:26:51] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[18/07/2025 10:26:51] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic complet du fork modal...
[18/07/2025 10:26:51] [FRONTEND] [LOG] 📊 [DIAGNOSTIC] Résultats: {"teamCalendarApp":false,"detectDropDateFunction":false,"showConfirmationMenuFunction":false,"handleRegularAssignmentDropFunction":false,"regularAssignments":0,"employees":0}
[18/07/2025 10:26:51] [FRONTEND] [ERROR] ❌ [DIAGNOSTIC] Fonctions manquantes détectées
[18/07/2025 10:26:53] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[18/07/2025 10:26:53] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[18/07/2025 10:26:53] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[18/07/2025 10:26:53] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[18/07/2025 10:26:53] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[18/07/2025 10:26:53] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[18/07/2025 10:26:53] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[18/07/2025 10:26:53] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[18/07/2025 10:26:53] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[18/07/2025 10:26:53] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[18/07/2025 10:26:53] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[18/07/2025 10:26:53] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[18/07/2025 10:26:53] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[18/07/2025 10:26:53] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[18/07/2025 10:26:53] [FRONTEND] [LOG] 🧪 [TEST] Script de test de la modale drag & drop chargé
[18/07/2025 10:26:53] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[18/07/2025 10:26:53] [FRONTEND] [LOG] - testAssignmentModal() : Tester l'ouverture de la modale
[18/07/2025 10:26:53] [FRONTEND] [LOG] - testButtonListeners() : Tester les listeners des boutons
[18/07/2025 10:26:53] [FRONTEND] [LOG] - diagnoseModalIssues() : Diagnostiquer les problèmes
[18/07/2025 10:26:53] [FRONTEND] [LOG] - forceReconfigureListeners() : Reconfigurer les listeners
[18/07/2025 10:26:53] [FRONTEND] [LOG] - validateMainFix() : Valider la correction principale
[18/07/2025 10:26:53] [FRONTEND] [LOG] - testButtonClicks() : Tester les clics sur les boutons
[18/07/2025 10:26:53] [FRONTEND] [LOG] ✅ [VALIDATION] Fonctions de validation disponibles:
[18/07/2025 10:26:53] [FRONTEND] [LOG] 🔍 [VALIDATION] Script de validation de la correction modale chargé
[18/07/2025 10:26:53] [FRONTEND] [LOG] - validateDOMElements() : Valider les éléments DOM
[18/07/2025 10:26:53] [FRONTEND] [LOG] - runCompleteValidation() : Validation complète
[18/07/2025 10:26:53] [FRONTEND] [LOG] - validateListeners() : Valider les listeners
[18/07/2025 10:26:53] [FRONTEND] [LOG] - testEndToEnd() : Test end-to-end complet
[18/07/2025 10:26:53] [FRONTEND] [LOG] 🧪 [TEST] Script de test pour la correction du fork modal chargé
[18/07/2025 10:26:53] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[18/07/2025 10:26:53] [FRONTEND] [LOG] - testForkModalWithDate() : Tester le modal avec date
[18/07/2025 10:26:53] [FRONTEND] [LOG] - testDropDateDetection() : Tester la détection de date
[18/07/2025 10:26:53] [FRONTEND] [LOG] - testCompleteForkFlow() : Tester le flux complet
[18/07/2025 10:26:53] [FRONTEND] [LOG] 🧪 [DATE-TEST] Script de validation des corrections de date chargé
[18/07/2025 10:26:53] [FRONTEND] [LOG] - diagnoseForkModalIssues() : Diagnostic complet
[18/07/2025 10:26:53] [FRONTEND] [LOG] ✅ [DATE-TEST] Fonctions de validation des dates disponibles:
[18/07/2025 10:26:53] [FRONTEND] [LOG] - testSafeDateConversion() : Tester la conversion sécurisée
[18/07/2025 10:26:53] [FRONTEND] [LOG] - testForkDateLogic() : Tester la logique de fork
[18/07/2025 10:26:53] [FRONTEND] [LOG] ✅ [GRIP-FIX] Fonctions de diagnostic et correction disponibles:
[18/07/2025 10:26:53] [FRONTEND] [LOG] - testTimezoneEnhancements() : Tester les améliorations timezone
[18/07/2025 10:26:53] [FRONTEND] [LOG] - testDropDateDetectionWithTimezone() : Tester la détection avec timezone
[18/07/2025 10:26:53] [FRONTEND] [LOG] - runDateValidationSuite() : Suite complète de validation
[18/07/2025 10:26:53] [FRONTEND] [LOG] 🔧 [GRIP-FIX] Script de diagnostic et correction des GRIP handles chargé
[18/07/2025 10:26:53] [FRONTEND] [LOG] - diagnoseGripHandles() : Diagnostic complet
[18/07/2025 10:26:53] [FRONTEND] [LOG] 🚀 [DATE-TEST] Lancement de la validation automatique des dates...
[18/07/2025 10:26:53] [FRONTEND] [LOG] 🚀 [DATE-TEST] Lancement de la suite de validation des dates...
[18/07/2025 10:26:53] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Conversion de date sécurisée
[18/07/2025 10:26:53] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la conversion de date sécurisée...
[18/07/2025 10:26:53] [FRONTEND] [ERROR] ❌ [DATE-TEST] formatDateToKey non disponible
[18/07/2025 10:26:53] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la logique de fork avec dates...
[18/07/2025 10:26:53] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Logique de fork avec dates
[18/07/2025 10:26:53] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Améliorations timezone
[18/07/2025 10:26:53] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Détection de date avec timezone
[18/07/2025 10:26:53] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la détection de date avec timezone...
[18/07/2025 10:26:53] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test des améliorations de timezone...
[18/07/2025 10:26:53] [FRONTEND] [ERROR] ❌ [DATE-TEST] Données insuffisantes pour le test
[18/07/2025 10:26:53] [FRONTEND] [WARN] ⚠️ [DATE-TEST] Sélecteur de timezone non trouvé (normal si modal fermé)
[18/07/2025 10:26:53] [FRONTEND] [ERROR] ❌ [DATE-TEST] detectDropDateFromPosition non disponible
[18/07/2025 10:26:53] [FRONTEND] [LOG] ❌ Conversion de date sécurisée: ÉCHOUÉ
[18/07/2025 10:26:53] [FRONTEND] [LOG] ❌ Logique de fork avec dates: ÉCHOUÉ
[18/07/2025 10:26:53] [FRONTEND] [LOG] 
📊 [DATE-TEST] Résultats de la validation:
[18/07/2025 10:26:53] [FRONTEND] [LOG] ✅ Améliorations timezone: PASSÉ
[18/07/2025 10:26:53] [FRONTEND] [LOG] ❌ Détection de date avec timezone: ÉCHOUÉ
[18/07/2025 10:26:53] [FRONTEND] [LOG] 
⚠️ [DATE-TEST] Certaines validations ont échoué
[18/07/2025 10:26:53] [FRONTEND] [LOG] 🔧 [DATE-TEST] Vérifiez les logs ci-dessus pour les détails
[18/07/2025 10:26:53] [FRONTEND] [LOG] 🚀 [GRIP-FIX] Lancement du diagnostic automatique...
[18/07/2025 10:26:53] [FRONTEND] [LOG] 🔍 [GRIP-FIX] Diagnostic complet des GRIP handles...
[18/07/2025 10:26:53] [FRONTEND] [LOG] 📊 [GRIP-FIX] Total shifts trouvés: 0
[18/07/2025 10:26:53] [FRONTEND] [LOG] - testSpecificGrip(assignmentId) : Test spécifique
[18/07/2025 10:26:53] [FRONTEND] [LOG] - autoFixGripIssues() : Correction automatique
[18/07/2025 10:26:53] [FRONTEND] [LOG] - forceRecreateGripHandles() : Recréation forcée
[18/07/2025 10:26:53] [FRONTEND] [LOG] 📊 [GRIP-FIX] Résultats du diagnostic: {"totalShifts":0,"regularShifts":0,"gripsInDOM":0,"gripsWithEvents":0,"gripsResponsive":0,"issues":[]}
[18/07/2025 10:26:53] [FRONTEND] [LOG] - startGripMonitoring() : Surveillance continue
[18/07/2025 10:26:54] [FRONTEND] [LOG] 🚀 [Agenda] Initialisation TeamCalendarApp...
[18/07/2025 10:26:54] [FRONTEND] [LOG] 🚀 [init] Initialisation de TeamCalendarApp...
[18/07/2025 10:26:54] [FRONTEND] [LOG] 🧹 [clearRenderCache] Nettoyage du cache de rendu
[18/07/2025 10:26:54] [FRONTEND] [LOG] ✅ [clearRenderCache] Cache de rendu nettoyé
[18/07/2025 10:26:54] [FRONTEND] [LOG] 🔌 [ConnectionIndicator] Création de l'indicateur de connexion
[18/07/2025 10:26:54] [FRONTEND] [LOG] 🔌 [init] Indicateur de connexion initialisé
[18/07/2025 10:26:54] [FRONTEND] [LOG] 📂 [TeamCalendarApp] Chargement de l'état...
[18/07/2025 10:26:54] [FRONTEND] [LOG] 🧹 [Agenda] Nettoyage composant Agenda
[18/07/2025 10:26:54] [FRONTEND] [LOG] 🔄 [Agenda] Réinitialisation nécessaire (employés non chargés)
[18/07/2025 10:26:54] [FRONTEND] [LOG] 🌐 [TeamCalendarApp] Chargement depuis l'API...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT e.*, et.name as template_name 
      FROM employees e 
      LEFT JOIN employee_templates et ON e.template_id = et.id 
      ORDER BY e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT e.*, et.name as template_name \n      FROM employees e \n      LEFT JOIN employee_templates et ON e.template_id = et.id \n      ORDER BY e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T14:26:54.031Z"
}
[18/07/2025 10:26:54] [BACKEND] [INFO] ✅ Requête exécutée en 111ms: 
      SELECT e.*, et.name as template_name 
      {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 111ms: 
      SELECT e.*, et.name as template_name 
      {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 111ms:",
    "\n      SELECT e.*, et.name as template_name \n     ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T14:26:54.143Z"
}
[18/07/2025 10:26:54] [FRONTEND] [LOG] 👥 [TeamCalendarApp] 5 employés chargés
[18/07/2025 10:26:54] [FRONTEND] [LOG] 📦 [loadState] Chargement de tous les shifts depuis la base de données...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT s.*, e.name as employee_name, sp.label as post_label
      FROM shifts s
      LEFT JOIN employees e ON s.employee_id = e.id
      LEFT JOIN standard_posts sp ON s.post_id = sp.id
      ORDER BY s.date_key, e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT s.*, e.name as employee_name, sp.label as post_label\n      FROM shifts s\n      LEFT JOIN employees e ON s.employee_id = e.id\n      LEFT JOIN standard_posts sp ON s.post_id = sp.id\n      ORDER BY s.date_key, e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T14:26:54.367Z"
}
[18/07/2025 10:26:54] [BACKEND] [INFO] ✅ Requête exécutée en 45ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 45ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 45ms:",
    "\n      SELECT s.*, e.name as employee_name, sp.lab",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T14:26:54.411Z"
}
[18/07/2025 10:26:54] [FRONTEND] [LOG] 📅 [loadState] 0 shifts chargés depuis la base de données
[18/07/2025 10:26:54] [FRONTEND] [LOG] ✅ [loadState] Shifts organisés dans this.data.schedule
[18/07/2025 10:26:54] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[18/07/2025 10:26:54] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Éléments DOM: {"modal":true,"closeBtn":true,"cancelBtn":true,"confirmBtn":true,"radioButtons":4}
[18/07/2025 10:26:54] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic des problèmes de modale...
[18/07/2025 10:26:54] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] État de la modale: {"hidden":true,"classes":["hidden","fixed","inset-0","bg-black/40","backdrop-blur-sm","z-50","flex","items-center","justify-center","p-4"],"style":""}
[18/07/2025 10:26:54] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] TeamCalendarApp: {"exists":false,"hasModalManager":false,"hasOpenFunction":false,"hasHandleFunction":false}
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T14:26:54.734Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 51ms: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 51ms:",
    "SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T14:26:54.786Z"
}
[18/07/2025 10:26:55] [BACKEND] [INFO] ✅ Requête exécutée en 51ms: SELECT * FROM standard_posts ORDER BY label {}
[18/07/2025 10:26:55] [FRONTEND] [LOG] 🏢 [TeamCalendarApp] 5 postes standards chargés
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T14:26:54.976Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 39ms: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 39ms:",
    "SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T14:26:55.014Z"
}
[18/07/2025 10:26:55] [BACKEND] [INFO] ✅ Requête exécutée en 39ms: SELECT * FROM app_settings ORDER BY setting_key {}
[18/07/2025 10:26:55] [FRONTEND] [LOG] ⚙️ [loadState] Paramètres chargés depuis API: {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT 
        ra.id,
        ra.employee_id,
        ra.post_id,
        ra.days_of_week,
        ra.start_date,
        ra.end_date,
        ra.is_active,
        ra.excluded_dates,
        e.name as employee_name,
        sp.label as post_label,
        sp.hours as post_hours,
        sp.type as post_type,
        array_length(ra.days_of_week, 1) as days_count
      FROM regular_assignments ra
      LEFT JOIN employees e ON ra.employee_id = e.id
      LEFT JOIN standard_posts sp ON ra.post_id = sp.id
      WHERE ra.is_active = true
      ORDER BY e.name, sp.label, ra.start_date
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT \n        ra.id,\n        ra.employee_id,\n        ra.post_id,\n        ra.days_of_week,\n        ra.start_date,\n        ra.end_date,\n        ra.is_active,\n        ra.excluded_dates,\n        e.name as employee_name,\n        sp.label as post_label,\n        sp.hours as post_hours,\n        sp.type as post_type,\n        array_length(ra.days_of_week, 1) as days_count\n      FROM regular_assignments ra\n      LEFT JOIN employees e ON ra.employee_id = e.id\n      LEFT JOIN standard_posts sp ON ra.post_id = sp.id\n      WHERE ra.is_active = true\n      ORDER BY e.name, sp.label, ra.start_date\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T14:26:55.176Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 69ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 69ms:",
    "\n      SELECT \n        ra.id,\n        ra.employee_",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T14:26:55.245Z"
}
[18/07/2025 10:26:55] [BACKEND] [INFO] ✅ Requête exécutée en 69ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
[18/07/2025 10:26:55] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"86f97857-d86a-4e2a-aec3-831bf92012d4","employee_id":"cf78e945-72c7-48f3-a72d-bd0e245a284d","post_id":"0c74e25d-4692-46d7-b5aa-b84eee797971","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-28T04:00:00.000Z","end_date":"2025-07-29T04:00:00.000Z","excluded_dates":[]}
[18/07/2025 10:26:55] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"86f97857-d86a-4e2a-aec3-831bf92012d4","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-28","endDate":"2025-07-29","excludedDates":[]}
[18/07/2025 10:26:56] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"6830f981-f642-4364-9b1d-0ff92e12fad2","employee_id":"cf78e945-72c7-48f3-a72d-bd0e245a284d","post_id":"0c74e25d-4692-46d7-b5aa-b84eee797971","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-08-01T04:00:00.000Z","end_date":null,"excluded_dates":[]}
[18/07/2025 10:26:56] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"08b7356a-e76a-484d-b599-2d5d4a0d8b6b","employee_id":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","post_id":"0c74e25d-4692-46d7-b5aa-b84eee797971","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-23T04:00:00.000Z","end_date":"2025-07-23T04:00:00.000Z","excluded_dates":[]}
[18/07/2025 10:26:56] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"08b7356a-e76a-484d-b599-2d5d4a0d8b6b","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-23","endDate":"2025-07-23","excludedDates":[]}
[18/07/2025 10:26:56] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"6830f981-f642-4364-9b1d-0ff92e12fad2","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"isLimited":false,"startDate":"2025-08-01","endDate":null,"excludedDates":[]}
[18/07/2025 10:26:56] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"42c64a69-ab23-451f-a314-c5c87622675d","employee_id":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","post_id":"0c74e25d-4692-46d7-b5aa-b84eee797971","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-30T04:00:00.000Z","end_date":"2025-07-31T04:00:00.000Z","excluded_dates":[]}
[18/07/2025 10:26:56] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"42c64a69-ab23-451f-a314-c5c87622675d","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-30","endDate":"2025-07-31","excludedDates":[]}
[18/07/2025 10:26:56] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"5400f0bd-d560-4167-89ea-a6da77478469","employee_id":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","post_id":"0c74e25d-4692-46d7-b5aa-b84eee797971","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-22T04:00:00.000Z","end_date":"2025-07-22T04:00:00.000Z","excluded_dates":[]}
[18/07/2025 10:26:56] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"5400f0bd-d560-4167-89ea-a6da77478469","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-22","endDate":"2025-07-22","excludedDates":[]}
[18/07/2025 10:26:56] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"316031c4-9402-4394-970e-e598022f5118","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-25","endDate":"2025-07-27","excludedDates":[]}
[18/07/2025 10:26:56] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"316031c4-9402-4394-970e-e598022f5118","employee_id":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","post_id":"0c74e25d-4692-46d7-b5aa-b84eee797971","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-25T04:00:00.000Z","end_date":"2025-07-27T04:00:00.000Z","excluded_dates":[]}
[18/07/2025 10:26:56] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"ed906322-db11-4d06-aa28-437b06c445c4","employee_id":"59f5df3a-33ef-425c-bfa2-adca818cf94f","post_id":"0c74e25d-4692-46d7-b5aa-b84eee797971","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-18T04:00:00.000Z","end_date":"2025-07-21T04:00:00.000Z","excluded_dates":[]}
[18/07/2025 10:26:56] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"ed906322-db11-4d06-aa28-437b06c445c4","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-18","endDate":"2025-07-21","excludedDates":[]}
[18/07/2025 10:26:56] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"d546a50b-43af-4f53-afd0-4a43c1f3959a","employee_id":"59f5df3a-33ef-425c-bfa2-adca818cf94f","post_id":"0c74e25d-4692-46d7-b5aa-b84eee797971","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-24T04:00:00.000Z","end_date":"2025-07-24T04:00:00.000Z","excluded_dates":[]}
[18/07/2025 10:26:56] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"d546a50b-43af-4f53-afd0-4a43c1f3959a","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-24","endDate":"2025-07-24","excludedDates":[]}
[18/07/2025 10:26:56] [FRONTEND] [LOG] 🔧 [fixInvalidAssignmentDates] Vérification des dates invalides...
[18/07/2025 10:26:56] [FRONTEND] [LOG] 📋 [loadState] 8/8 assignations régulières valides chargées et normalisées
[18/07/2025 10:26:56] [FRONTEND] [LOG] ✅ [fixInvalidAssignmentDates] Aucune date invalide trouvée
[18/07/2025 10:26:56] [FRONTEND] [LOG] 📋 [loadState] Assignations régulières chargées (application différée)
[18/07/2025 10:26:56] [FRONTEND] [LOG] ✅ [init] Icône paramètres trouvée: [Objet non sérialisable]
[18/07/2025 10:26:56] [FRONTEND] [LOG] ⚙️ [loadStateFromLocalStorage] Aucun paramètre sauvegardé.
[18/07/2025 10:26:56] [FRONTEND] [ERROR] ❌ [TeamCalendarApp] Erreur lors du chargement: Error: this.cleanupDuplicateRegularShifts is not a function
loadState@http://localhost:5173/src/teamCalendarApp.ts:933:40
async*init@http://localhost:5173/src/teamCalendarApp.ts:1011:16
async*initApp@http://localhost:5173/src/Agenda.tsx:40:31
setTimeout handler*AgendaFullscreen/<@http://localhost:5173/src/Agenda.tsx:50:29
react-stack-bottom-frame@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:17478:20
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
commitHookEffectListMount@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8460:122
commitHookPassiveMountEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8518:60
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10016:42
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
doubleInvokeEffectsOnFiber@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11461:207
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
recursivelyTraverseAndDoubleInvokeEffectsInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11432:78
commitDoubleInvokeEffectsInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11469:55
flushPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11309:41
node_modules/react-dom/cjs/react-dom-client.development.js/commitRoot/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11060:32
performWorkUntilDeadline@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:36:58
EventHandlerNonNull*node_modules/scheduler/cjs/scheduler.development.js/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:156:9
node_modules/scheduler/cjs/scheduler.development.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:266:7
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/scheduler/index.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:277:24
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/react-dom/cjs/react-dom-client.development.js/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:15216:23
node_modules/react-dom/cjs/react-dom-client.development.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18060:7
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/react-dom/client.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18071:24
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18075:16

[18/07/2025 10:26:56] [FRONTEND] [LOG] 🔍 [init] Recherche du bouton des paramètres dans le DOM
[18/07/2025 10:26:56] [FRONTEND] [LOG] 🔍 [init] Bouton des paramètres trouvé ? true
[18/07/2025 10:26:56] [FRONTEND] [LOG] 🔍 [init] Attachement du listener click sur le bouton des paramètres
[18/07/2025 10:26:56] [FRONTEND] [LOG] 🔍 [init] Recherche du bouton des paramètres header dans le DOM
[18/07/2025 10:26:56] [FRONTEND] [LOG] 🔍 [init] Bouton des paramètres header trouvé ? true
[18/07/2025 10:26:56] [FRONTEND] [LOG] ✅ [setupNavigationListeners] Écouteurs de navigation attachés
[18/07/2025 10:26:56] [FRONTEND] [LOG] 🔍 [init] Attachement du listener click sur le bouton des paramètres header
[18/07/2025 10:26:56] [FRONTEND] [LOG] 🎯 [setupViewNavigationListeners] Configuration des boutons de navigation...
[18/07/2025 10:26:56] [FRONTEND] [LOG] ✅ Boutons de navigation configurés
[18/07/2025 10:26:56] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation automatique...
[18/07/2025 10:26:56] [FRONTEND] [ERROR] ❌ [Agenda] Erreur lors de l'initialisation: Error: can't access property "init", this.ModalManager is undefined
init@http://localhost:5173/src/teamCalendarApp.ts:1116:5
async*initApp@http://localhost:5173/src/Agenda.tsx:40:31
setTimeout handler*AgendaFullscreen/<@http://localhost:5173/src/Agenda.tsx:50:29
react-stack-bottom-frame@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:17478:20
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
commitHookEffectListMount@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8460:122
commitHookPassiveMountEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8518:60
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10016:42
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
doubleInvokeEffectsOnFiber@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11461:207
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
recursivelyTraverseAndDoubleInvokeEffectsInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11432:78
commitDoubleInvokeEffectsInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11469:55
flushPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11309:41
node_modules/react-dom/cjs/react-dom-client.development.js/commitRoot/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11060:32
performWorkUntilDeadline@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:36:58
EventHandlerNonNull*node_modules/scheduler/cjs/scheduler.development.js/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:156:9
node_modules/scheduler/cjs/scheduler.development.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:266:7
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/scheduler/index.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:277:24
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/react-dom/cjs/react-dom-client.development.js/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:15216:23
node_modules/react-dom/cjs/react-dom-client.development.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18060:7
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/react-dom/client.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18071:24
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18075:16

[18/07/2025 10:26:56] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation complète...
[18/07/2025 10:26:56] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation de la correction principale...
[18/07/2025 10:26:56] [FRONTEND] [ERROR] ❌ [VALIDATION] TeamCalendarApp non disponible
[18/07/2025 10:26:56] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des éléments DOM...
[18/07/2025 10:26:56] [FRONTEND] [LOG] 📋 [VALIDATION] Éléments DOM: {"modal":true,"closeBtn":true,"cancelBtn":true,"confirmBtn":true,"radioButtons":true}
[18/07/2025 10:26:56] [FRONTEND] [LOG] ✅ [VALIDATION] Tous les éléments DOM sont présents
[18/07/2025 10:26:56] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des listeners...
[18/07/2025 10:26:56] [FRONTEND] [ERROR] ❌ [VALIDATION] Erreur lors de setupAssignmentContextModal: Error: can't access property "ModalManager", window.TeamCalendarApp is undefined
validateListeners@http://localhost:5173/validate-modal-fix.js:87:9
runCompleteValidation@http://localhost:5173/validate-modal-fix.js:103:20
@http://localhost:5173/validate-modal-fix.js:186:5
setTimeout handler*@http://localhost:5173/validate-modal-fix.js:184:11

[18/07/2025 10:26:56] [FRONTEND] [LOG] 📊 [VALIDATION] Résultats de validation: {"mainFix":false,"domElements":true,"listeners":false}
[18/07/2025 10:26:56] [FRONTEND] [ERROR] ❌ [VALIDATION] Certaines validations ont échoué
[18/07/2025 10:26:56] [FRONTEND] [LOG] 🔧 [VALIDATION] Actions recommandées:
[18/07/2025 10:26:56] [FRONTEND] [LOG] - Vérifier que TeamCalendarApp et ModalManager sont correctement initialisés
[18/07/2025 10:26:56] [FRONTEND] [LOG] - Vérifier les erreurs dans setupAssignmentContextModal
[18/07/2025 10:26:56] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[18/07/2025 10:26:56] [FRONTEND] [ERROR] ❌ [DIAGNOSTIC] Fonctions manquantes détectées
[18/07/2025 10:26:56] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic complet du fork modal...
[18/07/2025 10:26:56] [FRONTEND] [LOG] 📊 [DIAGNOSTIC] Résultats: {"teamCalendarApp":false,"detectDropDateFunction":false,"showConfirmationMenuFunction":false,"handleRegularAssignmentDropFunction":false,"regularAssignments":0,"employees":0}
[18/07/2025 10:26:57] [FRONTEND] [LOG] 🚀 [DATE-TEST] Lancement de la validation automatique des dates...
[18/07/2025 10:26:57] [FRONTEND] [LOG] 🚀 [DATE-TEST] Lancement de la suite de validation des dates...
[18/07/2025 10:26:57] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Conversion de date sécurisée
[18/07/2025 10:26:57] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la conversion de date sécurisée...
[18/07/2025 10:26:57] [FRONTEND] [ERROR] ❌ [DATE-TEST] formatDateToKey non disponible
[18/07/2025 10:26:57] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Logique de fork avec dates
[18/07/2025 10:26:57] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la logique de fork avec dates...
[18/07/2025 10:26:57] [FRONTEND] [ERROR] ❌ [DATE-TEST] Données insuffisantes pour le test
[18/07/2025 10:26:57] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Améliorations timezone
[18/07/2025 10:26:58] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test des améliorations de timezone...
[18/07/2025 10:26:58] [FRONTEND] [WARN] ⚠️ [DATE-TEST] Sélecteur de timezone non trouvé (normal si modal fermé)
[18/07/2025 10:26:58] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Détection de date avec timezone
[18/07/2025 10:26:58] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la détection de date avec timezone...
[18/07/2025 10:26:58] [FRONTEND] [ERROR] ❌ [DATE-TEST] detectDropDateFromPosition non disponible
[18/07/2025 10:26:58] [FRONTEND] [LOG] 
📊 [DATE-TEST] Résultats de la validation:
[18/07/2025 10:26:58] [FRONTEND] [LOG] ❌ Conversion de date sécurisée: ÉCHOUÉ
[18/07/2025 10:26:58] [FRONTEND] [LOG] ❌ Logique de fork avec dates: ÉCHOUÉ
[18/07/2025 10:26:58] [FRONTEND] [LOG] ✅ Améliorations timezone: PASSÉ
[18/07/2025 10:26:58] [FRONTEND] [LOG] ❌ Détection de date avec timezone: ÉCHOUÉ
[18/07/2025 10:26:58] [FRONTEND] [LOG] 
⚠️ [DATE-TEST] Certaines validations ont échoué
[18/07/2025 10:26:58] [FRONTEND] [LOG] 🔧 [DATE-TEST] Vérifiez les logs ci-dessus pour les détails
[18/07/2025 10:26:58] [FRONTEND] [LOG] 🚀 [GRIP-FIX] Lancement du diagnostic automatique...
[18/07/2025 10:26:58] [FRONTEND] [LOG] 🔍 [GRIP-FIX] Diagnostic complet des GRIP handles...
[18/07/2025 10:26:58] [FRONTEND] [LOG] 📊 [GRIP-FIX] Total shifts trouvés: 0
[18/07/2025 10:26:58] [FRONTEND] [LOG] 📊 [GRIP-FIX] Résultats du diagnostic: {"totalShifts":0,"regularShifts":0,"gripsInDOM":0,"gripsWithEvents":0,"gripsResponsive":0,"issues":[]}
[18/07/2025 10:27:03] [FRONTEND] [LOG] 🔍 [click] Event click détecté sur le bouton des paramètres
[18/07/2025 10:27:03] [FRONTEND] [LOG] ✅ [MODAL] Paramètres généraux activés
[18/07/2025 10:27:03] [FRONTEND] [LOG] ✅ [ModalManager] Fonctionnalités activées pour l'onglet general
[18/07/2025 10:27:03] [FRONTEND] [LOG] ✅ [ModalManager] Rendu du contenu terminé
[18/07/2025 10:27:03] [FRONTEND] [LOG] ✅ [MODAL] Modal paramètres ouvert avec succès
[18/07/2025 10:27:03] [FRONTEND] [LOG] 🔧 [ModalFunctionalitiesManager] Initialisation du gestionnaire de modales
[18/07/2025 10:27:03] [FRONTEND] [LOG] 🔧 [openSettingsModal] Ouverture du modal demandée via gestionnaire externe
[18/07/2025 10:27:03] [FRONTEND] [LOG] 🔍 [MODAL] Ouverture de la modale paramètres
[18/07/2025 10:27:03] [FRONTEND] [LOG] 🔧 [ModalManager] Création du modal paramètres
[18/07/2025 10:27:03] [FRONTEND] [LOG] ✅ [ModalManager] Modal paramètres créé avec succès
[18/07/2025 10:27:03] [FRONTEND] [LOG] ✅ [ModalManager] Conteneurs d'onglets détectés dans le modal React
[18/07/2025 10:27:03] [FRONTEND] [LOG] ✅ [ModalManager] Modal React trouvé, utilisation des conteneurs d'onglets existants
[18/07/2025 10:27:03] [FRONTEND] [LOG] ✅ [ModalManager] Utilisation du modal existant de l'interface React
[18/07/2025 10:27:03] [FRONTEND] [LOG] 🔍 [MODAL] Initialisation des fonctionnalités du modal
[18/07/2025 10:27:03] [FRONTEND] [LOG] 🔍 [MODAL] Clic sur onglet: tab-posts -> tabId: posts
[18/07/2025 10:27:03] [FRONTEND] [LOG] 🔍 [MODAL] Activation des onglets: 6 boutons, 6 contenus trouvés
[18/07/2025 10:27:03] [FRONTEND] [LOG] 🔍 [MODAL] Éléments trouvés: 6 boutons d'onglets, 6 contenus d'onglets
[18/07/2025 10:27:03] [FRONTEND] [LOG] ✅ [MODAL] Onglet activé et contenu affiché: posts
[18/07/2025 10:27:03] [FRONTEND] [LOG] ✅ [MODAL] Gestion des onglets activée
[18/07/2025 10:27:03] [FRONTEND] [LOG] ✅ [MODAL] Fermeture du modal activée
[18/07/2025 10:27:03] [FRONTEND] [LOG] ✅ [MODAL] Modal visible
[18/07/2025 10:27:03] [FRONTEND] [LOG] 🔍 [ModalManager] Début renderSettingsContent, vérification des éléments
[18/07/2025 10:27:03] [FRONTEND] [LOG] 🔍 [ModalManager] this.elements.settingsModal = existe
[18/07/2025 10:27:03] [FRONTEND] [LOG] 🔧 [ModalManager] Rendu du contenu du modal paramètres
[18/07/2025 10:27:03] [FRONTEND] [LOG] 🔍 [ModalManager] this.elements.settingsContent = existe
[18/07/2025 10:27:03] [FRONTEND] [LOG] ✅ [ModalManager] Onglet posts déjà peuplé, activation des fonctionnalités
[18/07/2025 10:27:03] [FRONTEND] [LOG] ✅ [MODAL] Gestion des modèles d'employés activée
[18/07/2025 10:27:03] [FRONTEND] [LOG] ✅ [ModalManager] Conteneur onglet posts trouvé, peuplement du contenu
[18/07/2025 10:27:03] [FRONTEND] [LOG] 🔧 [ModalManager] Utilisation des conteneurs d'onglets existants du modal React
[18/07/2025 10:27:03] [FRONTEND] [LOG] ✅ [ModalManager] Fonctionnalités activées pour l'onglet assignments
[18/07/2025 10:27:03] [FRONTEND] [LOG] 🔧 [ModalManager] Activation des fonctionnalités pour l'onglet assignments
[18/07/2025 10:27:03] [FRONTEND] [LOG] 🔍 [ModalManager] Nombre d'enfants du contenu: 1
[18/07/2025 10:27:03] [FRONTEND] [LOG] ✅ [MODAL] Gestion des vacances activée
[18/07/2025 10:27:03] [FRONTEND] [LOG] ✅ [MODAL] Gestion des postes activée
[18/07/2025 10:27:03] [FRONTEND] [LOG] 🔧 [ModalManager] Activation des fonctionnalités pour l'onglet vacations
[18/07/2025 10:27:03] [FRONTEND] [LOG] ✅ [ModalManager] Onglet vacations déjà peuplé, activation des fonctionnalités
[18/07/2025 10:27:03] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T14:27:03.650Z"
}
[18/07/2025 10:27:03] [FRONTEND] [LOG] ✅ [MODAL] Fonctionnalités de base activées, les onglets seront peuplés à la demande
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T14:27:03.662Z"
}
[18/07/2025 10:27:03] [FRONTEND] [LOG] 🔧 [ModalManager] Activation des fonctionnalités pour l'onglet posts
[18/07/2025 10:27:03] [FRONTEND] [LOG] ✅ [ModalManager] Conteneur onglet vacations trouvé, peuplement du contenu
[18/07/2025 10:27:03] [FRONTEND] [LOG] ✅ [MODAL] Toutes les fonctionnalités du modal ont été initialisées
[18/07/2025 10:27:03] [FRONTEND] [LOG] ✅ [ModalManager] Fonctionnalités activées pour l'onglet posts
[18/07/2025 10:27:03] [FRONTEND] [LOG] ✅ [MODAL] Gestion des attributions activée
[18/07/2025 10:27:03] [FRONTEND] [LOG] 🔧 [ModalManager] Activation des fonctionnalités pour l'onglet general
[18/07/2025 10:27:03] [FRONTEND] [LOG] ✅ [ModalManager] Conteneur onglet employee-templates trouvé, peuplement du contenu
[18/07/2025 10:27:03] [FRONTEND] [LOG] ✅ [ModalManager] Onglet general déjà peuplé, activation des fonctionnalités
[18/07/2025 10:27:03] [FRONTEND] [LOG] ✅ [ModalManager] Onglet employee-templates déjà peuplé, activation des fonctionnalités
[18/07/2025 10:27:03] [FRONTEND] [LOG] ✅ [ModalManager] Conteneur onglet general trouvé, peuplement du contenu
[18/07/2025 10:27:03] [FRONTEND] [LOG] 🔧 [ModalManager] Activation des fonctionnalités pour l'onglet employee-templates
[18/07/2025 10:27:03] [FRONTEND] [LOG] ✅ [ModalManager] Fonctionnalités activées pour l'onglet employees
[18/07/2025 10:27:03] [FRONTEND] [LOG] ✅ [ModalManager] Fonctionnalités activées pour l'onglet employee-templates
[18/07/2025 10:27:03] [FRONTEND] [LOG] ✅ [MODAL] Gestion des employés activée
[18/07/2025 10:27:04] [FRONTEND] [LOG] ✅ [ModalManager] Conteneur onglet employees trouvé, peuplement du contenu
[18/07/2025 10:27:04] [FRONTEND] [LOG] 🔧 [ModalManager] Activation des fonctionnalités pour l'onglet employees
[18/07/2025 10:27:04] [FRONTEND] [LOG] ✅ [ModalManager] Onglet employees déjà peuplé, activation des fonctionnalités
[18/07/2025 10:27:04] [FRONTEND] [LOG] ✅ [ModalManager] Onglet assignments déjà peuplé, activation des fonctionnalités
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T14:27:03.849Z"
}
[18/07/2025 10:27:04] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[18/07/2025 10:27:04] [FRONTEND] [LOG] ✅ [ModalManager] Conteneur onglet assignments trouvé, peuplement du contenu
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T14:27:03.853Z"
}
[18/07/2025 10:27:04] [FRONTEND] [LOG] ✅ [ModalManager] Fonctionnalités activées pour l'onglet vacations
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T14:27:03.866Z"
}
[18/07/2025 10:27:04] [FRONTEND] [ERROR] TypeError: can't access property "openPostModal", this.ModalManager is undefined http://localhost:5173/src/teamCalendarApp.ts 7141 5 Error: can't access property "openPostModal", this.ModalManager is undefined
handlePostEdit@http://localhost:5173/src/teamCalendarApp.ts:7141:5
init/<@http://localhost:5173/src/teamCalendarApp.ts:1095:70
EventListener.handleEvent*init@http://localhost:5173/src/teamCalendarApp.ts:1095:33
async*initApp@http://localhost:5173/src/Agenda.tsx:40:31
setTimeout handler*AgendaFullscreen/<@http://localhost:5173/src/Agenda.tsx:50:29
react-stack-bottom-frame@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:17478:20
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
commitHookEffectListMount@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8460:122
commitHookPassiveMountEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8518:60
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10016:42
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
doubleInvokeEffectsOnFiber@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11461:207
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
recursivelyTraverseAndDoubleInvokeEffectsInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11432:78
commitDoubleInvokeEffectsInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11469:55
flushPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11309:41
node_modules/react-dom/cjs/react-dom-client.development.js/commitRoot/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11060:32
performWorkUntilDeadline@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:36:58
EventHandlerNonNull*node_modules/scheduler/cjs/scheduler.development.js/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:156:9
node_modules/scheduler/cjs/scheduler.development.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:266:7
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/scheduler/index.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:277:24
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/react-dom/cjs/react-dom-client.development.js/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:15216:23
node_modules/react-dom/cjs/react-dom-client.development.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18060:7
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/react-dom/client.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18071:24
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18075:16

[18/07/2025 10:27:04] [FRONTEND] [WARN] ⚠️ [MODAL] TeamCalendarApp.handlePostEdit non disponible
[18/07/2025 10:27:05] [FRONTEND] [LOG] 🔍 [MODAL] Clic sur onglet: tab-vacations -> tabId: vacations
[18/07/2025 10:27:05] [FRONTEND] [LOG] ✅ [MODAL] Onglet activé et contenu affiché: vacations
[18/07/2025 10:27:05] [FRONTEND] [WARN] ⚠️ [MODAL] TeamCalendarApp.handleAddVacationPeriod non disponible
[18/07/2025 10:27:06] [FRONTEND] [LOG] 🔍 [MODAL] Clic sur onglet: tab-assignments -> tabId: assignments
[18/07/2025 10:27:06] [FRONTEND] [LOG] ✅ [MODAL] Onglet activé et contenu affiché: assignments
[18/07/2025 10:27:07] [FRONTEND] [WARN] ⚠️ [MODAL] TeamCalendarApp.handleAddAssignment non disponible
[18/07/2025 10:27:07] [FRONTEND] [LOG] 🗑️ [removeRegularAssignmentsFromDate] Ouverture du modal de suppression amélioré
[18/07/2025 10:27:07] [FRONTEND] [WARN] ⚠️ [MODAL] Fonctionnalité de suppression des attributions non encore implémentée
[18/07/2025 10:27:08] [FRONTEND] [LOG] 🔍 [MODAL] Clic sur onglet: tab-employee-templates -> tabId: employee-templates
[18/07/2025 10:27:08] [FRONTEND] [LOG] ✅ [MODAL] Onglet activé et contenu affiché: employee-templates
[18/07/2025 10:27:09] [FRONTEND] [WARN] ⚠️ [MODAL] Fonctionnalité de modèles d'employés non encore implémentée
[18/07/2025 10:27:10] [FRONTEND] [LOG] 🔍 [MODAL] Clic sur onglet: tab-employees -> tabId: employees
[18/07/2025 10:27:10] [FRONTEND] [LOG] ✅ [MODAL] Onglet activé et contenu affiché: employees
[18/07/2025 10:27:11] [FRONTEND] [WARN] ⚠️ [MODAL] Fonctionnalité de gestion des employés non encore implémentée
[18/07/2025 10:27:14] [FRONTEND] [LOG] 🔍 [MODAL] Clic sur onglet: tab-general -> tabId: general
[18/07/2025 10:27:14] [FRONTEND] [LOG] ✅ [MODAL] Onglet activé et contenu affiché: general
[18/07/2025 10:27:18] [FRONTEND] [LOG] ✅ [MODAL] Modal fermé avec Escape
[18/07/2025 10:27:18] [FRONTEND] [ERROR] TypeError: this.closeSettingsModal is not a function http://localhost:5173/src/teamCalendarApp.ts 1037 16 Error: this.closeSettingsModal is not a function
init/<@http://localhost:5173/src/teamCalendarApp.ts:1037:16
EventListener.handleEvent*init@http://localhost:5173/src/teamCalendarApp.ts:1033:14
async*initApp@http://localhost:5173/src/Agenda.tsx:40:31
setTimeout handler*AgendaFullscreen/<@http://localhost:5173/src/Agenda.tsx:50:29
react-stack-bottom-frame@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:17478:20
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
commitHookEffectListMount@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8460:122
commitHookPassiveMountEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8518:60
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10016:42
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
doubleInvokeEffectsOnFiber@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11461:207
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
recursivelyTraverseAndDoubleInvokeEffectsInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11432:78
commitDoubleInvokeEffectsInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11469:55
flushPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11309:41
node_modules/react-dom/cjs/react-dom-client.development.js/commitRoot/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11060:32
performWorkUntilDeadline@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:36:58
EventHandlerNonNull*node_modules/scheduler/cjs/scheduler.development.js/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:156:9
node_modules/scheduler/cjs/scheduler.development.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:266:7
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/scheduler/index.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:277:24
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/react-dom/cjs/react-dom-client.development.js/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:15216:23
node_modules/react-dom/cjs/react-dom-client.development.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18060:7
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/react-dom/client.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18071:24
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18075:16

[18/07/2025 10:27:19] [FRONTEND] [ERROR] TypeError: this.closeSettingsModal is not a function http://localhost:5173/src/teamCalendarApp.ts 1037 16 Error: this.closeSettingsModal is not a function
init/<@http://localhost:5173/src/teamCalendarApp.ts:1037:16
EventListener.handleEvent*init@http://localhost:5173/src/teamCalendarApp.ts:1033:14
async*initApp@http://localhost:5173/src/Agenda.tsx:40:31
setTimeout handler*AgendaFullscreen/<@http://localhost:5173/src/Agenda.tsx:50:29
react-stack-bottom-frame@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:17478:20
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
commitHookEffectListMount@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8460:122
commitHookPassiveMountEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8518:60
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10016:42
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
doubleInvokeEffectsOnFiber@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11461:207
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
recursivelyTraverseAndDoubleInvokeEffectsInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11432:78
commitDoubleInvokeEffectsInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11469:55
flushPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11309:41
node_modules/react-dom/cjs/react-dom-client.development.js/commitRoot/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11060:32
performWorkUntilDeadline@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:36:58
EventHandlerNonNull*node_modules/scheduler/cjs/scheduler.development.js/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:156:9
node_modules/scheduler/cjs/scheduler.development.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:266:7
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/scheduler/index.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:277:24
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/react-dom/cjs/react-dom-client.development.js/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:15216:23
node_modules/react-dom/cjs/react-dom-client.development.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18060:7
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/react-dom/client.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18071:24
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18075:16

[18/07/2025 10:27:19] [FRONTEND] [LOG] ✅ [MODAL] Modal fermé via bouton