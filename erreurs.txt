[15/07/2025 08:40:49] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[15/07/2025 08:40:49] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[15/07/2025 08:40:49] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[15/07/2025 08:40:49] [FRONTEND] [LOG] [LogsPage] Fermeture de la connexion SSE.
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:40:49.408Z"
}
[15/07/2025 08:40:49] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[15/07/2025 08:40:49] [BACKEND] [INFO] [LOGS] Client connecté: 1752583249407
[15/07/2025 08:40:42] [FRONTEND] [LOG] 📋 [loadState] Assignations régulières chargées (application différée)
[15/07/2025 08:40:42] [FRONTEND] [LOG] 🧹 [cleanupDuplicateRegularShifts] Nettoyage des doublons de shifts réguliers...
[15/07/2025 08:40:42] [FRONTEND] [LOG] ✅ [fixInvalidAssignmentDates] Aucune date invalide trouvée
[15/07/2025 08:40:42] [FRONTEND] [LOG] 🔧 [fixInvalidAssignmentDates] Vérification des dates invalides...
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🧹 [cleanupInvalidAssignmentIds] Nettoyage des assignment_id invalides...
[15/07/2025 08:40:43] [FRONTEND] [LOG] ✅ [loadState] État chargé depuis l'API avec succès
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🔍 [init] Recherche du bouton des paramètres dans le DOM
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🔍 [init] Bouton des paramètres trouvé ? true
[15/07/2025 08:40:43] [FRONTEND] [LOG] ✅ [init] Icône paramètres trouvée: [Objet non sérialisable]
[15/07/2025 08:40:43] [FRONTEND] [LOG] ✅ [cleanupInvalidAssignmentIds] Aucun assignment_id invalide détecté
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🔍 [init] Attachement du listener click sur le bouton des paramètres
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🔍 [init] Recherche du bouton des paramètres header dans le DOM
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🔍 [init] Bouton des paramètres header trouvé ? true
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🔍 [init] Attachement du listener click sur le bouton des paramètres header
[15/07/2025 08:40:43] [FRONTEND] [LOG] ✅ [setupNavigationListeners] Écouteurs de navigation attachés
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🎯 [setupViewNavigationListeners] Configuration des boutons de navigation...
[15/07/2025 08:40:43] [FRONTEND] [LOG] ✅ Boutons de navigation configurés
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🔧 [ModalManager] Configuration des gestionnaires de modal
[15/07/2025 08:40:43] [FRONTEND] [LOG] ✅ [ModalManager] Gestionnaires de modal configurés
[15/07/2025 08:40:43] [BACKEND] [INFO] 📋 [GET /api/employee-order] Récupération ordre des employés
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🔧 [setupGeneralSettings] Configuration des paramètres généraux
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🔧 [setupGeneralSettings] Création du conteneur de fuseau horaire
[15/07/2025 08:40:43] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:40:43.117Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 08:40:43] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:40:43.114Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 08:40:43] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'
         {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:40:42.938Z",
  "originalArgs": [
    "[query] Executing query: \n            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'\n        ",
    {}
  ]
}
[15/07/2025 08:40:43] [BACKEND] [INFO] [LOGGER] 📋 [GET /api/employee-order] Récupération ordre des employés
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:40:42.936Z",
  "originalArgs": [
    "📋 [GET /api/employee-order] Récupération ordre des employés"
  ]
}
[15/07/2025 08:40:43] [BACKEND] [INFO] [LOGGER] 📋 [GET /api/employee-order] Aucun ordre personnalisé trouvé
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:40:43.170Z",
  "originalArgs": [
    "📋 [GET /api/employee-order] Aucun ordre personnalisé trouvé"
  ]
}
[15/07/2025 08:40:43] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 230ms: 
            SELECT setting_value FROM app_setting {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:40:43.169Z",
  "originalArgs": [
    "✅ Requête exécutée en 230ms:",
    "\n            SELECT setting_value FROM app_setting",
    {}
  ]
}
[15/07/2025 08:40:43] [BACKEND] [INFO] ✅ Requête exécutée en 230ms: 
            SELECT setting_value FROM app_setting {}
[15/07/2025 08:40:43] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[15/07/2025 08:40:43] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:40:43.255Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 08:40:43] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:40:43.087Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 08:40:43] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:40:43.267Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 08:40:43] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:40:43.272Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 08:40:43] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:40:43.110Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 08:40:43] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:40:43.273Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 08:40:43] [FRONTEND] [LOG] ✅ [setupGlobalEscapeHandler] Gestionnaire ESC global configuré
[15/07/2025 08:40:43] [FRONTEND] [LOG] 📋 [loadEmployeeOrder] Chargement ordre des employés
[15/07/2025 08:40:43] [FRONTEND] [WARN] ⚠️ [loadEmployeeOrder] Réponse API sans tableau employeeOrder
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🔧 [init] Forçage du rendu initial...
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[15/07/2025 08:40:43] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[15/07/2025 08:40:43] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[15/07/2025 08:40:43] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 60 zones de drop disponibles
[15/07/2025 08:40:43] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 60 zones trouvées
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Matin: [1, 2, 3, 4, 5]
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste e0332f5d-23e2-40fa-bf2d-d9271f03100a configuré comme draggable
[15/07/2025 08:40:43] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste e0332f5d-23e2-40fa-bf2d-d9271f03100a
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Nuit: [1, 2, 3, 4, 5]
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 9ebe6752-7cf6-47de-bf62-8eb912bd439d configuré comme draggable
[15/07/2025 08:40:43] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 9ebe6752-7cf6-47de-bf62-8eb912bd439d
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Soir: [1, 2, 3, 4, 5]
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7224fea3-1bc9-4ff2-ab34-35cb0cf19f3e configuré comme draggable
[15/07/2025 08:40:43] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7224fea3-1bc9-4ff2-ab34-35cb0cf19f3e
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE1: [0, 6]
[15/07/2025 08:40:43] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 5480db79-77a5-4f23-aa37-614936e8514b
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 5480db79-77a5-4f23-aa37-614936e8514b configuré comme draggable
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE2: [0, 6]
[15/07/2025 08:40:43] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 1ed512ff-9b54-4a63-90da-790b0594198b
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 1ed512ff-9b54-4a63-90da-790b0594198b configuré comme draggable
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste e0332f5d-23e2-40fa-bf2d-d9271f03100a configuré comme draggable
[15/07/2025 08:40:43] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 9ebe6752-7cf6-47de-bf62-8eb912bd439d
[15/07/2025 08:40:43] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste e0332f5d-23e2-40fa-bf2d-d9271f03100a
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 9ebe6752-7cf6-47de-bf62-8eb912bd439d configuré comme draggable
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7224fea3-1bc9-4ff2-ab34-35cb0cf19f3e configuré comme draggable
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 5480db79-77a5-4f23-aa37-614936e8514b configuré comme draggable
[15/07/2025 08:40:43] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7224fea3-1bc9-4ff2-ab34-35cb0cf19f3e
[15/07/2025 08:40:43] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 5480db79-77a5-4f23-aa37-614936e8514b
[15/07/2025 08:40:43] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 1ed512ff-9b54-4a63-90da-790b0594198b
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 1ed512ff-9b54-4a63-90da-790b0594198b configuré comme draggable
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 08:40:43] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 08:40:43] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 08:40:43] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 08:40:43] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 08:40:43] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 08:40:43] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 08:40:43] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (60 zones)
[15/07/2025 08:40:43] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🔧 [init] Configuration du drag & drop des postes...
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 60 zones de drop disponibles
[15/07/2025 08:40:43] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 60 zones trouvées
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste e0332f5d-23e2-40fa-bf2d-d9271f03100a configuré comme draggable
[15/07/2025 08:40:43] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste e0332f5d-23e2-40fa-bf2d-d9271f03100a
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 9ebe6752-7cf6-47de-bf62-8eb912bd439d configuré comme draggable
[15/07/2025 08:40:43] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 9ebe6752-7cf6-47de-bf62-8eb912bd439d
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7224fea3-1bc9-4ff2-ab34-35cb0cf19f3e configuré comme draggable
[15/07/2025 08:40:43] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7224fea3-1bc9-4ff2-ab34-35cb0cf19f3e
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 5480db79-77a5-4f23-aa37-614936e8514b configuré comme draggable
[15/07/2025 08:40:43] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 5480db79-77a5-4f23-aa37-614936e8514b
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 1ed512ff-9b54-4a63-90da-790b0594198b configuré comme draggable
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste e0332f5d-23e2-40fa-bf2d-d9271f03100a configuré comme draggable
[15/07/2025 08:40:43] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 1ed512ff-9b54-4a63-90da-790b0594198b
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7224fea3-1bc9-4ff2-ab34-35cb0cf19f3e configuré comme draggable
[15/07/2025 08:40:43] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 9ebe6752-7cf6-47de-bf62-8eb912bd439d
[15/07/2025 08:40:43] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 9ebe6752-7cf6-47de-bf62-8eb912bd439d configuré comme draggable
[15/07/2025 08:40:43] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste e0332f5d-23e2-40fa-bf2d-d9271f03100a
[15/07/2025 08:40:44] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7224fea3-1bc9-4ff2-ab34-35cb0cf19f3e
[15/07/2025 08:40:44] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 5480db79-77a5-4f23-aa37-614936e8514b configuré comme draggable
[15/07/2025 08:40:44] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 5480db79-77a5-4f23-aa37-614936e8514b
[15/07/2025 08:40:44] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 1ed512ff-9b54-4a63-90da-790b0594198b configuré comme draggable
[15/07/2025 08:40:44] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 08:40:44] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 1ed512ff-9b54-4a63-90da-790b0594198b
[15/07/2025 08:40:44] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 08:40:44] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 08:40:44] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 08:40:44] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 08:40:44] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 08:40:44] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 08:40:44] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 08:40:44] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 08:40:44] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 08:40:44] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 08:40:44] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 08:40:44] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 08:40:44] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (60 zones)
[15/07/2025 08:40:44] [FRONTEND] [LOG] ℹ️ [setupEmployeeDragDrop] Fonction désactivée - Utiliser setupDragAndDrop
[15/07/2025 08:40:44] [FRONTEND] [LOG] ✅ [addEmergencyFixButton] Bouton de correction ajouté
[15/07/2025 08:40:44] [FRONTEND] [LOG] 🔧 [addEmergencyFixButton] Ajout du bouton de correction...
[15/07/2025 08:40:44] [FRONTEND] [LOG] ✅ [init] Initialisation terminée avec succès
[15/07/2025 08:40:44] [FRONTEND] [LOG] 🎉 [Agenda] Application fullscreen initialisée avec succès
[15/07/2025 08:40:44] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] 0 grips ajoutés
[15/07/2025 08:40:44] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 08:40:44] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 08:40:44] [FRONTEND] [LOG] 📊 [diagnoseRegularAssignmentGrips] Statistiques: {"totalShifts":0,"regularShifts":0,"regularAssignments":0}
[15/07/2025 08:40:44] [FRONTEND] [LOG] 🔍 [diagnoseRegularAssignmentGrips] Diagnostic des grips d'attributions régulières
[15/07/2025 08:40:44] [FRONTEND] [LOG] 🎯 [diagnoseRegularAssignmentGrips] Grips trouvés dans le DOM: 0
[15/07/2025 08:40:44] [FRONTEND] [LOG] ⚠️ [diagnoseRegularAssignmentGrips] Aucun shift régulier trouvé - Ré-application des attributions...
[15/07/2025 08:40:44] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 08:40:44] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 08:40:44] [FRONTEND] [LOG] 🎯 [POST DRAG] Début drag poste: e0332f5d-23e2-40fa-bf2d-d9271f03100a (Poste Matin)
[15/07/2025 08:40:44] [FRONTEND] [LOG] 🎯 [POST DRAG] DataTransfer configuré: {"postId":"e0332f5d-23e2-40fa-bf2d-d9271f03100a","label":"Poste Matin","types":["text/plain","application/x-post-id","postid","postlabel"]}
[15/07/2025 08:40:44] [FRONTEND] [LOG] 🎨 [enableAgendaPreviewMode] Activation preview agenda pour poste e0332f5d-23e2-40fa-bf2d-d9271f03100a
[15/07/2025 08:40:44] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 08:40:44] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 08:40:44] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] Ajout des fantômes pour Poste Matin
[15/07/2025 08:40:44] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] 25 fantômes créés
[15/07/2025 08:40:44] [FRONTEND] [LOG] 🖱️ [enableCellHoverTracking] Suivi activé sur 25 cellules
[15/07/2025 08:40:44] [FRONTEND] [LOG] ✅ [enableAgendaPreviewMode] Mode preview activé pour Poste Matin
[15/07/2025 08:40:44] [FRONTEND] [LOG] 🎯 [POST DRAG] 60 zones préparées pour le drop
[15/07/2025 08:40:44] [FRONTEND] [LOG] 🎯 [POST DRAG] Début drag poste: e0332f5d-23e2-40fa-bf2d-d9271f03100a (Poste Matin)
[15/07/2025 08:40:44] [FRONTEND] [LOG] 🎯 [POST DRAG] DataTransfer configuré: {"postId":"e0332f5d-23e2-40fa-bf2d-d9271f03100a","label":"Poste Matin","types":["text/plain","application/x-post-id","postid","postlabel"]}
[15/07/2025 08:40:44] [FRONTEND] [LOG] 🎨 [enableAgendaPreviewMode] Activation preview agenda pour poste e0332f5d-23e2-40fa-bf2d-d9271f03100a
[15/07/2025 08:40:44] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 08:40:44] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 08:40:44] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] Ajout des fantômes pour Poste Matin
[15/07/2025 08:40:44] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] 25 fantômes créés
[15/07/2025 08:40:45] [FRONTEND] [LOG] 🖱️ [enableCellHoverTracking] Suivi activé sur 50 cellules
[15/07/2025 08:40:45] [FRONTEND] [LOG] ✅ [enableAgendaPreviewMode] Mode preview activé pour Poste Matin
[15/07/2025 08:40:45] [FRONTEND] [LOG] 🎯 [POST DRAG] 60 zones préparées pour le drop
[15/07/2025 08:40:45] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 59f5df3a-33ef-425c-bfa2-adca818cf94f, types: text/plain, application/x-post-id, postid, postlabel
[15/07/2025 08:40:45] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 59f5df3a-33ef-425c-bfa2-adca818cf94f, types: text/plain, application/x-post-id, postid, postlabel
[15/07/2025 08:40:45] [FRONTEND] [LOG] 🎯 [DROP] ✅ Drop reçu sur employé: Sophie Leblanc
[15/07/2025 08:40:45] [FRONTEND] [LOG] 📦 [DROP] Nouveau poste: e0332f5d-23e2-40fa-bf2d-d9271f03100a → 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 08:40:45] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 08:40:45] [FRONTEND] [LOG] ✅ [DROP] PostId trouvé via application/x-post-id: "e0332f5d-23e2-40fa-bf2d-d9271f03100a"
[15/07/2025 08:40:45] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 08:40:45] [FRONTEND] [LOG] 🎯 [openAssignmentContextModal] Poste brut: e0332f5d-23e2-40fa-bf2d-d9271f03100a, Employé: 59f5df3a-33ef-425c-bfa2-adca818cf94f, Contexte: null
[15/07/2025 08:40:45] [FRONTEND] [LOG] ℹ️ [openAssignmentContextModal] Option "Attribution unique (cellule)" masquée - contexte inapproprié
[15/07/2025 08:40:45] [FRONTEND] [LOG] 📅 [openAssignmentContextModal] Date initialisée: 2025-07-15
[15/07/2025 08:40:45] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 08:40:45] [FRONTEND] [LOG] 🎯 [POST DRAG] Fin drag poste: e0332f5d-23e2-40fa-bf2d-d9271f03100a
[15/07/2025 08:40:45] [FRONTEND] [LOG] ✅ [DROP] Modal d'assignation ouvert
[15/07/2025 08:40:45] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 08:40:45] [FRONTEND] [LOG] 🎯 [POST DRAG] Nettoyage des 60 zones
[15/07/2025 08:40:45] [FRONTEND] [LOG] 🎯 [POST DRAG] Fin drag poste: e0332f5d-23e2-40fa-bf2d-d9271f03100a
[15/07/2025 08:40:45] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 08:40:45] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 08:40:45] [FRONTEND] [LOG] 🎯 [POST DRAG] Nettoyage des 60 zones
[15/07/2025 08:40:47] [FRONTEND] [LOG] 🔚 [ESC] Fermeture de toutes les modales ouvertes
[15/07/2025 08:40:47] [FRONTEND] [LOG] 🗙 Modal fermé: assignment-context-modal
[15/07/2025 08:40:47] [FRONTEND] [LOG] ✅ Toutes les modales fermées avec ESC
[15/07/2025 08:40:48] [FRONTEND] [DEBUG] [vite] connected.
[15/07/2025 08:40:49] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[15/07/2025 08:40:49] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[15/07/2025 08:40:49] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[15/07/2025 08:40:49] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[15/07/2025 08:40:49] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[15/07/2025 08:40:49] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[15/07/2025 08:40:49] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[15/07/2025 08:40:49] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[15/07/2025 08:40:49] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[15/07/2025 08:40:49] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[15/07/2025 08:40:49] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[15/07/2025 08:40:49] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[15/07/2025 08:40:49] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[15/07/2025 08:40:49] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[15/07/2025 08:40:49] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[15/07/2025 08:40:49] [FRONTEND] [LOG] [LogsPage] Fermeture de la connexion SSE.
[15/07/2025 08:40:49] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:40:49.408Z",
  "originalArgs": [
    "[query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ]
}
[15/07/2025 08:40:49] [BACKEND] [INFO] ✅ Requête exécutée en 106ms: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 106ms: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 106ms:",
    "SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:40:49.514Z"
}
[15/07/2025 08:40:52] [FRONTEND] [DEBUG] [vite] connected.
[15/07/2025 08:40:53] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[15/07/2025 08:40:53] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[15/07/2025 08:40:53] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[15/07/2025 08:40:53] [FRONTEND] [LOG] 🚀 [init] Initialisation de TeamCalendarApp...
[15/07/2025 08:40:53] [FRONTEND] [LOG] 🚀 [Agenda] Initialisation TeamCalendarApp...
[15/07/2025 08:40:53] [FRONTEND] [LOG] 🧹 [clearRenderCache] Nettoyage du cache de rendu
[15/07/2025 08:40:53] [FRONTEND] [LOG] ✅ [clearRenderCache] Cache de rendu nettoyé
[15/07/2025 08:40:53] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[15/07/2025 08:40:53] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[15/07/2025 08:40:53] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[15/07/2025 08:40:53] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[15/07/2025 08:40:54] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[15/07/2025 08:40:54] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[15/07/2025 08:40:54] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[15/07/2025 08:40:54] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[15/07/2025 08:40:54] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[15/07/2025 08:40:54] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[15/07/2025 08:40:54] [FRONTEND] [LOG] 🧹 [Agenda] Nettoyage composant Agenda
[15/07/2025 08:40:54] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[15/07/2025 08:40:54] [FRONTEND] [LOG] 🔄 [Agenda] Réinitialisation nécessaire (employés non chargés)
[15/07/2025 08:40:54] [FRONTEND] [LOG] 🔌 [ConnectionIndicator] Création de l'indicateur de connexion
[15/07/2025 08:40:54] [FRONTEND] [LOG] 🔌 [init] Indicateur de connexion initialisé
[15/07/2025 08:40:54] [FRONTEND] [LOG] 📂 [TeamCalendarApp] Chargement de l'état...
[15/07/2025 08:40:54] [FRONTEND] [LOG] 🌐 [TeamCalendarApp] Chargement depuis l'API...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT e.*, et.name as template_name 
      FROM employees e 
      LEFT JOIN employee_templates et ON e.template_id = et.id 
      ORDER BY e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT e.*, et.name as template_name \n      FROM employees e \n      LEFT JOIN employee_templates et ON e.template_id = et.id \n      ORDER BY e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:40:54.342Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 46ms: 
      SELECT e.*, et.name as template_name 
      {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 46ms:",
    "\n      SELECT e.*, et.name as template_name \n     ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:40:54.388Z"
}
[15/07/2025 08:40:54] [BACKEND] [INFO] ✅ Requête exécutée en 46ms: 
      SELECT e.*, et.name as template_name 
      {}
[15/07/2025 08:40:54] [FRONTEND] [LOG] 👥 [TeamCalendarApp] 5 employés chargés
[15/07/2025 08:40:54] [FRONTEND] [LOG] 📦 [loadState] Chargement de tous les shifts depuis la base de données...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT s.*, e.name as employee_name, sp.label as post_label
      FROM shifts s
      LEFT JOIN employees e ON s.employee_id = e.id
      LEFT JOIN standard_posts sp ON s.post_id = sp.id
      ORDER BY s.date_key, e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT s.*, e.name as employee_name, sp.label as post_label\n      FROM shifts s\n      LEFT JOIN employees e ON s.employee_id = e.id\n      LEFT JOIN standard_posts sp ON s.post_id = sp.id\n      ORDER BY s.date_key, e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:40:54.430Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 47ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 47ms:",
    "\n      SELECT s.*, e.name as employee_name, sp.lab",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:40:54.476Z"
}
[15/07/2025 08:40:54] [BACKEND] [INFO] ✅ Requête exécutée en 47ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
[15/07/2025 08:40:54] [FRONTEND] [LOG] 📅 [loadState] 0 shifts chargés depuis la base de données
[15/07/2025 08:40:54] [FRONTEND] [LOG] ✅ [loadState] Shifts organisés dans this.data.schedule
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:40:54.514Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 46ms: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 46ms:",
    "SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:40:54.560Z"
}
[15/07/2025 08:40:54] [BACKEND] [INFO] ✅ Requête exécutée en 46ms: SELECT * FROM standard_posts ORDER BY label {}
[15/07/2025 08:40:54] [FRONTEND] [LOG] 🏢 [TeamCalendarApp] 5 postes standards chargés
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:40:54.597Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 36ms: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 36ms:",
    "SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:40:54.634Z"
}
[15/07/2025 08:40:54] [BACKEND] [INFO] ✅ Requête exécutée en 36ms: SELECT * FROM app_settings ORDER BY setting_key {}
[15/07/2025 08:40:54] [FRONTEND] [LOG] ⚙️ [loadState] Paramètres chargés depuis API: {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT 
        ra.id,
        ra.employee_id,
        ra.post_id,
        ra.days_of_week,
        ra.start_date,
        ra.end_date,
        ra.is_active,
        ra.excluded_dates,
        e.name as employee_name,
        sp.label as post_label,
        sp.hours as post_hours,
        sp.type as post_type,
        array_length(ra.days_of_week, 1) as days_count
      FROM regular_assignments ra
      LEFT JOIN employees e ON ra.employee_id = e.id
      LEFT JOIN standard_posts sp ON ra.post_id = sp.id
      WHERE ra.is_active = true
      ORDER BY e.name, sp.label, ra.start_date
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT \n        ra.id,\n        ra.employee_id,\n        ra.post_id,\n        ra.days_of_week,\n        ra.start_date,\n        ra.end_date,\n        ra.is_active,\n        ra.excluded_dates,\n        e.name as employee_name,\n        sp.label as post_label,\n        sp.hours as post_hours,\n        sp.type as post_type,\n        array_length(ra.days_of_week, 1) as days_count\n      FROM regular_assignments ra\n      LEFT JOIN employees e ON ra.employee_id = e.id\n      LEFT JOIN standard_posts sp ON ra.post_id = sp.id\n      WHERE ra.is_active = true\n      ORDER BY e.name, sp.label, ra.start_date\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:40:54.673Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 46ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 46ms:",
    "\n      SELECT \n        ra.id,\n        ra.employee_",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:40:54.718Z"
}
[15/07/2025 08:40:54] [BACKEND] [INFO] ✅ Requête exécutée en 46ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
[15/07/2025 08:40:54] [FRONTEND] [LOG] 📋 [loadState] 0/0 assignations régulières valides chargées et normalisées
[15/07/2025 08:40:54] [FRONTEND] [LOG] 🔧 [fixInvalidAssignmentDates] Vérification des dates invalides...
[15/07/2025 08:40:54] [FRONTEND] [LOG] ✅ [fixInvalidAssignmentDates] Aucune date invalide trouvée
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📋 [GET /api/employee-order] Récupération ordre des employés
  Data: {
  "originalArgs": [
    "📋 [GET /api/employee-order] Récupération ordre des employés"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:40:54.850Z"
}
[15/07/2025 08:40:54] [FRONTEND] [LOG] 📋 [loadState] Assignations régulières chargées (application différée)
[15/07/2025 08:40:54] [FRONTEND] [LOG] 🧹 [cleanupDuplicateRegularShifts] Nettoyage des doublons de shifts réguliers...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'
         {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'\n        ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:40:54.851Z"
}
[15/07/2025 08:40:54] [FRONTEND] [LOG] ✅ [cleanupDuplicateRegularShifts] Aucun doublon détecté
[15/07/2025 08:40:54] [FRONTEND] [LOG] 🧹 [cleanupInvalidAssignmentIds] Nettoyage des assignment_id invalides...
[15/07/2025 08:40:54] [FRONTEND] [LOG] ✅ [cleanupInvalidAssignmentIds] Aucun assignment_id invalide détecté
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📋 [GET /api/employee-order] Aucun ordre personnalisé trouvé
  Data: {
  "originalArgs": [
    "📋 [GET /api/employee-order] Aucun ordre personnalisé trouvé"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:40:54.905Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 53ms: 
            SELECT setting_value FROM app_setting {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 53ms:",
    "\n            SELECT setting_value FROM app_setting",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:40:54.904Z"
}
[15/07/2025 08:40:54] [FRONTEND] [LOG] ✅ [loadState] État chargé depuis l'API avec succès
[15/07/2025 08:40:54] [FRONTEND] [LOG] ✅ [init] Icône paramètres trouvée: [Objet non sérialisable]
[15/07/2025 08:40:54] [FRONTEND] [LOG] 🔍 [init] Recherche du bouton des paramètres dans le DOM
[15/07/2025 08:40:54] [FRONTEND] [LOG] 🔍 [init] Attachement du listener click sur le bouton des paramètres
[15/07/2025 08:40:54] [FRONTEND] [LOG] 🔍 [init] Bouton des paramètres trouvé ? true
[15/07/2025 08:40:54] [FRONTEND] [LOG] 🔍 [init] Recherche du bouton des paramètres header dans le DOM
[15/07/2025 08:40:54] [FRONTEND] [LOG] 🔍 [init] Bouton des paramètres header trouvé ? true
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🔍 [init] Attachement du listener click sur le bouton des paramètres header
[15/07/2025 08:40:55] [FRONTEND] [LOG] ✅ [setupNavigationListeners] Écouteurs de navigation attachés
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🎯 [setupViewNavigationListeners] Configuration des boutons de navigation...
[15/07/2025 08:40:55] [FRONTEND] [LOG] ✅ Boutons de navigation configurés
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🔧 [ModalManager] Configuration des gestionnaires de modal
[15/07/2025 08:40:55] [FRONTEND] [LOG] ✅ [ModalManager] Gestionnaires de modal configurés
[15/07/2025 08:40:55] [BACKEND] [INFO] 📋 [GET /api/employee-order] Récupération ordre des employés
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:40:55.020Z"
}
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🔧 [setupGeneralSettings] Configuration des paramètres généraux
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🔧 [setupGeneralSettings] Création du conteneur de fuseau horaire
[15/07/2025 08:40:55] [FRONTEND] [LOG] ✅ [setupGlobalEscapeHandler] Gestionnaire ESC global configuré
[15/07/2025 08:40:55] [FRONTEND] [LOG] 📋 [loadEmployeeOrder] Chargement ordre des employés
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[15/07/2025 08:40:55] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[15/07/2025 08:40:55] [FRONTEND] [WARN] ⚠️ [loadEmployeeOrder] Réponse API sans tableau employeeOrder
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🔧 [init] Forçage du rendu initial...
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[15/07/2025 08:40:55] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[15/07/2025 08:40:55] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 08:40:55] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 60 zones trouvées
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 60 zones de drop disponibles
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste e0332f5d-23e2-40fa-bf2d-d9271f03100a configuré comme draggable
[15/07/2025 08:40:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste e0332f5d-23e2-40fa-bf2d-d9271f03100a
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Matin: [1, 2, 3, 4, 5]
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Nuit: [1, 2, 3, 4, 5]
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 9ebe6752-7cf6-47de-bf62-8eb912bd439d configuré comme draggable
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7224fea3-1bc9-4ff2-ab34-35cb0cf19f3e configuré comme draggable
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Soir: [1, 2, 3, 4, 5]
[15/07/2025 08:40:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 9ebe6752-7cf6-47de-bf62-8eb912bd439d
[15/07/2025 08:40:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7224fea3-1bc9-4ff2-ab34-35cb0cf19f3e
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE1: [0, 6]
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 1ed512ff-9b54-4a63-90da-790b0594198b configuré comme draggable
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE2: [0, 6]
[15/07/2025 08:40:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 5480db79-77a5-4f23-aa37-614936e8514b
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 5480db79-77a5-4f23-aa37-614936e8514b configuré comme draggable
[15/07/2025 08:40:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 1ed512ff-9b54-4a63-90da-790b0594198b
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste e0332f5d-23e2-40fa-bf2d-d9271f03100a configuré comme draggable
[15/07/2025 08:40:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 9ebe6752-7cf6-47de-bf62-8eb912bd439d
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 9ebe6752-7cf6-47de-bf62-8eb912bd439d configuré comme draggable
[15/07/2025 08:40:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste e0332f5d-23e2-40fa-bf2d-d9271f03100a
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7224fea3-1bc9-4ff2-ab34-35cb0cf19f3e configuré comme draggable
[15/07/2025 08:40:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7224fea3-1bc9-4ff2-ab34-35cb0cf19f3e
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 5480db79-77a5-4f23-aa37-614936e8514b configuré comme draggable
[15/07/2025 08:40:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 5480db79-77a5-4f23-aa37-614936e8514b
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 1ed512ff-9b54-4a63-90da-790b0594198b configuré comme draggable
[15/07/2025 08:40:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 1ed512ff-9b54-4a63-90da-790b0594198b
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 08:40:55] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 08:40:55] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 08:40:55] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 08:40:55] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 08:40:55] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 08:40:55] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[15/07/2025 08:40:55] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 08:40:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (60 zones)
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🔧 [init] Configuration du drag & drop des postes...
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 08:40:55] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 60 zones trouvées
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 60 zones de drop disponibles
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste e0332f5d-23e2-40fa-bf2d-d9271f03100a configuré comme draggable
[15/07/2025 08:40:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste e0332f5d-23e2-40fa-bf2d-d9271f03100a
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 9ebe6752-7cf6-47de-bf62-8eb912bd439d configuré comme draggable
[15/07/2025 08:40:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 9ebe6752-7cf6-47de-bf62-8eb912bd439d
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7224fea3-1bc9-4ff2-ab34-35cb0cf19f3e configuré comme draggable
[15/07/2025 08:40:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7224fea3-1bc9-4ff2-ab34-35cb0cf19f3e
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 5480db79-77a5-4f23-aa37-614936e8514b configuré comme draggable
[15/07/2025 08:40:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 5480db79-77a5-4f23-aa37-614936e8514b
[15/07/2025 08:40:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 1ed512ff-9b54-4a63-90da-790b0594198b
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste e0332f5d-23e2-40fa-bf2d-d9271f03100a configuré comme draggable
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 1ed512ff-9b54-4a63-90da-790b0594198b configuré comme draggable
[15/07/2025 08:40:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste e0332f5d-23e2-40fa-bf2d-d9271f03100a
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 9ebe6752-7cf6-47de-bf62-8eb912bd439d configuré comme draggable
[15/07/2025 08:40:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 9ebe6752-7cf6-47de-bf62-8eb912bd439d
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 5480db79-77a5-4f23-aa37-614936e8514b configuré comme draggable
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7224fea3-1bc9-4ff2-ab34-35cb0cf19f3e configuré comme draggable
[15/07/2025 08:40:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7224fea3-1bc9-4ff2-ab34-35cb0cf19f3e
[15/07/2025 08:40:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 5480db79-77a5-4f23-aa37-614936e8514b
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 1ed512ff-9b54-4a63-90da-790b0594198b configuré comme draggable
[15/07/2025 08:40:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 1ed512ff-9b54-4a63-90da-790b0594198b
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 08:40:55] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 08:40:55] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 08:40:55] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 08:40:55] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 08:40:55] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 08:40:56] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 08:40:56] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 08:40:56] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 08:40:56] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (60 zones)
[15/07/2025 08:40:56] [FRONTEND] [LOG] ℹ️ [setupEmployeeDragDrop] Fonction désactivée - Utiliser setupDragAndDrop
[15/07/2025 08:40:56] [FRONTEND] [LOG] 🔧 [addEmergencyFixButton] Ajout du bouton de correction...
[15/07/2025 08:40:56] [FRONTEND] [LOG] ✅ [addEmergencyFixButton] Bouton de correction ajouté
[15/07/2025 08:40:56] [FRONTEND] [LOG] ✅ [init] Initialisation terminée avec succès
[15/07/2025 08:40:56] [FRONTEND] [LOG] 🎉 [Agenda] Application fullscreen initialisée avec succès
[15/07/2025 08:40:56] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] 0 grips ajoutés
[15/07/2025 08:40:56] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 08:40:56] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 08:40:56] [FRONTEND] [LOG] 📊 [diagnoseRegularAssignmentGrips] Statistiques: {"totalShifts":0,"regularShifts":0,"regularAssignments":0}
[15/07/2025 08:40:56] [FRONTEND] [LOG] 🔍 [diagnoseRegularAssignmentGrips] Diagnostic des grips d'attributions régulières
[15/07/2025 08:40:56] [FRONTEND] [LOG] 🎯 [diagnoseRegularAssignmentGrips] Grips trouvés dans le DOM: 0
[15/07/2025 08:40:56] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 08:40:56] [FRONTEND] [LOG] ⚠️ [diagnoseRegularAssignmentGrips] Aucun shift régulier trouvé - Ré-application des attributions...
[15/07/2025 08:40:56] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 08:40:56] [FRONTEND] [LOG] 🎯 [POST DRAG] DataTransfer configuré: {"postId":"e0332f5d-23e2-40fa-bf2d-d9271f03100a","label":"Poste Matin","types":["text/plain","application/x-post-id","postid","postlabel"]}
[15/07/2025 08:40:56] [FRONTEND] [LOG] 🎨 [enableAgendaPreviewMode] Activation preview agenda pour poste e0332f5d-23e2-40fa-bf2d-d9271f03100a
[15/07/2025 08:40:56] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 08:40:56] [FRONTEND] [LOG] 🎯 [POST DRAG] Début drag poste: e0332f5d-23e2-40fa-bf2d-d9271f03100a (Poste Matin)
[15/07/2025 08:40:56] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 08:40:56] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] Ajout des fantômes pour Poste Matin
[15/07/2025 08:40:56] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] 25 fantômes créés
[15/07/2025 08:40:56] [FRONTEND] [LOG] 🎯 [POST DRAG] 60 zones préparées pour le drop
[15/07/2025 08:40:56] [FRONTEND] [LOG] ✅ [enableAgendaPreviewMode] Mode preview activé pour Poste Matin
[15/07/2025 08:40:56] [FRONTEND] [LOG] 🖱️ [enableCellHoverTracking] Suivi activé sur 25 cellules
[15/07/2025 08:40:56] [FRONTEND] [LOG] 🎯 [POST DRAG] Début drag poste: e0332f5d-23e2-40fa-bf2d-d9271f03100a (Poste Matin)
[15/07/2025 08:40:56] [FRONTEND] [LOG] 🎯 [POST DRAG] DataTransfer configuré: {"postId":"e0332f5d-23e2-40fa-bf2d-d9271f03100a","label":"Poste Matin","types":["text/plain","application/x-post-id","postid","postlabel"]}
[15/07/2025 08:40:57] [FRONTEND] [LOG] 🎨 [enableAgendaPreviewMode] Activation preview agenda pour poste e0332f5d-23e2-40fa-bf2d-d9271f03100a
[15/07/2025 08:40:57] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 08:40:57] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 08:40:57] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] Ajout des fantômes pour Poste Matin
[15/07/2025 08:40:57] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] 25 fantômes créés
[15/07/2025 08:40:57] [FRONTEND] [LOG] 🖱️ [enableCellHoverTracking] Suivi activé sur 50 cellules
[15/07/2025 08:40:57] [FRONTEND] [LOG] ✅ [enableAgendaPreviewMode] Mode preview activé pour Poste Matin
[15/07/2025 08:40:57] [FRONTEND] [LOG] 🎯 [POST DRAG] 60 zones préparées pour le drop
[15/07/2025 08:40:57] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 59f5df3a-33ef-425c-bfa2-adca818cf94f, types: text/plain, application/x-post-id, postid, postlabel
[15/07/2025 08:40:57] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 59f5df3a-33ef-425c-bfa2-adca818cf94f, types: text/plain, application/x-post-id, postid, postlabel
[15/07/2025 08:40:57] [FRONTEND] [LOG] 🎯 [DROP] ✅ Drop reçu sur employé: Sophie Leblanc
[15/07/2025 08:40:57] [FRONTEND] [LOG] ✅ [DROP] PostId trouvé via application/x-post-id: "e0332f5d-23e2-40fa-bf2d-d9271f03100a"
[15/07/2025 08:40:57] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 08:40:57] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 08:40:57] [FRONTEND] [LOG] 📦 [DROP] Nouveau poste: e0332f5d-23e2-40fa-bf2d-d9271f03100a → 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 08:40:57] [FRONTEND] [LOG] 🎯 [openAssignmentContextModal] Poste brut: e0332f5d-23e2-40fa-bf2d-d9271f03100a, Employé: 59f5df3a-33ef-425c-bfa2-adca818cf94f, Contexte: null
[15/07/2025 08:40:57] [FRONTEND] [LOG] ℹ️ [openAssignmentContextModal] Option "Attribution unique (cellule)" masquée - contexte inapproprié
[15/07/2025 08:40:57] [FRONTEND] [LOG] 📅 [openAssignmentContextModal] Date initialisée: 2025-07-15
[15/07/2025 08:40:57] [FRONTEND] [LOG] ✅ [DROP] Modal d'assignation ouvert
[15/07/2025 08:40:57] [FRONTEND] [LOG] 🎯 [POST DRAG] Fin drag poste: e0332f5d-23e2-40fa-bf2d-d9271f03100a
[15/07/2025 08:40:57] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 08:40:57] [FRONTEND] [LOG] 🎯 [POST DRAG] Nettoyage des 60 zones
[15/07/2025 08:40:57] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 08:40:57] [FRONTEND] [LOG] 🎯 [POST DRAG] Fin drag poste: e0332f5d-23e2-40fa-bf2d-d9271f03100a
[15/07/2025 08:40:57] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 08:40:57] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 08:40:57] [FRONTEND] [LOG] 🎯 [POST DRAG] Nettoyage des 60 zones