[15/07/2025 08:59:04] [FRONTEND] [LOG] - validateListeners() : <PERSON>ider les listeners
[15/07/2025 08:59:04] [FRONTEND] [LOG] - runCompleteValidation() : Validation complète
[15/07/2025 08:59:04] [FRONTEND] [LOG] - testEndToEnd() : Test end-to-end complet
[15/07/2025 08:59:04] [FRONTEND] [LOG] [LogsPage] Fermeture de la connexion SSE.
[15/07/2025 08:59:04] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:04.500Z"
}
[15/07/2025 08:59:04] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[15/07/2025 08:59:04] [BACKEND] [INFO] [LOGS] Client connecté: 1752584344499
[15/07/2025 08:58:44] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 08:58:44] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 08:58:44] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 08:58:44] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 08:58:44] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 08:58:44] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 08:58:44] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 08:58:44] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 08:58:44] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 08:58:44] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 08:58:44] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 08:58:44] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 08:58:44] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (60 zones)
[15/07/2025 08:58:44] [FRONTEND] [LOG] ℹ️ [setupEmployeeDragDrop] Fonction désactivée - Utiliser setupDragAndDrop
[15/07/2025 08:58:44] [FRONTEND] [LOG] 🔧 [addEmergencyFixButton] Ajout du bouton de correction...
[15/07/2025 08:58:44] [FRONTEND] [LOG] ✅ [init] Initialisation terminée avec succès
[15/07/2025 08:58:44] [FRONTEND] [LOG] ✅ [addEmergencyFixButton] Bouton de correction ajouté
[15/07/2025 08:58:44] [FRONTEND] [LOG] 🎉 [Agenda] Application fullscreen initialisée avec succès
[15/07/2025 08:58:44] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] 0 grips ajoutés
[15/07/2025 08:58:44] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 08:58:44] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 08:58:44] [FRONTEND] [LOG] 🔍 [diagnoseRegularAssignmentGrips] Diagnostic des grips d'attributions régulières
[15/07/2025 08:58:44] [FRONTEND] [LOG] 🎯 [diagnoseRegularAssignmentGrips] Grips trouvés dans le DOM: 0
[15/07/2025 08:58:44] [FRONTEND] [LOG] ⚠️ [diagnoseRegularAssignmentGrips] Aucun shift régulier trouvé - Ré-application des attributions...
[15/07/2025 08:58:44] [FRONTEND] [LOG] 📊 [diagnoseRegularAssignmentGrips] Statistiques: {"totalShifts":0,"regularShifts":0,"regularAssignments":0}
[15/07/2025 08:58:44] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 08:58:44] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 08:58:44] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[15/07/2025 08:58:44] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic des problèmes de modale...
[15/07/2025 08:58:44] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Éléments DOM: {"modal":true,"closeBtn":true,"cancelBtn":true,"confirmBtn":true,"radioButtons":4}
[15/07/2025 08:58:44] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] TeamCalendarApp: {"exists":true,"hasModalManager":true,"hasOpenFunction":true,"hasHandleFunction":true}
[15/07/2025 08:58:44] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] État de la modale: {"hidden":true,"classes":["hidden","fixed","inset-0","bg-black/40","backdrop-blur-sm","z-50","flex","items-center","justify-center","p-4"],"style":""}
[15/07/2025 08:58:44] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Données contextuelles: {"employees":5,"posts":5}
[15/07/2025 08:58:45] [FRONTEND] [LOG] 🎯 [POST DRAG] Début drag poste: 2a9a2244-1e0d-4062-8891-198d1cac2934 (Poste Matin)
[15/07/2025 08:58:45] [FRONTEND] [LOG] 🎯 [POST DRAG] DataTransfer configuré: {"postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","label":"Poste Matin","types":["text/plain","application/x-post-id","postid","postlabel"]}
[15/07/2025 08:58:45] [FRONTEND] [LOG] 🎨 [enableAgendaPreviewMode] Activation preview agenda pour poste 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 08:58:45] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 08:58:45] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 08:58:46] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] Ajout des fantômes pour Poste Matin
[15/07/2025 08:58:46] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] 25 fantômes créés
[15/07/2025 08:58:46] [FRONTEND] [LOG] 🖱️ [enableCellHoverTracking] Suivi activé sur 25 cellules
[15/07/2025 08:58:46] [FRONTEND] [LOG] 🎯 [POST DRAG] 60 zones préparées pour le drop
[15/07/2025 08:58:46] [FRONTEND] [LOG] ✅ [enableAgendaPreviewMode] Mode preview activé pour Poste Matin
[15/07/2025 08:58:46] [FRONTEND] [LOG] 🎯 [POST DRAG] Début drag poste: 2a9a2244-1e0d-4062-8891-198d1cac2934 (Poste Matin)
[15/07/2025 08:58:46] [FRONTEND] [LOG] 🎯 [POST DRAG] DataTransfer configuré: {"postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","label":"Poste Matin","types":["text/plain","application/x-post-id","postid","postlabel"]}
[15/07/2025 08:58:46] [FRONTEND] [LOG] 🎨 [enableAgendaPreviewMode] Activation preview agenda pour poste 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 08:58:46] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 08:58:46] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 08:58:46] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] Ajout des fantômes pour Poste Matin
[15/07/2025 08:58:46] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] 25 fantômes créés
[15/07/2025 08:58:46] [FRONTEND] [LOG] 🖱️ [enableCellHoverTracking] Suivi activé sur 50 cellules
[15/07/2025 08:58:46] [FRONTEND] [LOG] ✅ [enableAgendaPreviewMode] Mode preview activé pour Poste Matin
[15/07/2025 08:58:46] [FRONTEND] [LOG] 🎯 [POST DRAG] 60 zones préparées pour le drop
[15/07/2025 08:58:46] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 59f5df3a-33ef-425c-bfa2-adca818cf94f, types: text/plain, application/x-post-id, postid, postlabel
[15/07/2025 08:58:46] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 59f5df3a-33ef-425c-bfa2-adca818cf94f, types: text/plain, application/x-post-id, postid, postlabel
[15/07/2025 08:58:46] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 08:58:46] [FRONTEND] [LOG] 🎯 [DROP] ✅ Drop reçu sur employé: Sophie Leblanc
[15/07/2025 08:58:46] [FRONTEND] [LOG] 🎯 [openAssignmentContextModal] Poste brut: 2a9a2244-1e0d-4062-8891-198d1cac2934, Employé: 59f5df3a-33ef-425c-bfa2-adca818cf94f, Contexte: null
[15/07/2025 08:58:46] [FRONTEND] [LOG] ✅ [DROP] PostId trouvé via application/x-post-id: "2a9a2244-1e0d-4062-8891-198d1cac2934"
[15/07/2025 08:58:47] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 08:58:47] [FRONTEND] [LOG] 📦 [DROP] Nouveau poste: 2a9a2244-1e0d-4062-8891-198d1cac2934 → 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 08:58:47] [FRONTEND] [LOG] ℹ️ [openAssignmentContextModal] Option "Attribution unique (cellule)" masquée - contexte inapproprié
[15/07/2025 08:58:47] [FRONTEND] [LOG] 📅 [openAssignmentContextModal] Date initialisée: 2025-07-15
[15/07/2025 08:58:47] [FRONTEND] [LOG] ✅ [DROP] Modal d'assignation ouvert
[15/07/2025 08:58:47] [FRONTEND] [LOG] 🎯 [POST DRAG] Fin drag poste: 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 08:58:47] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 08:58:47] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 08:58:47] [FRONTEND] [LOG] 🎯 [POST DRAG] Fin drag poste: 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 08:58:47] [FRONTEND] [LOG] 🎯 [POST DRAG] Nettoyage des 60 zones
[15/07/2025 08:58:47] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 08:58:47] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 08:58:47] [FRONTEND] [LOG] 🎯 [POST DRAG] Nettoyage des 60 zones
[15/07/2025 08:58:56] [FRONTEND] [DEBUG] [vite] connected.
[15/07/2025 08:58:56] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[15/07/2025 08:58:56] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[15/07/2025 08:58:56] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[15/07/2025 08:58:56] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[15/07/2025 08:58:56] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[15/07/2025 08:58:56] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[15/07/2025 08:58:56] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[15/07/2025 08:58:56] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[15/07/2025 08:58:56] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[15/07/2025 08:58:56] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[15/07/2025 08:58:56] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[15/07/2025 08:58:56] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[15/07/2025 08:58:56] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[15/07/2025 08:58:56] [FRONTEND] [LOG] 🧪 [TEST] Script de test de la modale drag & drop chargé
[15/07/2025 08:58:56] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[15/07/2025 08:58:56] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[15/07/2025 08:58:56] [FRONTEND] [LOG] - testAssignmentModal() : Tester l'ouverture de la modale
[15/07/2025 08:58:56] [FRONTEND] [LOG] - testButtonListeners() : Tester les listeners des boutons
[15/07/2025 08:58:56] [FRONTEND] [LOG] - diagnoseModalIssues() : Diagnostiquer les problèmes
[15/07/2025 08:58:56] [FRONTEND] [LOG] - forceReconfigureListeners() : Reconfigurer les listeners
[15/07/2025 08:58:56] [FRONTEND] [LOG] - testButtonClicks() : Tester les clics sur les boutons
[15/07/2025 08:58:56] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[15/07/2025 08:58:56] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:58:56.682Z",
  "originalArgs": [
    "[query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ]
}
[15/07/2025 08:58:56] [FRONTEND] [LOG] [LogsPage] Fermeture de la connexion SSE.
[15/07/2025 08:58:56] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 118ms: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:58:56.800Z",
  "originalArgs": [
    "✅ Requête exécutée en 118ms:",
    "SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ]
}
[15/07/2025 08:58:56] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[15/07/2025 08:58:56] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:58:56.853Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 08:58:56] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:58:56.851Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 08:58:56] [BACKEND] [INFO] [LOGS] Client connecté: 1752584336680
[15/07/2025 08:58:58] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[15/07/2025 08:58:58] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic des problèmes de modale...
[15/07/2025 08:58:58] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Éléments DOM: {"modal":false,"closeBtn":false,"cancelBtn":false,"confirmBtn":false,"radioButtons":0}
[15/07/2025 08:58:58] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Données contextuelles: {"employees":0,"posts":0}
[15/07/2025 08:58:58] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] TeamCalendarApp: {"exists":true,"hasModalManager":true,"hasOpenFunction":true,"hasHandleFunction":true}
[15/07/2025 08:59:02] [FRONTEND] [DEBUG] [vite] connected.
[15/07/2025 08:59:02] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[15/07/2025 08:59:02] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[15/07/2025 08:59:02] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[15/07/2025 08:59:02] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[15/07/2025 08:59:02] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[15/07/2025 08:59:02] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[15/07/2025 08:59:02] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[15/07/2025 08:59:02] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[15/07/2025 08:59:02] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[15/07/2025 08:59:02] [BACKEND] [INFO] [LOGS] Client déconnecté: 1752584336680
[15/07/2025 08:59:02] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[15/07/2025 08:59:02] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[15/07/2025 08:59:02] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[15/07/2025 08:59:02] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[15/07/2025 08:59:02] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[15/07/2025 08:59:02] [FRONTEND] [LOG] 🧪 [TEST] Script de test de la modale drag & drop chargé
[15/07/2025 08:59:02] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[15/07/2025 08:59:02] [FRONTEND] [LOG] - testAssignmentModal() : Tester l'ouverture de la modale
[15/07/2025 08:59:02] [FRONTEND] [LOG] - testButtonListeners() : Tester les listeners des boutons
[15/07/2025 08:59:02] [FRONTEND] [LOG] - diagnoseModalIssues() : Diagnostiquer les problèmes
[15/07/2025 08:59:02] [FRONTEND] [LOG] - forceReconfigureListeners() : Reconfigurer les listeners
[15/07/2025 08:59:03] [FRONTEND] [LOG] - testButtonClicks() : Tester les clics sur les boutons
[15/07/2025 08:59:03] [FRONTEND] [LOG] 🔍 [VALIDATION] Script de validation de la correction modale chargé
[15/07/2025 08:59:03] [FRONTEND] [LOG] ✅ [VALIDATION] Fonctions de validation disponibles:
[15/07/2025 08:59:03] [FRONTEND] [LOG] - validateMainFix() : Valider la correction principale
[15/07/2025 08:59:03] [FRONTEND] [LOG] - validateDOMElements() : Valider les éléments DOM
[15/07/2025 08:59:03] [FRONTEND] [LOG] - validateListeners() : Valider les listeners
[15/07/2025 08:59:03] [FRONTEND] [LOG] - runCompleteValidation() : Validation complète
[15/07/2025 08:59:03] [FRONTEND] [LOG] - testEndToEnd() : Test end-to-end complet
[15/07/2025 08:59:03] [FRONTEND] [LOG] 🔄 [Agenda] Réinitialisation nécessaire (employés non chargés)
[15/07/2025 08:59:03] [FRONTEND] [LOG] 🧹 [Agenda] Nettoyage composant Agenda
[15/07/2025 08:59:03] [FRONTEND] [LOG] 🚀 [Agenda] Initialisation TeamCalendarApp...
[15/07/2025 08:59:03] [FRONTEND] [LOG] 🚀 [init] Initialisation de TeamCalendarApp...
[15/07/2025 08:59:03] [FRONTEND] [LOG] 🧹 [clearRenderCache] Nettoyage du cache de rendu
[15/07/2025 08:59:03] [FRONTEND] [LOG] ✅ [clearRenderCache] Cache de rendu nettoyé
[15/07/2025 08:59:03] [FRONTEND] [ERROR] [LogsPage] Erreur SSE: {"isTrusted":true}
[15/07/2025 08:59:03] [FRONTEND] [DEBUG] [vite] connected.
[15/07/2025 08:59:03] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'
         {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:03.377Z",
  "originalArgs": [
    "[query] Executing query: \n            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'\n        ",
    {}
  ]
}
[15/07/2025 08:59:03] [BACKEND] [INFO] 📋 [GET /api/employee-order] Récupération ordre des employés
[15/07/2025 08:59:03] [BACKEND] [INFO] [LOGGER] 📋 [GET /api/employee-order] Récupération ordre des employés
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:03.376Z",
  "originalArgs": [
    "📋 [GET /api/employee-order] Récupération ordre des employés"
  ]
}
[15/07/2025 08:59:03] [BACKEND] [INFO] [LOGGER] 📋 [GET /api/employee-order] Aucun ordre personnalisé trouvé
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:03.427Z",
  "originalArgs": [
    "📋 [GET /api/employee-order] Aucun ordre personnalisé trouvé"
  ]
}
[15/07/2025 08:59:03] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 49ms: 
            SELECT setting_value FROM app_setting {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:03.427Z",
  "originalArgs": [
    "✅ Requête exécutée en 49ms:",
    "\n            SELECT setting_value FROM app_setting",
    {}
  ]
}
[15/07/2025 08:59:03] [FRONTEND] [ERROR] Erreur de connexion API: Error: NetworkError when attempting to fetch resource.

[15/07/2025 08:59:03] [FRONTEND] [LOG] 🔄 [TeamCalendarApp] API non disponible, fallback localStorage
[15/07/2025 08:59:03] [FRONTEND] [LOG] 🔍 [init] Recherche du bouton des paramètres dans le DOM
[15/07/2025 08:59:03] [FRONTEND] [LOG] ✅ [init] Icône paramètres trouvée: [Objet non sérialisable]
[15/07/2025 08:59:03] [FRONTEND] [LOG] 🔍 [init] Bouton des paramètres trouvé ? true
[15/07/2025 08:59:03] [FRONTEND] [LOG] ⚙️ [loadStateFromLocalStorage] Aucun paramètre sauvegardé.
[15/07/2025 08:59:03] [FRONTEND] [LOG] 🔍 [init] Attachement du listener click sur le bouton des paramètres
[15/07/2025 08:59:03] [FRONTEND] [LOG] 🔍 [init] Bouton des paramètres header trouvé ? true
[15/07/2025 08:59:03] [FRONTEND] [LOG] 🔍 [init] Recherche du bouton des paramètres header dans le DOM
[15/07/2025 08:59:03] [FRONTEND] [LOG] ✅ [setupNavigationListeners] Écouteurs de navigation attachés
[15/07/2025 08:59:03] [FRONTEND] [LOG] 🎯 [setupViewNavigationListeners] Configuration des boutons de navigation...
[15/07/2025 08:59:03] [FRONTEND] [LOG] 🔍 [init] Attachement du listener click sur le bouton des paramètres header
[15/07/2025 08:59:03] [FRONTEND] [LOG] ✅ Boutons de navigation configurés
[15/07/2025 08:59:03] [FRONTEND] [LOG] 🔧 [ModalManager] Configuration des gestionnaires de modal
[15/07/2025 08:59:03] [FRONTEND] [LOG] ✅ [ModalManager] Gestionnaires de modal configurés
[15/07/2025 08:59:03] [FRONTEND] [LOG] 🔧 [setupGeneralSettings] Configuration des paramètres généraux
[15/07/2025 08:59:03] [FRONTEND] [LOG] 🔧 [setupGeneralSettings] Création du conteneur de fuseau horaire
[15/07/2025 08:59:03] [FRONTEND] [LOG] ✅ [setupGlobalEscapeHandler] Gestionnaire ESC global configuré
[15/07/2025 08:59:03] [FRONTEND] [LOG] 📋 [loadEmployeeOrder] Chargement ordre des employés
[15/07/2025 08:59:03] [FRONTEND] [DEBUG] [vite] connected.
[15/07/2025 08:59:04] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[15/07/2025 08:59:04] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[15/07/2025 08:59:04] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[15/07/2025 08:59:04] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[15/07/2025 08:59:04] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[15/07/2025 08:59:04] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[15/07/2025 08:59:04] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[15/07/2025 08:59:04] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[15/07/2025 08:59:04] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[15/07/2025 08:59:04] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[15/07/2025 08:59:04] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[15/07/2025 08:59:04] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[15/07/2025 08:59:04] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[15/07/2025 08:59:04] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[15/07/2025 08:59:04] [FRONTEND] [LOG] 🧪 [TEST] Script de test de la modale drag & drop chargé
[15/07/2025 08:59:04] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[15/07/2025 08:59:04] [FRONTEND] [LOG] - testButtonListeners() : Tester les listeners des boutons
[15/07/2025 08:59:04] [FRONTEND] [LOG] - testAssignmentModal() : Tester l'ouverture de la modale
[15/07/2025 08:59:04] [FRONTEND] [LOG] - diagnoseModalIssues() : Diagnostiquer les problèmes
[15/07/2025 08:59:04] [FRONTEND] [LOG] - testButtonClicks() : Tester les clics sur les boutons
[15/07/2025 08:59:04] [FRONTEND] [LOG] - forceReconfigureListeners() : Reconfigurer les listeners
[15/07/2025 08:59:04] [FRONTEND] [LOG] 🔍 [VALIDATION] Script de validation de la correction modale chargé
[15/07/2025 08:59:04] [FRONTEND] [LOG] ✅ [VALIDATION] Fonctions de validation disponibles:
[15/07/2025 08:59:04] [FRONTEND] [LOG] - validateMainFix() : Valider la correction principale
[15/07/2025 08:59:04] [FRONTEND] [LOG] - validateDOMElements() : Valider les éléments DOM
[15/07/2025 08:59:04] [FRONTEND] [LOG] - validateListeners() : Valider les listeners
[15/07/2025 08:59:04] [FRONTEND] [LOG] - runCompleteValidation() : Validation complète
[15/07/2025 08:59:04] [FRONTEND] [LOG] - testEndToEnd() : Test end-to-end complet
[15/07/2025 08:59:04] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[15/07/2025 08:59:04] [FRONTEND] [LOG] [LogsPage] Fermeture de la connexion SSE.
[15/07/2025 08:59:04] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[15/07/2025 08:59:04] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[15/07/2025 08:59:04] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[15/07/2025 08:59:04] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[15/07/2025 08:59:04] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 89ms: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 89ms:",
    "SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:04.590Z"
}
[15/07/2025 08:59:04] [BACKEND] [INFO] ✅ Requête exécutée en 89ms: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
[15/07/2025 08:59:04] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[15/07/2025 08:59:04] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[15/07/2025 08:59:04] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[15/07/2025 08:59:04] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[15/07/2025 08:59:04] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[15/07/2025 08:59:04] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[15/07/2025 08:59:04] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[15/07/2025 08:59:04] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[15/07/2025 08:59:04] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[15/07/2025 08:59:04] [FRONTEND] [LOG] 🧪 [TEST] Script de test de la modale drag & drop chargé
[15/07/2025 08:59:04] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[15/07/2025 08:59:04] [FRONTEND] [LOG] - testAssignmentModal() : Tester l'ouverture de la modale
[15/07/2025 08:59:04] [FRONTEND] [LOG] - testButtonListeners() : Tester les listeners des boutons
[15/07/2025 08:59:04] [FRONTEND] [LOG] - diagnoseModalIssues() : Diagnostiquer les problèmes
[15/07/2025 08:59:04] [FRONTEND] [LOG] - forceReconfigureListeners() : Reconfigurer les listeners
[15/07/2025 08:59:04] [FRONTEND] [LOG] - testButtonClicks() : Tester les clics sur les boutons
[15/07/2025 08:59:04] [FRONTEND] [LOG] 🔍 [VALIDATION] Script de validation de la correction modale chargé
[15/07/2025 08:59:04] [FRONTEND] [LOG] ✅ [VALIDATION] Fonctions de validation disponibles:
[15/07/2025 08:59:04] [FRONTEND] [LOG] - validateMainFix() : Valider la correction principale
[15/07/2025 08:59:04] [FRONTEND] [LOG] - validateDOMElements() : Valider les éléments DOM
[15/07/2025 08:59:04] [FRONTEND] [LOG] - validateListeners() : Valider les listeners
[15/07/2025 08:59:04] [FRONTEND] [LOG] - testEndToEnd() : Test end-to-end complet
[15/07/2025 08:59:04] [FRONTEND] [LOG] - runCompleteValidation() : Validation complète
[15/07/2025 08:59:04] [FRONTEND] [LOG] 🧹 [Agenda] Nettoyage composant Agenda
[15/07/2025 08:59:04] [FRONTEND] [LOG] 🔄 [Agenda] Réinitialisation nécessaire (employés non chargés)
[15/07/2025 08:59:05] [FRONTEND] [LOG] 🚀 [Agenda] Initialisation TeamCalendarApp...
[15/07/2025 08:59:05] [FRONTEND] [LOG] 🚀 [init] Initialisation de TeamCalendarApp...
[15/07/2025 08:59:05] [FRONTEND] [LOG] ✅ [clearRenderCache] Cache de rendu nettoyé
[15/07/2025 08:59:05] [FRONTEND] [LOG] 🧹 [clearRenderCache] Nettoyage du cache de rendu
[15/07/2025 08:59:05] [FRONTEND] [LOG] 🔌 [init] Indicateur de connexion initialisé
[15/07/2025 08:59:05] [FRONTEND] [LOG] 🔌 [ConnectionIndicator] Création de l'indicateur de connexion
[15/07/2025 08:59:05] [FRONTEND] [LOG] 🌐 [TeamCalendarApp] Chargement depuis l'API...
[15/07/2025 08:59:05] [FRONTEND] [LOG] 📂 [TeamCalendarApp] Chargement de l'état...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT e.*, et.name as template_name 
      FROM employees e 
      LEFT JOIN employee_templates et ON e.template_id = et.id 
      ORDER BY e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT e.*, et.name as template_name \n      FROM employees e \n      LEFT JOIN employee_templates et ON e.template_id = et.id \n      ORDER BY e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:05.481Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 52ms: 
      SELECT e.*, et.name as template_name 
      {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 52ms:",
    "\n      SELECT e.*, et.name as template_name \n     ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:05.533Z"
}
[15/07/2025 08:59:05] [BACKEND] [INFO] ✅ Requête exécutée en 52ms: 
      SELECT e.*, et.name as template_name 
      {}
[15/07/2025 08:59:05] [FRONTEND] [LOG] 👥 [TeamCalendarApp] 5 employés chargés
[15/07/2025 08:59:05] [FRONTEND] [LOG] 📦 [loadState] Chargement de tous les shifts depuis la base de données...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT s.*, e.name as employee_name, sp.label as post_label
      FROM shifts s
      LEFT JOIN employees e ON s.employee_id = e.id
      LEFT JOIN standard_posts sp ON s.post_id = sp.id
      ORDER BY s.date_key, e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT s.*, e.name as employee_name, sp.label as post_label\n      FROM shifts s\n      LEFT JOIN employees e ON s.employee_id = e.id\n      LEFT JOIN standard_posts sp ON s.post_id = sp.id\n      ORDER BY s.date_key, e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:05.584Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 46ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 46ms:",
    "\n      SELECT s.*, e.name as employee_name, sp.lab",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:05.629Z"
}
[15/07/2025 08:59:05] [BACKEND] [INFO] ✅ Requête exécutée en 46ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
[15/07/2025 08:59:05] [FRONTEND] [LOG] 📅 [loadState] 0 shifts chargés depuis la base de données
[15/07/2025 08:59:05] [FRONTEND] [LOG] ✅ [loadState] Shifts organisés dans this.data.schedule
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:05.655Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 67ms: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 67ms:",
    "SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:05.721Z"
}
[15/07/2025 08:59:05] [BACKEND] [INFO] ✅ Requête exécutée en 67ms: SELECT * FROM standard_posts ORDER BY label {}
[15/07/2025 08:59:05] [FRONTEND] [LOG] 🏢 [TeamCalendarApp] 5 postes standards chargés
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:05.746Z"
}
[15/07/2025 08:59:05] [BACKEND] [INFO] ✅ Requête exécutée en 39ms: SELECT * FROM app_settings ORDER BY setting_key {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 39ms: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 39ms:",
    "SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:05.784Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT 
        ra.id,
        ra.employee_id,
        ra.post_id,
        ra.days_of_week,
        ra.start_date,
        ra.end_date,
        ra.is_active,
        ra.excluded_dates,
        e.name as employee_name,
        sp.label as post_label,
        sp.hours as post_hours,
        sp.type as post_type,
        array_length(ra.days_of_week, 1) as days_count
      FROM regular_assignments ra
      LEFT JOIN employees e ON ra.employee_id = e.id
      LEFT JOIN standard_posts sp ON ra.post_id = sp.id
      WHERE ra.is_active = true
      ORDER BY e.name, sp.label, ra.start_date
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT \n        ra.id,\n        ra.employee_id,\n        ra.post_id,\n        ra.days_of_week,\n        ra.start_date,\n        ra.end_date,\n        ra.is_active,\n        ra.excluded_dates,\n        e.name as employee_name,\n        sp.label as post_label,\n        sp.hours as post_hours,\n        sp.type as post_type,\n        array_length(ra.days_of_week, 1) as days_count\n      FROM regular_assignments ra\n      LEFT JOIN employees e ON ra.employee_id = e.id\n      LEFT JOIN standard_posts sp ON ra.post_id = sp.id\n      WHERE ra.is_active = true\n      ORDER BY e.name, sp.label, ra.start_date\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:05.806Z"
}
[15/07/2025 08:59:05] [FRONTEND] [LOG] ⚙️ [loadState] Paramètres chargés depuis API: {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 49ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 49ms:",
    "\n      SELECT \n        ra.id,\n        ra.employee_",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:05.855Z"
}
[15/07/2025 08:59:05] [BACKEND] [INFO] ✅ Requête exécutée en 49ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
[15/07/2025 08:59:05] [FRONTEND] [LOG] 📋 [loadState] 0/0 assignations régulières valides chargées et normalisées
[15/07/2025 08:59:05] [FRONTEND] [LOG] 🔧 [fixInvalidAssignmentDates] Vérification des dates invalides...
[15/07/2025 08:59:05] [FRONTEND] [LOG] ✅ [fixInvalidAssignmentDates] Aucune date invalide trouvée
[15/07/2025 08:59:05] [FRONTEND] [LOG] 📋 [loadState] Assignations régulières chargées (application différée)
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🧹 [cleanupDuplicateRegularShifts] Nettoyage des doublons de shifts réguliers...
[15/07/2025 08:59:06] [FRONTEND] [LOG] ✅ [cleanupDuplicateRegularShifts] Aucun doublon détecté
[15/07/2025 08:59:06] [FRONTEND] [LOG] ✅ [cleanupInvalidAssignmentIds] Aucun assignment_id invalide détecté
[15/07/2025 08:59:06] [FRONTEND] [LOG] ✅ [loadState] État chargé depuis l'API avec succès
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🧹 [cleanupInvalidAssignmentIds] Nettoyage des assignment_id invalides...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'
         {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'\n        ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:05.972Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📋 [GET /api/employee-order] Récupération ordre des employés
  Data: {
  "originalArgs": [
    "📋 [GET /api/employee-order] Récupération ordre des employés"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:05.969Z"
}
[15/07/2025 08:59:06] [FRONTEND] [LOG] ✅ [init] Icône paramètres trouvée: [Objet non sérialisable]
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🔍 [init] Recherche du bouton des paramètres dans le DOM
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🔍 [init] Bouton des paramètres trouvé ? true
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 56ms: 
            SELECT setting_value FROM app_setting {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 56ms:",
    "\n            SELECT setting_value FROM app_setting",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:06.028Z"
}
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🔍 [init] Recherche du bouton des paramètres header dans le DOM
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📋 [GET /api/employee-order] Aucun ordre personnalisé trouvé
  Data: {
  "originalArgs": [
    "📋 [GET /api/employee-order] Aucun ordre personnalisé trouvé"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:06.029Z"
}
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🔍 [init] Attachement du listener click sur le bouton des paramètres
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🔍 [init] Bouton des paramètres header trouvé ? true
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🔍 [init] Attachement du listener click sur le bouton des paramètres header
[15/07/2025 08:59:06] [FRONTEND] [LOG] ✅ [setupNavigationListeners] Écouteurs de navigation attachés
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🎯 [setupViewNavigationListeners] Configuration des boutons de navigation...
[15/07/2025 08:59:06] [FRONTEND] [LOG] ✅ Boutons de navigation configurés
[15/07/2025 08:59:06] [FRONTEND] [LOG] ✅ [ModalManager] Gestionnaires de modal configurés
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🔧 [ModalManager] Configuration des gestionnaires de modal
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🔧 [setupGeneralSettings] Configuration des paramètres généraux
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🔧 [setupGeneralSettings] Création du conteneur de fuseau horaire
[15/07/2025 08:59:06] [FRONTEND] [LOG] ✅ [setupGlobalEscapeHandler] Gestionnaire ESC global configuré
[15/07/2025 08:59:06] [FRONTEND] [LOG] 📋 [loadEmployeeOrder] Chargement ordre des employés
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:06.144Z"
}
[15/07/2025 08:59:06] [BACKEND] [INFO] 📋 [GET /api/employee-order] Récupération ordre des employés
[15/07/2025 08:59:06] [FRONTEND] [WARN] ⚠️ [loadEmployeeOrder] Réponse API sans tableau employeeOrder
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🔧 [init] Forçage du rendu initial...
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[15/07/2025 08:59:06] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[15/07/2025 08:59:06] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 08:59:06] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[15/07/2025 08:59:06] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 60 zones trouvées
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 60 zones de drop disponibles
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Matin: [1, 2, 3, 4, 5]
[15/07/2025 08:59:06] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 2a9a2244-1e0d-4062-8891-198d1cac2934 configuré comme draggable
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste b55a363e-2261-4255-ae7d-1ebe40fa9e82 configuré comme draggable
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Nuit: [1, 2, 3, 4, 5]
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[15/07/2025 08:59:06] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Éléments DOM: {"modal":false,"closeBtn":false,"cancelBtn":false,"confirmBtn":false,"radioButtons":0}
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic des problèmes de modale...
[15/07/2025 08:59:06] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Données contextuelles: {"employees":0,"posts":0}
[15/07/2025 08:59:06] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste b55a363e-2261-4255-ae7d-1ebe40fa9e82
[15/07/2025 08:59:06] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] TeamCalendarApp: {"exists":true,"hasModalManager":true,"hasOpenFunction":true,"hasHandleFunction":true}
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff configuré comme draggable
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Soir: [1, 2, 3, 4, 5]
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE1: [0, 6]
[15/07/2025 08:59:06] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff
[15/07/2025 08:59:06] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3 configuré comme draggable
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE2: [0, 6]
[15/07/2025 08:59:06] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6a79ab18-319e-4a85-805e-f80d0de9a73f
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6a79ab18-319e-4a85-805e-f80d0de9a73f configuré comme draggable
[15/07/2025 08:59:06] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 2a9a2244-1e0d-4062-8891-198d1cac2934 configuré comme draggable
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste b55a363e-2261-4255-ae7d-1ebe40fa9e82 configuré comme draggable
[15/07/2025 08:59:06] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste b55a363e-2261-4255-ae7d-1ebe40fa9e82
[15/07/2025 08:59:06] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff configuré comme draggable
[15/07/2025 08:59:06] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3 configuré comme draggable
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6a79ab18-319e-4a85-805e-f80d0de9a73f configuré comme draggable
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 08:59:06] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6a79ab18-319e-4a85-805e-f80d0de9a73f
[15/07/2025 08:59:06] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 08:59:06] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 08:59:06] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 08:59:06] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 08:59:06] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 08:59:06] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (60 zones)
[15/07/2025 08:59:06] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 08:59:06] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🔧 [init] Configuration du drag & drop des postes...
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 08:59:06] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 60 zones trouvées
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 60 zones de drop disponibles
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 2a9a2244-1e0d-4062-8891-198d1cac2934 configuré comme draggable
[15/07/2025 08:59:06] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 08:59:06] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste b55a363e-2261-4255-ae7d-1ebe40fa9e82
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste b55a363e-2261-4255-ae7d-1ebe40fa9e82 configuré comme draggable
[15/07/2025 08:59:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff configuré comme draggable
[15/07/2025 08:59:07] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff
[15/07/2025 08:59:07] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3 configuré comme draggable
[15/07/2025 08:59:07] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3
[15/07/2025 08:59:07] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6a79ab18-319e-4a85-805e-f80d0de9a73f configuré comme draggable
[15/07/2025 08:59:07] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6a79ab18-319e-4a85-805e-f80d0de9a73f
[15/07/2025 08:59:07] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 2a9a2244-1e0d-4062-8891-198d1cac2934 configuré comme draggable
[15/07/2025 08:59:07] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 08:59:07] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste b55a363e-2261-4255-ae7d-1ebe40fa9e82 configuré comme draggable
[15/07/2025 08:59:07] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste b55a363e-2261-4255-ae7d-1ebe40fa9e82
[15/07/2025 08:59:07] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff configuré comme draggable
[15/07/2025 08:59:07] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff
[15/07/2025 08:59:07] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3 configuré comme draggable
[15/07/2025 08:59:07] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3
[15/07/2025 08:59:07] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6a79ab18-319e-4a85-805e-f80d0de9a73f configuré comme draggable
[15/07/2025 08:59:07] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6a79ab18-319e-4a85-805e-f80d0de9a73f
[15/07/2025 08:59:07] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 08:59:07] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 08:59:07] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 08:59:07] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 08:59:07] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 08:59:07] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 08:59:07] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 08:59:07] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 08:59:07] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 08:59:07] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 08:59:07] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 08:59:07] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 08:59:07] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 08:59:07] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (60 zones)
[15/07/2025 08:59:07] [FRONTEND] [LOG] ℹ️ [setupEmployeeDragDrop] Fonction désactivée - Utiliser setupDragAndDrop
[15/07/2025 08:59:07] [FRONTEND] [LOG] 🔧 [addEmergencyFixButton] Ajout du bouton de correction...
[15/07/2025 08:59:07] [FRONTEND] [LOG] ✅ [addEmergencyFixButton] Bouton de correction ajouté
[15/07/2025 08:59:07] [FRONTEND] [LOG] ✅ [init] Initialisation terminée avec succès
[15/07/2025 08:59:07] [FRONTEND] [LOG] 🎉 [Agenda] Application fullscreen initialisée avec succès
[15/07/2025 08:59:07] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 08:59:07] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 08:59:07] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] 0 grips ajoutés
[15/07/2025 08:59:07] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation automatique...
[15/07/2025 08:59:07] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation complète...
[15/07/2025 08:59:07] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation de la correction principale...
[15/07/2025 08:59:07] [FRONTEND] [ERROR] ❌ [VALIDATION] ModalManager.app non initialisé
[15/07/2025 08:59:07] [FRONTEND] [LOG] ✅ [VALIDATION] ModalManager.setupAssignmentContextModal disponible
[15/07/2025 08:59:07] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des éléments DOM...
[15/07/2025 08:59:07] [FRONTEND] [LOG] 📋 [VALIDATION] Éléments DOM: {"modal":false,"closeBtn":false,"cancelBtn":false,"confirmBtn":false,"radioButtons":false}
[15/07/2025 08:59:07] [FRONTEND] [ERROR] ❌ [VALIDATION] Éléments DOM manquants
[15/07/2025 08:59:07] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des listeners...
[15/07/2025 08:59:07] [FRONTEND] [LOG] 🔧 [setupAssignmentContextModal] Configuration du modal contextuel...
[15/07/2025 08:59:07] [FRONTEND] [ERROR] ❌ [setupAssignmentContextModal] Éléments manquants: {"closeBtn":false,"cancelBtn":false,"confirmBtn":false}
[15/07/2025 08:59:07] [FRONTEND] [LOG] ✅ [VALIDATION] setupAssignmentContextModal exécuté sans erreur
[15/07/2025 08:59:07] [FRONTEND] [LOG] 📊 [VALIDATION] Résultats de validation: {"mainFix":false,"domElements":false,"listeners":true}
[15/07/2025 08:59:07] [FRONTEND] [LOG] 🔧 [VALIDATION] Actions recommandées:
[15/07/2025 08:59:07] [FRONTEND] [LOG] - Vérifier que TeamCalendarApp et ModalManager sont correctement initialisés
[15/07/2025 08:59:07] [FRONTEND] [ERROR] ❌ [VALIDATION] Certaines validations ont échoué
[15/07/2025 08:59:07] [FRONTEND] [LOG] - Vérifier que la modale assignment-context-modal existe dans le DOM
[15/07/2025 08:59:07] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[15/07/2025 08:59:07] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] TeamCalendarApp: {"exists":true,"hasModalManager":true,"hasOpenFunction":true,"hasHandleFunction":true}
[15/07/2025 08:59:07] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Éléments DOM: {"modal":true,"closeBtn":true,"cancelBtn":true,"confirmBtn":true,"radioButtons":4}
[15/07/2025 08:59:07] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic des problèmes de modale...
[15/07/2025 08:59:07] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] État de la modale: {"hidden":true,"classes":["hidden","fixed","inset-0","bg-black/40","backdrop-blur-sm","z-50","flex","items-center","justify-center","p-4"],"style":""}
[15/07/2025 08:59:07] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Données contextuelles: {"employees":5,"posts":5}
[15/07/2025 08:59:08] [FRONTEND] [LOG] 🎯 [diagnoseRegularAssignmentGrips] Grips trouvés dans le DOM: 0
[15/07/2025 08:59:08] [FRONTEND] [LOG] 📊 [diagnoseRegularAssignmentGrips] Statistiques: {"totalShifts":0,"regularShifts":0,"regularAssignments":0}
[15/07/2025 08:59:08] [FRONTEND] [LOG] ⚠️ [diagnoseRegularAssignmentGrips] Aucun shift régulier trouvé - Ré-application des attributions...
[15/07/2025 08:59:08] [FRONTEND] [LOG] 🔍 [diagnoseRegularAssignmentGrips] Diagnostic des grips d'attributions régulières
[15/07/2025 08:59:08] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 08:59:08] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 08:59:09] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation complète...
[15/07/2025 08:59:09] [FRONTEND] [LOG] ✅ [VALIDATION] ModalManager.app initialisé
[15/07/2025 08:59:09] [FRONTEND] [LOG] ✅ [VALIDATION] ModalManager.setupAssignmentContextModal disponible
[15/07/2025 08:59:09] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation automatique...
[15/07/2025 08:59:09] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation de la correction principale...
[15/07/2025 08:59:09] [FRONTEND] [LOG] ✅ [VALIDATION] handleAssignmentContextConfirm disponible
[15/07/2025 08:59:09] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des éléments DOM...
[15/07/2025 08:59:09] [FRONTEND] [LOG] ✅ [VALIDATION] Tous les éléments DOM sont présents
[15/07/2025 08:59:09] [FRONTEND] [LOG] 📋 [VALIDATION] Éléments DOM: {"modal":true,"closeBtn":true,"cancelBtn":true,"confirmBtn":true,"radioButtons":true}
[15/07/2025 08:59:09] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des listeners...
[15/07/2025 08:59:09] [FRONTEND] [LOG] 🔧 [setupAssignmentContextModal] Configuration du modal contextuel...
[15/07/2025 08:59:09] [FRONTEND] [LOG] ✅ [setupAssignmentContextModal] Modal contextuel configuré avec listeners nettoyés
[15/07/2025 08:59:09] [FRONTEND] [LOG] ✅ [VALIDATION] setupAssignmentContextModal exécuté sans erreur
[15/07/2025 08:59:09] [FRONTEND] [LOG] 📊 [VALIDATION] Résultats de validation: {"mainFix":true,"domElements":true,"listeners":true}
[15/07/2025 08:59:09] [FRONTEND] [LOG] 🎉 [VALIDATION] Toutes les validations sont passées avec succès !
[15/07/2025 08:59:09] [FRONTEND] [LOG] 🧪 [VALIDATION] Pour tester pratiquement:
[15/07/2025 08:59:09] [FRONTEND] [LOG] ✅ [VALIDATION] La correction de la modale devrait maintenant fonctionner
[15/07/2025 08:59:09] [FRONTEND] [LOG] 1. Effectuez un drag & drop d'un poste vers un employé
[15/07/2025 08:59:09] [FRONTEND] [LOG] 2. Vérifiez que la modale s'ouvre
[15/07/2025 08:59:09] [FRONTEND] [LOG] 3. Cliquez sur "Confirmer" et vérifiez les logs
[15/07/2025 08:59:09] [FRONTEND] [LOG] 4. Ou utilisez testButtonClicks() pour un test automatique
[15/07/2025 08:59:17] [FRONTEND] [LOG] 🎯 [POST DRAG] Début drag poste: 2a9a2244-1e0d-4062-8891-198d1cac2934 (Poste Matin)
[15/07/2025 08:59:17] [FRONTEND] [LOG] 🎯 [POST DRAG] DataTransfer configuré: {"postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","label":"Poste Matin","types":["text/plain","application/x-post-id","postid","postlabel"]}
[15/07/2025 08:59:17] [FRONTEND] [LOG] 🎨 [enableAgendaPreviewMode] Activation preview agenda pour poste 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 08:59:17] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 08:59:17] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 08:59:17] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] Ajout des fantômes pour Poste Matin
[15/07/2025 08:59:17] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] 25 fantômes créés
[15/07/2025 08:59:17] [FRONTEND] [LOG] 🖱️ [enableCellHoverTracking] Suivi activé sur 25 cellules
[15/07/2025 08:59:17] [FRONTEND] [LOG] 🎯 [POST DRAG] 60 zones préparées pour le drop
[15/07/2025 08:59:17] [FRONTEND] [LOG] ✅ [enableAgendaPreviewMode] Mode preview activé pour Poste Matin
[15/07/2025 08:59:17] [FRONTEND] [LOG] 🎯 [POST DRAG] Début drag poste: 2a9a2244-1e0d-4062-8891-198d1cac2934 (Poste Matin)
[15/07/2025 08:59:17] [FRONTEND] [LOG] 🎯 [POST DRAG] DataTransfer configuré: {"postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","label":"Poste Matin","types":["text/plain","application/x-post-id","postid","postlabel"]}
[15/07/2025 08:59:17] [FRONTEND] [LOG] 🎨 [enableAgendaPreviewMode] Activation preview agenda pour poste 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 08:59:17] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 08:59:17] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 08:59:17] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] 25 fantômes créés
[15/07/2025 08:59:17] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] Ajout des fantômes pour Poste Matin
[15/07/2025 08:59:17] [FRONTEND] [LOG] ✅ [enableAgendaPreviewMode] Mode preview activé pour Poste Matin
[15/07/2025 08:59:17] [FRONTEND] [LOG] 🖱️ [enableCellHoverTracking] Suivi activé sur 50 cellules
[15/07/2025 08:59:17] [FRONTEND] [LOG] 🎯 [POST DRAG] 60 zones préparées pour le drop
[15/07/2025 08:59:17] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 59f5df3a-33ef-425c-bfa2-adca818cf94f, types: text/plain, application/x-post-id, postid, postlabel
[15/07/2025 08:59:17] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 59f5df3a-33ef-425c-bfa2-adca818cf94f, types: text/plain, application/x-post-id, postid, postlabel
[15/07/2025 08:59:18] [FRONTEND] [LOG] 🎯 [DROP] ✅ Drop reçu sur employé: Sophie Leblanc
[15/07/2025 08:59:18] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 08:59:18] [FRONTEND] [LOG] ✅ [DROP] PostId trouvé via application/x-post-id: "2a9a2244-1e0d-4062-8891-198d1cac2934"
[15/07/2025 08:59:18] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 08:59:18] [FRONTEND] [LOG] 📦 [DROP] Nouveau poste: 2a9a2244-1e0d-4062-8891-198d1cac2934 → 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 08:59:18] [FRONTEND] [LOG] 🎯 [openAssignmentContextModal] Poste brut: 2a9a2244-1e0d-4062-8891-198d1cac2934, Employé: 59f5df3a-33ef-425c-bfa2-adca818cf94f, Contexte: null
[15/07/2025 08:59:18] [FRONTEND] [LOG] ℹ️ [openAssignmentContextModal] Option "Attribution unique (cellule)" masquée - contexte inapproprié
[15/07/2025 08:59:18] [FRONTEND] [LOG] 📅 [openAssignmentContextModal] Date initialisée: 2025-07-15
[15/07/2025 08:59:18] [FRONTEND] [LOG] ✅ [DROP] Modal d'assignation ouvert
[15/07/2025 08:59:18] [FRONTEND] [LOG] 🎯 [POST DRAG] Fin drag poste: 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 08:59:18] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 08:59:18] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 08:59:18] [FRONTEND] [LOG] 🎯 [POST DRAG] Nettoyage des 60 zones
[15/07/2025 08:59:18] [FRONTEND] [LOG] 🎯 [POST DRAG] Fin drag poste: 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 08:59:18] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 08:59:18] [FRONTEND] [LOG] 🎯 [POST DRAG] Nettoyage des 60 zones
[15/07/2025 08:59:18] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 08:59:44] [FRONTEND] [LOG] 🔄 [setupAssignmentContextModal] Type d'attribution changé: regular-indefinite
[15/07/2025 08:59:44] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:44.651Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:44.515Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:44.653Z"
}
[15/07/2025 08:59:44] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:44.786Z"
}
[15/07/2025 08:59:46] [FRONTEND] [LOG] ✅ [setupAssignmentContextModal] Bouton confirmer cliqué
[15/07/2025 08:59:46] [FRONTEND] [LOG] 🎯 [handleAssignmentContextConfirm] Fonction appelée
[15/07/2025 08:59:46] [FRONTEND] [LOG] 🔍 [setupAssignmentContextModal] État currentContextAssignment: {"postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","context":null}
[15/07/2025 08:59:46] [FRONTEND] [LOG] 🔍 [handleAssignmentContextConfirm] currentContextAssignment: {"postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","context":null}
[15/07/2025 08:59:46] [FRONTEND] [LOG] 📦 [handleAssignmentContextConfirm] Données extraites: {"postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f"}
[15/07/2025 08:59:46] [FRONTEND] [LOG] ✅ [handleAssignmentContextConfirm] Type: regular-indefinite, Poste: 2a9a2244-1e0d-4062-8891-198d1cac2934, Employé: 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 08:59:46] [FRONTEND] [LOG] 🔍 [handleAssignmentContextConfirm] Input trouvé: {"input":true,"value":"regular-indefinite","checked":true}
[15/07/2025 08:59:46] [FRONTEND] [LOG] 🎯 [handleAssignmentContextConfirm] Exécution attribution régulière indéfinie
[15/07/2025 08:59:46] [FRONTEND] [LOG] 📅 [handleAssignmentContextConfirm] Date de début: 2025-07-15
[15/07/2025 08:59:46] [FRONTEND] [LOG] 🗙 [handleAssignmentContextConfirm] Fermeture du modal
[15/07/2025 08:59:47] [FRONTEND] [LOG] 🔄 [createRegularAssignment] Création d'une assignation régulière
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:47.016Z"
}
[15/07/2025 08:59:47] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[15/07/2025 08:59:47] [FRONTEND] [LOG] ✅ [setupAssignmentContextModal] handleAssignmentContextConfirm appelé avec succès
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:47.039Z"
}
[15/07/2025 08:59:47] [FRONTEND] [LOG] ✅ [handleAssignmentContextConfirm] Traitement terminé avec succès
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:47.056Z"
}
[15/07/2025 08:59:47] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 08:59:47] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"52a95d9b-4110-49e2-8d19-8a09d42073a5","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-14T04:00:00.000Z","dayDateKey":"2025-07-14","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 08:59:47] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"52a95d9b-4110-49e2-8d19-8a09d42073a5","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-13T04:00:00.000Z","dayDateKey":"2025-07-13","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 08:59:47] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 08:59:47] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] Pas de dates exclues pour l'attribution 52a95d9b-4110-49e2-8d19-8a09d42073a5 {"assignmentId":"52a95d9b-4110-49e2-8d19-8a09d42073a5","hasExcludedDates":false,"isArray":false}
[15/07/2025 08:59:47] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] Pas de dates exclues pour l'attribution 52a95d9b-4110-49e2-8d19-8a09d42073a5 {"assignmentId":"52a95d9b-4110-49e2-8d19-8a09d42073a5","hasExcludedDates":false,"isArray":false}
[15/07/2025 08:59:47] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"52a95d9b-4110-49e2-8d19-8a09d42073a5","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-15T04:00:00.000Z","dayDateKey":"2025-07-15","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 08:59:47] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-15
[15/07/2025 08:59:47] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 08:59:47] [FRONTEND] [LOG] 📅 [addShift] Conversion: dayIndex 5 → dateKey 2025-07-18
[15/07/2025 08:59:47] [FRONTEND] [LOG] 🎯 [addShiftByDateKey] AJOUT AVEC VÉRIFICATION STRICTE - dateKey 2025-07-18 pour employé 59f5df3a-33ef-425c-bfa2-adca818cf94f: {"postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","text":"08:00-16:00","type":"standard","isRegular":true,"assignmentId":"52a95d9b-4110-49e2-8d19-8a09d42073a5"}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ➕ [RegularAssignments] Création d'une attribution individuelle
  Data: {
  "originalArgs": [
    "➕ [RegularAssignments] Création d'une attribution individuelle"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:47.165Z"
}
[15/07/2025 08:59:47] [BACKEND] [INFO] ➕ [RegularAssignments] Création d'une attribution individuelle
[15/07/2025 08:59:47] [FRONTEND] [LOG] 🎯 [addShiftByDateKey] AJOUT AVEC VÉRIFICATION STRICTE - dateKey 2025-07-15 pour employé 59f5df3a-33ef-425c-bfa2-adca818cf94f: {"postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","text":"08:00-16:00","type":"standard","isRegular":true,"assignmentId":"52a95d9b-4110-49e2-8d19-8a09d42073a5"}
[15/07/2025 08:59:47] [FRONTEND] [LOG] 📝 [addShift] Ajout shift pour employé 59f5df3a-33ef-425c-bfa2-adca818cf94f, jour 2: {"postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","text":"08:00-16:00","type":"standard","isRegular":true,"assignmentId":"52a95d9b-4110-49e2-8d19-8a09d42073a5"}
[15/07/2025 08:59:47] [FRONTEND] [LOG] 📅 [addShift] Conversion: dayIndex 2 → dateKey 2025-07-15
[15/07/2025 08:59:47] [FRONTEND] [WARN] ⚠️ [renderUnifiedCalendar] Conteneur available-posts-container non trouvé, création...
[15/07/2025 08:59:47] [FRONTEND] [LOG] 🆕 [addShiftByDateKey] Création schedule pour employé 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 08:59:47] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[15/07/2025 08:59:47] [FRONTEND] [LOG] 🆕 [addShiftByDateKey] Création jour 2025-07-15 pour employé 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 08:59:47] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-14 avant startDate 2025-07-15 (original: 2025-07-15)
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:47.250Z"
}
[15/07/2025 08:59:47] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[15/07/2025 08:59:47] [FRONTEND] [LOG] 📋 [addShiftByDateKey] Shifts existants pour 2025-07-15: []
[15/07/2025 08:59:47] [FRONTEND] [LOG] ✅ [addShiftByDateKey] Shift ajouté avec succès. Nouveau total: [{"postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","text":"08:00-16:00","type":"standard","isRegular":true,"assignmentId":"52a95d9b-4110-49e2-8d19-8a09d42073a5"}]
[15/07/2025 08:59:47] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] Pas de dates exclues pour l'attribution 52a95d9b-4110-49e2-8d19-8a09d42073a5 {"assignmentId":"52a95d9b-4110-49e2-8d19-8a09d42073a5","hasExcludedDates":false,"isArray":false}
[15/07/2025 08:59:47] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 08:59:47] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"52a95d9b-4110-49e2-8d19-8a09d42073a5","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-16T04:00:00.000Z","dayDateKey":"2025-07-16","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 08:59:47] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-16
[15/07/2025 08:59:47] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 2a9a2244-1e0d-4062-8891-198d1cac2934 configuré comme draggable
[15/07/2025 08:59:47] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6a79ab18-319e-4a85-805e-f80d0de9a73f
[15/07/2025 08:59:47] [FRONTEND] [LOG] 📝 [addShift] Ajout shift pour employé 59f5df3a-33ef-425c-bfa2-adca818cf94f, jour 3: {"postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","text":"08:00-16:00","type":"standard","isRegular":true,"assignmentId":"52a95d9b-4110-49e2-8d19-8a09d42073a5"}
[15/07/2025 08:59:47] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6a79ab18-319e-4a85-805e-f80d0de9a73f configuré comme draggable
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:47.359Z"
}
[15/07/2025 08:59:47] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6a79ab18-319e-4a85-805e-f80d0de9a73f
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📝 [RegularAssignmentV2.create] Données reçues: {"employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","isLimited":false,"startDate":"2025-07-15","endDate":null,"selectedDays":[1,2,3,4,5]}
  Data: {
  "originalArgs": [
    "📝 [RegularAssignmentV2.create] Données reçues:",
    {
      "employeeId": "59f5df3a-33ef-425c-bfa2-adca818cf94f",
      "postId": "2a9a2244-1e0d-4062-8891-198d1cac2934",
      "isLimited": false,
      "startDate": "2025-07-15",
      "endDate": null,
      "selectedDays": [
        1,
        2,
        3,
        4,
        5
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:47.168Z"
}
[15/07/2025 08:59:47] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ [validateAndConvertUuid] UUID valide détecté pour post_id: 2a9a2244-1e0d-4062-8891-198d1cac2934
  Data: {
  "originalArgs": [
    "✅ [validateAndConvertUuid] UUID valide détecté pour post_id: 2a9a2244-1e0d-4062-8891-198d1cac2934"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:47.171Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔍 [RegularAssignmentV2.create] Post ID original: 2a9a2244-1e0d-4062-8891-198d1cac2934 Post ID validé: 2a9a2244-1e0d-4062-8891-198d1cac2934
  Data: {
  "originalArgs": [
    "🔍 [RegularAssignmentV2.create] Post ID original:",
    "2a9a2244-1e0d-4062-8891-198d1cac2934",
    "Post ID validé:",
    "2a9a2244-1e0d-4062-8891-198d1cac2934"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:47.175Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:47.375Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔍 [RegularAssignmentV2.create] Création attribution unique pour jours: [1, 2, 3, 4, 5]
  Data: {
  "originalArgs": [
    "🔍 [RegularAssignmentV2.create] Création attribution unique pour jours: [1, 2, 3, 4, 5]"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:47.176Z"
}
[15/07/2025 08:59:47] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      INSERT INTO regular_assignments (id, employee_id, post_id, days_of_week, start_date, end_date, is_active)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
     {"params":["ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","59f5df3a-33ef-425c-bfa2-adca818cf94f","2a9a2244-1e0d-4062-8891-198d1cac2934",[1,2,3,4,5],"2025-07-15",null,true]}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      INSERT INTO regular_assignments (id, employee_id, post_id, days_of_week, start_date, end_date, is_active)\n      VALUES ($1, $2, $3, $4, $5, $6, $7)\n      RETURNING *\n    ",
    {
      "params": [
        "ea8e0f4b-e5d5-4f22-a59d-200e2c776c76",
        "59f5df3a-33ef-425c-bfa2-adca818cf94f",
        "2a9a2244-1e0d-4062-8891-198d1cac2934",
        [
          1,
          2,
          3,
          4,
          5
        ],
        "2025-07-15",
        null,
        true
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:47.177Z"
}
[15/07/2025 08:59:47] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:47.390Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:47.396Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:47.395Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📤 [RegularAssignments] Données reçues: {
  "employeeId": "59f5df3a-33ef-425c-bfa2-adca818cf94f",
  "postId": "2a9a2244-1e0d-4062-8891-198d1cac2934",
  "isLimited": false,
  "startDate": "2025-07-15",
  "endDate": null,
  "selectedDays": [
    1,
    2,
    3,
    4,
    5
  ]
}
  Data: {
  "originalArgs": [
    "📤 [RegularAssignments] Données reçues:",
    "{\n  \"employeeId\": \"59f5df3a-33ef-425c-bfa2-adca818cf94f\",\n  \"postId\": \"2a9a2244-1e0d-4062-8891-198d1cac2934\",\n  \"isLimited\": false,\n  \"startDate\": \"2025-07-15\",\n  \"endDate\": null,\n  \"selectedDays\": [\n    1,\n    2,\n    3,\n    4,\n    5\n  ]\n}"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:47.166Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔍 [validateAndConvertUuid] Entrée: fieldName="post_id", value="2a9a2244-1e0d-4062-8891-198d1cac2934", type=string
  Data: {
  "originalArgs": [
    "🔍 [validateAndConvertUuid] Entrée: fieldName=\"post_id\", value=\"2a9a2244-1e0d-4062-8891-198d1cac2934\", type=string"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:47.169Z"
}
[15/07/2025 08:59:47] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 68 zones trouvées
[15/07/2025 08:59:47] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 08:59:47] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ [RegularAssignments] Attribution créée avec succès: ea8e0f4b-e5d5-4f22-a59d-200e2c776c76
  Data: {
  "originalArgs": [
    "✅ [RegularAssignments] Attribution créée avec succès:",
    "ea8e0f4b-e5d5-4f22-a59d-200e2c776c76"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:47.447Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ [RegularAssignmentV2.create] Attribution créée avec 5 jours
  Data: {
  "originalArgs": [
    "✅ [RegularAssignmentV2.create] Attribution créée avec 5 jours"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:47.447Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 269ms: 
      INSERT INTO regular_assignments (id, employ {"params":["ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","59f5df3a-33ef-425c-bfa2-adca818cf94f","2a9a2244-1e0d-4062-8891-198d1cac2934",[1,2,3,4,5],"2025-07-15",null,true]}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 269ms:",
    "\n      INSERT INTO regular_assignments (id, employ",
    {
      "params": [
        "ea8e0f4b-e5d5-4f22-a59d-200e2c776c76",
        "59f5df3a-33ef-425c-bfa2-adca818cf94f",
        "2a9a2244-1e0d-4062-8891-198d1cac2934",
        [
          1,
          2,
          3,
          4,
          5
        ],
        "2025-07-15",
        null,
        true
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:47.446Z"
}
[15/07/2025 08:59:47] [FRONTEND] [LOG] 📅 [addShift] Conversion: dayIndex 3 → dateKey 2025-07-16
[15/07/2025 08:59:47] [FRONTEND] [LOG] 🎯 [addShiftByDateKey] AJOUT AVEC VÉRIFICATION STRICTE - dateKey 2025-07-16 pour employé 59f5df3a-33ef-425c-bfa2-adca818cf94f: {"postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","text":"08:00-16:00","type":"standard","isRegular":true,"assignmentId":"52a95d9b-4110-49e2-8d19-8a09d42073a5"}
[15/07/2025 08:59:47] [FRONTEND] [LOG] 🆕 [addShiftByDateKey] Création jour 2025-07-16 pour employé 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 08:59:47] [FRONTEND] [LOG] 📋 [addShiftByDateKey] Shifts existants pour 2025-07-16: []
[15/07/2025 08:59:47] [FRONTEND] [LOG] ✅ [addShiftByDateKey] Shift ajouté avec succès. Nouveau total: [{"postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","text":"08:00-16:00","type":"standard","isRegular":true,"assignmentId":"52a95d9b-4110-49e2-8d19-8a09d42073a5"}]
[15/07/2025 08:59:47] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] 0 grips ajoutés
[15/07/2025 08:59:47] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:47.573Z"
}
[15/07/2025 08:59:47] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"52a95d9b-4110-49e2-8d19-8a09d42073a5","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-17T04:00:00.000Z","dayDateKey":"2025-07-17","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 08:59:47] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:47.580Z"
}
[15/07/2025 08:59:47] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:47.370Z"
}
[15/07/2025 08:59:47] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 08:59:47] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] Pas de dates exclues pour l'attribution 52a95d9b-4110-49e2-8d19-8a09d42073a5 {"assignmentId":"52a95d9b-4110-49e2-8d19-8a09d42073a5","hasExcludedDates":false,"isArray":false}
[15/07/2025 08:59:47] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-17
[15/07/2025 08:59:47] [FRONTEND] [LOG] 📅 [addShift] Conversion: dayIndex 4 → dateKey 2025-07-17
[15/07/2025 08:59:47] [FRONTEND] [LOG] 📝 [addShift] Ajout shift pour employé 59f5df3a-33ef-425c-bfa2-adca818cf94f, jour 4: {"postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","text":"08:00-16:00","type":"standard","isRegular":true,"assignmentId":"52a95d9b-4110-49e2-8d19-8a09d42073a5"}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:47.630Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:47.372Z"
}
[15/07/2025 08:59:47] [FRONTEND] [LOG] 🎯 [addShiftByDateKey] AJOUT AVEC VÉRIFICATION STRICTE - dateKey 2025-07-17 pour employé 59f5df3a-33ef-425c-bfa2-adca818cf94f: {"postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","text":"08:00-16:00","type":"standard","isRegular":true,"assignmentId":"52a95d9b-4110-49e2-8d19-8a09d42073a5"}
[15/07/2025 08:59:47] [FRONTEND] [LOG] 📋 [addShiftByDateKey] Shifts existants pour 2025-07-17: []
[15/07/2025 08:59:47] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:47.640Z"
}
[15/07/2025 08:59:47] [FRONTEND] [LOG] 🆕 [addShiftByDateKey] Création jour 2025-07-17 pour employé 59f5df3a-33ef-425c-bfa2-adca818cf94f
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:47.374Z"
}
[15/07/2025 08:59:47] [FRONTEND] [LOG] ✅ [addShiftByDateKey] Shift ajouté avec succès. Nouveau total: [{"postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","text":"08:00-16:00","type":"standard","isRegular":true,"assignmentId":"52a95d9b-4110-49e2-8d19-8a09d42073a5"}]
[15/07/2025 08:59:47] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 08:59:47] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"52a95d9b-4110-49e2-8d19-8a09d42073a5","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-18T04:00:00.000Z","dayDateKey":"2025-07-18","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 08:59:47] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] Pas de dates exclues pour l'attribution 52a95d9b-4110-49e2-8d19-8a09d42073a5 {"assignmentId":"52a95d9b-4110-49e2-8d19-8a09d42073a5","hasExcludedDates":false,"isArray":false}
[15/07/2025 08:59:47] [FRONTEND] [LOG] ✅ [updateAssignmentIdInShifts] 4 shifts mis à jour
[15/07/2025 08:59:47] [FRONTEND] [LOG] 🔄 [updateAssignmentIdInShifts] Mise à jour 52a95d9b-4110-49e2-8d19-8a09d42073a5 → ea8e0f4b-e5d5-4f22-a59d-200e2c776c76
[15/07/2025 08:59:47] [FRONTEND] [LOG] ✅ Attribution régulière créée et appliquée - 4 jour(s)
[15/07/2025 08:59:47] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-18
[15/07/2025 08:59:47] [FRONTEND] [LOG] 📝 [addShift] Ajout shift pour employé 59f5df3a-33ef-425c-bfa2-adca818cf94f, jour 5: {"postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","text":"08:00-16:00","type":"standard","isRegular":true,"assignmentId":"52a95d9b-4110-49e2-8d19-8a09d42073a5"}
[15/07/2025 08:59:47] [FRONTEND] [LOG] ✅ [addShiftByDateKey] Shift ajouté avec succès. Nouveau total: [{"postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","text":"08:00-16:00","type":"standard","isRegular":true,"assignmentId":"52a95d9b-4110-49e2-8d19-8a09d42073a5"}]
[15/07/2025 08:59:47] [FRONTEND] [LOG] 📋 [addShiftByDateKey] Shifts existants pour 2025-07-18: []
[15/07/2025 08:59:47] [FRONTEND] [LOG] 🆕 [addShiftByDateKey] Création jour 2025-07-18 pour employé 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 08:59:47] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"52a95d9b-4110-49e2-8d19-8a09d42073a5","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-19T04:00:00.000Z","dayDateKey":"2025-07-19","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 08:59:47] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 08:59:47] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneur available-posts-container créé
[15/07/2025 08:59:47] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 08:59:47] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[15/07/2025 08:59:47] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 52a95d9b-4110-49e2-8d19-8a09d42073a5
[15/07/2025 08:59:47] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 08:59:47] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 52a95d9b-4110-49e2-8d19-8a09d42073a5
[15/07/2025 08:59:47] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 08:59:47] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 52a95d9b-4110-49e2-8d19-8a09d42073a5
[15/07/2025 08:59:47] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 08:59:47] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 52a95d9b-4110-49e2-8d19-8a09d42073a5
[15/07/2025 08:59:47] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[15/07/2025 08:59:47] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[15/07/2025 08:59:47] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[15/07/2025 08:59:47] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 08:59:47] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 08:59:47] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 68 zones trouvées
[15/07/2025 08:59:47] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 68 zones de drop disponibles
[15/07/2025 08:59:48] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 08:59:48] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 2a9a2244-1e0d-4062-8891-198d1cac2934 configuré comme draggable
[15/07/2025 08:59:48] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 08:59:48] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste b55a363e-2261-4255-ae7d-1ebe40fa9e82 configuré comme draggable
[15/07/2025 08:59:48] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste b55a363e-2261-4255-ae7d-1ebe40fa9e82
[15/07/2025 08:59:48] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff configuré comme draggable
[15/07/2025 08:59:48] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff
[15/07/2025 08:59:48] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3 configuré comme draggable
[15/07/2025 08:59:48] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3
[15/07/2025 08:59:48] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6a79ab18-319e-4a85-805e-f80d0de9a73f configuré comme draggable
[15/07/2025 08:59:48] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 08:59:48] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste b55a363e-2261-4255-ae7d-1ebe40fa9e82
[15/07/2025 08:59:48] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste b55a363e-2261-4255-ae7d-1ebe40fa9e82 configuré comme draggable
[15/07/2025 08:59:48] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff configuré comme draggable
[15/07/2025 08:59:48] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff
[15/07/2025 08:59:48] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3 configuré comme draggable
[15/07/2025 08:59:48] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3
[15/07/2025 08:59:48] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 08:59:48] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 08:59:48] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 08:59:48] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 08:59:48] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 08:59:48] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 08:59:48] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (68 zones)
[15/07/2025 08:59:48] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[15/07/2025 08:59:48] [FRONTEND] [LOG] ✅ [SETUP] Drag & drop des employés configuré (mode simplifié)
[15/07/2025 08:59:48] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 08:59:48] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 68 zones de drop disponibles
[15/07/2025 08:59:48] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 08:59:48] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 2a9a2244-1e0d-4062-8891-198d1cac2934 configuré comme draggable
[15/07/2025 08:59:48] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 08:59:48] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste b55a363e-2261-4255-ae7d-1ebe40fa9e82 configuré comme draggable
[15/07/2025 08:59:48] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste b55a363e-2261-4255-ae7d-1ebe40fa9e82
[15/07/2025 08:59:48] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff configuré comme draggable
[15/07/2025 08:59:48] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3
[15/07/2025 08:59:48] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3 configuré comme draggable
[15/07/2025 08:59:48] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff
[15/07/2025 08:59:48] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6a79ab18-319e-4a85-805e-f80d0de9a73f configuré comme draggable
[15/07/2025 08:59:48] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 2a9a2244-1e0d-4062-8891-198d1cac2934 configuré comme draggable
[15/07/2025 08:59:48] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste b55a363e-2261-4255-ae7d-1ebe40fa9e82 configuré comme draggable
[15/07/2025 08:59:48] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 08:59:48] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6a79ab18-319e-4a85-805e-f80d0de9a73f
[15/07/2025 08:59:48] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste b55a363e-2261-4255-ae7d-1ebe40fa9e82
[15/07/2025 08:59:48] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff configuré comme draggable
[15/07/2025 08:59:48] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff
[15/07/2025 08:59:48] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3
[15/07/2025 08:59:48] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6a79ab18-319e-4a85-805e-f80d0de9a73f configuré comme draggable
[15/07/2025 08:59:48] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3 configuré comme draggable
[15/07/2025 08:59:48] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6a79ab18-319e-4a85-805e-f80d0de9a73f
[15/07/2025 08:59:48] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 08:59:48] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 08:59:48] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 08:59:48] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 08:59:48] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 08:59:48] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 08:59:48] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 08:59:48] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 08:59:48] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 08:59:48] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 08:59:48] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 08:59:48] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 08:59:48] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 08:59:48] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (68 zones)
[15/07/2025 08:59:48] [FRONTEND] [LOG] ✅ [renderAssignments] Rendu des assignations terminé
[15/07/2025 08:59:48] [FRONTEND] [LOG] 🔍 [renderAssignments] Début du rendu des assignations
[15/07/2025 08:59:53] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 59f5df3a-33ef-425c-bfa2-adca818cf94f, types: 
[15/07/2025 08:59:54] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 18fe9cd1-e8a5-44e3-90b3-a856086ce27e, types: 
[15/07/2025 08:59:54] [FRONTEND] [LOG] 🎯 [DROP] ✅ Drop reçu sur employé: Pierre Durand
[15/07/2025 08:59:54] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 08:59:54] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 08:59:54] [FRONTEND] [LOG] 🔍 [DROP] État du cache: {"cacheExists":false,"cacheContent":null,"hasEmployeeName":"MANQUANT"}
[15/07/2025 08:59:54] [FRONTEND] [LOG] 🔄 [DROP] Données récupérées du dataTransfer: {"employeeName":"","draggedEmployeeId":""}
[15/07/2025 08:59:54] [FRONTEND] [WARN] ⚠️ [DROP] Aucune donnée valide trouvée
[15/07/2025 08:59:54] [FRONTEND] [WARN] ⚠️ [DROP] Types disponibles: []
[15/07/2025 08:59:56] [FRONTEND] [LOG] 🎯 [GRIP] Début drag grip pour attribution ea8e0f4b-e5d5-4f22-a59d-200e2c776c76
[15/07/2025 08:59:56] [FRONTEND] [LOG] 🎯 [GRIP] regularAssignmentId stocké: ea8e0f4b-e5d5-4f22-a59d-200e2c776c76
[15/07/2025 08:59:56] [FRONTEND] [LOG] 🎯 [GRIP] DataTransfer types après: ["regularassignmentid","text/plain"]
[15/07/2025 08:59:56] [FRONTEND] [LOG] 🎯 [GRIP] DataTransfer types avant: []
[15/07/2025 08:59:56] [FRONTEND] [LOG] 🎨 [highlightEmployeeDropZones] Ajout surbrillance sur 68 zones
[15/07/2025 08:59:56] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 59f5df3a-33ef-425c-bfa2-adca818cf94f, types: regularassignmentid, text/plain
[15/07/2025 08:59:56] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 18fe9cd1-e8a5-44e3-90b3-a856086ce27e, types: regularassignmentid, text/plain
[15/07/2025 08:59:56] [FRONTEND] [LOG] 🎨 [highlightEmployeeDropZones] Zones trouvées: [{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30"},{"element":"BUTTON","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors"},{"element":"BUTTON","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors"},{"element":"BUTTON","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30"},{"element":"BUTTON","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors"},{"element":"BUTTON","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors"},{"element":"BUTTON","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30"},{"element":"BUTTON","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors"},{"element":"BUTTON","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors"},{"element":"BUTTON","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30"},{"element":"BUTTON","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors"},{"element":"BUTTON","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors"},{"element":"BUTTON","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30"},{"element":"BUTTON","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors"},{"element":"BUTTON","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors"},{"element":"BUTTON","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-200/60 hover:bg-sky-300/70 text-sky-800 border border-sky-300/60 has-grip"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-200/60 hover:bg-sky-300/70 text-sky-800 border border-sky-300/60"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60"}]
[15/07/2025 08:59:57] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 18fe9cd1-e8a5-44e3-90b3-a856086ce27e, types: regularassignmentid, text/plain
[15/07/2025 08:59:57] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 08:59:57] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 08:59:57] [FRONTEND] [LOG] 🔄 [DROP] Attribution régulière: ea8e0f4b-e5d5-4f22-a59d-200e2c776c76 → 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 08:59:57] [FRONTEND] [LOG] 🎯 [DROP] ✅ Drop reçu sur employé: Pierre Durand
[15/07/2025 08:59:57] [FRONTEND] [LOG] 🎯 [handleRegularAssignmentDrop] Drop assignment ea8e0f4b-e5d5-4f22-a59d-200e2c776c76 sur employé Pierre Durand
[15/07/2025 08:59:57] [FRONTEND] [LOG] 🎯 [showRegularAssignmentConfirmationMenu] Affichage menu pour: {"assignment":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","from":"Sophie Leblanc","to":"Pierre Durand","post":"Poste Matin","referenceDate":null,"minDate":"2025-07-15","todayKey":"2025-07-15"}
[15/07/2025 08:59:57] [FRONTEND] [LOG] 🎨 [highlightEmployeeDropZones] Suppression surbrillance sur 68 zones
[15/07/2025 08:59:57] [FRONTEND] [LOG] 🎯 [GRIP] Fin drag grip pour attribution ea8e0f4b-e5d5-4f22-a59d-200e2c776c76
[15/07/2025 08:59:57] [FRONTEND] [LOG] 🎨 [highlightEmployeeDropZones] Zones trouvées: [{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200 regular-assignment-drop-zone"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30 regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors regular-assignment-drop-zone"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200 regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30 regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200 regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30 regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30 regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30 regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-200/60 hover:bg-sky-300/70 text-sky-800 border border-sky-300/60 has-grip regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-200/60 hover:bg-sky-300/70 text-sky-800 border border-sky-300/60 regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 regular-assignment-drop-zone"}]
[15/07/2025 08:59:58] [FRONTEND] [LOG] 🔄 [handlePermanentRegularAssignmentChange] Changement permanent ea8e0f4b-e5d5-4f22-a59d-200e2c776c76 → 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 08:59:58] [FRONTEND] [LOG] 🔄 [handlePermanentRegularAssignmentChange] Désactiver l'original: false
[15/07/2025 08:59:58] [FRONTEND] [LOG] 📅 [handlePermanentRegularAssignmentChange] Date minimale: 2025-07-14
[15/07/2025 08:59:58] [FRONTEND] [LOG] 🔄 [reassignRegularAssignmentFromDate] ea8e0f4b-e5d5-4f22-a59d-200e2c776c76 → 18fe9cd1-e8a5-44e3-90b3-a856086ce27e à partir de 2025-07-14
[15/07/2025 08:59:58] [FRONTEND] [LOG] 🔄 [reassignRegularAssignmentFromDate] Désactiver l'original: false
[15/07/2025 08:59:58] [BACKEND] [INFO] ✏️ [RegularAssignments] Mise à jour attribution ea8e0f4b-e5d5-4f22-a59d-200e2c776c76
[15/07/2025 08:59:58] [FRONTEND] [LOG] 🔄 [reassignRegularAssignmentFromDate] Fork créé, historique préservé
[15/07/2025 08:59:58] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:58.447Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:58.460Z"
}
[15/07/2025 08:59:58] [FRONTEND] [LOG] ✅ [updateShiftsForDateBasedReassignment] 4 shifts mis à jour
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:58.462Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:58.460Z"
}
[15/07/2025 08:59:58] [FRONTEND] [LOG] 🔄 [updateShiftsForDateBasedReassignment] Mise à jour des shifts à partir de 2025-07-14
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✏️ [RegularAssignments] Mise à jour attribution ea8e0f4b-e5d5-4f22-a59d-200e2c776c76
  Data: {
  "originalArgs": [
    "✏️ [RegularAssignments] Mise à jour attribution ea8e0f4b-e5d5-4f22-a59d-200e2c776c76"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:58.320Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔍 [validateAndConvertUuid] Entrée: fieldName="post_id", value="2a9a2244-1e0d-4062-8891-198d1cac2934", type=string
  Data: {
  "originalArgs": [
    "🔍 [validateAndConvertUuid] Entrée: fieldName=\"post_id\", value=\"2a9a2244-1e0d-4062-8891-198d1cac2934\", type=string"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:58.324Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      UPDATE regular_assignments 
      SET employee_id = $1, post_id = $2, days_of_week = $3, start_date = $4, 
          end_date = $5, is_active = $6, excluded_dates = $7, updated_at = CURRENT_TIMESTAMP
      WHERE id = $8
      RETURNING *
     {"params":["59f5df3a-33ef-425c-bfa2-adca818cf94f","2a9a2244-1e0d-4062-8891-198d1cac2934",[1,2,3,4,5],"2025-07-15","2025-07-13",true,[],"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76"]}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      UPDATE regular_assignments \n      SET employee_id = $1, post_id = $2, days_of_week = $3, start_date = $4, \n          end_date = $5, is_active = $6, excluded_dates = $7, updated_at = CURRENT_TIMESTAMP\n      WHERE id = $8\n      RETURNING *\n    ",
    {
      "params": [
        "59f5df3a-33ef-425c-bfa2-adca818cf94f",
        "2a9a2244-1e0d-4062-8891-198d1cac2934",
        [
          1,
          2,
          3,
          4,
          5
        ],
        "2025-07-15",
        "2025-07-13",
        true,
        [],
        "ea8e0f4b-e5d5-4f22-a59d-200e2c776c76"
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:58.327Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ [validateAndConvertUuid] UUID valide détecté pour post_id: 2a9a2244-1e0d-4062-8891-198d1cac2934
  Data: {
  "originalArgs": [
    "✅ [validateAndConvertUuid] UUID valide détecté pour post_id: 2a9a2244-1e0d-4062-8891-198d1cac2934"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:58.324Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📝 [RegularAssignmentV2.update] Données reçues: {"employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","isLimited":false,"startDate":"2025-07-15","endDate":"2025-07-13","selectedDays":[1,2,3,4,5],"id":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","isActive":true}
  Data: {
  "originalArgs": [
    "📝 [RegularAssignmentV2.update] Données reçues:",
    {
      "employeeId": "59f5df3a-33ef-425c-bfa2-adca818cf94f",
      "postId": "2a9a2244-1e0d-4062-8891-198d1cac2934",
      "isLimited": false,
      "startDate": "2025-07-15",
      "endDate": "2025-07-13",
      "selectedDays": [
        1,
        2,
        3,
        4,
        5
      ],
      "id": "ea8e0f4b-e5d5-4f22-a59d-200e2c776c76",
      "isActive": true
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:58.323Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ [RegularAssignments] Attribution mise à jour: ea8e0f4b-e5d5-4f22-a59d-200e2c776c76
  Data: {
  "originalArgs": [
    "✅ [RegularAssignments] Attribution mise à jour:",
    "ea8e0f4b-e5d5-4f22-a59d-200e2c776c76"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:58.538Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 208ms: 
      UPDATE regular_assignments 
      SET emplo {"params":["59f5df3a-33ef-425c-bfa2-adca818cf94f","2a9a2244-1e0d-4062-8891-198d1cac2934",[1,2,3,4,5],"2025-07-15","2025-07-13",true,[],"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76"]}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 208ms:",
    "\n      UPDATE regular_assignments \n      SET emplo",
    {
      "params": [
        "59f5df3a-33ef-425c-bfa2-adca818cf94f",
        "2a9a2244-1e0d-4062-8891-198d1cac2934",
        [
          1,
          2,
          3,
          4,
          5
        ],
        "2025-07-15",
        "2025-07-13",
        true,
        [],
        "ea8e0f4b-e5d5-4f22-a59d-200e2c776c76"
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:58.535Z"
}
[15/07/2025 08:59:58] [BACKEND] [INFO] ✅ Requête exécutée en 208ms: 
      UPDATE regular_assignments 
      SET emplo {"params":["59f5df3a-33ef-425c-bfa2-adca818cf94f","2a9a2244-1e0d-4062-8891-198d1cac2934",[1,2,3,4,5],"2025-07-15","2025-07-13",true,[],"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76"]}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ➕ [RegularAssignments] Création d'une attribution individuelle
  Data: {
  "originalArgs": [
    "➕ [RegularAssignments] Création d'une attribution individuelle"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:58.587Z"
}
[15/07/2025 08:59:58] [FRONTEND] [LOG] ✅ [reassignRegularAssignmentFromDate] Attribution existante mise à jour avec date de fin
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔍 [validateAndConvertUuid] Entrée: fieldName="post_id", value="2a9a2244-1e0d-4062-8891-198d1cac2934", type=string
  Data: {
  "originalArgs": [
    "🔍 [validateAndConvertUuid] Entrée: fieldName=\"post_id\", value=\"2a9a2244-1e0d-4062-8891-198d1cac2934\", type=string"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:58.591Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📤 [RegularAssignments] Données reçues: {
  "employeeId": "18fe9cd1-e8a5-44e3-90b3-a856086ce27e",
  "postId": "2a9a2244-1e0d-4062-8891-198d1cac2934",
  "isLimited": false,
  "startDate": "2025-07-14",
  "endDate": null,
  "selectedDays": [
    1,
    2,
    3,
    4,
    5
  ],
  "isActive": true,
  "id": "553dc002-3c94-482a-a9bf-364868a8b717"
}
  Data: {
  "originalArgs": [
    "📤 [RegularAssignments] Données reçues:",
    "{\n  \"employeeId\": \"18fe9cd1-e8a5-44e3-90b3-a856086ce27e\",\n  \"postId\": \"2a9a2244-1e0d-4062-8891-198d1cac2934\",\n  \"isLimited\": false,\n  \"startDate\": \"2025-07-14\",\n  \"endDate\": null,\n  \"selectedDays\": [\n    1,\n    2,\n    3,\n    4,\n    5\n  ],\n  \"isActive\": true,\n  \"id\": \"553dc002-3c94-482a-a9bf-364868a8b717\"\n}"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:58.588Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔍 [RegularAssignmentV2.create] Post ID original: 2a9a2244-1e0d-4062-8891-198d1cac2934 Post ID validé: 2a9a2244-1e0d-4062-8891-198d1cac2934
  Data: {
  "originalArgs": [
    "🔍 [RegularAssignmentV2.create] Post ID original:",
    "2a9a2244-1e0d-4062-8891-198d1cac2934",
    "Post ID validé:",
    "2a9a2244-1e0d-4062-8891-198d1cac2934"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:58.593Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ [validateAndConvertUuid] UUID valide détecté pour post_id: 2a9a2244-1e0d-4062-8891-198d1cac2934
  Data: {
  "originalArgs": [
    "✅ [validateAndConvertUuid] UUID valide détecté pour post_id: 2a9a2244-1e0d-4062-8891-198d1cac2934"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:58.591Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📝 [RegularAssignmentV2.create] Données reçues: {"employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","isLimited":false,"startDate":"2025-07-14","endDate":null,"selectedDays":[1,2,3,4,5],"isActive":true,"id":"553dc002-3c94-482a-a9bf-364868a8b717"}
  Data: {
  "originalArgs": [
    "📝 [RegularAssignmentV2.create] Données reçues:",
    {
      "employeeId": "18fe9cd1-e8a5-44e3-90b3-a856086ce27e",
      "postId": "2a9a2244-1e0d-4062-8891-198d1cac2934",
      "isLimited": false,
      "startDate": "2025-07-14",
      "endDate": null,
      "selectedDays": [
        1,
        2,
        3,
        4,
        5
      ],
      "isActive": true,
      "id": "553dc002-3c94-482a-a9bf-364868a8b717"
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:58.590Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      INSERT INTO regular_assignments (id, employee_id, post_id, days_of_week, start_date, end_date, is_active)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
     {"params":["85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","18fe9cd1-e8a5-44e3-90b3-a856086ce27e","2a9a2244-1e0d-4062-8891-198d1cac2934",[1,2,3,4,5],"2025-07-14",null,true]}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      INSERT INTO regular_assignments (id, employee_id, post_id, days_of_week, start_date, end_date, is_active)\n      VALUES ($1, $2, $3, $4, $5, $6, $7)\n      RETURNING *\n    ",
    {
      "params": [
        "85d637c2-6ed3-447b-8a37-a0ba9fcf89b6",
        "18fe9cd1-e8a5-44e3-90b3-a856086ce27e",
        "2a9a2244-1e0d-4062-8891-198d1cac2934",
        [
          1,
          2,
          3,
          4,
          5
        ],
        "2025-07-14",
        null,
        true
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:58.594Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔍 [RegularAssignmentV2.create] Création attribution unique pour jours: [1, 2, 3, 4, 5]
  Data: {
  "originalArgs": [
    "🔍 [RegularAssignmentV2.create] Création attribution unique pour jours: [1, 2, 3, 4, 5]"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:58.593Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:58.482Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:58.483Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:58.637Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:58.641Z"
}
[15/07/2025 08:59:58] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:58.481Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:58.471Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:58.654Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:58.652Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ [RegularAssignments] Attribution créée avec succès: 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
  Data: {
  "originalArgs": [
    "✅ [RegularAssignments] Attribution créée avec succès:",
    "85d637c2-6ed3-447b-8a37-a0ba9fcf89b6"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:58.659Z"
}
[15/07/2025 08:59:58] [FRONTEND] [LOG] ✅ [reassignRegularAssignmentFromDate] Nouvelle attribution créée avec succès
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 63ms: 
      INSERT INTO regular_assignments (id, employ {"params":["85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","18fe9cd1-e8a5-44e3-90b3-a856086ce27e","2a9a2244-1e0d-4062-8891-198d1cac2934",[1,2,3,4,5],"2025-07-14",null,true]}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 63ms:",
    "\n      INSERT INTO regular_assignments (id, employ",
    {
      "params": [
        "85d637c2-6ed3-447b-8a37-a0ba9fcf89b6",
        "18fe9cd1-e8a5-44e3-90b3-a856086ce27e",
        "2a9a2244-1e0d-4062-8891-198d1cac2934",
        [
          1,
          2,
          3,
          4,
          5
        ],
        "2025-07-14",
        null,
        true
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:58.657Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:58.662Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ [RegularAssignmentV2.create] Attribution créée avec 5 jours
  Data: {
  "originalArgs": [
    "✅ [RegularAssignmentV2.create] Attribution créée avec 5 jours"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:58.658Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:58.485Z"
}
[15/07/2025 08:59:58] [FRONTEND] [LOG] 📂 [TeamCalendarApp] Chargement de l'état...
[15/07/2025 08:59:58] [FRONTEND] [LOG] 🔄 [reassignRegularAssignmentFromDate] Rechargement complet des données...
[15/07/2025 08:59:58] [FRONTEND] [LOG] 🌐 [TeamCalendarApp] Chargement depuis l'API...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT e.*, et.name as template_name 
      FROM employees e 
      LEFT JOIN employee_templates et ON e.template_id = et.id 
      ORDER BY e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT e.*, et.name as template_name \n      FROM employees e \n      LEFT JOIN employee_templates et ON e.template_id = et.id \n      ORDER BY e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:58.798Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 70ms: 
      SELECT e.*, et.name as template_name 
      {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 70ms:",
    "\n      SELECT e.*, et.name as template_name \n     ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:58.871Z"
}
[15/07/2025 08:59:58] [BACKEND] [INFO] ✅ Requête exécutée en 70ms: 
      SELECT e.*, et.name as template_name 
      {}
[15/07/2025 08:59:59] [FRONTEND] [LOG] 👥 [TeamCalendarApp] 5 employés chargés
[15/07/2025 08:59:59] [FRONTEND] [LOG] 📦 [loadState] Chargement de tous les shifts depuis la base de données...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT s.*, e.name as employee_name, sp.label as post_label
      FROM shifts s
      LEFT JOIN employees e ON s.employee_id = e.id
      LEFT JOIN standard_posts sp ON s.post_id = sp.id
      ORDER BY s.date_key, e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT s.*, e.name as employee_name, sp.label as post_label\n      FROM shifts s\n      LEFT JOIN employees e ON s.employee_id = e.id\n      LEFT JOIN standard_posts sp ON s.post_id = sp.id\n      ORDER BY s.date_key, e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:58.977Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 48ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 48ms:",
    "\n      SELECT s.*, e.name as employee_name, sp.lab",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:59.027Z"
}
[15/07/2025 08:59:59] [BACKEND] [INFO] ✅ Requête exécutée en 48ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
[15/07/2025 08:59:59] [FRONTEND] [LOG] 📅 [loadState] 0 shifts chargés depuis la base de données
[15/07/2025 08:59:59] [FRONTEND] [LOG] ✅ [loadState] Shifts organisés dans this.data.schedule
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:59.097Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 46ms: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 46ms:",
    "SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:59.144Z"
}
[15/07/2025 08:59:59] [BACKEND] [INFO] ✅ Requête exécutée en 46ms: SELECT * FROM standard_posts ORDER BY label {}
[15/07/2025 08:59:59] [FRONTEND] [LOG] 🏢 [TeamCalendarApp] 5 postes standards chargés
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:59.191Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 42ms: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 42ms:",
    "SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:59.233Z"
}
[15/07/2025 08:59:59] [BACKEND] [INFO] ✅ Requête exécutée en 42ms: SELECT * FROM app_settings ORDER BY setting_key {}
[15/07/2025 08:59:59] [FRONTEND] [LOG] ⚙️ [loadState] Paramètres chargés depuis API: {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT 
        ra.id,
        ra.employee_id,
        ra.post_id,
        ra.days_of_week,
        ra.start_date,
        ra.end_date,
        ra.is_active,
        ra.excluded_dates,
        e.name as employee_name,
        sp.label as post_label,
        sp.hours as post_hours,
        sp.type as post_type,
        array_length(ra.days_of_week, 1) as days_count
      FROM regular_assignments ra
      LEFT JOIN employees e ON ra.employee_id = e.id
      LEFT JOIN standard_posts sp ON ra.post_id = sp.id
      WHERE ra.is_active = true
      ORDER BY e.name, sp.label, ra.start_date
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT \n        ra.id,\n        ra.employee_id,\n        ra.post_id,\n        ra.days_of_week,\n        ra.start_date,\n        ra.end_date,\n        ra.is_active,\n        ra.excluded_dates,\n        e.name as employee_name,\n        sp.label as post_label,\n        sp.hours as post_hours,\n        sp.type as post_type,\n        array_length(ra.days_of_week, 1) as days_count\n      FROM regular_assignments ra\n      LEFT JOIN employees e ON ra.employee_id = e.id\n      LEFT JOIN standard_posts sp ON ra.post_id = sp.id\n      WHERE ra.is_active = true\n      ORDER BY e.name, sp.label, ra.start_date\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:59.263Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 47ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 47ms:",
    "\n      SELECT \n        ra.id,\n        ra.employee_",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T12:59:59.310Z"
}
[15/07/2025 08:59:59] [BACKEND] [INFO] ✅ Requête exécutée en 47ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
[15/07/2025 08:59:59] [FRONTEND] [LOG] 📋 [loadState] 2/2 assignations régulières valides chargées et normalisées
[15/07/2025 08:59:59] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","employee_id":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","post_id":"2a9a2244-1e0d-4062-8891-198d1cac2934","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-14T04:00:00.000Z","end_date":null,"excluded_dates":[]}
[15/07/2025 08:59:59] [FRONTEND] [WARN]    Start: 2025-07-15 > End: 2025-07-13
[15/07/2025 08:59:59] [FRONTEND] [LOG] 🔧 [fixInvalidAssignmentDates] Vérification des dates invalides...
[15/07/2025 08:59:59] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","selectedDays":[1,2,3,4,5],"isLimited":false,"startDate":"2025-07-14","endDate":null,"excludedDates":[]}
[15/07/2025 08:59:59] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","employee_id":"59f5df3a-33ef-425c-bfa2-adca818cf94f","post_id":"2a9a2244-1e0d-4062-8891-198d1cac2934","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-15T04:00:00.000Z","end_date":"2025-07-13T04:00:00.000Z","excluded_dates":[]}
[15/07/2025 08:59:59] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-15","endDate":"2025-07-13","excludedDates":[]}
[15/07/2025 09:00:00] [FRONTEND] [WARN] ⚠️ [fixInvalidAssignmentDates] Date invalide détectée pour l'assignation ea8e0f4b-e5d5-4f22-a59d-200e2c776c76:
[15/07/2025 09:00:00] [FRONTEND] [LOG] ✅ [fixInvalidAssignmentDates] Dates corrigées pour ea8e0f4b-e5d5-4f22-a59d-200e2c776c76:
[15/07/2025 09:00:00] [FRONTEND] [LOG]    Nouveau Start: 2025-07-13, Nouveau End: 2025-07-15
[15/07/2025 09:00:00] [FRONTEND] [LOG] ✅ [fixInvalidAssignmentDates] 1 assignation(s) corrigée(s)
[15/07/2025 09:00:00] [FRONTEND] [LOG] 🧹 [cleanupDuplicateRegularShifts] Nettoyage des doublons de shifts réguliers...
[15/07/2025 09:00:00] [FRONTEND] [LOG] 📋 [loadState] Assignations régulières chargées (application différée)
[15/07/2025 09:00:00] [FRONTEND] [LOG] ✅ [cleanupDuplicateRegularShifts] Aucun doublon détecté
[15/07/2025 09:00:00] [FRONTEND] [LOG] 🧹 [cleanupInvalidAssignmentIds] Nettoyage des assignment_id invalides...
[15/07/2025 09:00:00] [FRONTEND] [LOG] ✅ [cleanupInvalidAssignmentIds] Aucun assignment_id invalide détecté
[15/07/2025 09:00:00] [FRONTEND] [LOG] 🧹 [reassignRegularAssignmentFromDate] Nettoyage complet des shifts réguliers...
[15/07/2025 09:00:00] [FRONTEND] [LOG] ✅ [loadState] État chargé depuis l'API avec succès
[15/07/2025 09:00:00] [FRONTEND] [LOG] 🗑️ [reassignRegularAssignmentFromDate] Invalidation des caches...
[15/07/2025 09:00:00] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:00:00] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:00:00] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:00:00] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6a79ab18-319e-4a85-805e-f80d0de9a73f
[15/07/2025 09:00:00] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff
[15/07/2025 09:00:00] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 09:00:00] [FRONTEND] [LOG] 🔄 [reassignRegularAssignmentFromDate] Application des attributions avec la nouvelle logique...
[15/07/2025 09:00:00] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] Nettoyage des shifts réguliers existants...
[15/07/2025 09:00:00] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6 a une intersection avec cette semaine (2025-07-14 → ∞) vs (2025-07-13 → 2025-07-19)
[15/07/2025 09:00:00] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Application pour semaine 2025-W28
[15/07/2025 09:00:00] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] 0 shifts réguliers supprimés
[15/07/2025 09:00:00] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution ea8e0f4b-e5d5-4f22-a59d-200e2c776c76 a une intersection avec cette semaine (2025-07-13 → 2025-07-15) vs (2025-07-13 → 2025-07-19)
[15/07/2025 09:00:00] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 2 attributions actives sur 2 total
[15/07/2025 09:00:00] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6: [{"dateKey":"2025-07-13","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-14","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-15","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-16","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-17","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-07-18","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-07-19","isWeekend":true,"dayOfWeek":6}]
[15/07/2025 09:00:00] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-13 (0)
[15/07/2025 09:00:00] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","selectedDays":[1,2,3,4,5],"startDate":"2025-07-14","endDate":null,"excludedDates":[]}
[15/07/2025 09:00:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-13T04:00:00.000Z","dayDateKey":"2025-07-13","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5]}
[15/07/2025 09:00:00] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-13 ignoré pour attribution 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:00] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:00:00] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-14 (1)
[15/07/2025 09:00:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-14T04:00:00.000Z","dayDateKey":"2025-07-14","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5]}
[15/07/2025 09:00:00] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[15/07/2025 09:00:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-14: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","dayDateStr":"2025-07-14","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:00] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-14
[15/07/2025 09:00:00] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-15 (2)
[15/07/2025 09:00:00] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-14 validé, shifts existants: 0
[15/07/2025 09:00:00] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-14 pour Pierre Durand
[15/07/2025 09:00:00] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[15/07/2025 09:00:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-15T04:00:00.000Z","dayDateKey":"2025-07-15","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5]}
[15/07/2025 09:00:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-15: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","dayDateStr":"2025-07-15","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:00] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-15
[15/07/2025 09:00:00] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-15 validé, shifts existants: 0
[15/07/2025 09:00:00] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-16 (3)
[15/07/2025 09:00:00] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-15 pour Pierre Durand
[15/07/2025 09:00:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-16T04:00:00.000Z","dayDateKey":"2025-07-16","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5]}
[15/07/2025 09:00:00] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[15/07/2025 09:00:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-16: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","dayDateStr":"2025-07-16","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:00] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-16
[15/07/2025 09:00:00] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-16 validé, shifts existants: 0
[15/07/2025 09:00:00] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-16 pour Pierre Durand
[15/07/2025 09:00:00] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-17 (4)
[15/07/2025 09:00:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-17: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","dayDateStr":"2025-07-17","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-17T04:00:00.000Z","dayDateKey":"2025-07-17","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5]}
[15/07/2025 09:00:00] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-17
[15/07/2025 09:00:00] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[15/07/2025 09:00:00] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-17 validé, shifts existants: 0
[15/07/2025 09:00:00] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-07-18 (5)
[15/07/2025 09:00:00] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-17 pour Pierre Durand
[15/07/2025 09:00:00] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[15/07/2025 09:00:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-18T04:00:00.000Z","dayDateKey":"2025-07-18","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5]}
[15/07/2025 09:00:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-18: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","dayDateStr":"2025-07-18","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:00] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-18
[15/07/2025 09:00:00] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-18 validé, shifts existants: 0
[15/07/2025 09:00:00] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-18 pour Pierre Durand
[15/07/2025 09:00:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-19T04:00:00.000Z","dayDateKey":"2025-07-19","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5]}
[15/07/2025 09:00:00] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-07-19 (6)
[15/07/2025 09:00:00] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:00:00] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-19 ignoré pour attribution 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:00] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","selectedDays":[1,2,3,4,5],"startDate":"2025-07-13","endDate":"2025-07-15","excludedDates":[]}
[15/07/2025 09:00:00] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution ea8e0f4b-e5d5-4f22-a59d-200e2c776c76: [{"dateKey":"2025-07-13","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-14","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-15","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-16","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-17","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-07-18","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-07-19","isWeekend":true,"dayOfWeek":6}]
[15/07/2025 09:00:00] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-13 (0)
[15/07/2025 09:00:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-13T04:00:00.000Z","dayDateKey":"2025-07-13","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5]}
[15/07/2025 09:00:00] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:00:00] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-13 ignoré pour attribution ea8e0f4b-e5d5-4f22-a59d-200e2c776c76
[15/07/2025 09:00:00] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-14 (1)
[15/07/2025 09:00:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-14T04:00:00.000Z","dayDateKey":"2025-07-14","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5]}
[15/07/2025 09:00:00] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[15/07/2025 09:00:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-14: {"assignmentId":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","dayDateStr":"2025-07-14","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:00] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-14
[15/07/2025 09:00:00] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-14 validé, shifts existants: 0
[15/07/2025 09:00:00] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-15 (2)
[15/07/2025 09:00:00] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-14 pour Sophie Leblanc
[15/07/2025 09:00:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-15T04:00:00.000Z","dayDateKey":"2025-07-15","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5]}
[15/07/2025 09:00:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-15: {"assignmentId":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","dayDateStr":"2025-07-15","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:00] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[15/07/2025 09:00:00] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-15
[15/07/2025 09:00:00] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-15 validé, shifts existants: 0
[15/07/2025 09:00:00] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-15 pour Sophie Leblanc
[15/07/2025 09:00:00] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-16 (3)
[15/07/2025 09:00:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-16T04:00:00.000Z","dayDateKey":"2025-07-16","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5]}
[15/07/2025 09:00:00] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[15/07/2025 09:00:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-16: {"assignmentId":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","dayDateStr":"2025-07-16","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:00] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-16 après endDate 2025-07-15 (original: 2025-07-15) (attribution ea8e0f4b-e5d5-4f22-a59d-200e2c776c76)
[15/07/2025 09:00:00] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-16 ignoré pour attribution ea8e0f4b-e5d5-4f22-a59d-200e2c776c76
[15/07/2025 09:00:00] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-17 (4)
[15/07/2025 09:00:00] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[15/07/2025 09:00:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-17T04:00:00.000Z","dayDateKey":"2025-07-17","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5]}
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-17: {"assignmentId":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","dayDateStr":"2025-07-17","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:01] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-17 après endDate 2025-07-15 (original: 2025-07-15) (attribution ea8e0f4b-e5d5-4f22-a59d-200e2c776c76)
[15/07/2025 09:00:01] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-17 ignoré pour attribution ea8e0f4b-e5d5-4f22-a59d-200e2c776c76
[15/07/2025 09:00:01] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-07-18 (5)
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-18T04:00:00.000Z","dayDateKey":"2025-07-18","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5]}
[15/07/2025 09:00:01] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-18: {"assignmentId":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","dayDateStr":"2025-07-18","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:01] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-18 ignoré pour attribution ea8e0f4b-e5d5-4f22-a59d-200e2c776c76
[15/07/2025 09:00:01] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-18 après endDate 2025-07-15 (original: 2025-07-15) (attribution ea8e0f4b-e5d5-4f22-a59d-200e2c776c76)
[15/07/2025 09:00:01] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-07-19 (6)
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-19T04:00:00.000Z","dayDateKey":"2025-07-19","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5]}
[15/07/2025 09:00:01] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:00:01] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-19 ignoré pour attribution ea8e0f4b-e5d5-4f22-a59d-200e2c776c76
[15/07/2025 09:00:01] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 7 attributions appliquées pour la semaine 2025-W28
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Rendu demandé car 7 shifts créés
[15/07/2025 09:00:01] [FRONTEND] [LOG] ✅ [reassignRegularAssignmentFromDate] Attribution divisée avec succès
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🔄 [reassignRegularAssignmentFromDate] Rendu final...
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Matin: [1, 2, 3, 4, 5]
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[15/07/2025 09:00:01] [FRONTEND] [LOG] 📝 [logModification] Enregistrement: {"id":"a664a21d-b2bc-4e56-aff3-d78c292b2636","type":"regular-assignment","title":"Attribution régulière modifiée (permanent)","description":"Poste Matin transféré de Sophie Leblanc vers Pierre Durand à partir du 13/07/2025","timestamp":"2025-07-15T12:59:59.431Z","employeeName":"Pierre Durand","postLabel":"Poste Matin","date":"2025-07-14","assignmentId":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","sourceEmployeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","targetEmployeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e"}
[15/07/2025 09:00:01] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[15/07/2025 09:00:01] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:01] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:01] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:01] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:01] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:01] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:01] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière ea8e0f4b-e5d5-4f22-a59d-200e2c776c76
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière ea8e0f4b-e5d5-4f22-a59d-200e2c776c76
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[15/07/2025 09:00:01] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 09:00:01] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 09:00:01] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 74 zones trouvées
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 74 zones de drop disponibles
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 2a9a2244-1e0d-4062-8891-198d1cac2934 configuré comme draggable
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste b55a363e-2261-4255-ae7d-1ebe40fa9e82 configuré comme draggable
[15/07/2025 09:00:01] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Nuit: [1, 2, 3, 4, 5]
[15/07/2025 09:00:01] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste b55a363e-2261-4255-ae7d-1ebe40fa9e82
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Soir: [1, 2, 3, 4, 5]
[15/07/2025 09:00:01] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff configuré comme draggable
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE1: [0, 6]
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3 configuré comme draggable
[15/07/2025 09:00:01] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6a79ab18-319e-4a85-805e-f80d0de9a73f configuré comme draggable
[15/07/2025 09:00:01] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6a79ab18-319e-4a85-805e-f80d0de9a73f
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE2: [0, 6]
[15/07/2025 09:00:01] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 2a9a2244-1e0d-4062-8891-198d1cac2934 configuré comme draggable
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste b55a363e-2261-4255-ae7d-1ebe40fa9e82 configuré comme draggable
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff configuré comme draggable
[15/07/2025 09:00:01] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste b55a363e-2261-4255-ae7d-1ebe40fa9e82
[15/07/2025 09:00:01] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3 configuré comme draggable
[15/07/2025 09:00:01] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6a79ab18-319e-4a85-805e-f80d0de9a73f configuré comme draggable
[15/07/2025 09:00:01] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6a79ab18-319e-4a85-805e-f80d0de9a73f
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 09:00:01] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:00:01] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:00:01] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 09:00:01] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (74 zones)
[15/07/2025 09:00:01] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[15/07/2025 09:00:01] [FRONTEND] [WARN] ⚠️ [SETUP] Erreur lors de la destruction: Error: can't access property "Sortable1752584344465", el is null
destroy@http://localhost:5173/node_modules/.vite/deps/sortablejs.js?v=189de042:1812:5
setupDragAndDrop@http://localhost:5173/src/teamCalendarApp.ts:5808:33
attachAllEventListeners@http://localhost:5173/src/teamCalendarApp.ts:5358:10
actualRender@http://localhost:5173/src/teamCalendarApp.ts:2466:10
render/this._debouncedRender<@http://localhost:5173/src/teamCalendarApp.ts:2451:12
setTimeout handler*render@http://localhost:5173/src/teamCalendarApp.ts:2450:29
reassignRegularAssignmentFromDate@http://localhost:5173/src/teamCalendarApp.ts:11487:12
async*handlePermanentRegularAssignmentChange@http://localhost:5173/src/teamCalendarApp.ts:10885:18
showRegularAssignmentConfirmationMenu/<@http://localhost:5173/src/teamCalendarApp.ts:10850:12
EventListener.handleEvent*showRegularAssignmentConfirmationMenu@http://localhost:5173/src/teamCalendarApp.ts:10844:35
handleRegularAssignmentDrop@http://localhost:5173/src/teamCalendarApp.ts:10432:10
dropHandler@http://localhost:5173/src/teamCalendarApp.ts:6170:16

[15/07/2025 09:00:01] [FRONTEND] [LOG] ✅ [SETUP] Drag & drop des employés configuré (mode simplifié)
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 09:00:01] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 74 zones trouvées
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 74 zones de drop disponibles
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 2a9a2244-1e0d-4062-8891-198d1cac2934 configuré comme draggable
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste b55a363e-2261-4255-ae7d-1ebe40fa9e82 configuré comme draggable
[15/07/2025 09:00:01] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 09:00:01] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste b55a363e-2261-4255-ae7d-1ebe40fa9e82
[15/07/2025 09:00:01] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff configuré comme draggable
[15/07/2025 09:00:01] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3 configuré comme draggable
[15/07/2025 09:00:01] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6a79ab18-319e-4a85-805e-f80d0de9a73f
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 2a9a2244-1e0d-4062-8891-198d1cac2934 configuré comme draggable
[15/07/2025 09:00:01] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6a79ab18-319e-4a85-805e-f80d0de9a73f configuré comme draggable
[15/07/2025 09:00:02] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 09:00:02] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste b55a363e-2261-4255-ae7d-1ebe40fa9e82 configuré comme draggable
[15/07/2025 09:00:02] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste b55a363e-2261-4255-ae7d-1ebe40fa9e82
[15/07/2025 09:00:02] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff configuré comme draggable
[15/07/2025 09:00:02] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3 configuré comme draggable
[15/07/2025 09:00:02] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3
[15/07/2025 09:00:02] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6a79ab18-319e-4a85-805e-f80d0de9a73f configuré comme draggable
[15/07/2025 09:00:02] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 09:00:02] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 09:00:02] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:00:02] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:00:02] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:00:02] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:00:02] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:00:02] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:00:02] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:00:02] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:00:02] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:00:02] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:00:02] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 09:00:02] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (74 zones)
[15/07/2025 09:00:02] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] 0 grips ajoutés
[15/07/2025 09:00:02] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 09:00:04] [FRONTEND] [LOG] ➡️ Clic sur suivant
[15/07/2025 09:00:04] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-20T04:00:00.000Z","dayDateKey":"2025-07-20","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:04] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:00:04] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-20 ignoré pour attribution 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:04] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-21 (1)
[15/07/2025 09:00:04] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-21T04:00:00.000Z","dayDateKey":"2025-07-21","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:04] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:00:04] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-21: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","dayDateStr":"2025-07-21","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:04] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-21
[15/07/2025 09:00:04] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-21 validé, shifts existants: 0
[15/07/2025 09:00:04] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-21 pour Pierre Durand
[15/07/2025 09:00:04] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-22 (2)
[15/07/2025 09:00:04] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-22T04:00:00.000Z","dayDateKey":"2025-07-22","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:04] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:00:04] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-22: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","dayDateStr":"2025-07-22","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:04] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-22
[15/07/2025 09:00:04] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-22 pour Pierre Durand
[15/07/2025 09:00:04] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-22 validé, shifts existants: 0
[15/07/2025 09:00:04] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-23 (3)
[15/07/2025 09:00:04] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-23T04:00:00.000Z","dayDateKey":"2025-07-23","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:04] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:00:04] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-23: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","dayDateStr":"2025-07-23","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:04] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-23
[15/07/2025 09:00:04] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-23 validé, shifts existants: 0
[15/07/2025 09:00:04] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-23 pour Pierre Durand
[15/07/2025 09:00:04] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-24 (4)
[15/07/2025 09:00:04] [FRONTEND] [LOG] 🧭 [navigateWeek] Direction: 1, Offset actuel: 0
[15/07/2025 09:00:04] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-24: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","dayDateStr":"2025-07-24","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:04] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-24
[15/07/2025 09:00:04] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-24T04:00:00.000Z","dayDateKey":"2025-07-24","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:04] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:00:04] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-24 validé, shifts existants: 0
[15/07/2025 09:00:04] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-25T04:00:00.000Z","dayDateKey":"2025-07-25","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:04] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-24 pour Pierre Durand
[15/07/2025 09:00:04] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-07-25 (5)
[15/07/2025 09:00:05] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:00:05] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-25: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","dayDateStr":"2025-07-25","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:05] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-25
[15/07/2025 09:00:05] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-25 validé, shifts existants: 0
[15/07/2025 09:00:05] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-25 pour Pierre Durand
[15/07/2025 09:00:05] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:00:05] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-07-26 (6)
[15/07/2025 09:00:05] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-26T04:00:00.000Z","dayDateKey":"2025-07-26","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:05] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-26 ignoré pour attribution 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:05] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 5 attributions appliquées pour la semaine 2025-W29
[15/07/2025 09:00:05] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Rendu demandé car 5 shifts créés
[15/07/2025 09:00:05] [FRONTEND] [LOG] 📋 [navigateWeek] Application des attributions pour la nouvelle semaine 2025-W29
[15/07/2025 09:00:05] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] Nettoyage des shifts réguliers existants...
[15/07/2025 09:00:05] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[15/07/2025 09:00:05] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:05] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[15/07/2025 09:00:05] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:05] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:05] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:05] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:05] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:05] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 09:00:05] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:05] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste b55a363e-2261-4255-ae7d-1ebe40fa9e82 configuré comme draggable
[15/07/2025 09:00:05] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 09:00:05] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:05] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:05] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:05] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste b55a363e-2261-4255-ae7d-1ebe40fa9e82
[15/07/2025 09:00:05] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] 0 shifts réguliers supprimés
[15/07/2025 09:00:05] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Cache invalidé pour forcer la régénération
[15/07/2025 09:00:05] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Application pour semaine 2025-W29
[15/07/2025 09:00:05] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[15/07/2025 09:00:05] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[15/07/2025 09:00:05] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6 a une intersection avec cette semaine (2025-07-14 → ∞) vs (2025-07-20 → 2025-07-26)
[15/07/2025 09:00:05] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[15/07/2025 09:00:05] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 09:00:05] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 70 zones trouvées
[15/07/2025 09:00:05] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 09:00:05] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 70 zones de drop disponibles
[15/07/2025 09:00:05] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff
[15/07/2025 09:00:05] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3
[15/07/2025 09:00:05] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3 configuré comme draggable
[15/07/2025 09:00:05] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 2a9a2244-1e0d-4062-8891-198d1cac2934 configuré comme draggable
[15/07/2025 09:00:05] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff
[15/07/2025 09:00:05] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff configuré comme draggable
[15/07/2025 09:00:05] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3
[15/07/2025 09:00:05] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3 configuré comme draggable
[15/07/2025 09:00:05] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6a79ab18-319e-4a85-805e-f80d0de9a73f configuré comme draggable
[15/07/2025 09:00:05] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6a79ab18-319e-4a85-805e-f80d0de9a73f
[15/07/2025 09:00:05] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 2a9a2244-1e0d-4062-8891-198d1cac2934 configuré comme draggable
[15/07/2025 09:00:05] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 09:00:05] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","selectedDays":[1,2,3,4,5],"startDate":"2025-07-14","endDate":null,"excludedDates":[]}
[15/07/2025 09:00:05] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution ea8e0f4b-e5d5-4f22-a59d-200e2c776c76 terminée avant cette semaine (endDate: 2025-07-15, semaine commence: 2025-07-20)
[15/07/2025 09:00:05] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 1 attributions actives sur 2 total
[15/07/2025 09:00:05] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6: [{"dateKey":"2025-07-20","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-21","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-22","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-23","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-24","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-07-25","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-07-26","isWeekend":true,"dayOfWeek":6}]
[15/07/2025 09:00:05] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-20 (0)
[15/07/2025 09:00:05] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste b55a363e-2261-4255-ae7d-1ebe40fa9e82 configuré comme draggable
[15/07/2025 09:00:05] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff configuré comme draggable
[15/07/2025 09:00:05] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste b55a363e-2261-4255-ae7d-1ebe40fa9e82
[15/07/2025 09:00:05] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff
[15/07/2025 09:00:05] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3 configuré comme draggable
[15/07/2025 09:00:05] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3
[15/07/2025 09:00:05] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6a79ab18-319e-4a85-805e-f80d0de9a73f configuré comme draggable
[15/07/2025 09:00:05] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6a79ab18-319e-4a85-805e-f80d0de9a73f
[15/07/2025 09:00:05] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 09:00:05] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 09:00:05] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:00:05] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:00:05] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:00:05] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:00:05] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:00:05] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 09:00:05] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (70 zones)
[15/07/2025 09:00:05] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[15/07/2025 09:00:05] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[15/07/2025 09:00:05] [FRONTEND] [LOG] ✅ [SETUP] Drag & drop des employés configuré (mode simplifié)
[15/07/2025 09:00:05] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 09:00:05] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 09:00:05] [FRONTEND] [WARN] ⚠️ [SETUP] Erreur lors de la destruction: Error: can't access property "Sortable1752584344465", el is null
destroy@http://localhost:5173/node_modules/.vite/deps/sortablejs.js?v=189de042:1812:5
setupDragAndDrop@http://localhost:5173/src/teamCalendarApp.ts:5808:33
attachAllEventListeners@http://localhost:5173/src/teamCalendarApp.ts:5358:10
actualRender@http://localhost:5173/src/teamCalendarApp.ts:2466:10
render/this._debouncedRender<@http://localhost:5173/src/teamCalendarApp.ts:2451:12
setTimeout handler*render@http://localhost:5173/src/teamCalendarApp.ts:2450:29
navigateWeek@http://localhost:5173/src/teamCalendarApp.ts:661:10
setupNavigationListeners/<@http://localhost:5173/src/teamCalendarApp.ts:2436:14
EventListener.handleEvent*setupNavigationListeners@http://localhost:5173/src/teamCalendarApp.ts:2434:15
init@http://localhost:5173/src/teamCalendarApp.ts:1047:10
async*initApp@http://localhost:5173/src/Agenda.tsx:40:31
setTimeout handler*AgendaFullscreen/<@http://localhost:5173/src/Agenda.tsx:50:29
react-stack-bottom-frame@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:17478:20
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
commitHookEffectListMount@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8460:122
commitHookPassiveMountEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8518:60
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10016:42
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
doubleInvokeEffectsOnFiber@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11461:207
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
recursivelyTraverseAndDoubleInvokeEffectsInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11432:78
commitDoubleInvokeEffectsInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11469:55
flushPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11309:41
node_modules/react-dom/cjs/react-dom-client.development.js/commitRoot/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11060:32
performWorkUntilDeadline@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:36:58
EventHandlerNonNull*node_modules/scheduler/cjs/scheduler.development.js/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:156:9
node_modules/scheduler/cjs/scheduler.development.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:266:7
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/scheduler/index.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:277:24
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/react-dom/cjs/react-dom-client.development.js/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:15216:23
node_modules/react-dom/cjs/react-dom-client.development.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18060:7
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/react-dom/client.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18071:24
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18075:16

[15/07/2025 09:00:05] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 70 zones trouvées
[15/07/2025 09:00:05] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 70 zones de drop disponibles
[15/07/2025 09:00:05] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 09:00:05] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 2a9a2244-1e0d-4062-8891-198d1cac2934 configuré comme draggable
[15/07/2025 09:00:05] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 09:00:05] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste b55a363e-2261-4255-ae7d-1ebe40fa9e82 configuré comme draggable
[15/07/2025 09:00:05] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste b55a363e-2261-4255-ae7d-1ebe40fa9e82
[15/07/2025 09:00:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff configuré comme draggable
[15/07/2025 09:00:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6a79ab18-319e-4a85-805e-f80d0de9a73f configuré comme draggable
[15/07/2025 09:00:06] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6a79ab18-319e-4a85-805e-f80d0de9a73f
[15/07/2025 09:00:06] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 09:00:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 2a9a2244-1e0d-4062-8891-198d1cac2934 configuré comme draggable
[15/07/2025 09:00:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste b55a363e-2261-4255-ae7d-1ebe40fa9e82 configuré comme draggable
[15/07/2025 09:00:06] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste b55a363e-2261-4255-ae7d-1ebe40fa9e82
[15/07/2025 09:00:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff configuré comme draggable
[15/07/2025 09:00:06] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff
[15/07/2025 09:00:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3 configuré comme draggable
[15/07/2025 09:00:06] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3
[15/07/2025 09:00:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6a79ab18-319e-4a85-805e-f80d0de9a73f configuré comme draggable
[15/07/2025 09:00:06] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6a79ab18-319e-4a85-805e-f80d0de9a73f
[15/07/2025 09:00:06] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 09:00:06] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 09:00:06] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:00:06] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:00:06] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:00:06] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:00:06] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:00:06] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:00:06] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:00:06] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:00:06] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:00:06] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 09:00:06] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:00:06] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (70 zones)
[15/07/2025 09:00:06] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] 0 grips ajoutés
[15/07/2025 09:00:06] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 09:00:06] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 09:00:06] [FRONTEND] [LOG] ⬅️ Clic sur précédent
[15/07/2025 09:00:06] [FRONTEND] [LOG] 🧭 [navigateWeek] Direction: -1, Offset actuel: 1
[15/07/2025 09:00:06] [FRONTEND] [LOG] 🗑️ [applyRegularAssignmentsForCurrentWeek] Suppression shift régulier: 18fe9cd1-e8a5-44e3-90b3-a856086ce27e - 2025-07-14 - assignment: 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:06] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] Nettoyage des shifts réguliers existants...
[15/07/2025 09:00:06] [FRONTEND] [LOG] 📋 [navigateWeek] Application des attributions pour la nouvelle semaine 2025-W28
[15/07/2025 09:00:06] [FRONTEND] [LOG] 🗑️ [applyRegularAssignmentsForCurrentWeek] Suppression shift régulier: 18fe9cd1-e8a5-44e3-90b3-a856086ce27e - 2025-07-15 - assignment: 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:06] [FRONTEND] [LOG] 🗑️ [applyRegularAssignmentsForCurrentWeek] Suppression shift régulier: 59f5df3a-33ef-425c-bfa2-adca818cf94f - 2025-07-14 - assignment: ea8e0f4b-e5d5-4f22-a59d-200e2c776c76
[15/07/2025 09:00:06] [FRONTEND] [LOG] 🗑️ [applyRegularAssignmentsForCurrentWeek] Suppression shift régulier: 59f5df3a-33ef-425c-bfa2-adca818cf94f - 2025-07-15 - assignment: ea8e0f4b-e5d5-4f22-a59d-200e2c776c76
[15/07/2025 09:00:06] [FRONTEND] [LOG] 🗑️ [applyRegularAssignmentsForCurrentWeek] Suppression shift régulier: 18fe9cd1-e8a5-44e3-90b3-a856086ce27e - 2025-07-16 - assignment: 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:06] [FRONTEND] [LOG] 🗑️ [applyRegularAssignmentsForCurrentWeek] Suppression shift régulier: 18fe9cd1-e8a5-44e3-90b3-a856086ce27e - 2025-07-17 - assignment: 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:06] [FRONTEND] [LOG] 🗑️ [applyRegularAssignmentsForCurrentWeek] Suppression shift régulier: 18fe9cd1-e8a5-44e3-90b3-a856086ce27e - 2025-07-18 - assignment: 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:06] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] 7 shifts réguliers supprimés
[15/07/2025 09:00:06] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Cache invalidé pour forcer la régénération
[15/07/2025 09:00:06] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 2 attributions actives sur 2 total
[15/07/2025 09:00:06] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution ea8e0f4b-e5d5-4f22-a59d-200e2c776c76 a une intersection avec cette semaine (2025-07-13 → 2025-07-15) vs (2025-07-13 → 2025-07-19)
[15/07/2025 09:00:06] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6 a une intersection avec cette semaine (2025-07-14 → ∞) vs (2025-07-13 → 2025-07-19)
[15/07/2025 09:00:06] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Application pour semaine 2025-W28
[15/07/2025 09:00:06] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","selectedDays":[1,2,3,4,5],"startDate":"2025-07-14","endDate":null,"excludedDates":[]}
[15/07/2025 09:00:06] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-13 (0)
[15/07/2025 09:00:06] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6: [{"dateKey":"2025-07-13","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-14","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-15","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-16","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-17","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-07-18","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-07-19","isWeekend":true,"dayOfWeek":6}]
[15/07/2025 09:00:06] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-13T04:00:00.000Z","dayDateKey":"2025-07-13","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:06] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-13 ignoré pour attribution 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:06] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-14 (1)
[15/07/2025 09:00:06] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:00:06] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:00:06] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-14T04:00:00.000Z","dayDateKey":"2025-07-14","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:06] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-14: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","dayDateStr":"2025-07-14","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:06] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-14
[15/07/2025 09:00:06] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-14 validé, shifts existants: 0
[15/07/2025 09:00:06] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-14 pour Pierre Durand
[15/07/2025 09:00:06] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-15 (2)
[15/07/2025 09:00:06] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-15T04:00:00.000Z","dayDateKey":"2025-07-15","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:06] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:00:06] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-15: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","dayDateStr":"2025-07-15","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:06] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-15
[15/07/2025 09:00:06] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-15 validé, shifts existants: 0
[15/07/2025 09:00:06] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-15 pour Pierre Durand
[15/07/2025 09:00:06] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-16 (3)
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-16T04:00:00.000Z","dayDateKey":"2025-07-16","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:07] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-16
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:00:07] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-16 validé, shifts existants: 0
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-16: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","dayDateStr":"2025-07-16","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:07] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-16 pour Pierre Durand
[15/07/2025 09:00:07] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-17 (4)
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-17T04:00:00.000Z","dayDateKey":"2025-07-17","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-17: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","dayDateStr":"2025-07-17","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:07] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-17
[15/07/2025 09:00:07] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-17 validé, shifts existants: 0
[15/07/2025 09:00:07] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-17 pour Pierre Durand
[15/07/2025 09:00:07] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-07-18 (5)
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-18T04:00:00.000Z","dayDateKey":"2025-07-18","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:00:07] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-18
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-18: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","dayDateStr":"2025-07-18","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:07] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-18 validé, shifts existants: 0
[15/07/2025 09:00:07] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-07-19 (6)
[15/07/2025 09:00:07] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-18 pour Pierre Durand
[15/07/2025 09:00:07] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-15 (2)
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-14T04:00:00.000Z","dayDateKey":"2025-07-14","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-19T04:00:00.000Z","dayDateKey":"2025-07-19","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:00:07] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-15
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-15T04:00:00.000Z","dayDateKey":"2025-07-15","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-15: {"assignmentId":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","dayDateStr":"2025-07-15","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:07] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-14 (1)
[15/07/2025 09:00:07] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-14 pour Sophie Leblanc
[15/07/2025 09:00:07] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-14 validé, shifts existants: 0
[15/07/2025 09:00:07] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-13 ignoré pour attribution ea8e0f4b-e5d5-4f22-a59d-200e2c776c76
[15/07/2025 09:00:07] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:00:07] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-14
[15/07/2025 09:00:07] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-13 (0)
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-16T04:00:00.000Z","dayDateKey":"2025-07-16","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-13T04:00:00.000Z","dayDateKey":"2025-07-13","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution ea8e0f4b-e5d5-4f22-a59d-200e2c776c76: [{"dateKey":"2025-07-13","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-14","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-15","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-16","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-17","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-07-18","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-07-19","isWeekend":true,"dayOfWeek":6}]
[15/07/2025 09:00:07] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-16 (3)
[15/07/2025 09:00:07] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-15 pour Sophie Leblanc
[15/07/2025 09:00:07] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","selectedDays":[1,2,3,4,5],"startDate":"2025-07-13","endDate":"2025-07-15","excludedDates":[]}
[15/07/2025 09:00:07] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-19 ignoré pour attribution 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:00:07] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-16 ignoré pour attribution ea8e0f4b-e5d5-4f22-a59d-200e2c776c76
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-17: {"assignmentId":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","dayDateStr":"2025-07-17","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:07] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-17 après endDate 2025-07-15 (original: 2025-07-15) (attribution ea8e0f4b-e5d5-4f22-a59d-200e2c776c76)
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-17T04:00:00.000Z","dayDateKey":"2025-07-17","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:00:07] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-18 après endDate 2025-07-15 (original: 2025-07-15) (attribution ea8e0f4b-e5d5-4f22-a59d-200e2c776c76)
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Rendu demandé car 7 shifts créés
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-18: {"assignmentId":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","dayDateStr":"2025-07-18","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:07] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 7 attributions appliquées pour la semaine 2025-W28
[15/07/2025 09:00:07] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-18T04:00:00.000Z","dayDateKey":"2025-07-18","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:07] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-19 ignoré pour attribution ea8e0f4b-e5d5-4f22-a59d-200e2c776c76
[15/07/2025 09:00:07] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-07-18 (5)
[15/07/2025 09:00:07] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-17 ignoré pour attribution ea8e0f4b-e5d5-4f22-a59d-200e2c776c76
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-19T04:00:00.000Z","dayDateKey":"2025-07-19","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:07] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-07-19 (6)
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:00:07] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:00:07] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-18 ignoré pour attribution ea8e0f4b-e5d5-4f22-a59d-200e2c776c76
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-14: {"assignmentId":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","dayDateStr":"2025-07-14","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:07] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-15 validé, shifts existants: 0
[15/07/2025 09:00:07] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-17 (4)
[15/07/2025 09:00:07] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-16 après endDate 2025-07-15 (original: 2025-07-15) (attribution ea8e0f4b-e5d5-4f22-a59d-200e2c776c76)
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-16: {"assignmentId":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","dayDateStr":"2025-07-16","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:07] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[15/07/2025 09:00:07] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 74 zones trouvées
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 74 zones de drop disponibles
[15/07/2025 09:00:07] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:07] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:07] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:07] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 09:00:07] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:00:07] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff
[15/07/2025 09:00:07] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:00:07] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:00:07] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] 0 grips ajoutés
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière ea8e0f4b-e5d5-4f22-a59d-200e2c776c76
[15/07/2025 09:00:07] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:07] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 09:00:07] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 09:00:07] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière ea8e0f4b-e5d5-4f22-a59d-200e2c776c76
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[15/07/2025 09:00:07] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[15/07/2025 09:00:07] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 2a9a2244-1e0d-4062-8891-198d1cac2934 configuré comme draggable
[15/07/2025 09:00:07] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste b55a363e-2261-4255-ae7d-1ebe40fa9e82 configuré comme draggable
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff configuré comme draggable
[15/07/2025 09:00:07] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste b55a363e-2261-4255-ae7d-1ebe40fa9e82
[15/07/2025 09:00:07] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3 configuré comme draggable
[15/07/2025 09:00:07] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6a79ab18-319e-4a85-805e-f80d0de9a73f configuré comme draggable
[15/07/2025 09:00:07] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6a79ab18-319e-4a85-805e-f80d0de9a73f
[15/07/2025 09:00:07] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 2a9a2244-1e0d-4062-8891-198d1cac2934 configuré comme draggable
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste b55a363e-2261-4255-ae7d-1ebe40fa9e82 configuré comme draggable
[15/07/2025 09:00:07] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste b55a363e-2261-4255-ae7d-1ebe40fa9e82
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff configuré comme draggable
[15/07/2025 09:00:07] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3 configuré comme draggable
[15/07/2025 09:00:07] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6a79ab18-319e-4a85-805e-f80d0de9a73f configuré comme draggable
[15/07/2025 09:00:07] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6a79ab18-319e-4a85-805e-f80d0de9a73f
[15/07/2025 09:00:07] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 09:00:08] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:00:08] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:00:08] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 09:00:08] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (74 zones)
[15/07/2025 09:00:08] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[15/07/2025 09:00:08] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[15/07/2025 09:00:08] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 09:00:08] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 09:00:08] [FRONTEND] [WARN] ⚠️ [SETUP] Erreur lors de la destruction: Error: can't access property "Sortable1752584344465", el is null
destroy@http://localhost:5173/node_modules/.vite/deps/sortablejs.js?v=189de042:1812:5
setupDragAndDrop@http://localhost:5173/src/teamCalendarApp.ts:5808:33
attachAllEventListeners@http://localhost:5173/src/teamCalendarApp.ts:5358:10
actualRender@http://localhost:5173/src/teamCalendarApp.ts:2466:10
render/this._debouncedRender<@http://localhost:5173/src/teamCalendarApp.ts:2451:12
setTimeout handler*render@http://localhost:5173/src/teamCalendarApp.ts:2450:29
navigateWeek@http://localhost:5173/src/teamCalendarApp.ts:661:10
setupNavigationListeners/<@http://localhost:5173/src/teamCalendarApp.ts:2432:14
EventListener.handleEvent*setupNavigationListeners@http://localhost:5173/src/teamCalendarApp.ts:2430:15
init@http://localhost:5173/src/teamCalendarApp.ts:1047:10
async*initApp@http://localhost:5173/src/Agenda.tsx:40:31
setTimeout handler*AgendaFullscreen/<@http://localhost:5173/src/Agenda.tsx:50:29
react-stack-bottom-frame@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:17478:20
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
commitHookEffectListMount@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8460:122
commitHookPassiveMountEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8518:60
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10016:42
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
doubleInvokeEffectsOnFiber@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11461:207
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
recursivelyTraverseAndDoubleInvokeEffectsInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11432:78
commitDoubleInvokeEffectsInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11469:55
flushPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11309:41
node_modules/react-dom/cjs/react-dom-client.development.js/commitRoot/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11060:32
performWorkUntilDeadline@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:36:58
EventHandlerNonNull*node_modules/scheduler/cjs/scheduler.development.js/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:156:9
node_modules/scheduler/cjs/scheduler.development.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:266:7
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/scheduler/index.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:277:24
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/react-dom/cjs/react-dom-client.development.js/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:15216:23
node_modules/react-dom/cjs/react-dom-client.development.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18060:7
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/react-dom/client.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18071:24
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18075:16

[15/07/2025 09:00:08] [FRONTEND] [LOG] ✅ [SETUP] Drag & drop des employés configuré (mode simplifié)
[15/07/2025 09:00:08] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 74 zones trouvées
[15/07/2025 09:00:08] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 74 zones de drop disponibles
[15/07/2025 09:00:08] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 2a9a2244-1e0d-4062-8891-198d1cac2934 configuré comme draggable
[15/07/2025 09:00:08] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 09:00:08] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 09:00:08] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste b55a363e-2261-4255-ae7d-1ebe40fa9e82 configuré comme draggable
[15/07/2025 09:00:08] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste b55a363e-2261-4255-ae7d-1ebe40fa9e82
[15/07/2025 09:00:08] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff configuré comme draggable
[15/07/2025 09:00:08] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff
[15/07/2025 09:00:08] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3 configuré comme draggable
[15/07/2025 09:00:08] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3
[15/07/2025 09:00:08] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6a79ab18-319e-4a85-805e-f80d0de9a73f configuré comme draggable
[15/07/2025 09:00:08] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6a79ab18-319e-4a85-805e-f80d0de9a73f
[15/07/2025 09:00:08] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 2a9a2244-1e0d-4062-8891-198d1cac2934 configuré comme draggable
[15/07/2025 09:00:08] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 09:00:08] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste b55a363e-2261-4255-ae7d-1ebe40fa9e82 configuré comme draggable
[15/07/2025 09:00:08] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste b55a363e-2261-4255-ae7d-1ebe40fa9e82
[15/07/2025 09:00:08] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff configuré comme draggable
[15/07/2025 09:00:08] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3 configuré comme draggable
[15/07/2025 09:00:08] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3
[15/07/2025 09:00:08] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6a79ab18-319e-4a85-805e-f80d0de9a73f configuré comme draggable
[15/07/2025 09:00:08] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6a79ab18-319e-4a85-805e-f80d0de9a73f
[15/07/2025 09:00:08] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 09:00:08] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 09:00:08] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:00:08] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:00:08] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:00:08] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:00:08] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:00:08] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:00:08] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:00:08] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:00:08] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 09:00:08] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:00:08] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:00:08] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (74 zones)
[15/07/2025 09:00:08] [FRONTEND] [LOG] ➡️ Clic sur suivant
[15/07/2025 09:00:08] [FRONTEND] [LOG] 🗑️ [applyRegularAssignmentsForCurrentWeek] Suppression shift régulier: 18fe9cd1-e8a5-44e3-90b3-a856086ce27e - 2025-07-24 - assignment: 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:08] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] Nettoyage des shifts réguliers existants...
[15/07/2025 09:00:08] [FRONTEND] [LOG] 📋 [navigateWeek] Application des attributions pour la nouvelle semaine 2025-W29
[15/07/2025 09:00:08] [FRONTEND] [LOG] 🗑️ [applyRegularAssignmentsForCurrentWeek] Suppression shift régulier: 18fe9cd1-e8a5-44e3-90b3-a856086ce27e - 2025-07-21 - assignment: 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:08] [FRONTEND] [LOG] 🗑️ [applyRegularAssignmentsForCurrentWeek] Suppression shift régulier: 18fe9cd1-e8a5-44e3-90b3-a856086ce27e - 2025-07-23 - assignment: 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:08] [FRONTEND] [LOG] 🧭 [navigateWeek] Direction: 1, Offset actuel: 0
[15/07/2025 09:00:08] [FRONTEND] [LOG] 🗑️ [applyRegularAssignmentsForCurrentWeek] Suppression shift régulier: 18fe9cd1-e8a5-44e3-90b3-a856086ce27e - 2025-07-22 - assignment: 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:08] [FRONTEND] [LOG] 🗑️ [applyRegularAssignmentsForCurrentWeek] Suppression shift régulier: 18fe9cd1-e8a5-44e3-90b3-a856086ce27e - 2025-07-25 - assignment: 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:08] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Rendu demandé car 5 shifts créés
[15/07/2025 09:00:08] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:00:08] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Application pour semaine 2025-W29
[15/07/2025 09:00:08] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-26 ignoré pour attribution 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:08] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Cache invalidé pour forcer la régénération
[15/07/2025 09:00:08] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 5 attributions appliquées pour la semaine 2025-W29
[15/07/2025 09:00:08] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] 5 shifts réguliers supprimés
[15/07/2025 09:00:08] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6 a une intersection avec cette semaine (2025-07-14 → ∞) vs (2025-07-20 → 2025-07-26)
[15/07/2025 09:00:08] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-26T04:00:00.000Z","dayDateKey":"2025-07-26","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:08] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-25 validé, shifts existants: 0
[15/07/2025 09:00:08] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-25 pour Pierre Durand
[15/07/2025 09:00:08] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 1 attributions actives sur 2 total
[15/07/2025 09:00:08] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6: [{"dateKey":"2025-07-20","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-21","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-22","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-23","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-24","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-07-25","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-07-26","isWeekend":true,"dayOfWeek":6}]
[15/07/2025 09:00:08] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","selectedDays":[1,2,3,4,5],"startDate":"2025-07-14","endDate":null,"excludedDates":[]}
[15/07/2025 09:00:08] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-07-26 (6)
[15/07/2025 09:00:08] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution ea8e0f4b-e5d5-4f22-a59d-200e2c776c76 terminée avant cette semaine (endDate: 2025-07-15, semaine commence: 2025-07-20)
[15/07/2025 09:00:08] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-25
[15/07/2025 09:00:08] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-20 (0)
[15/07/2025 09:00:08] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-20T04:00:00.000Z","dayDateKey":"2025-07-20","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:08] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-25: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","dayDateStr":"2025-07-25","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:08] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:00:08] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:00:08] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-20 ignoré pour attribution 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:08] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-07-25 (5)
[15/07/2025 09:00:08] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-25T04:00:00.000Z","dayDateKey":"2025-07-25","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-21T04:00:00.000Z","dayDateKey":"2025-07-21","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-24 pour Pierre Durand
[15/07/2025 09:00:09] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-21 (1)
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-24: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","dayDateStr":"2025-07-24","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-21: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","dayDateStr":"2025-07-21","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-24
[15/07/2025 09:00:09] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-24 validé, shifts existants: 0
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-21
[15/07/2025 09:00:09] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-21 validé, shifts existants: 0
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-24T04:00:00.000Z","dayDateKey":"2025-07-24","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:09] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-22 (2)
[15/07/2025 09:00:09] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-24 (4)
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-21 pour Pierre Durand
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-23 pour Pierre Durand
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-22T04:00:00.000Z","dayDateKey":"2025-07-22","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:09] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-23 validé, shifts existants: 0
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-23
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-23: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","dayDateStr":"2025-07-23","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-22
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-22: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","dayDateStr":"2025-07-22","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:00:09] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-22 validé, shifts existants: 0
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-23T04:00:00.000Z","dayDateKey":"2025-07-23","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-22 pour Pierre Durand
[15/07/2025 09:00:09] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-23 (3)
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 70 zones de drop disponibles
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 70 zones trouvées
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 2a9a2244-1e0d-4062-8891-198d1cac2934 configuré comme draggable
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste b55a363e-2261-4255-ae7d-1ebe40fa9e82 configuré comme draggable
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste b55a363e-2261-4255-ae7d-1ebe40fa9e82
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff configuré comme draggable
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3 configuré comme draggable
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6a79ab18-319e-4a85-805e-f80d0de9a73f
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6a79ab18-319e-4a85-805e-f80d0de9a73f configuré comme draggable
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 2a9a2244-1e0d-4062-8891-198d1cac2934 configuré comme draggable
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste b55a363e-2261-4255-ae7d-1ebe40fa9e82 configuré comme draggable
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff configuré comme draggable
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste b55a363e-2261-4255-ae7d-1ebe40fa9e82
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3 configuré comme draggable
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6a79ab18-319e-4a85-805e-f80d0de9a73f configuré comme draggable
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6a79ab18-319e-4a85-805e-f80d0de9a73f
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (70 zones)
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[15/07/2025 09:00:09] [FRONTEND] [WARN] ⚠️ [SETUP] Erreur lors de la destruction: Error: can't access property "Sortable1752584344465", el is null
destroy@http://localhost:5173/node_modules/.vite/deps/sortablejs.js?v=189de042:1812:5
setupDragAndDrop@http://localhost:5173/src/teamCalendarApp.ts:5808:33
attachAllEventListeners@http://localhost:5173/src/teamCalendarApp.ts:5358:10
actualRender@http://localhost:5173/src/teamCalendarApp.ts:2466:10
render/this._debouncedRender<@http://localhost:5173/src/teamCalendarApp.ts:2451:12
setTimeout handler*render@http://localhost:5173/src/teamCalendarApp.ts:2450:29
navigateWeek@http://localhost:5173/src/teamCalendarApp.ts:661:10
setupNavigationListeners/<@http://localhost:5173/src/teamCalendarApp.ts:2436:14
EventListener.handleEvent*setupNavigationListeners@http://localhost:5173/src/teamCalendarApp.ts:2434:15
init@http://localhost:5173/src/teamCalendarApp.ts:1047:10
async*initApp@http://localhost:5173/src/Agenda.tsx:40:31
setTimeout handler*AgendaFullscreen/<@http://localhost:5173/src/Agenda.tsx:50:29
react-stack-bottom-frame@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:17478:20
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
commitHookEffectListMount@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8460:122
commitHookPassiveMountEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8518:60
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10016:42
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
doubleInvokeEffectsOnFiber@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11461:207
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
recursivelyTraverseAndDoubleInvokeEffectsInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11432:78
commitDoubleInvokeEffectsInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11469:55
flushPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11309:41
node_modules/react-dom/cjs/react-dom-client.development.js/commitRoot/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11060:32
performWorkUntilDeadline@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:36:58
EventHandlerNonNull*node_modules/scheduler/cjs/scheduler.development.js/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:156:9
node_modules/scheduler/cjs/scheduler.development.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:266:7
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/scheduler/index.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:277:24
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/react-dom/cjs/react-dom-client.development.js/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:15216:23
node_modules/react-dom/cjs/react-dom-client.development.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18060:7
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/react-dom/client.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18071:24
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18075:16

[15/07/2025 09:00:09] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [SETUP] Drag & drop des employés configuré (mode simplifié)
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 70 zones trouvées
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 70 zones de drop disponibles
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 2a9a2244-1e0d-4062-8891-198d1cac2934 configuré comme draggable
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste b55a363e-2261-4255-ae7d-1ebe40fa9e82 configuré comme draggable
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste b55a363e-2261-4255-ae7d-1ebe40fa9e82
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff configuré comme draggable
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3 configuré comme draggable
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6a79ab18-319e-4a85-805e-f80d0de9a73f configuré comme draggable
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6a79ab18-319e-4a85-805e-f80d0de9a73f
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 2a9a2244-1e0d-4062-8891-198d1cac2934 configuré comme draggable
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste b55a363e-2261-4255-ae7d-1ebe40fa9e82 configuré comme draggable
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste b55a363e-2261-4255-ae7d-1ebe40fa9e82
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff configuré comme draggable
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3 configuré comme draggable
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6a79ab18-319e-4a85-805e-f80d0de9a73f configuré comme draggable
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6a79ab18-319e-4a85-805e-f80d0de9a73f
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:00:09] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:00:09] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:00:10] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:00:10] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (70 zones)
[15/07/2025 09:00:10] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 09:00:10] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 09:00:10] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] 0 grips ajoutés
[15/07/2025 09:00:10] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 09:00:15] [FRONTEND] [LOG] 🎯 [GRIP] Début drag grip pour attribution 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:15] [FRONTEND] [LOG] 🎯 [GRIP] DataTransfer types avant: ["text/_moz_htmlcontext","text/_moz_htmlinfo","text/html","text/plain"]
[15/07/2025 09:00:15] [FRONTEND] [LOG] 🎯 [GRIP] DataTransfer types après: ["text/_moz_htmlcontext","text/_moz_htmlinfo","text/html","text/plain","regularassignmentid"]
[15/07/2025 09:00:15] [FRONTEND] [LOG] 🎯 [GRIP] regularAssignmentId stocké: 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:15] [FRONTEND] [LOG] 🎨 [highlightEmployeeDropZones] Ajout surbrillance sur 70 zones
[15/07/2025 09:00:15] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé cf78e945-72c7-48f3-a72d-bd0e245a284d, types: text/_moz_htmlcontext, text/_moz_htmlinfo, text/html, text/plain, regularassignmentid
[15/07/2025 09:00:15] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a, types: text/_moz_htmlcontext, text/_moz_htmlinfo, text/html, text/plain, regularassignmentid
[15/07/2025 09:00:15] [FRONTEND] [LOG] 🎨 [highlightEmployeeDropZones] Zones trouvées: [{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30"},{"element":"BUTTON","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors"},{"element":"BUTTON","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors"},{"element":"BUTTON","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30"},{"element":"BUTTON","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors"},{"element":"BUTTON","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors"},{"element":"BUTTON","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30"},{"element":"BUTTON","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors"},{"element":"BUTTON","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors"},{"element":"BUTTON","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30"},{"element":"BUTTON","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors"},{"element":"BUTTON","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors"},{"element":"BUTTON","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30"},{"element":"BUTTON","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors"},{"element":"BUTTON","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors"},{"element":"BUTTON","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60"}]
[15/07/2025 09:00:15] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a, types: text/_moz_htmlcontext, text/_moz_htmlinfo, text/html, text/plain, regularassignmentid
[15/07/2025 09:00:16] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a, types: text/_moz_htmlcontext, text/_moz_htmlinfo, text/html, text/plain, regularassignmentid
[15/07/2025 09:00:16] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 09:00:16] [FRONTEND] [LOG] 🎯 [DROP] ✅ Drop reçu sur employé: Marie Martin
[15/07/2025 09:00:16] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 09:00:16] [FRONTEND] [LOG] 🔄 [DROP] Attribution régulière: 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6 → 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:00:16] [FRONTEND] [LOG] 🎯 [handleRegularAssignmentDrop] Drop assignment 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6 sur employé Marie Martin
[15/07/2025 09:00:16] [FRONTEND] [LOG] 🎯 [showRegularAssignmentConfirmationMenu] Affichage menu pour: {"assignment":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","from":"Pierre Durand","to":"Marie Martin","post":"Poste Matin","referenceDate":null,"minDate":"2025-07-15","todayKey":"2025-07-15"}
[15/07/2025 09:00:16] [FRONTEND] [LOG] 🎯 [GRIP] Fin drag grip pour attribution 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:16] [FRONTEND] [LOG] 🎨 [highlightEmployeeDropZones] Suppression surbrillance sur 70 zones
[15/07/2025 09:00:16] [FRONTEND] [LOG] 🎨 [highlightEmployeeDropZones] Zones trouvées: [{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200 regular-assignment-drop-zone"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30 regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors regular-assignment-drop-zone"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30 regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30 regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200 regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30 regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200 regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30 regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 regular-assignment-drop-zone"}]
[15/07/2025 09:00:18] [FRONTEND] [LOG] 🔄 [handlePermanentRegularAssignmentChange] Changement permanent 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6 → 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:00:18] [FRONTEND] [LOG] 🔄 [reassignRegularAssignmentFromDate] Désactiver l'original: false
[15/07/2025 09:00:18] [FRONTEND] [LOG] 📅 [handlePermanentRegularAssignmentChange] Date minimale: 2025-07-14
[15/07/2025 09:00:18] [FRONTEND] [LOG] 🔄 [handlePermanentRegularAssignmentChange] Désactiver l'original: false
[15/07/2025 09:00:18] [FRONTEND] [LOG] 🔄 [reassignRegularAssignmentFromDate] 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6 → 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a à partir de 2025-07-14
[15/07/2025 09:00:18] [FRONTEND] [LOG] 🔄 [reassignRegularAssignmentFromDate] Fork créé, historique préservé
[15/07/2025 09:00:18] [FRONTEND] [LOG] 🔄 [updateShiftsForDateBasedReassignment] Mise à jour des shifts à partir de 2025-07-14
[15/07/2025 09:00:18] [FRONTEND] [LOG] ✅ [updateShiftsForDateBasedReassignment] 10 shifts mis à jour
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:00:18.789Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✏️ [RegularAssignments] Mise à jour attribution 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
  Data: {
  "originalArgs": [
    "✏️ [RegularAssignments] Mise à jour attribution 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:00:18.636Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:00:18.804Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      UPDATE regular_assignments 
      SET employee_id = $1, post_id = $2, days_of_week = $3, start_date = $4, 
          end_date = $5, is_active = $6, excluded_dates = $7, updated_at = CURRENT_TIMESTAMP
      WHERE id = $8
      RETURNING *
     {"params":["18fe9cd1-e8a5-44e3-90b3-a856086ce27e","2a9a2244-1e0d-4062-8891-198d1cac2934",[1,2,3,4,5],"2025-07-14","2025-07-13",true,[],"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6"]}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      UPDATE regular_assignments \n      SET employee_id = $1, post_id = $2, days_of_week = $3, start_date = $4, \n          end_date = $5, is_active = $6, excluded_dates = $7, updated_at = CURRENT_TIMESTAMP\n      WHERE id = $8\n      RETURNING *\n    ",
    {
      "params": [
        "18fe9cd1-e8a5-44e3-90b3-a856086ce27e",
        "2a9a2244-1e0d-4062-8891-198d1cac2934",
        [
          1,
          2,
          3,
          4,
          5
        ],
        "2025-07-14",
        "2025-07-13",
        true,
        [],
        "85d637c2-6ed3-447b-8a37-a0ba9fcf89b6"
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:00:18.643Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:00:18.808Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:00:18.806Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:00:18.798Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:00:18.812Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ [validateAndConvertUuid] UUID valide détecté pour post_id: 2a9a2244-1e0d-4062-8891-198d1cac2934
  Data: {
  "originalArgs": [
    "✅ [validateAndConvertUuid] UUID valide détecté pour post_id: 2a9a2244-1e0d-4062-8891-198d1cac2934"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:00:18.641Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📝 [RegularAssignmentV2.update] Données reçues: {"id":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","startDate":"2025-07-14","endDate":"2025-07-13","isLimited":false,"isActive":true,"selectedDays":[1,2,3,4,5],"daysOfWeek":[1,2,3,4,5],"employeeName":"Pierre Durand","postLabel":"Poste Matin","postHours":"08:00-16:00","postType":"standard","daysCount":5,"excludedDates":[]}
  Data: {
  "originalArgs": [
    "📝 [RegularAssignmentV2.update] Données reçues:",
    {
      "id": "85d637c2-6ed3-447b-8a37-a0ba9fcf89b6",
      "employeeId": "18fe9cd1-e8a5-44e3-90b3-a856086ce27e",
      "postId": "2a9a2244-1e0d-4062-8891-198d1cac2934",
      "startDate": "2025-07-14",
      "endDate": "2025-07-13",
      "isLimited": false,
      "isActive": true,
      "selectedDays": [
        1,
        2,
        3,
        4,
        5
      ],
      "daysOfWeek": [
        1,
        2,
        3,
        4,
        5
      ],
      "employeeName": "Pierre Durand",
      "postLabel": "Poste Matin",
      "postHours": "08:00-16:00",
      "postType": "standard",
      "daysCount": 5,
      "excludedDates": []
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:00:18.639Z"
}
[15/07/2025 09:00:18] [BACKEND] [INFO] ✏️ [RegularAssignments] Mise à jour attribution 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔍 [validateAndConvertUuid] Entrée: fieldName="post_id", value="2a9a2244-1e0d-4062-8891-198d1cac2934", type=string
  Data: {
  "originalArgs": [
    "🔍 [validateAndConvertUuid] Entrée: fieldName=\"post_id\", value=\"2a9a2244-1e0d-4062-8891-198d1cac2934\", type=string"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:00:18.640Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:00:18.819Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ [RegularAssignments] Attribution mise à jour: 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
  Data: {
  "originalArgs": [
    "✅ [RegularAssignments] Attribution mise à jour:",
    "85d637c2-6ed3-447b-8a37-a0ba9fcf89b6"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:00:18.866Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 222ms: 
      UPDATE regular_assignments 
      SET emplo {"params":["18fe9cd1-e8a5-44e3-90b3-a856086ce27e","2a9a2244-1e0d-4062-8891-198d1cac2934",[1,2,3,4,5],"2025-07-14","2025-07-13",true,[],"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6"]}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 222ms:",
    "\n      UPDATE regular_assignments \n      SET emplo",
    {
      "params": [
        "18fe9cd1-e8a5-44e3-90b3-a856086ce27e",
        "2a9a2244-1e0d-4062-8891-198d1cac2934",
        [
          1,
          2,
          3,
          4,
          5
        ],
        "2025-07-14",
        "2025-07-13",
        true,
        [],
        "85d637c2-6ed3-447b-8a37-a0ba9fcf89b6"
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:00:18.864Z"
}
[15/07/2025 09:00:18] [BACKEND] [INFO] ✅ Requête exécutée en 222ms: 
      UPDATE regular_assignments 
      SET emplo {"params":["18fe9cd1-e8a5-44e3-90b3-a856086ce27e","2a9a2244-1e0d-4062-8891-198d1cac2934",[1,2,3,4,5],"2025-07-14","2025-07-13",true,[],"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6"]}
[15/07/2025 09:00:18] [FRONTEND] [LOG] ✅ [reassignRegularAssignmentFromDate] Attribution existante mise à jour avec date de fin
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ [validateAndConvertUuid] UUID valide détecté pour post_id: 2a9a2244-1e0d-4062-8891-198d1cac2934
  Data: {
  "originalArgs": [
    "✅ [validateAndConvertUuid] UUID valide détecté pour post_id: 2a9a2244-1e0d-4062-8891-198d1cac2934"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:00:18.894Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ➕ [RegularAssignments] Création d'une attribution individuelle
  Data: {
  "originalArgs": [
    "➕ [RegularAssignments] Création d'une attribution individuelle"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:00:18.889Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📝 [RegularAssignmentV2.create] Données reçues: {"employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","isLimited":false,"startDate":"2025-07-14","endDate":null,"selectedDays":[1,2,3,4,5],"daysOfWeek":[1,2,3,4,5],"isActive":true,"id":"3b1b27fd-a4a8-404f-bc15-76f34a20b1d5"}
  Data: {
  "originalArgs": [
    "📝 [RegularAssignmentV2.create] Données reçues:",
    {
      "employeeId": "604a1d0c-2141-4543-b7f9-7fc9adfa7b9a",
      "postId": "2a9a2244-1e0d-4062-8891-198d1cac2934",
      "isLimited": false,
      "startDate": "2025-07-14",
      "endDate": null,
      "selectedDays": [
        1,
        2,
        3,
        4,
        5
      ],
      "daysOfWeek": [
        1,
        2,
        3,
        4,
        5
      ],
      "isActive": true,
      "id": "3b1b27fd-a4a8-404f-bc15-76f34a20b1d5"
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:00:18.892Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📤 [RegularAssignments] Données reçues: {
  "employeeId": "604a1d0c-2141-4543-b7f9-7fc9adfa7b9a",
  "postId": "2a9a2244-1e0d-4062-8891-198d1cac2934",
  "isLimited": false,
  "startDate": "2025-07-14",
  "endDate": null,
  "selectedDays": [
    1,
    2,
    3,
    4,
    5
  ],
  "daysOfWeek": [
    1,
    2,
    3,
    4,
    5
  ],
  "isActive": true,
  "id": "3b1b27fd-a4a8-404f-bc15-76f34a20b1d5"
}
  Data: {
  "originalArgs": [
    "📤 [RegularAssignments] Données reçues:",
    "{\n  \"employeeId\": \"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a\",\n  \"postId\": \"2a9a2244-1e0d-4062-8891-198d1cac2934\",\n  \"isLimited\": false,\n  \"startDate\": \"2025-07-14\",\n  \"endDate\": null,\n  \"selectedDays\": [\n    1,\n    2,\n    3,\n    4,\n    5\n  ],\n  \"daysOfWeek\": [\n    1,\n    2,\n    3,\n    4,\n    5\n  ],\n  \"isActive\": true,\n  \"id\": \"3b1b27fd-a4a8-404f-bc15-76f34a20b1d5\"\n}"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:00:18.891Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      INSERT INTO regular_assignments (id, employee_id, post_id, days_of_week, start_date, end_date, is_active)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
     {"params":["0e248502-624d-4249-9764-2cb9b08768e9","604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","2a9a2244-1e0d-4062-8891-198d1cac2934",[1,2,3,4,5],"2025-07-14",null,true]}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      INSERT INTO regular_assignments (id, employee_id, post_id, days_of_week, start_date, end_date, is_active)\n      VALUES ($1, $2, $3, $4, $5, $6, $7)\n      RETURNING *\n    ",
    {
      "params": [
        "0e248502-624d-4249-9764-2cb9b08768e9",
        "604a1d0c-2141-4543-b7f9-7fc9adfa7b9a",
        "2a9a2244-1e0d-4062-8891-198d1cac2934",
        [
          1,
          2,
          3,
          4,
          5
        ],
        "2025-07-14",
        null,
        true
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:00:18.896Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔍 [RegularAssignmentV2.create] Post ID original: 2a9a2244-1e0d-4062-8891-198d1cac2934 Post ID validé: 2a9a2244-1e0d-4062-8891-198d1cac2934
  Data: {
  "originalArgs": [
    "🔍 [RegularAssignmentV2.create] Post ID original:",
    "2a9a2244-1e0d-4062-8891-198d1cac2934",
    "Post ID validé:",
    "2a9a2244-1e0d-4062-8891-198d1cac2934"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:00:18.895Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔍 [RegularAssignmentV2.create] Création attribution unique pour jours: [1, 2, 3, 4, 5]
  Data: {
  "originalArgs": [
    "🔍 [RegularAssignmentV2.create] Création attribution unique pour jours: [1, 2, 3, 4, 5]"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:00:18.895Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔍 [validateAndConvertUuid] Entrée: fieldName="post_id", value="2a9a2244-1e0d-4062-8891-198d1cac2934", type=string
  Data: {
  "originalArgs": [
    "🔍 [validateAndConvertUuid] Entrée: fieldName=\"post_id\", value=\"2a9a2244-1e0d-4062-8891-198d1cac2934\", type=string"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:00:18.893Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ [RegularAssignments] Attribution créée avec succès: 0e248502-624d-4249-9764-2cb9b08768e9
  Data: {
  "originalArgs": [
    "✅ [RegularAssignments] Attribution créée avec succès:",
    "0e248502-624d-4249-9764-2cb9b08768e9"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:00:18.944Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ [RegularAssignmentV2.create] Attribution créée avec 5 jours
  Data: {
  "originalArgs": [
    "✅ [RegularAssignmentV2.create] Attribution créée avec 5 jours"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:00:18.943Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 45ms: 
      INSERT INTO regular_assignments (id, employ {"params":["0e248502-624d-4249-9764-2cb9b08768e9","604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","2a9a2244-1e0d-4062-8891-198d1cac2934",[1,2,3,4,5],"2025-07-14",null,true]}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 45ms:",
    "\n      INSERT INTO regular_assignments (id, employ",
    {
      "params": [
        "0e248502-624d-4249-9764-2cb9b08768e9",
        "604a1d0c-2141-4543-b7f9-7fc9adfa7b9a",
        "2a9a2244-1e0d-4062-8891-198d1cac2934",
        [
          1,
          2,
          3,
          4,
          5
        ],
        "2025-07-14",
        null,
        true
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:00:18.942Z"
}
[15/07/2025 09:00:19] [BACKEND] [INFO] ✅ Requête exécutée en 45ms: 
      INSERT INTO regular_assignments (id, employ {"params":["0e248502-624d-4249-9764-2cb9b08768e9","604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","2a9a2244-1e0d-4062-8891-198d1cac2934",[1,2,3,4,5],"2025-07-14",null,true]}
[15/07/2025 09:00:19] [FRONTEND] [LOG] ✅ [reassignRegularAssignmentFromDate] Nouvelle attribution créée avec succès
[15/07/2025 09:00:19] [FRONTEND] [LOG] 📂 [TeamCalendarApp] Chargement de l'état...
[15/07/2025 09:00:19] [FRONTEND] [LOG] 🔄 [reassignRegularAssignmentFromDate] Rechargement complet des données...
[15/07/2025 09:00:19] [FRONTEND] [LOG] 🌐 [TeamCalendarApp] Chargement depuis l'API...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT e.*, et.name as template_name 
      FROM employees e 
      LEFT JOIN employee_templates et ON e.template_id = et.id 
      ORDER BY e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT e.*, et.name as template_name \n      FROM employees e \n      LEFT JOIN employee_templates et ON e.template_id = et.id \n      ORDER BY e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:00:19.037Z"
}
[15/07/2025 09:00:19] [BACKEND] [INFO] ✅ Requête exécutée en 32ms: 
      SELECT e.*, et.name as template_name 
      {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 32ms: 
      SELECT e.*, et.name as template_name 
      {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 32ms:",
    "\n      SELECT e.*, et.name as template_name \n     ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:00:19.068Z"
}
[15/07/2025 09:00:19] [FRONTEND] [LOG] 👥 [TeamCalendarApp] 5 employés chargés
[15/07/2025 09:00:19] [FRONTEND] [LOG] 📦 [loadState] Chargement de tous les shifts depuis la base de données...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT s.*, e.name as employee_name, sp.label as post_label
      FROM shifts s
      LEFT JOIN employees e ON s.employee_id = e.id
      LEFT JOIN standard_posts sp ON s.post_id = sp.id
      ORDER BY s.date_key, e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT s.*, e.name as employee_name, sp.label as post_label\n      FROM shifts s\n      LEFT JOIN employees e ON s.employee_id = e.id\n      LEFT JOIN standard_posts sp ON s.post_id = sp.id\n      ORDER BY s.date_key, e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:00:19.099Z"
}
[15/07/2025 09:00:19] [BACKEND] [INFO] ✅ Requête exécutée en 27ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 27ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 27ms:",
    "\n      SELECT s.*, e.name as employee_name, sp.lab",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:00:19.125Z"
}
[15/07/2025 09:00:19] [FRONTEND] [LOG] 📅 [loadState] 0 shifts chargés depuis la base de données
[15/07/2025 09:00:19] [FRONTEND] [LOG] ✅ [loadState] Shifts organisés dans this.data.schedule
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:00:19.164Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 35ms: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 35ms:",
    "SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:00:19.200Z"
}
[15/07/2025 09:00:19] [BACKEND] [INFO] ✅ Requête exécutée en 35ms: SELECT * FROM standard_posts ORDER BY label {}
[15/07/2025 09:00:19] [FRONTEND] [LOG] 🏢 [TeamCalendarApp] 5 postes standards chargés
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:00:19.252Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 39ms: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 39ms:",
    "SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:00:19.291Z"
}
[15/07/2025 09:00:19] [BACKEND] [INFO] ✅ Requête exécutée en 39ms: SELECT * FROM app_settings ORDER BY setting_key {}
[15/07/2025 09:00:19] [FRONTEND] [LOG] ⚙️ [loadState] Paramètres chargés depuis API: {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT 
        ra.id,
        ra.employee_id,
        ra.post_id,
        ra.days_of_week,
        ra.start_date,
        ra.end_date,
        ra.is_active,
        ra.excluded_dates,
        e.name as employee_name,
        sp.label as post_label,
        sp.hours as post_hours,
        sp.type as post_type,
        array_length(ra.days_of_week, 1) as days_count
      FROM regular_assignments ra
      LEFT JOIN employees e ON ra.employee_id = e.id
      LEFT JOIN standard_posts sp ON ra.post_id = sp.id
      WHERE ra.is_active = true
      ORDER BY e.name, sp.label, ra.start_date
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT \n        ra.id,\n        ra.employee_id,\n        ra.post_id,\n        ra.days_of_week,\n        ra.start_date,\n        ra.end_date,\n        ra.is_active,\n        ra.excluded_dates,\n        e.name as employee_name,\n        sp.label as post_label,\n        sp.hours as post_hours,\n        sp.type as post_type,\n        array_length(ra.days_of_week, 1) as days_count\n      FROM regular_assignments ra\n      LEFT JOIN employees e ON ra.employee_id = e.id\n      LEFT JOIN standard_posts sp ON ra.post_id = sp.id\n      WHERE ra.is_active = true\n      ORDER BY e.name, sp.label, ra.start_date\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:00:19.327Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 57ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 57ms:",
    "\n      SELECT \n        ra.id,\n        ra.employee_",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:00:19.384Z"
}
[15/07/2025 09:00:19] [BACKEND] [INFO] ✅ Requête exécutée en 57ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
[15/07/2025 09:00:19] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","employee_id":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","post_id":"2a9a2244-1e0d-4062-8891-198d1cac2934","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-14T04:00:00.000Z","end_date":"2025-07-13T04:00:00.000Z","excluded_dates":[]}
[15/07/2025 09:00:19] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"0e248502-624d-4249-9764-2cb9b08768e9","employee_id":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","post_id":"2a9a2244-1e0d-4062-8891-198d1cac2934","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-14T04:00:00.000Z","end_date":null,"excluded_dates":[]}
[15/07/2025 09:00:19] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"0e248502-624d-4249-9764-2cb9b08768e9","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","selectedDays":[1,2,3,4,5],"isLimited":false,"startDate":"2025-07-14","endDate":null,"excludedDates":[]}
[15/07/2025 09:00:19] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-14","endDate":"2025-07-13","excludedDates":[]}
[15/07/2025 09:00:19] [FRONTEND] [LOG] 🔧 [fixInvalidAssignmentDates] Vérification des dates invalides...
[15/07/2025 09:00:19] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","employee_id":"59f5df3a-33ef-425c-bfa2-adca818cf94f","post_id":"2a9a2244-1e0d-4062-8891-198d1cac2934","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-15T04:00:00.000Z","end_date":"2025-07-13T04:00:00.000Z","excluded_dates":[]}
[15/07/2025 09:00:19] [FRONTEND] [LOG] 📋 [loadState] 3/3 assignations régulières valides chargées et normalisées
[15/07/2025 09:00:19] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-15","endDate":"2025-07-13","excludedDates":[]}
[15/07/2025 09:00:19] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 09:00:19] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:00:19] [FRONTEND] [WARN] ⚠️ [SETUP] Erreur lors de la destruction: Error: can't access property "Sortable1752584344465", el is null
destroy@http://localhost:5173/node_modules/.vite/deps/sortablejs.js?v=189de042:1812:5
setupDragAndDrop@http://localhost:5173/src/teamCalendarApp.ts:5808:33
attachAllEventListeners@http://localhost:5173/src/teamCalendarApp.ts:5358:10
actualRender@http://localhost:5173/src/teamCalendarApp.ts:2466:10
render/this._debouncedRender<@http://localhost:5173/src/teamCalendarApp.ts:2451:12
setTimeout handler*render@http://localhost:5173/src/teamCalendarApp.ts:2450:29
reassignRegularAssignmentFromDate@http://localhost:5173/src/teamCalendarApp.ts:11487:12
async*handlePermanentRegularAssignmentChange@http://localhost:5173/src/teamCalendarApp.ts:10885:18
showRegularAssignmentConfirmationMenu/<@http://localhost:5173/src/teamCalendarApp.ts:10850:12
EventListener.handleEvent*showRegularAssignmentConfirmationMenu@http://localhost:5173/src/teamCalendarApp.ts:10844:35
handleRegularAssignmentDrop@http://localhost:5173/src/teamCalendarApp.ts:10432:10
dropHandler@http://localhost:5173/src/teamCalendarApp.ts:6170:16
EventListener.handleEvent*setupCentralizedDropZone/<@http://localhost:5173/src/teamCalendarApp.ts:6232:11
setupCentralizedDropZone@http://localhost:5173/src/teamCalendarApp.ts:6103:18
setupPostDragDrop@http://localhost:5173/src/teamCalendarApp.ts:5913:10
renderUnifiedCalendar@http://localhost:5173/src/teamCalendarApp.ts:2935:10
actualRender@http://localhost:5173/src/teamCalendarApp.ts:2465:10
render/this._debouncedRender<@http://localhost:5173/src/teamCalendarApp.ts:2451:12
setTimeout handler*render@http://localhost:5173/src/teamCalendarApp.ts:2450:29
navigateWeek@http://localhost:5173/src/teamCalendarApp.ts:661:10
setupNavigationListeners/<@http://localhost:5173/src/teamCalendarApp.ts:2436:14
EventListener.handleEvent*setupNavigationListeners@http://localhost:5173/src/teamCalendarApp.ts:2434:15
init@http://localhost:5173/src/teamCalendarApp.ts:1047:10
async*initApp@http://localhost:5173/src/Agenda.tsx:40:31
setTimeout handler*AgendaFullscreen/<@http://localhost:5173/src/Agenda.tsx:50:29
react-stack-bottom-frame@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:17478:20
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
commitHookEffectListMount@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8460:122
commitHookPassiveMountEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8518:60
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10016:42
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55

[15/07/2025 09:00:19] [FRONTEND] [WARN] ⚠️ [fixInvalidAssignmentDates] Date invalide détectée pour l'assignation 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6:
[15/07/2025 09:00:19] [FRONTEND] [WARN]    Start: 2025-07-14 > End: 2025-07-13
[15/07/2025 09:00:19] [FRONTEND] [LOG] ✅ [fixInvalidAssignmentDates] Dates corrigées pour 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6:
[15/07/2025 09:00:19] [FRONTEND] [LOG]    Nouveau Start: 2025-07-13, Nouveau End: 2025-07-14
[15/07/2025 09:00:19] [FRONTEND] [WARN] ⚠️ [fixInvalidAssignmentDates] Date invalide détectée pour l'assignation ea8e0f4b-e5d5-4f22-a59d-200e2c776c76:
[15/07/2025 09:00:19] [FRONTEND] [WARN]    Start: 2025-07-15 > End: 2025-07-13
[15/07/2025 09:00:20] [FRONTEND] [LOG] ✅ [fixInvalidAssignmentDates] Dates corrigées pour ea8e0f4b-e5d5-4f22-a59d-200e2c776c76:
[15/07/2025 09:00:20] [FRONTEND] [LOG]    Nouveau Start: 2025-07-13, Nouveau End: 2025-07-15
[15/07/2025 09:00:20] [FRONTEND] [LOG] ✅ [fixInvalidAssignmentDates] 2 assignation(s) corrigée(s)
[15/07/2025 09:00:20] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] 0 grips ajoutés
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🧹 [cleanupDuplicateRegularShifts] Nettoyage des doublons de shifts réguliers...
[15/07/2025 09:00:20] [FRONTEND] [LOG] 📋 [loadState] Assignations régulières chargées (application différée)
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 09:00:20] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🧹 [cleanupInvalidAssignmentIds] Nettoyage des assignment_id invalides...
[15/07/2025 09:00:20] [FRONTEND] [LOG] ✅ [cleanupDuplicateRegularShifts] Aucun doublon détecté
[15/07/2025 09:00:20] [FRONTEND] [LOG] ✅ [cleanupInvalidAssignmentIds] Aucun assignment_id invalide détecté
[15/07/2025 09:00:20] [FRONTEND] [LOG] ✅ [loadState] État chargé depuis l'API avec succès
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🧹 [reassignRegularAssignmentFromDate] Nettoyage complet des shifts réguliers...
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🗑️ [reassignRegularAssignmentFromDate] Invalidation des caches...
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🔄 [reassignRegularAssignmentFromDate] Application des attributions avec la nouvelle logique...
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] Nettoyage des shifts réguliers existants...
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Cache invalidé pour forcer la régénération
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] 0 shifts réguliers supprimés
[15/07/2025 09:00:20] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Application pour semaine 2025-W29
[15/07/2025 09:00:20] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution 0e248502-624d-4249-9764-2cb9b08768e9 a une intersection avec cette semaine (2025-07-14 → ∞) vs (2025-07-20 → 2025-07-26)
[15/07/2025 09:00:20] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6 terminée avant cette semaine (endDate: 2025-07-14, semaine commence: 2025-07-20)
[15/07/2025 09:00:20] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 1 attributions actives sur 3 total
[15/07/2025 09:00:20] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution ea8e0f4b-e5d5-4f22-a59d-200e2c776c76 terminée avant cette semaine (endDate: 2025-07-15, semaine commence: 2025-07-20)
[15/07/2025 09:00:20] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"0e248502-624d-4249-9764-2cb9b08768e9","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","selectedDays":[1,2,3,4,5],"startDate":"2025-07-14","endDate":null,"excludedDates":[]}
[15/07/2025 09:00:20] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-20 (0)
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"0e248502-624d-4249-9764-2cb9b08768e9","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-20T04:00:00.000Z","dayDateKey":"2025-07-20","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5]}
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution 0e248502-624d-4249-9764-2cb9b08768e9: [{"dateKey":"2025-07-20","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-21","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-22","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-23","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-24","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-07-25","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-07-26","isWeekend":true,"dayOfWeek":6}]
[15/07/2025 09:00:20] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:00:20] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-20 ignoré pour attribution 0e248502-624d-4249-9764-2cb9b08768e9
[15/07/2025 09:00:20] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-21 (1)
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"0e248502-624d-4249-9764-2cb9b08768e9","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-21T04:00:00.000Z","dayDateKey":"2025-07-21","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5]}
[15/07/2025 09:00:20] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-21: {"assignmentId":"0e248502-624d-4249-9764-2cb9b08768e9","dayDateStr":"2025-07-21","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:20] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-21
[15/07/2025 09:00:20] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-21 validé, shifts existants: 0
[15/07/2025 09:00:20] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-21 pour Marie Martin
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"0e248502-624d-4249-9764-2cb9b08768e9","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-22T04:00:00.000Z","dayDateKey":"2025-07-22","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5]}
[15/07/2025 09:00:20] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-22 (2)
[15/07/2025 09:00:20] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-22: {"assignmentId":"0e248502-624d-4249-9764-2cb9b08768e9","dayDateStr":"2025-07-22","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:20] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-22
[15/07/2025 09:00:20] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-22 validé, shifts existants: 0
[15/07/2025 09:00:20] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-22 pour Marie Martin
[15/07/2025 09:00:20] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-23 (3)
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"0e248502-624d-4249-9764-2cb9b08768e9","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-23T04:00:00.000Z","dayDateKey":"2025-07-23","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5]}
[15/07/2025 09:00:20] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-23: {"assignmentId":"0e248502-624d-4249-9764-2cb9b08768e9","dayDateStr":"2025-07-23","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:20] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-23 pour Marie Martin
[15/07/2025 09:00:20] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-24 (4)
[15/07/2025 09:00:20] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-23
[15/07/2025 09:00:20] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-23 validé, shifts existants: 0
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"0e248502-624d-4249-9764-2cb9b08768e9","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-24T04:00:00.000Z","dayDateKey":"2025-07-24","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5]}
[15/07/2025 09:00:20] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-24 validé, shifts existants: 0
[15/07/2025 09:00:20] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-24
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-24: {"assignmentId":"0e248502-624d-4249-9764-2cb9b08768e9","dayDateStr":"2025-07-24","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:20] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[15/07/2025 09:00:20] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-24 pour Marie Martin
[15/07/2025 09:00:20] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-07-25 (5)
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"0e248502-624d-4249-9764-2cb9b08768e9","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-25T04:00:00.000Z","dayDateKey":"2025-07-25","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5]}
[15/07/2025 09:00:20] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[15/07/2025 09:00:20] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-25
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-25: {"assignmentId":"0e248502-624d-4249-9764-2cb9b08768e9","dayDateStr":"2025-07-25","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:20] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-25 validé, shifts existants: 0
[15/07/2025 09:00:20] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-25 pour Marie Martin
[15/07/2025 09:00:20] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-07-26 (6)
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"0e248502-624d-4249-9764-2cb9b08768e9","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-26T04:00:00.000Z","dayDateKey":"2025-07-26","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5]}
[15/07/2025 09:00:20] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:00:20] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-26 ignoré pour attribution 0e248502-624d-4249-9764-2cb9b08768e9
[15/07/2025 09:00:20] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 5 attributions appliquées pour la semaine 2025-W29
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Rendu demandé car 5 shifts créés
[15/07/2025 09:00:20] [FRONTEND] [LOG] ✅ [reassignRegularAssignmentFromDate] Attribution divisée avec succès
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🔄 [reassignRegularAssignmentFromDate] Rendu final...
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Matin: [1, 2, 3, 4, 5]
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[15/07/2025 09:00:20] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[15/07/2025 09:00:20] [FRONTEND] [LOG] 📝 [logModification] Enregistrement: {"id":"b15183ea-d2a2-4669-8c66-04cf193990ae","type":"regular-assignment","title":"Attribution régulière modifiée (permanent)","description":"Poste Matin transféré de Pierre Durand vers Marie Martin à partir du 13/07/2025","timestamp":"2025-07-15T13:00:19.459Z","employeeName":"Marie Martin","postLabel":"Poste Matin","date":"2025-07-14","assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","sourceEmployeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","targetEmployeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a"}
[15/07/2025 09:00:20] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 0e248502-624d-4249-9764-2cb9b08768e9
[15/07/2025 09:00:20] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:20] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 0e248502-624d-4249-9764-2cb9b08768e9
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 0e248502-624d-4249-9764-2cb9b08768e9
[15/07/2025 09:00:20] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 0e248502-624d-4249-9764-2cb9b08768e9
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 0e248502-624d-4249-9764-2cb9b08768e9
[15/07/2025 09:00:20] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[15/07/2025 09:00:20] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[15/07/2025 09:00:20] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 09:00:20] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 70 zones trouvées
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 70 zones de drop disponibles
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste b55a363e-2261-4255-ae7d-1ebe40fa9e82 configuré comme draggable
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 2a9a2244-1e0d-4062-8891-198d1cac2934 configuré comme draggable
[15/07/2025 09:00:20] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Nuit: [1, 2, 3, 4, 5]
[15/07/2025 09:00:20] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste b55a363e-2261-4255-ae7d-1ebe40fa9e82
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Soir: [1, 2, 3, 4, 5]
[15/07/2025 09:00:20] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff configuré comme draggable
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE1: [0, 6]
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3 configuré comme draggable
[15/07/2025 09:00:20] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE2: [0, 6]
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6a79ab18-319e-4a85-805e-f80d0de9a73f configuré comme draggable
[15/07/2025 09:00:20] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6a79ab18-319e-4a85-805e-f80d0de9a73f
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 2a9a2244-1e0d-4062-8891-198d1cac2934 configuré comme draggable
[15/07/2025 09:00:20] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste b55a363e-2261-4255-ae7d-1ebe40fa9e82 configuré comme draggable
[15/07/2025 09:00:20] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff configuré comme draggable
[15/07/2025 09:00:20] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste b55a363e-2261-4255-ae7d-1ebe40fa9e82
[15/07/2025 09:00:20] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff
[15/07/2025 09:00:21] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3 configuré comme draggable
[15/07/2025 09:00:21] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3
[15/07/2025 09:00:21] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6a79ab18-319e-4a85-805e-f80d0de9a73f configuré comme draggable
[15/07/2025 09:00:21] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6a79ab18-319e-4a85-805e-f80d0de9a73f
[15/07/2025 09:00:21] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 09:00:21] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:00:21] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:00:21] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:00:21] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:00:21] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 09:00:21] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (70 zones)
[15/07/2025 09:00:21] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[15/07/2025 09:00:21] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[15/07/2025 09:00:21] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 09:00:21] [FRONTEND] [LOG] ✅ [SETUP] Drag & drop des employés configuré (mode simplifié)
[15/07/2025 09:00:21] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 09:00:21] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 70 zones trouvées
[15/07/2025 09:00:21] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 70 zones de drop disponibles
[15/07/2025 09:00:21] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 09:00:21] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 2a9a2244-1e0d-4062-8891-198d1cac2934 configuré comme draggable
[15/07/2025 09:00:21] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 09:00:21] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste b55a363e-2261-4255-ae7d-1ebe40fa9e82 configuré comme draggable
[15/07/2025 09:00:21] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste b55a363e-2261-4255-ae7d-1ebe40fa9e82
[15/07/2025 09:00:21] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff configuré comme draggable
[15/07/2025 09:00:21] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff
[15/07/2025 09:00:21] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3 configuré comme draggable
[15/07/2025 09:00:21] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3
[15/07/2025 09:00:21] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6a79ab18-319e-4a85-805e-f80d0de9a73f configuré comme draggable
[15/07/2025 09:00:21] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6a79ab18-319e-4a85-805e-f80d0de9a73f
[15/07/2025 09:00:21] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 2a9a2244-1e0d-4062-8891-198d1cac2934 configuré comme draggable
[15/07/2025 09:00:21] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 09:00:21] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste b55a363e-2261-4255-ae7d-1ebe40fa9e82 configuré comme draggable
[15/07/2025 09:00:21] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste b55a363e-2261-4255-ae7d-1ebe40fa9e82
[15/07/2025 09:00:21] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff configuré comme draggable
[15/07/2025 09:00:21] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff
[15/07/2025 09:00:21] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3 configuré comme draggable
[15/07/2025 09:00:21] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3
[15/07/2025 09:00:21] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6a79ab18-319e-4a85-805e-f80d0de9a73f configuré comme draggable
[15/07/2025 09:00:21] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6a79ab18-319e-4a85-805e-f80d0de9a73f
[15/07/2025 09:00:21] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 09:00:21] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 09:00:21] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:00:21] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:00:21] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:00:21] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:00:21] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:00:21] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:00:21] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:00:21] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:00:21] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:00:21] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 09:00:21] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:00:21] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (70 zones)
[15/07/2025 09:00:21] [FRONTEND] [LOG] ⬅️ Clic sur précédent
[15/07/2025 09:00:21] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-13 (0)
[15/07/2025 09:00:21] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution 0e248502-624d-4249-9764-2cb9b08768e9: [{"dateKey":"2025-07-13","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-14","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-15","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-16","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-17","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-07-18","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-07-19","isWeekend":true,"dayOfWeek":6}]
[15/07/2025 09:00:21] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"0e248502-624d-4249-9764-2cb9b08768e9","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-13T04:00:00.000Z","dayDateKey":"2025-07-13","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:21] [FRONTEND] [LOG] 🧭 [navigateWeek] Direction: -1, Offset actuel: 1
[15/07/2025 09:00:21] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:00:21] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-13 ignoré pour attribution 0e248502-624d-4249-9764-2cb9b08768e9
[15/07/2025 09:00:21] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-14 (1)
[15/07/2025 09:00:21] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"0e248502-624d-4249-9764-2cb9b08768e9","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-14T04:00:00.000Z","dayDateKey":"2025-07-14","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:21] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:00:21] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-14: {"assignmentId":"0e248502-624d-4249-9764-2cb9b08768e9","dayDateStr":"2025-07-14","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:21] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-14
[15/07/2025 09:00:21] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-14 validé, shifts existants: 0
[15/07/2025 09:00:21] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-14 pour Marie Martin
[15/07/2025 09:00:21] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-15 (2)
[15/07/2025 09:00:21] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"0e248502-624d-4249-9764-2cb9b08768e9","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-15T04:00:00.000Z","dayDateKey":"2025-07-15","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:21] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:00:21] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-15: {"assignmentId":"0e248502-624d-4249-9764-2cb9b08768e9","dayDateStr":"2025-07-15","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:21] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-15
[15/07/2025 09:00:21] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-15 validé, shifts existants: 0
[15/07/2025 09:00:21] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-15 pour Marie Martin
[15/07/2025 09:00:21] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-16 (3)
[15/07/2025 09:00:21] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:00:21] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"0e248502-624d-4249-9764-2cb9b08768e9","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-16T04:00:00.000Z","dayDateKey":"2025-07-16","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-16: {"assignmentId":"0e248502-624d-4249-9764-2cb9b08768e9","dayDateStr":"2025-07-16","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:22] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-16
[15/07/2025 09:00:22] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-16 pour Marie Martin
[15/07/2025 09:00:22] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-16 validé, shifts existants: 0
[15/07/2025 09:00:22] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-17 (4)
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"0e248502-624d-4249-9764-2cb9b08768e9","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-17T04:00:00.000Z","dayDateKey":"2025-07-17","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-17: {"assignmentId":"0e248502-624d-4249-9764-2cb9b08768e9","dayDateStr":"2025-07-17","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:22] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-17
[15/07/2025 09:00:22] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-17 validé, shifts existants: 0
[15/07/2025 09:00:22] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-17 pour Marie Martin
[15/07/2025 09:00:22] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-07-18 (5)
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"0e248502-624d-4249-9764-2cb9b08768e9","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-18T04:00:00.000Z","dayDateKey":"2025-07-18","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-18: {"assignmentId":"0e248502-624d-4249-9764-2cb9b08768e9","dayDateStr":"2025-07-18","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:22] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-18
[15/07/2025 09:00:22] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-18 validé, shifts existants: 0
[15/07/2025 09:00:22] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-18 pour Marie Martin
[15/07/2025 09:00:22] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-07-19 (6)
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"0e248502-624d-4249-9764-2cb9b08768e9","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-19T04:00:00.000Z","dayDateKey":"2025-07-19","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:22] [FRONTEND] [LOG] 📋 [navigateWeek] Application des attributions pour la nouvelle semaine 2025-W28
[15/07/2025 09:00:22] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:00:22] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-19 ignoré pour attribution 0e248502-624d-4249-9764-2cb9b08768e9
[15/07/2025 09:00:22] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","selectedDays":[1,2,3,4,5],"startDate":"2025-07-13","endDate":"2025-07-14","excludedDates":[]}
[15/07/2025 09:00:22] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-13 (0)
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6: [{"dateKey":"2025-07-13","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-14","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-15","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-16","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-17","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-07-18","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-07-19","isWeekend":true,"dayOfWeek":6}]
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-13T04:00:00.000Z","dayDateKey":"2025-07-13","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:22] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:00:22] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-14 (1)
[15/07/2025 09:00:22] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-13 ignoré pour attribution 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-14T04:00:00.000Z","dayDateKey":"2025-07-14","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:00:22] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-15 (2)
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-15T04:00:00.000Z","dayDateKey":"2025-07-15","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-14: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","dayDateStr":"2025-07-14","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] Nettoyage des shifts réguliers existants...
[15/07/2025 09:00:22] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-15 après endDate 2025-07-14 (original: 2025-07-14) (attribution 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6)
[15/07/2025 09:00:22] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-16 (3)
[15/07/2025 09:00:22] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-14
[15/07/2025 09:00:22] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-15 ignoré pour attribution 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-15: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","dayDateStr":"2025-07-15","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-16T04:00:00.000Z","dayDateKey":"2025-07-16","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:00:22] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-17 (4)
[15/07/2025 09:00:22] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-16 ignoré pour attribution 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-17T04:00:00.000Z","dayDateKey":"2025-07-17","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:22] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-17 ignoré pour attribution 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:22] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-07-18 (5)
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-18T04:00:00.000Z","dayDateKey":"2025-07-18","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-17: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","dayDateStr":"2025-07-17","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:22] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-17 après endDate 2025-07-14 (original: 2025-07-14) (attribution 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6)
[15/07/2025 09:00:22] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-18 après endDate 2025-07-14 (original: 2025-07-14) (attribution 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6)
[15/07/2025 09:00:22] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-19 ignoré pour attribution 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] 0 shifts réguliers supprimés
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-19T04:00:00.000Z","dayDateKey":"2025-07-19","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:22] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-18 ignoré pour attribution 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:00:22] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","selectedDays":[1,2,3,4,5],"startDate":"2025-07-13","endDate":"2025-07-15","excludedDates":[]}
[15/07/2025 09:00:22] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-13 (0)
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution ea8e0f4b-e5d5-4f22-a59d-200e2c776c76: [{"dateKey":"2025-07-13","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-14","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-15","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-16","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-17","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-07-18","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-07-19","isWeekend":true,"dayOfWeek":6}]
[15/07/2025 09:00:22] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-14 validé, shifts existants: 0
[15/07/2025 09:00:22] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-14 pour Pierre Durand
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-14: {"assignmentId":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","dayDateStr":"2025-07-14","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-14T04:00:00.000Z","dayDateKey":"2025-07-14","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-16: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","dayDateStr":"2025-07-16","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:22] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-14 (1)
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-15T04:00:00.000Z","dayDateKey":"2025-07-15","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:00:22] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:00:22] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-13 ignoré pour attribution ea8e0f4b-e5d5-4f22-a59d-200e2c776c76
[15/07/2025 09:00:22] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-15 validé, shifts existants: 0
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Cache invalidé pour forcer la régénération
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-17T04:00:00.000Z","dayDateKey":"2025-07-17","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:22] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-16 (3)
[15/07/2025 09:00:22] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-15 pour Sophie Leblanc
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:00:22] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-17 (4)
[15/07/2025 09:00:22] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-16 ignoré pour attribution ea8e0f4b-e5d5-4f22-a59d-200e2c776c76
[15/07/2025 09:00:22] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-15
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-15: {"assignmentId":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","dayDateStr":"2025-07-15","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:22] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-17 après endDate 2025-07-15 (original: 2025-07-15) (attribution ea8e0f4b-e5d5-4f22-a59d-200e2c776c76)
[15/07/2025 09:00:22] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-17 ignoré pour attribution ea8e0f4b-e5d5-4f22-a59d-200e2c776c76
[15/07/2025 09:00:22] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-15 (2)
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-18T04:00:00.000Z","dayDateKey":"2025-07-18","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:22] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-07-18 (5)
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-17: {"assignmentId":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","dayDateStr":"2025-07-17","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:22] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-14 pour Sophie Leblanc
[15/07/2025 09:00:22] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-18 ignoré pour attribution ea8e0f4b-e5d5-4f22-a59d-200e2c776c76
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-18: {"assignmentId":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","dayDateStr":"2025-07-18","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:22] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-18 après endDate 2025-07-15 (original: 2025-07-15) (attribution ea8e0f4b-e5d5-4f22-a59d-200e2c776c76)
[15/07/2025 09:00:22] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-19 ignoré pour attribution ea8e0f4b-e5d5-4f22-a59d-200e2c776c76
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-16: {"assignmentId":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","dayDateStr":"2025-07-16","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:22] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-16 après endDate 2025-07-15 (original: 2025-07-15) (attribution ea8e0f4b-e5d5-4f22-a59d-200e2c776c76)
[15/07/2025 09:00:22] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:00:22] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 8 attributions appliquées pour la semaine 2025-W28
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-19T04:00:00.000Z","dayDateKey":"2025-07-19","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Rendu demandé car 8 shifts créés
[15/07/2025 09:00:22] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-07-19 (6)
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-13T04:00:00.000Z","dayDateKey":"2025-07-13","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:22] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Application pour semaine 2025-W28
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ea8e0f4b-e5d5-4f22-a59d-200e2c776c76","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-16T04:00:00.000Z","dayDateKey":"2025-07-16","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:22] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-07-19 (6)
[15/07/2025 09:00:22] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-14 validé, shifts existants: 0
[15/07/2025 09:00:22] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-14
[15/07/2025 09:00:22] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:00:22] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-16 après endDate 2025-07-14 (original: 2025-07-14) (attribution 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6)
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:00:22] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution 0e248502-624d-4249-9764-2cb9b08768e9 a une intersection avec cette semaine (2025-07-14 → ∞) vs (2025-07-13 → 2025-07-19)
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-18: {"assignmentId":"85d637c2-6ed3-447b-8a37-a0ba9fcf89b6","dayDateStr":"2025-07-18","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[15/07/2025 09:00:22] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6 a une intersection avec cette semaine (2025-07-13 → 2025-07-14) vs (2025-07-13 → 2025-07-19)
[15/07/2025 09:00:22] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[15/07/2025 09:00:22] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 0e248502-624d-4249-9764-2cb9b08768e9
[15/07/2025 09:00:22] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 0e248502-624d-4249-9764-2cb9b08768e9
[15/07/2025 09:00:22] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:22] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution ea8e0f4b-e5d5-4f22-a59d-200e2c776c76 a une intersection avec cette semaine (2025-07-13 → 2025-07-15) vs (2025-07-13 → 2025-07-19)
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 0e248502-624d-4249-9764-2cb9b08768e9
[15/07/2025 09:00:22] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:22] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 0e248502-624d-4249-9764-2cb9b08768e9
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 0e248502-624d-4249-9764-2cb9b08768e9
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6
[15/07/2025 09:00:22] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:22] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:22] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3 configuré comme draggable
[15/07/2025 09:00:22] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 3 attributions actives sur 3 total
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière ea8e0f4b-e5d5-4f22-a59d-200e2c776c76
[15/07/2025 09:00:22] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6a79ab18-319e-4a85-805e-f80d0de9a73f
[15/07/2025 09:00:22] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3
[15/07/2025 09:00:22] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6a79ab18-319e-4a85-805e-f80d0de9a73f configuré comme draggable
[15/07/2025 09:00:22] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"0e248502-624d-4249-9764-2cb9b08768e9","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","selectedDays":[1,2,3,4,5],"startDate":"2025-07-14","endDate":null,"excludedDates":[]}
[15/07/2025 09:00:22] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6a79ab18-319e-4a85-805e-f80d0de9a73f
[15/07/2025 09:00:22] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 09:00:23] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:00:23] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 76 zones de drop disponibles
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste b55a363e-2261-4255-ae7d-1ebe40fa9e82 configuré comme draggable
[15/07/2025 09:00:23] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste b55a363e-2261-4255-ae7d-1ebe40fa9e82
[15/07/2025 09:00:23] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff configuré comme draggable
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière ea8e0f4b-e5d5-4f22-a59d-200e2c776c76
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[15/07/2025 09:00:23] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[15/07/2025 09:00:23] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[15/07/2025 09:00:23] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 09:00:23] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] 0 grips ajoutés
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 09:00:23] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 76 zones trouvées
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 2a9a2244-1e0d-4062-8891-198d1cac2934 configuré comme draggable
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 76 zones de drop disponibles
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste b55a363e-2261-4255-ae7d-1ebe40fa9e82 configuré comme draggable
[15/07/2025 09:00:23] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste b55a363e-2261-4255-ae7d-1ebe40fa9e82
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff configuré comme draggable
[15/07/2025 09:00:23] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 2a9a2244-1e0d-4062-8891-198d1cac2934 configuré comme draggable
[15/07/2025 09:00:23] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste b55a363e-2261-4255-ae7d-1ebe40fa9e82 configuré comme draggable
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff configuré comme draggable
[15/07/2025 09:00:23] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste b55a363e-2261-4255-ae7d-1ebe40fa9e82
[15/07/2025 09:00:23] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3 configuré comme draggable
[15/07/2025 09:00:23] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6a79ab18-319e-4a85-805e-f80d0de9a73f configuré comme draggable
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 09:00:23] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:00:23] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:00:23] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:00:23] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (76 zones)
[15/07/2025 09:00:23] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[15/07/2025 09:00:23] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 76 zones trouvées
[15/07/2025 09:00:23] [FRONTEND] [LOG] ✅ [SETUP] Drag & drop des employés configuré (mode simplifié)
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 09:00:23] [FRONTEND] [WARN] ⚠️ [SETUP] Erreur lors de la destruction: (message répété, suppression des suivants) Error: can't access property "Sortable1752584344465", el is null
destroy@http://localhost:5173/node_modules/.vite/deps/sortablejs.js?v=189de042:1812:5
setupDragAndDrop@http://localhost:5173/src/teamCalendarApp.ts:5808:33
attachAllEventListeners@http://localhost:5173/src/teamCalendarApp.ts:5358:10
actualRender@http://localhost:5173/src/teamCalendarApp.ts:2466:10
render/this._debouncedRender<@http://localhost:5173/src/teamCalendarApp.ts:2451:12
setTimeout handler*render@http://localhost:5173/src/teamCalendarApp.ts:2450:29
navigateWeek@http://localhost:5173/src/teamCalendarApp.ts:661:10
setupNavigationListeners/<@http://localhost:5173/src/teamCalendarApp.ts:2432:14
EventListener.handleEvent*setupNavigationListeners@http://localhost:5173/src/teamCalendarApp.ts:2430:15
init@http://localhost:5173/src/teamCalendarApp.ts:1047:10
async*initApp@http://localhost:5173/src/Agenda.tsx:40:31
setTimeout handler*AgendaFullscreen/<@http://localhost:5173/src/Agenda.tsx:50:29
react-stack-bottom-frame@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:17478:20
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
commitHookEffectListMount@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8460:122
commitHookPassiveMountEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8518:60
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10016:42
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
doubleInvokeEffectsOnFiber@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11461:207
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
recursivelyTraverseAndDoubleInvokeEffectsInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11432:78
commitDoubleInvokeEffectsInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11469:55
flushPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11309:41
node_modules/react-dom/cjs/react-dom-client.development.js/commitRoot/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11060:32
performWorkUntilDeadline@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:36:58
EventHandlerNonNull*node_modules/scheduler/cjs/scheduler.development.js/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:156:9
node_modules/scheduler/cjs/scheduler.development.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:266:7
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/scheduler/index.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:277:24
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/react-dom/cjs/react-dom-client.development.js/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:15216:23
node_modules/react-dom/cjs/react-dom-client.development.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18060:7
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/react-dom/client.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18071:24
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18075:16

[15/07/2025 09:00:23] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 2a9a2244-1e0d-4062-8891-198d1cac2934 configuré comme draggable
[15/07/2025 09:00:23] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 09:00:23] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff
[15/07/2025 09:00:23] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3 configuré comme draggable
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6a79ab18-319e-4a85-805e-f80d0de9a73f configuré comme draggable
[15/07/2025 09:00:23] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6a79ab18-319e-4a85-805e-f80d0de9a73f
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 2a9a2244-1e0d-4062-8891-198d1cac2934 configuré comme draggable
[15/07/2025 09:00:23] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste b55a363e-2261-4255-ae7d-1ebe40fa9e82 configuré comme draggable
[15/07/2025 09:00:23] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste b55a363e-2261-4255-ae7d-1ebe40fa9e82
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff configuré comme draggable
[15/07/2025 09:00:23] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3 configuré comme draggable
[15/07/2025 09:00:23] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 09:00:23] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6a79ab18-319e-4a85-805e-f80d0de9a73f
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6a79ab18-319e-4a85-805e-f80d0de9a73f configuré comme draggable
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 09:00:23] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:00:23] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:00:23] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:00:23] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:00:23] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (76 zones)
[15/07/2025 09:00:23] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 09:00:23] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 09:00:23] [FRONTEND] [LOG] ➡️ Clic sur suivant
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🗑️ [applyRegularAssignmentsForCurrentWeek] Suppression shift régulier: 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a - 2025-07-22 - assignment: 0e248502-624d-4249-9764-2cb9b08768e9
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🗑️ [applyRegularAssignmentsForCurrentWeek] Suppression shift régulier: 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a - 2025-07-23 - assignment: 0e248502-624d-4249-9764-2cb9b08768e9
[15/07/2025 09:00:23] [FRONTEND] [LOG] 📋 [navigateWeek] Application des attributions pour la nouvelle semaine 2025-W29
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🗑️ [applyRegularAssignmentsForCurrentWeek] Suppression shift régulier: 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a - 2025-07-21 - assignment: 0e248502-624d-4249-9764-2cb9b08768e9
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] Nettoyage des shifts réguliers existants...
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🧭 [navigateWeek] Direction: 1, Offset actuel: 0
[15/07/2025 09:00:23] [FRONTEND] [LOG] 🗑️ [applyRegularAssignmentsForCurrentWeek] Suppression shift régulier: 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a - 2025-07-24 - assignment: 0e248502-624d-4249-9764-2cb9b08768e9
[15/07/2025 09:00:24] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-24 (4)
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-23 pour Marie Martin
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] 5 shifts réguliers supprimés
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🗑️ [applyRegularAssignmentsForCurrentWeek] Suppression shift régulier: 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a - 2025-07-25 - assignment: 0e248502-624d-4249-9764-2cb9b08768e9
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Cache invalidé pour forcer la régénération
[15/07/2025 09:00:24] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Application pour semaine 2025-W29
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-23
[15/07/2025 09:00:24] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-23 validé, shifts existants: 0
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-24
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Rendu demandé car 5 shifts créés
[15/07/2025 09:00:24] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-26 ignoré pour attribution 0e248502-624d-4249-9764-2cb9b08768e9
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:00:24] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 5 attributions appliquées pour la semaine 2025-W29
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-24: {"assignmentId":"0e248502-624d-4249-9764-2cb9b08768e9","dayDateStr":"2025-07-24","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:24] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"0e248502-624d-4249-9764-2cb9b08768e9","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-24T04:00:00.000Z","dayDateKey":"2025-07-24","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution 0e248502-624d-4249-9764-2cb9b08768e9 a une intersection avec cette semaine (2025-07-14 → ∞) vs (2025-07-20 → 2025-07-26)
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"0e248502-624d-4249-9764-2cb9b08768e9","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-26T04:00:00.000Z","dayDateKey":"2025-07-26","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:24] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 85d637c2-6ed3-447b-8a37-a0ba9fcf89b6 terminée avant cette semaine (endDate: 2025-07-14, semaine commence: 2025-07-20)
[15/07/2025 09:00:24] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-07-26 (6)
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-25 pour Marie Martin
[15/07/2025 09:00:24] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution ea8e0f4b-e5d5-4f22-a59d-200e2c776c76 terminée avant cette semaine (endDate: 2025-07-15, semaine commence: 2025-07-20)
[15/07/2025 09:00:24] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 1 attributions actives sur 3 total
[15/07/2025 09:00:24] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-25 validé, shifts existants: 0
[15/07/2025 09:00:24] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"0e248502-624d-4249-9764-2cb9b08768e9","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","selectedDays":[1,2,3,4,5],"startDate":"2025-07-14","endDate":null,"excludedDates":[]}
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-25
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution 0e248502-624d-4249-9764-2cb9b08768e9: [{"dateKey":"2025-07-20","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-21","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-22","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-23","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-24","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-07-25","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-07-26","isWeekend":true,"dayOfWeek":6}]
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-25: {"assignmentId":"0e248502-624d-4249-9764-2cb9b08768e9","dayDateStr":"2025-07-25","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"0e248502-624d-4249-9764-2cb9b08768e9","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-20T04:00:00.000Z","dayDateKey":"2025-07-20","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:00:24] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-20 (0)
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"0e248502-624d-4249-9764-2cb9b08768e9","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-25T04:00:00.000Z","dayDateKey":"2025-07-25","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:24] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-07-25 (5)
[15/07/2025 09:00:24] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-20 ignoré pour attribution 0e248502-624d-4249-9764-2cb9b08768e9
[15/07/2025 09:00:24] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-24 pour Marie Martin
[15/07/2025 09:00:24] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-21 (1)
[15/07/2025 09:00:24] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-24 validé, shifts existants: 0
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-23: {"assignmentId":"0e248502-624d-4249-9764-2cb9b08768e9","dayDateStr":"2025-07-23","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"0e248502-624d-4249-9764-2cb9b08768e9","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-23T04:00:00.000Z","dayDateKey":"2025-07-23","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-21: {"assignmentId":"0e248502-624d-4249-9764-2cb9b08768e9","dayDateStr":"2025-07-21","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"0e248502-624d-4249-9764-2cb9b08768e9","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-21T04:00:00.000Z","dayDateKey":"2025-07-21","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-21
[15/07/2025 09:00:24] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-23 (3)
[15/07/2025 09:00:24] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-21 validé, shifts existants: 0
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-22 pour Marie Martin
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-21 pour Marie Martin
[15/07/2025 09:00:24] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-22 validé, shifts existants: 0
[15/07/2025 09:00:24] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-22 (2)
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"0e248502-624d-4249-9764-2cb9b08768e9","postId":"2a9a2244-1e0d-4062-8891-198d1cac2934","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-22T04:00:00.000Z","dayDateKey":"2025-07-22","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-22
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-22: {"assignmentId":"0e248502-624d-4249-9764-2cb9b08768e9","dayDateStr":"2025-07-22","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff configuré comme draggable
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3 configuré comme draggable
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 0e248502-624d-4249-9764-2cb9b08768e9
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 0e248502-624d-4249-9764-2cb9b08768e9
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 0e248502-624d-4249-9764-2cb9b08768e9
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 0e248502-624d-4249-9764-2cb9b08768e9
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 2a9a2244-1e0d-4062-8891-198d1cac2934 -> "08:00-16:00"
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 0e248502-624d-4249-9764-2cb9b08768e9
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 70 zones trouvées
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 70 zones de drop disponibles
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 2a9a2244-1e0d-4062-8891-198d1cac2934 configuré comme draggable
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste b55a363e-2261-4255-ae7d-1ebe40fa9e82 configuré comme draggable
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste b55a363e-2261-4255-ae7d-1ebe40fa9e82
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff configuré comme draggable
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3 configuré comme draggable
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6a79ab18-319e-4a85-805e-f80d0de9a73f configuré comme draggable
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6a79ab18-319e-4a85-805e-f80d0de9a73f
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 2a9a2244-1e0d-4062-8891-198d1cac2934 configuré comme draggable
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff configuré comme draggable
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste b55a363e-2261-4255-ae7d-1ebe40fa9e82
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste b55a363e-2261-4255-ae7d-1ebe40fa9e82 configuré comme draggable
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3 configuré comme draggable
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6a79ab18-319e-4a85-805e-f80d0de9a73f
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6a79ab18-319e-4a85-805e-f80d0de9a73f configuré comme draggable
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (70 zones)
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [SETUP] Drag & drop des employés configuré (mode simplifié)
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 70 zones trouvées
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 70 zones de drop disponibles
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 2a9a2244-1e0d-4062-8891-198d1cac2934 configuré comme draggable
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste b55a363e-2261-4255-ae7d-1ebe40fa9e82 configuré comme draggable
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste b55a363e-2261-4255-ae7d-1ebe40fa9e82
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6a79ab18-319e-4a85-805e-f80d0de9a73f configuré comme draggable
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6a79ab18-319e-4a85-805e-f80d0de9a73f
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 2a9a2244-1e0d-4062-8891-198d1cac2934 configuré comme draggable
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 2a9a2244-1e0d-4062-8891-198d1cac2934
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste b55a363e-2261-4255-ae7d-1ebe40fa9e82 configuré comme draggable
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste b55a363e-2261-4255-ae7d-1ebe40fa9e82
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff configuré comme draggable
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 95dd1759-619a-4300-bb2c-d49a5c2f9eff
[15/07/2025 09:00:24] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3 configuré comme draggable
[15/07/2025 09:00:24] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3
[15/07/2025 09:00:25] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6a79ab18-319e-4a85-805e-f80d0de9a73f configuré comme draggable
[15/07/2025 09:00:25] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 09:00:25] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6a79ab18-319e-4a85-805e-f80d0de9a73f
[15/07/2025 09:00:25] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 09:00:25] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:00:25] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:00:25] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:00:25] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:00:25] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:00:25] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:00:25] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:00:25] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:00:25] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:00:25] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:00:25] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 09:00:25] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (70 zones)
[15/07/2025 09:00:25] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] 0 grips ajoutés
[15/07/2025 09:00:25] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 09:00:25] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées