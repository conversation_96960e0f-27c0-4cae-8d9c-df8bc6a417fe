[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:36:41.338Z"
}
[18/07/2025 11:36:41] [BACKEND] [INFO] [LOGS] Client connecté: 1752853001337
[18/07/2025 11:36:41] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[18/07/2025 11:34:22] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 6830f981-f642-4364-9b1d-0ff92e12fad2
[18/07/2025 11:34:22] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 6830f981-f642-4364-9b1d-0ff92e12fad2
[18/07/2025 11:34:22] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 0c74e25d-4692-46d7-b5aa-b84eee797971 -> "00:00-08:00"
[18/07/2025 11:34:22] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 6830f981-f642-4364-9b1d-0ff92e12fad2
[18/07/2025 11:34:22] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 0c74e25d-4692-46d7-b5aa-b84eee797971 -> "00:00-08:00"
[18/07/2025 11:34:22] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 6830f981-f642-4364-9b1d-0ff92e12fad2
[18/07/2025 11:34:22] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 6830f981-f642-4364-9b1d-0ff92e12fad2
[18/07/2025 11:34:22] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[18/07/2025 11:34:22] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[18/07/2025 11:34:22] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[18/07/2025 11:34:22] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[18/07/2025 11:34:22] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 70 zones de drop disponibles
[18/07/2025 11:34:22] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[18/07/2025 11:34:22] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 11:34:22] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 11:34:22] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 11:34:22] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 11:34:22] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 11:34:22] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 11:34:22] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 11:34:22] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 11:34:22] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 11:34:22] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 11:34:22] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 11:34:22] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 11:34:22] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 11:34:22] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[18/07/2025 11:34:22] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[18/07/2025 11:34:22] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 11:34:22] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 11:34:22] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 11:34:22] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 11:34:22] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[18/07/2025 11:34:22] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[18/07/2025 11:34:22] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[18/07/2025 11:34:22] [FRONTEND] [LOG] ✅ [SETUP] Drag & drop des employés configuré (mode simplifié)
[18/07/2025 11:34:22] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[18/07/2025 11:34:22] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 70 zones trouvées
[18/07/2025 11:34:22] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 70 zones de drop disponibles
[18/07/2025 11:34:22] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[18/07/2025 11:34:22] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 11:34:22] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 11:34:22] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 11:34:22] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 11:34:22] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 11:34:22] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 11:34:22] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 11:34:22] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 11:34:22] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 11:34:22] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 11:34:22] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 11:34:22] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 11:34:22] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 11:34:22] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[18/07/2025 11:34:22] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 11:34:22] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 11:34:22] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 11:34:22] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (70 zones)
[18/07/2025 11:34:22] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 11:34:22] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 11:34:22] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 11:34:22] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 11:34:31] [FRONTEND] [LOG] 🎯 [POST DRAG] Début drag poste: 0c74e25d-4692-46d7-b5aa-b84eee797971 (Poste Nuit)
[18/07/2025 11:34:31] [FRONTEND] [LOG] 🎨 [enableAgendaPreviewMode] Activation preview agenda pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 11:34:31] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[18/07/2025 11:34:31] [FRONTEND] [LOG] 🎯 [POST DRAG] DataTransfer configuré: {"postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","label":"Poste Nuit","types":["text/plain","application/x-post-id","postid","postlabel"]}
[18/07/2025 11:34:31] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] Ajout des fantômes pour Poste Nuit
[18/07/2025 11:34:31] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[18/07/2025 11:34:31] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] 25 fantômes créés
[18/07/2025 11:34:31] [FRONTEND] [LOG] 🖱️ [enableCellHoverTracking] Suivi activé sur 25 cellules
[18/07/2025 11:34:31] [FRONTEND] [LOG] ✅ [enableAgendaPreviewMode] Mode preview activé pour Poste Nuit
[18/07/2025 11:34:31] [FRONTEND] [LOG] 🎯 [POST DRAG] 70 zones préparées pour le drop
[18/07/2025 11:34:31] [FRONTEND] [LOG] 🎯 [POST DRAG] Début drag poste: 0c74e25d-4692-46d7-b5aa-b84eee797971 (Poste Nuit)
[18/07/2025 11:34:31] [FRONTEND] [LOG] 🎯 [POST DRAG] DataTransfer configuré: {"postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","label":"Poste Nuit","types":["text/plain","application/x-post-id","postid","postlabel"]}
[18/07/2025 11:34:31] [FRONTEND] [LOG] 🎨 [enableAgendaPreviewMode] Activation preview agenda pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 11:34:31] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[18/07/2025 11:34:31] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[18/07/2025 11:34:31] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] Ajout des fantômes pour Poste Nuit
[18/07/2025 11:34:31] [FRONTEND] [LOG] ✅ [enableAgendaPreviewMode] Mode preview activé pour Poste Nuit
[18/07/2025 11:34:31] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] 25 fantômes créés
[18/07/2025 11:34:31] [FRONTEND] [LOG] 🖱️ [enableCellHoverTracking] Suivi activé sur 50 cellules
[18/07/2025 11:34:31] [FRONTEND] [LOG] 🎯 [POST DRAG] 70 zones préparées pour le drop
[18/07/2025 11:34:31] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 59f5df3a-33ef-425c-bfa2-adca818cf94f, types: text/plain, application/x-post-id, postid, postlabel
[18/07/2025 11:34:31] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 18fe9cd1-e8a5-44e3-90b3-a856086ce27e, types: text/plain, application/x-post-id, postid, postlabel
[18/07/2025 11:34:31] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 18fe9cd1-e8a5-44e3-90b3-a856086ce27e, types: text/plain, application/x-post-id, postid, postlabel
[18/07/2025 11:34:31] [FRONTEND] [LOG] 🎯 [DROP] ✅ Drop reçu sur employé: Pierre Durand
[18/07/2025 11:34:31] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[18/07/2025 11:34:31] [FRONTEND] [LOG] ✅ [DROP] PostId trouvé via application/x-post-id: "0c74e25d-4692-46d7-b5aa-b84eee797971"
[18/07/2025 11:34:31] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[18/07/2025 11:34:31] [FRONTEND] [LOG] 📦 [DROP] Nouveau poste: 0c74e25d-4692-46d7-b5aa-b84eee797971 → 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 11:34:31] [FRONTEND] [LOG] 🎯 [openAssignmentContextModal] Poste brut: 0c74e25d-4692-46d7-b5aa-b84eee797971, Employé: 18fe9cd1-e8a5-44e3-90b3-a856086ce27e, Contexte: null
[18/07/2025 11:34:31] [FRONTEND] [LOG] ℹ️ [openAssignmentContextModal] Option "Attribution unique (cellule)" masquée - contexte inapproprié
[18/07/2025 11:34:31] [FRONTEND] [LOG] ✅ [DROP] Modal d'assignation ouvert
[18/07/2025 11:34:31] [FRONTEND] [LOG] 🎯 [POST DRAG] Fin drag poste: 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 11:34:31] [FRONTEND] [LOG] 📅 [openAssignmentContextModal] Date initialisée: 2025-07-18
[18/07/2025 11:34:31] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[18/07/2025 11:34:31] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[18/07/2025 11:34:31] [FRONTEND] [LOG] 🎯 [POST DRAG] Fin drag poste: 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 11:34:31] [FRONTEND] [LOG] 🎯 [POST DRAG] Nettoyage des 70 zones
[18/07/2025 11:34:31] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[18/07/2025 11:34:31] [FRONTEND] [LOG] 🎯 [POST DRAG] Nettoyage des 70 zones
[18/07/2025 11:34:31] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[18/07/2025 11:36:36] [FRONTEND] [DEBUG] [vite] connected.
[18/07/2025 11:36:36] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:36:35.981Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 11:36:36] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:36:36.114Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 11:36:36] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[18/07/2025 11:36:36] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:36:36.278Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 11:36:36] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:36:36.121Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 11:36:36] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[18/07/2025 11:36:36] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[18/07/2025 11:36:36] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[18/07/2025 11:36:36] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[18/07/2025 11:36:36] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[18/07/2025 11:36:36] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[18/07/2025 11:36:36] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[18/07/2025 11:36:36] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[18/07/2025 11:36:36] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[18/07/2025 11:36:36] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[18/07/2025 11:36:36] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[18/07/2025 11:36:36] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[18/07/2025 11:36:36] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[18/07/2025 11:36:36] [FRONTEND] [LOG] 🧪 [TEST] Script de test de la modale drag & drop chargé
[18/07/2025 11:36:36] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[18/07/2025 11:36:36] [FRONTEND] [LOG] - testAssignmentModal() : Tester l'ouverture de la modale
[18/07/2025 11:36:36] [FRONTEND] [LOG] - testButtonListeners() : Tester les listeners des boutons
[18/07/2025 11:36:37] [FRONTEND] [LOG] - diagnoseModalIssues() : Diagnostiquer les problèmes
[18/07/2025 11:36:37] [FRONTEND] [LOG] - forceReconfigureListeners() : Reconfigurer les listeners
[18/07/2025 11:36:37] [FRONTEND] [LOG] - testButtonClicks() : Tester les clics sur les boutons
[18/07/2025 11:36:37] [FRONTEND] [LOG] 🔍 [VALIDATION] Script de validation de la correction modale chargé
[18/07/2025 11:36:37] [FRONTEND] [LOG] ✅ [VALIDATION] Fonctions de validation disponibles:
[18/07/2025 11:36:37] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[18/07/2025 11:36:37] [FRONTEND] [LOG] - validateMainFix() : Valider la correction principale
[18/07/2025 11:36:37] [FRONTEND] [LOG] - validateListeners() : Valider les listeners
[18/07/2025 11:36:37] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[18/07/2025 11:36:37] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[18/07/2025 11:36:37] [FRONTEND] [LOG] - runCompleteValidation() : Validation complète
[18/07/2025 11:36:37] [FRONTEND] [LOG] - testEndToEnd() : Test end-to-end complet
[18/07/2025 11:36:37] [FRONTEND] [LOG] 🧪 [TEST] Script de test pour la correction du fork modal chargé
[18/07/2025 11:36:37] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[18/07/2025 11:36:37] [FRONTEND] [LOG] - testDropDateDetection() : Tester la détection de date
[18/07/2025 11:36:37] [FRONTEND] [LOG] - testForkModalWithDate() : Tester le modal avec date
[18/07/2025 11:36:37] [FRONTEND] [LOG] - testCompleteForkFlow() : Tester le flux complet
[18/07/2025 11:36:37] [FRONTEND] [LOG] - diagnoseForkModalIssues() : Diagnostic complet
[18/07/2025 11:36:37] [FRONTEND] [LOG] 🧪 [DATE-TEST] Script de validation des corrections de date chargé
[18/07/2025 11:36:37] [FRONTEND] [LOG] ✅ [DATE-TEST] Fonctions de validation des dates disponibles:
[18/07/2025 11:36:37] [FRONTEND] [LOG] - testSafeDateConversion() : Tester la conversion sécurisée
[18/07/2025 11:36:37] [FRONTEND] [LOG] - testForkDateLogic() : Tester la logique de fork
[18/07/2025 11:36:37] [FRONTEND] [LOG] - testTimezoneEnhancements() : Tester les améliorations timezone
[18/07/2025 11:36:37] [FRONTEND] [LOG] - testDropDateDetectionWithTimezone() : Tester la détection avec timezone
[18/07/2025 11:36:37] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:36:36.818Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 11:36:37] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[18/07/2025 11:36:37] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:36:36.974Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 11:36:37] [FRONTEND] [LOG] 🔧 [GRIP-FIX] Script de diagnostic et correction des GRIP handles chargé
[18/07/2025 11:36:37] [FRONTEND] [LOG] ✅ [GRIP-FIX] Fonctions de diagnostic et correction disponibles:
[18/07/2025 11:36:37] [FRONTEND] [LOG] - diagnoseGripHandles() : Diagnostic complet
[18/07/2025 11:36:37] [FRONTEND] [LOG] - forceRecreateGripHandles() : Recréation forcée
[18/07/2025 11:36:37] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:36:36.825Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 11:36:37] [FRONTEND] [LOG] - validateDOMElements() : Valider les éléments DOM
[18/07/2025 11:36:37] [FRONTEND] [LOG] - testSpecificGrip(assignmentId) : Test spécifique
[18/07/2025 11:36:37] [FRONTEND] [LOG] - autoFixGripIssues() : Correction automatique
[18/07/2025 11:36:37] [FRONTEND] [LOG] - startGripMonitoring() : Surveillance continue
[18/07/2025 11:36:37] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[18/07/2025 11:36:37] [FRONTEND] [LOG] [LogsPage] Fermeture de la connexion SSE.
[18/07/2025 11:36:37] [BACKEND] [INFO] [LOGS] Client connecté: 1752852997075
[18/07/2025 11:36:37] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:36:37.076Z",
  "originalArgs": [
    "[query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ]
}
[18/07/2025 11:36:37] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[18/07/2025 11:36:37] [FRONTEND] [LOG] - runDateValidationSuite() : Suite complète de validation
[18/07/2025 11:36:37] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:36:37.143Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 11:36:37] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[18/07/2025 11:36:37] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:36:37.015Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 11:36:37] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:36:37.202Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 11:36:37] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 127ms: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:36:37.203Z",
  "originalArgs": [
    "✅ Requête exécutée en 127ms:",
    "SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ]
}
[18/07/2025 11:36:37] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:36:37.202Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 11:36:37] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:36:37.016Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[18/07/2025 11:36:38] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[18/07/2025 11:36:38] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic des problèmes de modale...
[18/07/2025 11:36:38] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Éléments DOM: {"modal":false,"closeBtn":false,"cancelBtn":false,"confirmBtn":false,"radioButtons":0}
[18/07/2025 11:36:38] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] TeamCalendarApp: {"exists":false,"hasModalManager":false,"hasOpenFunction":false,"hasHandleFunction":false}
[18/07/2025 11:36:39] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation automatique...
[18/07/2025 11:36:39] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation complète...
[18/07/2025 11:36:39] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation de la correction principale...
[18/07/2025 11:36:39] [FRONTEND] [ERROR] ❌ [VALIDATION] TeamCalendarApp non disponible
[18/07/2025 11:36:39] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des éléments DOM...
[18/07/2025 11:36:39] [FRONTEND] [LOG] 📋 [VALIDATION] Éléments DOM: {"modal":false,"closeBtn":false,"cancelBtn":false,"confirmBtn":false,"radioButtons":false}
[18/07/2025 11:36:39] [FRONTEND] [ERROR] ❌ [VALIDATION] Éléments DOM manquants
[18/07/2025 11:36:39] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des listeners...
[18/07/2025 11:36:39] [FRONTEND] [ERROR] ❌ [VALIDATION] Erreur lors de setupAssignmentContextModal: Error: can't access property "ModalManager", window.TeamCalendarApp is undefined
validateListeners@http://localhost:5173/validate-modal-fix.js:87:9
runCompleteValidation@http://localhost:5173/validate-modal-fix.js:103:20
@http://localhost:5173/validate-modal-fix.js:186:5

[18/07/2025 11:36:39] [FRONTEND] [LOG] 📊 [VALIDATION] Résultats de validation: {"mainFix":false,"domElements":false,"listeners":false}
[18/07/2025 11:36:39] [FRONTEND] [ERROR] ❌ [VALIDATION] Certaines validations ont échoué
[18/07/2025 11:36:39] [FRONTEND] [LOG] 🔧 [VALIDATION] Actions recommandées:
[18/07/2025 11:36:39] [FRONTEND] [LOG] - Vérifier que TeamCalendarApp et ModalManager sont correctement initialisés
[18/07/2025 11:36:39] [FRONTEND] [LOG] - Vérifier que la modale assignment-context-modal existe dans le DOM
[18/07/2025 11:36:39] [FRONTEND] [LOG] - Vérifier les erreurs dans setupAssignmentContextModal
[18/07/2025 11:36:39] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[18/07/2025 11:36:39] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic complet du fork modal...
[18/07/2025 11:36:39] [FRONTEND] [LOG] 📊 [DIAGNOSTIC] Résultats: {"teamCalendarApp":false,"detectDropDateFunction":false,"showConfirmationMenuFunction":false,"handleRegularAssignmentDropFunction":false,"regularAssignments":0,"employees":0}
[18/07/2025 11:36:39] [FRONTEND] [ERROR] ❌ [DIAGNOSTIC] Fonctions manquantes détectées
[18/07/2025 11:36:40] [BACKEND] [INFO] [LOGS] Client déconnecté: 1752852997075
[18/07/2025 11:36:40] [FRONTEND] [ERROR] [LogsPage] Erreur SSE: {"isTrusted":true}
[18/07/2025 11:36:40] [FRONTEND] [DEBUG] [vite] connected.
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 119ms: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 119ms:",
    "SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:36:41.457Z"
}
[18/07/2025 11:36:41] [BACKEND] [INFO] ✅ Requête exécutée en 119ms: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
[18/07/2025 11:36:41] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[18/07/2025 11:36:41] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[18/07/2025 11:36:41] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[18/07/2025 11:36:41] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[18/07/2025 11:36:41] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[18/07/2025 11:36:41] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[18/07/2025 11:36:41] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[18/07/2025 11:36:41] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[18/07/2025 11:36:41] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[18/07/2025 11:36:41] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[18/07/2025 11:36:41] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[18/07/2025 11:36:41] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[18/07/2025 11:36:41] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[18/07/2025 11:36:41] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[18/07/2025 11:36:41] [FRONTEND] [LOG] 🧪 [TEST] Script de test de la modale drag & drop chargé
[18/07/2025 11:36:41] [FRONTEND] [LOG] - testAssignmentModal() : Tester l'ouverture de la modale
[18/07/2025 11:36:41] [FRONTEND] [LOG] - testButtonListeners() : Tester les listeners des boutons
[18/07/2025 11:36:41] [FRONTEND] [LOG] - diagnoseModalIssues() : Diagnostiquer les problèmes
[18/07/2025 11:36:41] [FRONTEND] [LOG] - forceReconfigureListeners() : Reconfigurer les listeners
[18/07/2025 11:36:41] [FRONTEND] [LOG] 🔍 [VALIDATION] Script de validation de la correction modale chargé
[18/07/2025 11:36:41] [FRONTEND] [LOG] - testButtonClicks() : Tester les clics sur les boutons
[18/07/2025 11:36:41] [FRONTEND] [LOG] ✅ [VALIDATION] Fonctions de validation disponibles:
[18/07/2025 11:36:41] [FRONTEND] [LOG] - validateMainFix() : Valider la correction principale
[18/07/2025 11:36:41] [FRONTEND] [LOG] - validateDOMElements() : Valider les éléments DOM
[18/07/2025 11:36:41] [FRONTEND] [LOG] - validateListeners() : Valider les listeners
[18/07/2025 11:36:41] [FRONTEND] [LOG] - runCompleteValidation() : Validation complète
[18/07/2025 11:36:41] [FRONTEND] [LOG] 🧪 [TEST] Script de test pour la correction du fork modal chargé
[18/07/2025 11:36:41] [FRONTEND] [LOG] - testEndToEnd() : Test end-to-end complet
[18/07/2025 11:36:42] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[18/07/2025 11:36:42] [FRONTEND] [LOG] - diagnoseForkModalIssues() : Diagnostic complet
[18/07/2025 11:36:42] [FRONTEND] [LOG] - testDropDateDetection() : Tester la détection de date
[18/07/2025 11:36:42] [FRONTEND] [LOG] - testForkModalWithDate() : Tester le modal avec date
[18/07/2025 11:36:42] [FRONTEND] [LOG] - testCompleteForkFlow() : Tester le flux complet
[18/07/2025 11:36:42] [FRONTEND] [LOG] 🧪 [DATE-TEST] Script de validation des corrections de date chargé
[18/07/2025 11:36:42] [FRONTEND] [LOG] ✅ [DATE-TEST] Fonctions de validation des dates disponibles:
[18/07/2025 11:36:42] [FRONTEND] [LOG] - testSafeDateConversion() : Tester la conversion sécurisée
[18/07/2025 11:36:42] [FRONTEND] [LOG] - testForkDateLogic() : Tester la logique de fork
[18/07/2025 11:36:42] [FRONTEND] [LOG] - testTimezoneEnhancements() : Tester les améliorations timezone
[18/07/2025 11:36:42] [FRONTEND] [LOG] - testDropDateDetectionWithTimezone() : Tester la détection avec timezone
[18/07/2025 11:36:42] [FRONTEND] [LOG] - runDateValidationSuite() : Suite complète de validation
[18/07/2025 11:36:42] [FRONTEND] [LOG] 🔧 [GRIP-FIX] Script de diagnostic et correction des GRIP handles chargé
[18/07/2025 11:36:42] [FRONTEND] [LOG] ✅ [GRIP-FIX] Fonctions de diagnostic et correction disponibles:
[18/07/2025 11:36:42] [FRONTEND] [LOG] - forceRecreateGripHandles() : Recréation forcée
[18/07/2025 11:36:42] [FRONTEND] [LOG] - diagnoseGripHandles() : Diagnostic complet
[18/07/2025 11:36:42] [FRONTEND] [LOG] - autoFixGripIssues() : Correction automatique
[18/07/2025 11:36:42] [FRONTEND] [LOG] - testSpecificGrip(assignmentId) : Test spécifique
[18/07/2025 11:36:42] [FRONTEND] [LOG] - startGripMonitoring() : Surveillance continue
[18/07/2025 11:36:42] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[18/07/2025 11:36:42] [FRONTEND] [LOG] [LogsPage] Fermeture de la connexion SSE.
[18/07/2025 11:36:42] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[18/07/2025 11:36:43] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[18/07/2025 11:36:43] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] TeamCalendarApp: {"exists":false,"hasModalManager":false,"hasOpenFunction":false,"hasHandleFunction":false}
[18/07/2025 11:36:43] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic des problèmes de modale...
[18/07/2025 11:36:43] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Éléments DOM: {"modal":false,"closeBtn":false,"cancelBtn":false,"confirmBtn":false,"radioButtons":0}
[18/07/2025 11:36:44] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des éléments DOM...
[18/07/2025 11:36:44] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation de la correction principale...
[18/07/2025 11:36:44] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation automatique...
[18/07/2025 11:36:44] [FRONTEND] [ERROR] ❌ [VALIDATION] TeamCalendarApp non disponible
[18/07/2025 11:36:44] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation complète...
[18/07/2025 11:36:44] [FRONTEND] [DEBUG] [vite] connected.
[18/07/2025 11:36:44] [FRONTEND] [LOG] 📋 [VALIDATION] Éléments DOM: {"modal":false,"closeBtn":false,"cancelBtn":false,"confirmBtn":false,"radioButtons":false}
[18/07/2025 11:36:44] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des listeners...
[18/07/2025 11:36:44] [FRONTEND] [ERROR] ❌ [VALIDATION] Éléments DOM manquants
[18/07/2025 11:36:44] [FRONTEND] [ERROR] ❌ [VALIDATION] Erreur lors de setupAssignmentContextModal: Error: can't access property "ModalManager", window.TeamCalendarApp is undefined
validateListeners@http://localhost:5173/validate-modal-fix.js:87:9
runCompleteValidation@http://localhost:5173/validate-modal-fix.js:103:20
@http://localhost:5173/validate-modal-fix.js:186:5

[18/07/2025 11:36:44] [FRONTEND] [LOG] 📊 [VALIDATION] Résultats de validation: {"mainFix":false,"domElements":false,"listeners":false}
[18/07/2025 11:36:44] [FRONTEND] [LOG] - Vérifier que TeamCalendarApp et ModalManager sont correctement initialisés
[18/07/2025 11:36:44] [FRONTEND] [LOG] 🔧 [VALIDATION] Actions recommandées:
[18/07/2025 11:36:44] [FRONTEND] [LOG] - Vérifier que la modale assignment-context-modal existe dans le DOM
[18/07/2025 11:36:44] [FRONTEND] [ERROR] ❌ [VALIDATION] Certaines validations ont échoué
[18/07/2025 11:36:44] [FRONTEND] [LOG] - Vérifier les erreurs dans setupAssignmentContextModal
[18/07/2025 11:36:44] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[18/07/2025 11:36:44] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic complet du fork modal...
[18/07/2025 11:36:44] [FRONTEND] [ERROR] ❌ [DIAGNOSTIC] Fonctions manquantes détectées
[18/07/2025 11:36:44] [FRONTEND] [LOG] 📊 [DIAGNOSTIC] Résultats: {"teamCalendarApp":false,"detectDropDateFunction":false,"showConfirmationMenuFunction":false,"handleRegularAssignmentDropFunction":false,"regularAssignments":0,"employees":0}
[18/07/2025 11:36:45] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[18/07/2025 11:36:45] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[18/07/2025 11:36:45] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[18/07/2025 11:36:45] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[18/07/2025 11:36:45] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[18/07/2025 11:36:45] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[18/07/2025 11:36:45] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[18/07/2025 11:36:45] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[18/07/2025 11:36:45] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[18/07/2025 11:36:45] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[18/07/2025 11:36:45] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[18/07/2025 11:36:45] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[18/07/2025 11:36:45] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[18/07/2025 11:36:45] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[18/07/2025 11:36:45] [FRONTEND] [LOG] 🧪 [TEST] Script de test de la modale drag & drop chargé
[18/07/2025 11:36:45] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[18/07/2025 11:36:45] [FRONTEND] [LOG] - testAssignmentModal() : Tester l'ouverture de la modale
[18/07/2025 11:36:45] [FRONTEND] [LOG] - testButtonListeners() : Tester les listeners des boutons
[18/07/2025 11:36:45] [FRONTEND] [LOG] - forceReconfigureListeners() : Reconfigurer les listeners
[18/07/2025 11:36:45] [FRONTEND] [LOG] - diagnoseModalIssues() : Diagnostiquer les problèmes
[18/07/2025 11:36:45] [FRONTEND] [LOG] - testButtonClicks() : Tester les clics sur les boutons
[18/07/2025 11:36:46] [FRONTEND] [LOG] 🔍 [VALIDATION] Script de validation de la correction modale chargé
[18/07/2025 11:36:46] [FRONTEND] [LOG] - validateMainFix() : Valider la correction principale
[18/07/2025 11:36:46] [FRONTEND] [LOG] - validateDOMElements() : Valider les éléments DOM
[18/07/2025 11:36:46] [FRONTEND] [LOG] ✅ [VALIDATION] Fonctions de validation disponibles:
[18/07/2025 11:36:46] [FRONTEND] [LOG] - validateListeners() : Valider les listeners
[18/07/2025 11:36:46] [FRONTEND] [LOG] - runCompleteValidation() : Validation complète
[18/07/2025 11:36:46] [FRONTEND] [LOG] - testEndToEnd() : Test end-to-end complet
[18/07/2025 11:36:46] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[18/07/2025 11:36:46] [FRONTEND] [LOG] 🧪 [TEST] Script de test pour la correction du fork modal chargé
[18/07/2025 11:36:46] [FRONTEND] [LOG] - testDropDateDetection() : Tester la détection de date
[18/07/2025 11:36:46] [FRONTEND] [LOG] - testForkModalWithDate() : Tester le modal avec date
[18/07/2025 11:36:46] [FRONTEND] [LOG] - testCompleteForkFlow() : Tester le flux complet
[18/07/2025 11:36:46] [FRONTEND] [LOG] - diagnoseForkModalIssues() : Diagnostic complet
[18/07/2025 11:36:46] [FRONTEND] [LOG] 🧪 [DATE-TEST] Script de validation des corrections de date chargé
[18/07/2025 11:36:46] [FRONTEND] [LOG] ✅ [DATE-TEST] Fonctions de validation des dates disponibles:
[18/07/2025 11:36:46] [FRONTEND] [LOG] - testSafeDateConversion() : Tester la conversion sécurisée
[18/07/2025 11:36:46] [FRONTEND] [LOG] - testForkDateLogic() : Tester la logique de fork
[18/07/2025 11:36:46] [FRONTEND] [LOG] - testTimezoneEnhancements() : Tester les améliorations timezone
[18/07/2025 11:36:46] [FRONTEND] [LOG] - runDateValidationSuite() : Suite complète de validation
[18/07/2025 11:36:46] [FRONTEND] [LOG] 🔧 [GRIP-FIX] Script de diagnostic et correction des GRIP handles chargé
[18/07/2025 11:36:46] [FRONTEND] [LOG] - testDropDateDetectionWithTimezone() : Tester la détection avec timezone
[18/07/2025 11:36:46] [FRONTEND] [LOG] ✅ [GRIP-FIX] Fonctions de diagnostic et correction disponibles:
[18/07/2025 11:36:46] [FRONTEND] [LOG] - diagnoseGripHandles() : Diagnostic complet
[18/07/2025 11:36:46] [FRONTEND] [LOG] - testSpecificGrip(assignmentId) : Test spécifique
[18/07/2025 11:36:46] [FRONTEND] [LOG] - forceRecreateGripHandles() : Recréation forcée
[18/07/2025 11:36:46] [FRONTEND] [LOG] - autoFixGripIssues() : Correction automatique
[18/07/2025 11:36:46] [FRONTEND] [LOG] - startGripMonitoring() : Surveillance continue
[18/07/2025 11:36:46] [FRONTEND] [LOG] 🧹 [Agenda] Nettoyage composant Agenda
[18/07/2025 11:36:46] [FRONTEND] [LOG] 🚀 [Agenda] Initialisation TeamCalendarApp...
[18/07/2025 11:36:46] [FRONTEND] [LOG] 🚀 [init] Initialisation de TeamCalendarApp...
[18/07/2025 11:36:46] [FRONTEND] [LOG] 🔄 [Agenda] Réinitialisation nécessaire (employés non chargés)
[18/07/2025 11:36:46] [FRONTEND] [LOG] 🧹 [clearRenderCache] Nettoyage du cache de rendu
[18/07/2025 11:36:46] [FRONTEND] [LOG] ✅ [clearRenderCache] Cache de rendu nettoyé
[18/07/2025 11:36:46] [FRONTEND] [LOG] 🔌 [ConnectionIndicator] Création de l'indicateur de connexion
[18/07/2025 11:36:46] [FRONTEND] [LOG] 🚀 [DATE-TEST] Lancement de la validation automatique des dates...
[18/07/2025 11:36:46] [FRONTEND] [LOG] 🚀 [DATE-TEST] Lancement de la suite de validation des dates...
[18/07/2025 11:36:46] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Conversion de date sécurisée
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT e.*, et.name as template_name 
      FROM employees e 
      LEFT JOIN employee_templates et ON e.template_id = et.id 
      ORDER BY e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT e.*, et.name as template_name \n      FROM employees e \n      LEFT JOIN employee_templates et ON e.template_id = et.id \n      ORDER BY e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:36:46.059Z"
}
[18/07/2025 11:36:46] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la conversion de date sécurisée...
[18/07/2025 11:36:46] [FRONTEND] [ERROR] ❌ [DATE-TEST] formatDateToKey non disponible
[18/07/2025 11:36:46] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Logique de fork avec dates
[18/07/2025 11:36:46] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la logique de fork avec dates...
[18/07/2025 11:36:46] [FRONTEND] [ERROR] ❌ [DATE-TEST] Données insuffisantes pour le test
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 42ms: 
      SELECT e.*, et.name as template_name 
      {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 42ms:",
    "\n      SELECT e.*, et.name as template_name \n     ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:36:46.102Z"
}
[18/07/2025 11:36:46] [BACKEND] [INFO] ✅ Requête exécutée en 42ms: 
      SELECT e.*, et.name as template_name 
      {}
[18/07/2025 11:36:46] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test des améliorations de timezone...
[18/07/2025 11:36:46] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Améliorations timezone
[18/07/2025 11:36:46] [FRONTEND] [WARN] ⚠️ [DATE-TEST] Sélecteur de timezone non trouvé (normal si modal fermé)
[18/07/2025 11:36:46] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Détection de date avec timezone
[18/07/2025 11:36:46] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la détection de date avec timezone...
[18/07/2025 11:36:46] [FRONTEND] [LOG] 
📊 [DATE-TEST] Résultats de la validation:
[18/07/2025 11:36:46] [FRONTEND] [ERROR] ❌ [DATE-TEST] detectDropDateFromPosition non disponible
[18/07/2025 11:36:46] [FRONTEND] [LOG] ❌ Conversion de date sécurisée: ÉCHOUÉ
[18/07/2025 11:36:46] [FRONTEND] [LOG] ❌ Logique de fork avec dates: ÉCHOUÉ
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT s.*, e.name as employee_name, sp.label as post_label
      FROM shifts s
      LEFT JOIN employees e ON s.employee_id = e.id
      LEFT JOIN standard_posts sp ON s.post_id = sp.id
      ORDER BY s.date_key, e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT s.*, e.name as employee_name, sp.label as post_label\n      FROM shifts s\n      LEFT JOIN employees e ON s.employee_id = e.id\n      LEFT JOIN standard_posts sp ON s.post_id = sp.id\n      ORDER BY s.date_key, e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:36:46.172Z"
}
[18/07/2025 11:36:46] [FRONTEND] [LOG] ❌ Détection de date avec timezone: ÉCHOUÉ
[18/07/2025 11:36:46] [FRONTEND] [LOG] ✅ Améliorations timezone: PASSÉ
[18/07/2025 11:36:46] [FRONTEND] [LOG] 
⚠️ [DATE-TEST] Certaines validations ont échoué
[18/07/2025 11:36:46] [FRONTEND] [LOG] 🔧 [DATE-TEST] Vérifiez les logs ci-dessus pour les détails
[18/07/2025 11:36:46] [FRONTEND] [LOG] 🔌 [init] Indicateur de connexion initialisé
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 55ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 55ms:",
    "\n      SELECT s.*, e.name as employee_name, sp.lab",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:36:46.228Z"
}
[18/07/2025 11:36:46] [FRONTEND] [LOG] 📂 [TeamCalendarApp] Chargement de l'état...
[18/07/2025 11:36:46] [BACKEND] [INFO] ✅ Requête exécutée en 55ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
[18/07/2025 11:36:46] [FRONTEND] [LOG] 📦 [loadState] Chargement de tous les shifts depuis la base de données...
[18/07/2025 11:36:46] [FRONTEND] [LOG] 🌐 [TeamCalendarApp] Chargement depuis l'API...
[18/07/2025 11:36:46] [FRONTEND] [LOG] 👥 [TeamCalendarApp] 5 employés chargés
[18/07/2025 11:36:46] [FRONTEND] [LOG] 📅 [loadState] 0 shifts chargés depuis la base de données
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:36:46.281Z"
}
[18/07/2025 11:36:46] [FRONTEND] [LOG] 🚀 [GRIP-FIX] Lancement du diagnostic automatique...
[18/07/2025 11:36:46] [FRONTEND] [LOG] 📊 [GRIP-FIX] Total shifts trouvés: 0
[18/07/2025 11:36:46] [FRONTEND] [LOG] 🔍 [GRIP-FIX] Diagnostic complet des GRIP handles...
[18/07/2025 11:36:46] [FRONTEND] [LOG] 📊 [GRIP-FIX] Résultats du diagnostic: {"totalShifts":0,"regularShifts":0,"gripsInDOM":0,"gripsWithEvents":0,"gripsResponsive":0,"issues":[]}
[18/07/2025 11:36:46] [FRONTEND] [LOG] ✅ [loadState] Shifts organisés dans this.data.schedule
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 54ms: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 54ms:",
    "SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:36:46.335Z"
}
[18/07/2025 11:36:46] [BACKEND] [INFO] ✅ Requête exécutée en 54ms: SELECT * FROM standard_posts ORDER BY label {}
[18/07/2025 11:36:46] [FRONTEND] [LOG] 🏢 [TeamCalendarApp] 5 postes standards chargés
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:36:46.365Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 35ms: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 35ms:",
    "SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:36:46.400Z"
}
[18/07/2025 11:36:46] [BACKEND] [INFO] ✅ Requête exécutée en 35ms: SELECT * FROM app_settings ORDER BY setting_key {}
[18/07/2025 11:36:46] [FRONTEND] [LOG] ⚙️ [loadState] Paramètres chargés depuis API: {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT 
        ra.id,
        ra.employee_id,
        ra.post_id,
        ra.days_of_week,
        ra.start_date,
        ra.end_date,
        ra.is_active,
        ra.excluded_dates,
        e.name as employee_name,
        sp.label as post_label,
        sp.hours as post_hours,
        sp.type as post_type,
        array_length(ra.days_of_week, 1) as days_count
      FROM regular_assignments ra
      LEFT JOIN employees e ON ra.employee_id = e.id
      LEFT JOIN standard_posts sp ON ra.post_id = sp.id
      WHERE ra.is_active = true
      ORDER BY e.name, sp.label, ra.start_date
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT \n        ra.id,\n        ra.employee_id,\n        ra.post_id,\n        ra.days_of_week,\n        ra.start_date,\n        ra.end_date,\n        ra.is_active,\n        ra.excluded_dates,\n        e.name as employee_name,\n        sp.label as post_label,\n        sp.hours as post_hours,\n        sp.type as post_type,\n        array_length(ra.days_of_week, 1) as days_count\n      FROM regular_assignments ra\n      LEFT JOIN employees e ON ra.employee_id = e.id\n      LEFT JOIN standard_posts sp ON ra.post_id = sp.id\n      WHERE ra.is_active = true\n      ORDER BY e.name, sp.label, ra.start_date\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:36:46.432Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 57ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 57ms:",
    "\n      SELECT \n        ra.id,\n        ra.employee_",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:36:46.490Z"
}
[18/07/2025 11:36:46] [BACKEND] [INFO] ✅ Requête exécutée en 57ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
[18/07/2025 11:36:46] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"86f97857-d86a-4e2a-aec3-831bf92012d4","employee_id":"cf78e945-72c7-48f3-a72d-bd0e245a284d","post_id":"0c74e25d-4692-46d7-b5aa-b84eee797971","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-28T04:00:00.000Z","end_date":"2025-07-29T04:00:00.000Z","excluded_dates":[]}
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'
         {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'\n        ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:36:46.812Z"
}
[18/07/2025 11:36:47] [BACKEND] [INFO] 📋 [GET /api/employee-order] Récupération ordre des employés
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📋 [GET /api/employee-order] Récupération ordre des employés
  Data: {
  "originalArgs": [
    "📋 [GET /api/employee-order] Récupération ordre des employés"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:36:46.811Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 41ms: 
            SELECT setting_value FROM app_setting {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 41ms:",
    "\n            SELECT setting_value FROM app_setting",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:36:46.854Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📋 [GET /api/employee-order] Aucun ordre personnalisé trouvé
  Data: {
  "originalArgs": [
    "📋 [GET /api/employee-order] Aucun ordre personnalisé trouvé"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:36:46.854Z"
}
[18/07/2025 11:36:47] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"6830f981-f642-4364-9b1d-0ff92e12fad2","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"isLimited":false,"startDate":"2025-08-01","endDate":null,"excludedDates":[]}
[18/07/2025 11:36:47] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"86f97857-d86a-4e2a-aec3-831bf92012d4","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-28","endDate":"2025-07-29","excludedDates":[]}
[18/07/2025 11:36:47] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"6830f981-f642-4364-9b1d-0ff92e12fad2","employee_id":"cf78e945-72c7-48f3-a72d-bd0e245a284d","post_id":"0c74e25d-4692-46d7-b5aa-b84eee797971","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-08-01T04:00:00.000Z","end_date":null,"excluded_dates":[]}
[18/07/2025 11:36:47] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"08b7356a-e76a-484d-b599-2d5d4a0d8b6b","employee_id":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","post_id":"0c74e25d-4692-46d7-b5aa-b84eee797971","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-23T04:00:00.000Z","end_date":"2025-07-23T04:00:00.000Z","excluded_dates":[]}
[18/07/2025 11:36:47] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"08b7356a-e76a-484d-b599-2d5d4a0d8b6b","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-23","endDate":"2025-07-23","excludedDates":[]}
[18/07/2025 11:36:47] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"42c64a69-ab23-451f-a314-c5c87622675d","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-30","endDate":"2025-07-31","excludedDates":[]}
[18/07/2025 11:36:47] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"42c64a69-ab23-451f-a314-c5c87622675d","employee_id":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","post_id":"0c74e25d-4692-46d7-b5aa-b84eee797971","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-30T04:00:00.000Z","end_date":"2025-07-31T04:00:00.000Z","excluded_dates":[]}
[18/07/2025 11:36:47] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"316031c4-9402-4394-970e-e598022f5118","employee_id":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","post_id":"0c74e25d-4692-46d7-b5aa-b84eee797971","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-25T04:00:00.000Z","end_date":"2025-07-27T04:00:00.000Z","excluded_dates":[]}
[18/07/2025 11:36:47] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"5400f0bd-d560-4167-89ea-a6da77478469","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-22","endDate":"2025-07-22","excludedDates":[]}
[18/07/2025 11:36:47] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"5400f0bd-d560-4167-89ea-a6da77478469","employee_id":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","post_id":"0c74e25d-4692-46d7-b5aa-b84eee797971","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-22T04:00:00.000Z","end_date":"2025-07-22T04:00:00.000Z","excluded_dates":[]}
[18/07/2025 11:36:47] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"ed906322-db11-4d06-aa28-437b06c445c4","employee_id":"59f5df3a-33ef-425c-bfa2-adca818cf94f","post_id":"0c74e25d-4692-46d7-b5aa-b84eee797971","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-18T04:00:00.000Z","end_date":"2025-07-21T04:00:00.000Z","excluded_dates":[]}
[18/07/2025 11:36:47] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"316031c4-9402-4394-970e-e598022f5118","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-25","endDate":"2025-07-27","excludedDates":[]}
[18/07/2025 11:36:47] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"d546a50b-43af-4f53-afd0-4a43c1f3959a","employee_id":"59f5df3a-33ef-425c-bfa2-adca818cf94f","post_id":"0c74e25d-4692-46d7-b5aa-b84eee797971","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-24T04:00:00.000Z","end_date":"2025-07-24T04:00:00.000Z","excluded_dates":[]}
[18/07/2025 11:36:47] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"d546a50b-43af-4f53-afd0-4a43c1f3959a","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-24","endDate":"2025-07-24","excludedDates":[]}
[18/07/2025 11:36:47] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"ed906322-db11-4d06-aa28-437b06c445c4","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-18","endDate":"2025-07-21","excludedDates":[]}
[18/07/2025 11:36:47] [FRONTEND] [LOG] 📋 [loadState] 8/8 assignations régulières valides chargées et normalisées
[18/07/2025 11:36:47] [FRONTEND] [LOG] ✅ [fixInvalidAssignmentDates] Aucune date invalide trouvée
[18/07/2025 11:36:47] [FRONTEND] [LOG] 🔧 [fixInvalidAssignmentDates] Vérification des dates invalides...
[18/07/2025 11:36:47] [FRONTEND] [LOG] ✅ [cleanupDuplicateRegularShifts] 0 doublons supprimés
[18/07/2025 11:36:47] [FRONTEND] [LOG] 📋 [loadState] Assignations régulières chargées (application différée)
[18/07/2025 11:36:47] [FRONTEND] [LOG] ✅ [setupEmployeeNameDisplay] Styles CSS injectés pour l'affichage des noms
[18/07/2025 11:36:47] [FRONTEND] [LOG] ⚙️ [loadStateFromLocalStorage] Aucun paramètre sauvegardé.
[18/07/2025 11:36:47] [FRONTEND] [ERROR] ❌ [TeamCalendarApp] Erreur lors du chargement: Error: this.cleanupInvalidAssignmentIds is not a function
loadState@http://localhost:5173/src/teamCalendarApp.ts?t=1752850722144:978:40
async*init@http://localhost:5173/src/teamCalendarApp.ts?t=1752850722144:1052:16
async*initApp@http://localhost:5173/src/Agenda.tsx?t=1752850722144:40:31
setTimeout handler*AgendaFullscreen/<@http://localhost:5173/src/Agenda.tsx?t=1752850722144:50:29
react-stack-bottom-frame@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:17478:20
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
commitHookEffectListMount@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8460:122
commitHookPassiveMountEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8518:60
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10016:42
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
doubleInvokeEffectsOnFiber@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11461:207
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
recursivelyTraverseAndDoubleInvokeEffectsInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11432:78
commitDoubleInvokeEffectsInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11469:55
flushPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11309:41
node_modules/react-dom/cjs/react-dom-client.development.js/commitRoot/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11060:32
performWorkUntilDeadline@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:36:58
EventHandlerNonNull*node_modules/scheduler/cjs/scheduler.development.js/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:156:9
node_modules/scheduler/cjs/scheduler.development.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:266:7
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/scheduler/index.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:277:24
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/react-dom/cjs/react-dom-client.development.js/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:15216:23
node_modules/react-dom/cjs/react-dom-client.development.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18060:7
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/react-dom/client.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18071:24
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18075:16

[18/07/2025 11:36:47] [FRONTEND] [LOG] ✅ [init] Icône paramètres trouvée: [Objet non sérialisable]
[18/07/2025 11:36:47] [FRONTEND] [LOG] 🔍 [init] Recherche du bouton des paramètres dans le DOM
[18/07/2025 11:36:47] [FRONTEND] [LOG] 🔍 [init] Bouton des paramètres trouvé ? true
[18/07/2025 11:36:47] [FRONTEND] [LOG] 🔍 [init] Recherche du bouton des paramètres header dans le DOM
[18/07/2025 11:36:47] [FRONTEND] [LOG] 🔍 [init] Attachement du listener click sur le bouton des paramètres
[18/07/2025 11:36:47] [FRONTEND] [LOG] 🔍 [init] Bouton des paramètres header trouvé ? true
[18/07/2025 11:36:47] [FRONTEND] [LOG] 🔍 [init] Attachement du listener click sur le bouton des paramètres header
[18/07/2025 11:36:47] [FRONTEND] [LOG] ✅ [setupNavigationListeners] Écouteurs de navigation attachés
[18/07/2025 11:36:47] [FRONTEND] [LOG] 🎯 [setupViewNavigationListeners] Configuration des boutons de navigation...
[18/07/2025 11:36:47] [FRONTEND] [LOG] ✅ Boutons de navigation configurés
[18/07/2025 11:36:47] [FRONTEND] [LOG] ✅ [setupGlobalEscapeHandler] Gestionnaire ESC global configuré
[18/07/2025 11:36:47] [FRONTEND] [LOG] 🔧 [ModalFunctionalitiesManager] Initialisation du gestionnaire de modales
[18/07/2025 11:36:47] [FRONTEND] [LOG] 📋 [loadEmployeeOrder] Chargement ordre des employés
[18/07/2025 11:36:47] [FRONTEND] [LOG] 🔧 [init] Forçage du rendu initial...
[18/07/2025 11:36:47] [FRONTEND] [WARN] ⚠️ [loadEmployeeOrder] Réponse API sans tableau employeeOrder
[18/07/2025 11:36:47] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[18/07/2025 11:36:47] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[18/07/2025 11:36:47] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[18/07/2025 11:36:47] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[18/07/2025 11:36:47] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[18/07/2025 11:36:47] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[18/07/2025 11:36:47] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 60 zones trouvées
[18/07/2025 11:36:47] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[18/07/2025 11:36:47] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 60 zones de drop disponibles
[18/07/2025 11:36:47] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Matin: [1, 2, 3, 4, 5]
[18/07/2025 11:36:47] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[18/07/2025 11:36:47] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 11:36:47] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Nuit: [1, 2, 3, 4, 5]
[18/07/2025 11:36:47] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 11:36:47] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 11:36:47] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 11:36:47] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Soir: [1, 2, 3, 4, 5]
[18/07/2025 11:36:47] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 11:36:47] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 11:36:47] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE1: [0, 6]
[18/07/2025 11:36:47] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 11:36:47] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE2: [0, 6]
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 11:36:48] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 11:36:48] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 11:36:48] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 11:36:48] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 11:36:48] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 11:36:48] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[18/07/2025 11:36:48] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 11:36:48] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 11:36:48] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 11:36:48] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 11:36:48] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 11:36:48] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[18/07/2025 11:36:48] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[18/07/2025 11:36:48] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (60 zones)
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🔧 [init] Configuration du drag & drop des postes...
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 60 zones de drop disponibles
[18/07/2025 11:36:48] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 60 zones trouvées
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 11:36:48] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 11:36:48] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 11:36:48] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 11:36:48] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 11:36:48] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 11:36:48] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 11:36:48] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 11:36:48] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 11:36:48] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 11:36:48] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 11:36:48] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[18/07/2025 11:36:48] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 11:36:48] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 11:36:48] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 11:36:48] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 11:36:48] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[18/07/2025 11:36:48] [FRONTEND] [LOG] ℹ️ [setupEmployeeDragDrop] Fonction désactivée - Utiliser setupDragAndDrop
[18/07/2025 11:36:48] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (60 zones)
[18/07/2025 11:36:48] [FRONTEND] [LOG] ✅ [addEmergencyFixButton] Boutons ajoutés (emergency fix buttons supprimés)
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🔧 [addEmergencyFixButton] Ajout du bouton d'historique...
[18/07/2025 11:36:48] [FRONTEND] [LOG] 📋 [init] Application des attributions régulières après initialisation
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] Nettoyage des shifts réguliers existants...
[18/07/2025 11:36:48] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 86f97857-d86a-4e2a-aec3-831bf92012d4 commence après cette semaine (startDate: 2025-07-28, semaine se termine: 2025-07-19)
[18/07/2025 11:36:48] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Application pour semaine 2025-W28
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] 0 shifts réguliers supprimés
[18/07/2025 11:36:48] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 6830f981-f642-4364-9b1d-0ff92e12fad2 commence après cette semaine (startDate: 2025-08-01, semaine se termine: 2025-07-19)
[18/07/2025 11:36:48] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 08b7356a-e76a-484d-b599-2d5d4a0d8b6b commence après cette semaine (startDate: 2025-07-23, semaine se termine: 2025-07-19)
[18/07/2025 11:36:48] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 5400f0bd-d560-4167-89ea-a6da77478469 commence après cette semaine (startDate: 2025-07-22, semaine se termine: 2025-07-19)
[18/07/2025 11:36:48] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 316031c4-9402-4394-970e-e598022f5118 commence après cette semaine (startDate: 2025-07-25, semaine se termine: 2025-07-19)
[18/07/2025 11:36:48] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 42c64a69-ab23-451f-a314-c5c87622675d commence après cette semaine (startDate: 2025-07-30, semaine se termine: 2025-07-19)
[18/07/2025 11:36:48] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution ed906322-db11-4d06-aa28-437b06c445c4 a une intersection avec cette semaine (2025-07-18 → 2025-07-21) vs (2025-07-13 → 2025-07-19)
[18/07/2025 11:36:48] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution d546a50b-43af-4f53-afd0-4a43c1f3959a commence après cette semaine (startDate: 2025-07-24, semaine se termine: 2025-07-19)
[18/07/2025 11:36:48] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 1 attributions actives sur 8 total
[18/07/2025 11:36:48] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"ed906322-db11-4d06-aa28-437b06c445c4","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"startDate":"2025-07-18","endDate":"2025-07-21","excludedDates":[]}
[18/07/2025 11:36:48] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-13 (0)
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution ed906322-db11-4d06-aa28-437b06c445c4: [{"dateKey":"2025-07-13","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-14","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-15","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-16","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-17","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-07-18","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-07-19","isWeekend":true,"dayOfWeek":6}]
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-13T04:00:00.000Z","dayDateKey":"2025-07-13","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:36:48] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[18/07/2025 11:36:48] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-13 ignoré pour attribution ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 11:36:48] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-14 (1)
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-14T04:00:00.000Z","dayDateKey":"2025-07-14","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:36:48] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-14 avant startDate 2025-07-18 (original: 2025-07-18)
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-14: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","dayDateStr":"2025-07-14","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:36:48] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-14 ignoré pour attribution ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 11:36:48] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-15 (2)
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-15T04:00:00.000Z","dayDateKey":"2025-07-15","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:36:48] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-15 avant startDate 2025-07-18 (original: 2025-07-18)
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-15: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","dayDateStr":"2025-07-15","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:36:48] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-15 ignoré pour attribution ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 11:36:48] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-16 (3)
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-16T04:00:00.000Z","dayDateKey":"2025-07-16","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-16: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","dayDateStr":"2025-07-16","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:36:48] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-16 avant startDate 2025-07-18 (original: 2025-07-18)
[18/07/2025 11:36:48] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-17 (4)
[18/07/2025 11:36:48] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-16 ignoré pour attribution ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-17T04:00:00.000Z","dayDateKey":"2025-07-17","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-17: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","dayDateStr":"2025-07-17","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:36:48] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:36:49] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-17 ignoré pour attribution ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 11:36:49] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-07-18 (5)
[18/07/2025 11:36:49] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-17 avant startDate 2025-07-18 (original: 2025-07-18)
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-18T04:00:00.000Z","dayDateKey":"2025-07-18","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-18: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","dayDateStr":"2025-07-18","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-18
[18/07/2025 11:36:49] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-18 validé, shifts existants: 0
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Nuit → 2025-07-18 pour Sophie Leblanc
[18/07/2025 11:36:49] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-07-19 (6)
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-19T04:00:00.000Z","dayDateKey":"2025-07-19","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:36:49] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[18/07/2025 11:36:49] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 1 attributions appliquées pour la semaine 2025-W28
[18/07/2025 11:36:49] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-19 ignoré pour attribution ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Rendu demandé car 1 shifts créés
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [init] Initialisation terminée avec succès
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🎉 [Agenda] Application fullscreen initialisée avec succès
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] Résultats: 0 ajoutés, 0 validés, 0 corrigés
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[18/07/2025 11:36:49] [FRONTEND] [WARN] ⚠️ [renderUnifiedCalendar] Conteneur available-posts-container non trouvé, création...
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneur available-posts-container créé
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 0c74e25d-4692-46d7-b5aa-b84eee797971 -> "00:00-08:00"
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 62 zones trouvées
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 62 zones de drop disponibles
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (62 zones)
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [SETUP] Drag & drop des employés configuré (mode simplifié)
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 62 zones trouvées
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 62 zones de drop disponibles
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[18/07/2025 11:36:49] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (62 zones)
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[18/07/2025 11:36:49] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic des problèmes de modale...
[18/07/2025 11:36:49] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Éléments DOM: {"modal":true,"closeBtn":true,"cancelBtn":true,"confirmBtn":true,"radioButtons":4}
[18/07/2025 11:36:49] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] TeamCalendarApp: {"exists":false,"hasModalManager":false,"hasOpenFunction":false,"hasHandleFunction":false}
[18/07/2025 11:36:49] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] État de la modale: {"hidden":true,"classes":["hidden","fixed","inset-0","bg-black/40","backdrop-blur-sm","z-50","flex","items-center","justify-center","p-4"],"style":""}
[18/07/2025 11:36:50] [FRONTEND] [LOG] 📊 [diagnoseRegularAssignmentGrips] Statistiques: {"totalShifts":1,"regularShifts":1,"regularAssignments":8}
[18/07/2025 11:36:50] [FRONTEND] [LOG] 🔍 [diagnoseRegularAssignmentGrips] Diagnostic des grips d'attributions régulières
[18/07/2025 11:36:50] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[18/07/2025 11:36:50] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 1: {"employee":"Sophie Leblanc","date":"2025-07-18","assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","text":"00:00-08:00"}
[18/07/2025 11:36:50] [FRONTEND] [LOG] 🎯 [diagnoseRegularAssignmentGrips] Grips trouvés dans le DOM: 1
[18/07/2025 11:36:50] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[18/07/2025 11:36:50] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] Résultats: 0 ajoutés, 1 validés, 0 corrigés
[18/07/2025 11:36:51] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation complète...
[18/07/2025 11:36:51] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation automatique...
[18/07/2025 11:36:51] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[18/07/2025 11:36:51] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[18/07/2025 11:36:51] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation de la correction principale...
[18/07/2025 11:36:51] [FRONTEND] [ERROR] ❌ [VALIDATION] TeamCalendarApp non disponible
[18/07/2025 11:36:51] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des éléments DOM...
[18/07/2025 11:36:51] [FRONTEND] [LOG] 📋 [VALIDATION] Éléments DOM: {"modal":true,"closeBtn":true,"cancelBtn":true,"confirmBtn":true,"radioButtons":true}
[18/07/2025 11:36:51] [FRONTEND] [LOG] ✅ [VALIDATION] Tous les éléments DOM sont présents
[18/07/2025 11:36:51] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des listeners...
[18/07/2025 11:36:51] [FRONTEND] [ERROR] ❌ [VALIDATION] Erreur lors de setupAssignmentContextModal: Error: can't access property "ModalManager", window.TeamCalendarApp is undefined
validateListeners@http://localhost:5173/validate-modal-fix.js:87:9
runCompleteValidation@http://localhost:5173/validate-modal-fix.js:103:20
@http://localhost:5173/validate-modal-fix.js:186:5
setTimeout handler*@http://localhost:5173/validate-modal-fix.js:184:11

[18/07/2025 11:36:51] [FRONTEND] [LOG] 🔧 [VALIDATION] Actions recommandées:
[18/07/2025 11:36:51] [FRONTEND] [ERROR] ❌ [VALIDATION] Certaines validations ont échoué
[18/07/2025 11:36:51] [FRONTEND] [LOG] 📊 [VALIDATION] Résultats de validation: {"mainFix":false,"domElements":true,"listeners":false}
[18/07/2025 11:36:51] [FRONTEND] [LOG] - Vérifier les erreurs dans setupAssignmentContextModal
[18/07/2025 11:36:51] [FRONTEND] [LOG] - Vérifier que TeamCalendarApp et ModalManager sont correctement initialisés
[18/07/2025 11:36:52] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[18/07/2025 11:36:52] [FRONTEND] [LOG] 🚀 [DATE-TEST] Lancement de la validation automatique des dates...
[18/07/2025 11:36:52] [FRONTEND] [LOG] 📊 [DIAGNOSTIC] Résultats: {"teamCalendarApp":false,"detectDropDateFunction":false,"showConfirmationMenuFunction":false,"handleRegularAssignmentDropFunction":false,"regularAssignments":0,"employees":0}
[18/07/2025 11:36:52] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic complet du fork modal...
[18/07/2025 11:36:52] [FRONTEND] [ERROR] ❌ [DIAGNOSTIC] Fonctions manquantes détectées
[18/07/2025 11:36:52] [FRONTEND] [LOG] 🚀 [DATE-TEST] Lancement de la suite de validation des dates...
[18/07/2025 11:36:52] [FRONTEND] [ERROR] ❌ [DATE-TEST] formatDateToKey non disponible
[18/07/2025 11:36:52] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Logique de fork avec dates
[18/07/2025 11:36:52] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la conversion de date sécurisée...
[18/07/2025 11:36:52] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Conversion de date sécurisée
[18/07/2025 11:36:52] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la logique de fork avec dates...
[18/07/2025 11:36:52] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test des améliorations de timezone...
[18/07/2025 11:36:52] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Améliorations timezone
[18/07/2025 11:36:52] [FRONTEND] [ERROR] ❌ [DATE-TEST] Données insuffisantes pour le test
[18/07/2025 11:36:52] [FRONTEND] [WARN] ⚠️ [DATE-TEST] Sélecteur de timezone non trouvé (normal si modal fermé)
[18/07/2025 11:36:52] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Détection de date avec timezone
[18/07/2025 11:36:52] [FRONTEND] [LOG] 
📊 [DATE-TEST] Résultats de la validation:
[18/07/2025 11:36:52] [FRONTEND] [ERROR] ❌ [DATE-TEST] detectDropDateFromPosition non disponible
[18/07/2025 11:36:52] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la détection de date avec timezone...
[18/07/2025 11:36:52] [FRONTEND] [LOG] ❌ Conversion de date sécurisée: ÉCHOUÉ
[18/07/2025 11:36:52] [FRONTEND] [LOG] ❌ Logique de fork avec dates: ÉCHOUÉ
[18/07/2025 11:36:52] [FRONTEND] [LOG] ✅ Améliorations timezone: PASSÉ
[18/07/2025 11:36:52] [FRONTEND] [LOG] ❌ Détection de date avec timezone: ÉCHOUÉ
[18/07/2025 11:36:52] [FRONTEND] [LOG] 
⚠️ [DATE-TEST] Certaines validations ont échoué
[18/07/2025 11:36:52] [FRONTEND] [LOG] 🔧 [DATE-TEST] Vérifiez les logs ci-dessus pour les détails
[18/07/2025 11:36:53] [FRONTEND] [LOG] 🔍 [GRIP-FIX] Diagnostic complet des GRIP handles...
[18/07/2025 11:36:53] [FRONTEND] [LOG] 📊 [GRIP-FIX] Résultats du diagnostic: {"totalShifts":1,"regularShifts":1,"gripsInDOM":1,"gripsWithEvents":0,"gripsResponsive":1,"issues":[]}
[18/07/2025 11:36:53] [FRONTEND] [LOG] 📊 [GRIP-FIX] Total shifts trouvés: 1
[18/07/2025 11:36:53] [FRONTEND] [LOG] 🔍 [GRIP-FIX] Test responsivité ed906322-db11-4d06-aa28-437b06c445c4: {"hasMouseEvents":false,"isVisible":true,"hasCorrectCursor":true,"hasValidDimensions":true,"rect":{"width":130,"height":6}}
[18/07/2025 11:36:53] [FRONTEND] [LOG] 🚀 [GRIP-FIX] Lancement du diagnostic automatique...
[18/07/2025 11:36:58] [FRONTEND] [LOG] ➡️ Clic sur suivant
[18/07/2025 11:36:58] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 6830f981-f642-4364-9b1d-0ff92e12fad2 commence après cette semaine (startDate: 2025-08-01, semaine se termine: 2025-07-26)
[18/07/2025 11:36:58] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 86f97857-d86a-4e2a-aec3-831bf92012d4 commence après cette semaine (startDate: 2025-07-28, semaine se termine: 2025-07-26)
[18/07/2025 11:36:58] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution 08b7356a-e76a-484d-b599-2d5d4a0d8b6b a une intersection avec cette semaine (2025-07-23 → 2025-07-23) vs (2025-07-20 → 2025-07-26)
[18/07/2025 11:36:58] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 42c64a69-ab23-451f-a314-c5c87622675d commence après cette semaine (startDate: 2025-07-30, semaine se termine: 2025-07-26)
[18/07/2025 11:36:58] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution 5400f0bd-d560-4167-89ea-a6da77478469 a une intersection avec cette semaine (2025-07-22 → 2025-07-22) vs (2025-07-20 → 2025-07-26)
[18/07/2025 11:36:58] [FRONTEND] [LOG] 🧭 [navigateWeek] Direction: 1, Offset actuel: 0
[18/07/2025 11:36:58] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution 316031c4-9402-4394-970e-e598022f5118 a une intersection avec cette semaine (2025-07-25 → 2025-07-27) vs (2025-07-20 → 2025-07-26)
[18/07/2025 11:36:58] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution ed906322-db11-4d06-aa28-437b06c445c4 a une intersection avec cette semaine (2025-07-18 → 2025-07-21) vs (2025-07-20 → 2025-07-26)
[18/07/2025 11:36:58] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution d546a50b-43af-4f53-afd0-4a43c1f3959a a une intersection avec cette semaine (2025-07-24 → 2025-07-24) vs (2025-07-20 → 2025-07-26)
[18/07/2025 11:36:58] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 5 attributions actives sur 8 total
[18/07/2025 11:36:58] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"08b7356a-e76a-484d-b599-2d5d4a0d8b6b","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"startDate":"2025-07-23","endDate":"2025-07-23","excludedDates":[]}
[18/07/2025 11:36:58] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution 08b7356a-e76a-484d-b599-2d5d4a0d8b6b: [{"dateKey":"2025-07-20","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-21","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-22","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-23","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-24","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-07-25","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-07-26","isWeekend":true,"dayOfWeek":6}]
[18/07/2025 11:36:58] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-20 (0)
[18/07/2025 11:36:58] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"08b7356a-e76a-484d-b599-2d5d4a0d8b6b","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-20T04:00:00.000Z","dayDateKey":"2025-07-20","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:36:58] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[18/07/2025 11:36:58] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-20 ignoré pour attribution 08b7356a-e76a-484d-b599-2d5d4a0d8b6b
[18/07/2025 11:36:59] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-21 (1)
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"08b7356a-e76a-484d-b599-2d5d4a0d8b6b","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-21T04:00:00.000Z","dayDateKey":"2025-07-21","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-21: {"assignmentId":"08b7356a-e76a-484d-b599-2d5d4a0d8b6b","dayDateStr":"2025-07-21","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:36:59] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-21 avant startDate 2025-07-23 (original: 2025-07-23)
[18/07/2025 11:36:59] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-21 ignoré pour attribution 08b7356a-e76a-484d-b599-2d5d4a0d8b6b
[18/07/2025 11:36:59] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-22 (2)
[18/07/2025 11:36:59] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-22 avant startDate 2025-07-23 (original: 2025-07-23)
[18/07/2025 11:36:59] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-22 ignoré pour attribution 08b7356a-e76a-484d-b599-2d5d4a0d8b6b
[18/07/2025 11:36:59] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-23 (3)
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"08b7356a-e76a-484d-b599-2d5d4a0d8b6b","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-23T04:00:00.000Z","dayDateKey":"2025-07-23","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:36:59] [FRONTEND] [LOG] 📋 [navigateWeek] Application des attributions pour la nouvelle semaine 2025-W29
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-23: {"assignmentId":"08b7356a-e76a-484d-b599-2d5d4a0d8b6b","dayDateStr":"2025-07-23","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:36:59.019Z"
}
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-22: {"assignmentId":"08b7356a-e76a-484d-b599-2d5d4a0d8b6b","dayDateStr":"2025-07-22","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:36:59.026Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:36:59.032Z"
}
[18/07/2025 11:36:59] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"08b7356a-e76a-484d-b599-2d5d4a0d8b6b","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-22T04:00:00.000Z","dayDateKey":"2025-07-22","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:36:59] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-23
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-24: {"assignmentId":"08b7356a-e76a-484d-b599-2d5d4a0d8b6b","dayDateStr":"2025-07-24","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:36:59] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-25 après endDate 2025-07-23 (original: 2025-07-23) (attribution 08b7356a-e76a-484d-b599-2d5d4a0d8b6b)
[18/07/2025 11:36:59] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-24 après endDate 2025-07-23 (original: 2025-07-23) (attribution 08b7356a-e76a-484d-b599-2d5d4a0d8b6b)
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"08b7356a-e76a-484d-b599-2d5d4a0d8b6b","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-26T04:00:00.000Z","dayDateKey":"2025-07-26","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:36:59] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-24 ignoré pour attribution 08b7356a-e76a-484d-b599-2d5d4a0d8b6b
[18/07/2025 11:36:59] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-07-26 (6)
[18/07/2025 11:36:59] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-07-25 (5)
[18/07/2025 11:36:59] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-25 ignoré pour attribution 08b7356a-e76a-484d-b599-2d5d4a0d8b6b
[18/07/2025 11:36:59] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:36:59.185Z"
}
[18/07/2025 11:36:59] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-23 validé, shifts existants: 0
[18/07/2025 11:36:59] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution 5400f0bd-d560-4167-89ea-a6da77478469: [{"dateKey":"2025-07-20","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-21","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-22","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-23","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-24","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-07-25","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-07-26","isWeekend":true,"dayOfWeek":6}]
[18/07/2025 11:36:59] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[18/07/2025 11:36:59] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"5400f0bd-d560-4167-89ea-a6da77478469","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"startDate":"2025-07-22","endDate":"2025-07-22","excludedDates":[]}
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-22: {"assignmentId":"5400f0bd-d560-4167-89ea-a6da77478469","dayDateStr":"2025-07-22","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] Nettoyage des shifts réguliers existants...
[18/07/2025 11:36:59] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-22
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:36:59.239Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:36:59.238Z"
}
[18/07/2025 11:36:59] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Nuit → 2025-07-22 pour Pierre Durand
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:36:59.239Z"
}
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"08b7356a-e76a-484d-b599-2d5d4a0d8b6b","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-24T04:00:00.000Z","dayDateKey":"2025-07-24","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:36:59] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-24 (4)
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"5400f0bd-d560-4167-89ea-a6da77478469","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-23T04:00:00.000Z","dayDateKey":"2025-07-23","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-25: {"assignmentId":"5400f0bd-d560-4167-89ea-a6da77478469","dayDateStr":"2025-07-25","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:36:59] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Nuit → 2025-07-23 pour Marie Martin
[18/07/2025 11:36:59] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-20 ignoré pour attribution 316031c4-9402-4394-970e-e598022f5118
[18/07/2025 11:36:59] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution ed906322-db11-4d06-aa28-437b06c445c4: [{"dateKey":"2025-07-20","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-21","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-22","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-23","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-24","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-07-25","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-07-26","isWeekend":true,"dayOfWeek":6}]
[18/07/2025 11:36:59] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-26 ignoré pour attribution 316031c4-9402-4394-970e-e598022f5118
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"316031c4-9402-4394-970e-e598022f5118","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-20T04:00:00.000Z","dayDateKey":"2025-07-20","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:36:59] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-20 (0)
[18/07/2025 11:36:59] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[18/07/2025 11:36:59] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"ed906322-db11-4d06-aa28-437b06c445c4","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"startDate":"2025-07-18","endDate":"2025-07-21","excludedDates":[]}
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution 316031c4-9402-4394-970e-e598022f5118: [{"dateKey":"2025-07-20","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-21","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-22","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-23","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-24","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-07-25","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-07-26","isWeekend":true,"dayOfWeek":6}]
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"316031c4-9402-4394-970e-e598022f5118","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-26T04:00:00.000Z","dayDateKey":"2025-07-26","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:36:59] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-23 (3)
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-25T04:00:00.000Z","dayDateKey":"2025-07-25","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-26T04:00:00.000Z","dayDateKey":"2025-07-26","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:36:59] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-07-25 (5)
[18/07/2025 11:36:59] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[18/07/2025 11:36:59] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-26 ignoré pour attribution ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] 0 shifts réguliers supprimés
[18/07/2025 11:36:59] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"d546a50b-43af-4f53-afd0-4a43c1f3959a","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"startDate":"2025-07-24","endDate":"2025-07-24","excludedDates":[]}
[18/07/2025 11:36:59] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-21 avant startDate 2025-07-24 (original: 2025-07-24)
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:36:59] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-22 ignoré pour attribution d546a50b-43af-4f53-afd0-4a43c1f3959a
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-23: {"assignmentId":"d546a50b-43af-4f53-afd0-4a43c1f3959a","dayDateStr":"2025-07-23","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:36:59] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-22 avant startDate 2025-07-24 (original: 2025-07-24)
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-21: {"assignmentId":"d546a50b-43af-4f53-afd0-4a43c1f3959a","dayDateStr":"2025-07-21","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"d546a50b-43af-4f53-afd0-4a43c1f3959a","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-23T04:00:00.000Z","dayDateKey":"2025-07-23","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-22: {"assignmentId":"d546a50b-43af-4f53-afd0-4a43c1f3959a","dayDateStr":"2025-07-22","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:36:59] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-23 (3)
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"d546a50b-43af-4f53-afd0-4a43c1f3959a","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-22T04:00:00.000Z","dayDateKey":"2025-07-22","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:36:59] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-07-25 (5)
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Rendu demandé car 5 shifts créés
[18/07/2025 11:36:59] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Nuit → 2025-07-24 pour Sophie Leblanc
[18/07/2025 11:36:59] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-24 validé, shifts existants: 0
[18/07/2025 11:36:59] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-24
[18/07/2025 11:36:59] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-26 ignoré pour attribution d546a50b-43af-4f53-afd0-4a43c1f3959a
[18/07/2025 11:36:59] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 5 attributions appliquées pour la semaine 2025-W29
[18/07/2025 11:36:59] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-24: {"assignmentId":"d546a50b-43af-4f53-afd0-4a43c1f3959a","dayDateStr":"2025-07-24","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Cache invalidé pour forcer la régénération
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"d546a50b-43af-4f53-afd0-4a43c1f3959a","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-26T04:00:00.000Z","dayDateKey":"2025-07-26","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"d546a50b-43af-4f53-afd0-4a43c1f3959a","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-24T04:00:00.000Z","dayDateKey":"2025-07-24","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:36:59] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-24 (4)
[18/07/2025 11:36:59] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-07-26 (6)
[18/07/2025 11:36:59] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-25 ignoré pour attribution d546a50b-43af-4f53-afd0-4a43c1f3959a
[18/07/2025 11:36:59] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-23 ignoré pour attribution d546a50b-43af-4f53-afd0-4a43c1f3959a
[18/07/2025 11:36:59] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Application pour semaine 2025-W29
[18/07/2025 11:36:59] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-23 avant startDate 2025-07-24 (original: 2025-07-24)
[18/07/2025 11:36:59] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-25 après endDate 2025-07-24 (original: 2025-07-24) (attribution d546a50b-43af-4f53-afd0-4a43c1f3959a)
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"d546a50b-43af-4f53-afd0-4a43c1f3959a","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-21T04:00:00.000Z","dayDateKey":"2025-07-21","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-25: {"assignmentId":"d546a50b-43af-4f53-afd0-4a43c1f3959a","dayDateStr":"2025-07-25","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:36:59] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-20 ignoré pour attribution d546a50b-43af-4f53-afd0-4a43c1f3959a
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:36:59] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-21 (1)
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"d546a50b-43af-4f53-afd0-4a43c1f3959a","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-25T04:00:00.000Z","dayDateKey":"2025-07-25","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:36:59] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[18/07/2025 11:36:59] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-22 (2)
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"d546a50b-43af-4f53-afd0-4a43c1f3959a","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-20T04:00:00.000Z","dayDateKey":"2025-07-20","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:36:59] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-21 ignoré pour attribution d546a50b-43af-4f53-afd0-4a43c1f3959a
[18/07/2025 11:36:59] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-20 (0)
[18/07/2025 11:36:59] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-24 ignoré pour attribution ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 11:36:59] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-24 après endDate 2025-07-21 (original: 2025-07-21) (attribution ed906322-db11-4d06-aa28-437b06c445c4)
[18/07/2025 11:36:59] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-07-26 (6)
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution d546a50b-43af-4f53-afd0-4a43c1f3959a: [{"dateKey":"2025-07-20","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-21","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-22","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-23","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-24","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-07-25","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-07-26","isWeekend":true,"dayOfWeek":6}]
[18/07/2025 11:36:59] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-25 ignoré pour attribution ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-24: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","dayDateStr":"2025-07-24","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:36:59] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-25 après endDate 2025-07-21 (original: 2025-07-21) (attribution ed906322-db11-4d06-aa28-437b06c445c4)
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-25: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","dayDateStr":"2025-07-25","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:36:59] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-24T04:00:00.000Z","dayDateKey":"2025-07-24","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:36:59] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-24 (4)
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:37:00] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-23 ignoré pour attribution ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 11:37:00] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-22 ignoré pour attribution ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 11:37:00] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-23 après endDate 2025-07-21 (original: 2025-07-21) (attribution ed906322-db11-4d06-aa28-437b06c445c4)
[18/07/2025 11:37:00] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-22 après endDate 2025-07-21 (original: 2025-07-21) (attribution ed906322-db11-4d06-aa28-437b06c445c4)
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-22: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","dayDateStr":"2025-07-22","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-23: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","dayDateStr":"2025-07-23","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-23T04:00:00.000Z","dayDateKey":"2025-07-23","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-22T04:00:00.000Z","dayDateKey":"2025-07-22","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:00] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-07-26 (6)
[18/07/2025 11:37:00] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-22 (2)
[18/07/2025 11:37:00] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Nuit → 2025-07-25 pour Pierre Durand
[18/07/2025 11:37:00] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-25 validé, shifts existants: 0
[18/07/2025 11:37:00] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Nuit → 2025-07-21 pour Sophie Leblanc
[18/07/2025 11:37:00] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-21 validé, shifts existants: 0
[18/07/2025 11:37:00] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-25
[18/07/2025 11:37:00] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-21
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-21: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","dayDateStr":"2025-07-21","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-25: {"assignmentId":"316031c4-9402-4394-970e-e598022f5118","dayDateStr":"2025-07-25","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"316031c4-9402-4394-970e-e598022f5118","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-25T04:00:00.000Z","dayDateKey":"2025-07-25","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:00] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-21 (1)
[18/07/2025 11:37:00] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-07-25 (5)
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-21T04:00:00.000Z","dayDateKey":"2025-07-21","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:00] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-24 ignoré pour attribution 316031c4-9402-4394-970e-e598022f5118
[18/07/2025 11:37:00] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-20 ignoré pour attribution ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 11:37:00] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-24 avant startDate 2025-07-25 (original: 2025-07-25)
[18/07/2025 11:37:00] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-24: {"assignmentId":"316031c4-9402-4394-970e-e598022f5118","dayDateStr":"2025-07-24","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"316031c4-9402-4394-970e-e598022f5118","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-24T04:00:00.000Z","dayDateKey":"2025-07-24","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-20T04:00:00.000Z","dayDateKey":"2025-07-20","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:00] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"316031c4-9402-4394-970e-e598022f5118","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"startDate":"2025-07-25","endDate":"2025-07-27","excludedDates":[]}
[18/07/2025 11:37:00] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-24 (4)
[18/07/2025 11:37:00] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-20 (0)
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:37:00] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-26 ignoré pour attribution 5400f0bd-d560-4167-89ea-a6da77478469
[18/07/2025 11:37:00] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-23 ignoré pour attribution 316031c4-9402-4394-970e-e598022f5118
[18/07/2025 11:37:00] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"5400f0bd-d560-4167-89ea-a6da77478469","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-26T04:00:00.000Z","dayDateKey":"2025-07-26","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:00] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-23 avant startDate 2025-07-25 (original: 2025-07-25)
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-23: {"assignmentId":"316031c4-9402-4394-970e-e598022f5118","dayDateStr":"2025-07-23","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:37:00] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-07-26 (6)
[18/07/2025 11:37:00] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-25 ignoré pour attribution 5400f0bd-d560-4167-89ea-a6da77478469
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"316031c4-9402-4394-970e-e598022f5118","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-23T04:00:00.000Z","dayDateKey":"2025-07-23","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:00] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-25 après endDate 2025-07-22 (original: 2025-07-22) (attribution 5400f0bd-d560-4167-89ea-a6da77478469)
[18/07/2025 11:37:00] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-23 (3)
[18/07/2025 11:37:00] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-23 (3)
[18/07/2025 11:37:00] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-22 ignoré pour attribution 316031c4-9402-4394-970e-e598022f5118
[18/07/2025 11:37:00] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-22 validé, shifts existants: 0
[18/07/2025 11:37:00] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-22 avant startDate 2025-07-25 (original: 2025-07-25)
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-22: {"assignmentId":"316031c4-9402-4394-970e-e598022f5118","dayDateStr":"2025-07-22","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"5400f0bd-d560-4167-89ea-a6da77478469","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-22T04:00:00.000Z","dayDateKey":"2025-07-22","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:37:00] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-22 (2)
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"316031c4-9402-4394-970e-e598022f5118","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-22T04:00:00.000Z","dayDateKey":"2025-07-22","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:00] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-21 ignoré pour attribution 5400f0bd-d560-4167-89ea-a6da77478469
[18/07/2025 11:37:00] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-22 (2)
[18/07/2025 11:37:00] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-21 avant startDate 2025-07-22 (original: 2025-07-22)
[18/07/2025 11:37:00] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-21 ignoré pour attribution 316031c4-9402-4394-970e-e598022f5118
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-21: {"assignmentId":"5400f0bd-d560-4167-89ea-a6da77478469","dayDateStr":"2025-07-21","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:37:00] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-21 avant startDate 2025-07-25 (original: 2025-07-25)
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-21: {"assignmentId":"316031c4-9402-4394-970e-e598022f5118","dayDateStr":"2025-07-21","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"5400f0bd-d560-4167-89ea-a6da77478469","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-21T04:00:00.000Z","dayDateKey":"2025-07-21","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"316031c4-9402-4394-970e-e598022f5118","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-21T04:00:00.000Z","dayDateKey":"2025-07-21","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:00] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-20 ignoré pour attribution 5400f0bd-d560-4167-89ea-a6da77478469
[18/07/2025 11:37:00] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-21 (1)
[18/07/2025 11:37:00] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-21 (1)
[18/07/2025 11:37:00] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-20 (0)
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"5400f0bd-d560-4167-89ea-a6da77478469","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-25T04:00:00.000Z","dayDateKey":"2025-07-25","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"5400f0bd-d560-4167-89ea-a6da77478469","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-20T04:00:00.000Z","dayDateKey":"2025-07-20","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:37:00] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-07-25 (5)
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"08b7356a-e76a-484d-b599-2d5d4a0d8b6b","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-25T04:00:00.000Z","dayDateKey":"2025-07-25","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:37:00] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-24 ignoré pour attribution 5400f0bd-d560-4167-89ea-a6da77478469
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-25: {"assignmentId":"08b7356a-e76a-484d-b599-2d5d4a0d8b6b","dayDateStr":"2025-07-25","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:37:00] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-24 après endDate 2025-07-22 (original: 2025-07-22) (attribution 5400f0bd-d560-4167-89ea-a6da77478469)
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-23: {"assignmentId":"5400f0bd-d560-4167-89ea-a6da77478469","dayDateStr":"2025-07-23","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-24: {"assignmentId":"5400f0bd-d560-4167-89ea-a6da77478469","dayDateStr":"2025-07-24","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:37:00] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-26 ignoré pour attribution 08b7356a-e76a-484d-b599-2d5d4a0d8b6b
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"5400f0bd-d560-4167-89ea-a6da77478469","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-24T04:00:00.000Z","dayDateKey":"2025-07-24","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:00] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-24 (4)
[18/07/2025 11:37:00] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-23 après endDate 2025-07-22 (original: 2025-07-22) (attribution 5400f0bd-d560-4167-89ea-a6da77478469)
[18/07/2025 11:37:00] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-23 ignoré pour attribution 5400f0bd-d560-4167-89ea-a6da77478469
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[18/07/2025 11:37:00] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 08b7356a-e76a-484d-b599-2d5d4a0d8b6b
[18/07/2025 11:37:00] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 0c74e25d-4692-46d7-b5aa-b84eee797971 -> "00:00-08:00"
[18/07/2025 11:37:00] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 0c74e25d-4692-46d7-b5aa-b84eee797971 -> "00:00-08:00"
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 5400f0bd-d560-4167-89ea-a6da77478469
[18/07/2025 11:37:00] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 0c74e25d-4692-46d7-b5aa-b84eee797971 -> "00:00-08:00"
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 316031c4-9402-4394-970e-e598022f5118
[18/07/2025 11:37:00] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 0c74e25d-4692-46d7-b5aa-b84eee797971 -> "00:00-08:00"
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 11:37:00] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 0c74e25d-4692-46d7-b5aa-b84eee797971 -> "00:00-08:00"
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière d546a50b-43af-4f53-afd0-4a43c1f3959a
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[18/07/2025 11:37:00] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[18/07/2025 11:37:00] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[18/07/2025 11:37:00] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 70 zones trouvées
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 70 zones de drop disponibles
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 11:37:00] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 11:37:00] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 11:37:00] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 11:37:00] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 11:37:00] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 11:37:00] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 11:37:00] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 11:37:00] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 11:37:00] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 11:37:00] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[18/07/2025 11:37:00] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 11:37:00] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[18/07/2025 11:37:00] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 11:37:00] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 11:37:00] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🎯 [init] Clic sur icône paramètres détecté !
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🔧 [openSettingsModal] Ouverture du modal demandée via gestionnaire externe
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (70 zones)
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🔧 [ModalFunctionalitiesManager] Initialisation du gestionnaire de modales
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🔍 [MODAL] Ouverture de la modale paramètres
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🔧 [ModalManager] Création du modal paramètres
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [ModalManager] Modal React trouvé, utilisation des conteneurs d'onglets existants
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [ModalManager] Utilisation du modal existant de l'interface React
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [ModalManager] Conteneurs d'onglets détectés dans le modal React
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [ModalManager] Modal paramètres créé avec succès
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🔍 [MODAL] Initialisation des fonctionnalités du modal
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🔍 [MODAL] Éléments trouvés: 6 boutons d'onglets, 6 contenus d'onglets
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [SETUP] Drag & drop des employés configuré (mode simplifié)
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🔍 [MODAL] Activation des onglets: 6 boutons, 6 contenus trouvés
[18/07/2025 11:37:01] [FRONTEND] [WARN] ⚠️ [SETUP] Erreur lors de la destruction: Error: can't access property "Sortable1752853005226", el is null
destroy@http://localhost:5173/node_modules/.vite/deps/sortablejs.js?v=189de042:1812:5
setupDragAndDrop@http://localhost:5173/src/teamCalendarApp.ts?t=1752850722144:6142:33
attachAllEventListeners@http://localhost:5173/src/teamCalendarApp.ts?t=1752850722144:5692:10
actualRender@http://localhost:5173/src/teamCalendarApp.ts?t=1752850722144:2708:10
render/this._debouncedRender<@http://localhost:5173/src/teamCalendarApp.ts?t=1752850722144:2693:12
setTimeout handler*render@http://localhost:5173/src/teamCalendarApp.ts?t=1752850722144:2692:29
navigateWeek@http://localhost:5173/src/teamCalendarApp.ts?t=1752850722144:726:10
setupNavigationListeners/<@http://localhost:5173/src/teamCalendarApp.ts?t=1752850722144:2678:14
EventListener.handleEvent*setupNavigationListeners@http://localhost:5173/src/teamCalendarApp.ts?t=1752850722144:2676:15
init@http://localhost:5173/src/teamCalendarApp.ts?t=1752850722144:1142:10
async*initApp@http://localhost:5173/src/Agenda.tsx?t=1752850722144:40:31
setTimeout handler*AgendaFullscreen/<@http://localhost:5173/src/Agenda.tsx?t=1752850722144:50:29
react-stack-bottom-frame@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:17478:20
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
commitHookEffectListMount@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8460:122
commitHookPassiveMountEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8518:60
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10016:42
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
doubleInvokeEffectsOnFiber@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11461:207
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
recursivelyTraverseAndDoubleInvokeEffectsInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11432:78
commitDoubleInvokeEffectsInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11469:55
flushPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11309:41
node_modules/react-dom/cjs/react-dom-client.development.js/commitRoot/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11060:32
performWorkUntilDeadline@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:36:58
EventHandlerNonNull*node_modules/scheduler/cjs/scheduler.development.js/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:156:9
node_modules/scheduler/cjs/scheduler.development.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:266:7
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/scheduler/index.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:277:24
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/react-dom/cjs/react-dom-client.development.js/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:15216:23
node_modules/react-dom/cjs/react-dom-client.development.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18060:7
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/react-dom/client.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18071:24
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18075:16

[18/07/2025 11:37:01] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🔍 [MODAL] Clic sur onglet: tab-posts -> tabId: posts
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 70 zones trouvées
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 70 zones de drop disponibles
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [MODAL] Onglet activé et contenu affiché: posts
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [MODAL] Gestion des onglets activée
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [MODAL] Fermeture du modal activée
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [MODAL] Fonctionnalités de base activées, les onglets seront peuplés à la demande
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [MODAL] Toutes les fonctionnalités du modal ont été initialisées
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [MODAL] Modal visible
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🔍 [ModalManager] Début renderSettingsContent, vérification des éléments
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🔍 [ModalManager] this.elements.settingsModal = existe
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🔍 [ModalManager] this.elements.settingsContent = existe
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🔧 [ModalManager] Rendu du contenu du modal paramètres
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🔍 [ModalManager] Nombre d'enfants du contenu: 1
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🔧 [ModalManager] Utilisation des conteneurs d'onglets existants du modal React
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [ModalManager] Conteneur onglet posts trouvé, peuplement du contenu
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [ModalManager] Onglet posts déjà peuplé, activation des fonctionnalités
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🔧 [ModalManager] Activation des fonctionnalités pour l'onglet posts
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [MODAL] Gestion des postes activée
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [ModalManager] Fonctionnalités activées pour l'onglet posts
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [ModalManager] Conteneur onglet vacations trouvé, peuplement du contenu
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🔧 [ModalManager] Activation des fonctionnalités pour l'onglet vacations
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [ModalManager] Onglet vacations déjà peuplé, activation des fonctionnalités
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [MODAL] Gestion des vacances activée
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [ModalManager] Fonctionnalités activées pour l'onglet vacations
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [ModalManager] Conteneur onglet assignments trouvé, peuplement du contenu
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [ModalManager] Onglet assignments déjà peuplé, activation des fonctionnalités
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🔧 [ModalManager] Activation des fonctionnalités pour l'onglet assignments
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [MODAL] Gestion des attributions activée
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [ModalManager] Conteneur onglet employee-templates trouvé, peuplement du contenu
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [ModalManager] Fonctionnalités activées pour l'onglet assignments
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [ModalManager] Onglet employee-templates déjà peuplé, activation des fonctionnalités
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🔧 [ModalManager] Activation des fonctionnalités pour l'onglet employee-templates
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [MODAL] Gestion des modèles d'employés activée
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [ModalManager] Fonctionnalités activées pour l'onglet employee-templates
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [ModalManager] Conteneur onglet employees trouvé, peuplement du contenu
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [ModalManager] Onglet employees déjà peuplé, activation des fonctionnalités
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (70 zones)
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🔧 [ModalManager] Activation des fonctionnalités pour l'onglet employees
[18/07/2025 11:37:01] [FRONTEND] [LOG] ⚠️ [validateGripFunctionality] Grip 08b7356a-e76a-484d-b599-2d5d4a0d8b6b invalide: {"hasCorrectId":true,"isDraggable":false,"hasRegularFlag":true,"hasValidDimensions":true,"isVisible":true,"hasCorrectCursor":true}
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [MODAL] Gestion des employés activée
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Recréation du grip défaillant pour 08b7356a-e76a-484d-b599-2d5d4a0d8b6b
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [ModalManager] Conteneur onglet general trouvé, peuplement du contenu
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [ModalManager] Onglet general déjà peuplé, activation des fonctionnalités
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [ModalManager] Fonctionnalités activées pour l'onglet employees
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🔗 [attachGripEvents] Attachement événements pour 08b7356a-e76a-484d-b599-2d5d4a0d8b6b
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🔧 [ModalManager] Activation des fonctionnalités pour l'onglet general
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [createAndAttachGrip] Grip créé et attaché pour 08b7356a-e76a-484d-b599-2d5d4a0d8b6b
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [attachGripEvents] Événements attachés pour 08b7356a-e76a-484d-b599-2d5d4a0d8b6b
[18/07/2025 11:37:01] [FRONTEND] [LOG] ⚠️ [validateGripFunctionality] Grip 5400f0bd-d560-4167-89ea-a6da77478469 invalide: {"hasCorrectId":true,"isDraggable":false,"hasRegularFlag":true,"hasValidDimensions":true,"isVisible":true,"hasCorrectCursor":true}
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Recréation du grip défaillant pour 5400f0bd-d560-4167-89ea-a6da77478469
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🔗 [attachGripEvents] Attachement événements pour 5400f0bd-d560-4167-89ea-a6da77478469
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [createAndAttachGrip] Grip créé et attaché pour 5400f0bd-d560-4167-89ea-a6da77478469
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [attachGripEvents] Événements attachés pour 5400f0bd-d560-4167-89ea-a6da77478469
[18/07/2025 11:37:01] [FRONTEND] [LOG] ⚠️ [validateGripFunctionality] Grip 316031c4-9402-4394-970e-e598022f5118 invalide: {"hasCorrectId":true,"isDraggable":false,"hasRegularFlag":true,"hasValidDimensions":true,"isVisible":true,"hasCorrectCursor":true}
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Recréation du grip défaillant pour 316031c4-9402-4394-970e-e598022f5118
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🔗 [attachGripEvents] Attachement événements pour 316031c4-9402-4394-970e-e598022f5118
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [attachGripEvents] Événements attachés pour 316031c4-9402-4394-970e-e598022f5118
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [createAndAttachGrip] Grip créé et attaché pour 316031c4-9402-4394-970e-e598022f5118
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Recréation du grip défaillant pour ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 11:37:01] [FRONTEND] [LOG] ⚠️ [validateGripFunctionality] Grip ed906322-db11-4d06-aa28-437b06c445c4 invalide: {"hasCorrectId":true,"isDraggable":false,"hasRegularFlag":true,"hasValidDimensions":true,"isVisible":true,"hasCorrectCursor":true}
[18/07/2025 11:37:01] [FRONTEND] [LOG] 🔗 [attachGripEvents] Attachement événements pour ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [attachGripEvents] Événements attachés pour ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 11:37:01] [FRONTEND] [LOG] ✅ [createAndAttachGrip] Grip créé et attaché pour ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 11:37:02] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Recréation du grip défaillant pour d546a50b-43af-4f53-afd0-4a43c1f3959a
[18/07/2025 11:37:02] [FRONTEND] [LOG] ⚠️ [validateGripFunctionality] Grip d546a50b-43af-4f53-afd0-4a43c1f3959a invalide: {"hasCorrectId":true,"isDraggable":false,"hasRegularFlag":true,"hasValidDimensions":true,"isVisible":true,"hasCorrectCursor":true}
[18/07/2025 11:37:02] [FRONTEND] [LOG] 🔗 [attachGripEvents] Attachement événements pour d546a50b-43af-4f53-afd0-4a43c1f3959a
[18/07/2025 11:37:02] [FRONTEND] [LOG] ✅ [createAndAttachGrip] Grip créé et attaché pour d546a50b-43af-4f53-afd0-4a43c1f3959a
[18/07/2025 11:37:02] [FRONTEND] [LOG] ✅ [attachGripEvents] Événements attachés pour d546a50b-43af-4f53-afd0-4a43c1f3959a
[18/07/2025 11:37:02] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] Résultats: 0 ajoutés, 0 validés, 5 corrigés
[18/07/2025 11:37:02] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[18/07/2025 11:37:02] [FRONTEND] [LOG] 🎯 [ensureRegularAssignmentGrips] Grips réguliers maintenant disponibles pour le drag & drop
[18/07/2025 11:37:02] [FRONTEND] [LOG] ✅ [ModalManager] Rendu du contenu terminé
[18/07/2025 11:37:02] [FRONTEND] [LOG] ✅ [MODAL] Paramètres généraux activés
[18/07/2025 11:37:02] [FRONTEND] [LOG] ✅ [ModalManager] Fonctionnalités activées pour l'onglet general
[18/07/2025 11:37:02] [FRONTEND] [LOG] ✅ [MODAL] Modal paramètres ouvert avec succès
[18/07/2025 11:37:02] [FRONTEND] [LOG] 🔍 [ModalManager] Début renderSettingsContent, vérification des éléments
[18/07/2025 11:37:02] [FRONTEND] [LOG] 🔍 [click] Event click détecté sur le bouton des paramètres header
[18/07/2025 11:37:02] [FRONTEND] [LOG] 🔧 [ModalManager] Rendu du contenu du modal paramètres
[18/07/2025 11:37:02] [FRONTEND] [LOG] 🔍 [ModalManager] this.elements.settingsContent = existe
[18/07/2025 11:37:02] [FRONTEND] [LOG] 🔍 [ModalManager] this.elements.settingsModal = existe
[18/07/2025 11:37:02] [FRONTEND] [LOG] ✅ [MODAL] Modal visible
[18/07/2025 11:37:02] [FRONTEND] [LOG] 🔧 [openSettingsModal] Ouverture du modal demandée via gestionnaire externe
[18/07/2025 11:37:02] [FRONTEND] [LOG] 🔧 [ModalFunctionalitiesManager] Initialisation du gestionnaire de modales
[18/07/2025 11:37:02] [FRONTEND] [LOG] ✅ [ModalManager] Conteneur onglet posts trouvé, peuplement du contenu
[18/07/2025 11:37:02] [FRONTEND] [LOG] ✅ [ModalManager] Onglet posts déjà peuplé, activation des fonctionnalités
[18/07/2025 11:37:02] [FRONTEND] [LOG] 🔧 [ModalManager] Utilisation des conteneurs d'onglets existants du modal React
[18/07/2025 11:37:02] [FRONTEND] [LOG] 🔍 [ModalManager] Nombre d'enfants du contenu: 1
[18/07/2025 11:37:02] [FRONTEND] [LOG] 🔍 [MODAL] Ouverture de la modale paramètres
[18/07/2025 11:37:02] [FRONTEND] [LOG] ✅ [ModalManager] Modal paramètres déjà créé
[18/07/2025 11:37:02] [FRONTEND] [LOG] 🔧 [ModalManager] Activation des fonctionnalités pour l'onglet posts
[18/07/2025 11:37:02] [FRONTEND] [LOG] ✅ [MODAL] Gestion des postes activée
[18/07/2025 11:37:02] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[18/07/2025 11:37:02] [FRONTEND] [LOG] ✅ [ModalManager] Fonctionnalités activées pour l'onglet posts
[18/07/2025 11:37:02] [FRONTEND] [LOG] ✅ [ModalManager] Conteneur onglet vacations trouvé, peuplement du contenu
[18/07/2025 11:37:02] [FRONTEND] [LOG] ✅ [ModalManager] Onglet vacations déjà peuplé, activation des fonctionnalités
[18/07/2025 11:37:02] [FRONTEND] [LOG] 🔧 [ModalManager] Activation des fonctionnalités pour l'onglet vacations
[18/07/2025 11:37:02] [FRONTEND] [LOG] ✅ [MODAL] Gestion des vacances activée
[18/07/2025 11:37:02] [FRONTEND] [LOG] ✅ [ModalManager] Fonctionnalités activées pour l'onglet vacations
[18/07/2025 11:37:02] [FRONTEND] [LOG] ✅ [ModalManager] Onglet assignments déjà peuplé, activation des fonctionnalités
[18/07/2025 11:37:02] [FRONTEND] [LOG] ✅ [ModalManager] Conteneur onglet assignments trouvé, peuplement du contenu
[18/07/2025 11:37:02] [FRONTEND] [LOG] 🔧 [ModalManager] Activation des fonctionnalités pour l'onglet assignments
[18/07/2025 11:37:02] [FRONTEND] [LOG] ✅ [MODAL] Gestion des attributions activée
[18/07/2025 11:37:02] [FRONTEND] [LOG] ✅ [ModalManager] Fonctionnalités activées pour l'onglet assignments
[18/07/2025 11:37:02] [FRONTEND] [LOG] ✅ [ModalManager] Conteneur onglet employee-templates trouvé, peuplement du contenu
[18/07/2025 11:37:02] [FRONTEND] [LOG] ✅ [ModalManager] Onglet employee-templates déjà peuplé, activation des fonctionnalités
[18/07/2025 11:37:02] [FRONTEND] [LOG] 🔧 [ModalManager] Activation des fonctionnalités pour l'onglet employee-templates
[18/07/2025 11:37:02] [FRONTEND] [LOG] ✅ [ModalManager] Conteneur onglet employees trouvé, peuplement du contenu
[18/07/2025 11:37:02] [FRONTEND] [LOG] ✅ [ModalManager] Fonctionnalités activées pour l'onglet employee-templates
[18/07/2025 11:37:02] [FRONTEND] [LOG] ✅ [ModalManager] Onglet employees déjà peuplé, activation des fonctionnalités
[18/07/2025 11:37:02] [FRONTEND] [LOG] ✅ [MODAL] Gestion des modèles d'employés activée
[18/07/2025 11:37:02] [FRONTEND] [LOG] 🔧 [ModalManager] Activation des fonctionnalités pour l'onglet employees
[18/07/2025 11:37:02] [FRONTEND] [LOG] ✅ [MODAL] Gestion des employés activée
[18/07/2025 11:37:02] [FRONTEND] [LOG] ✅ [ModalManager] Fonctionnalités activées pour l'onglet employees
[18/07/2025 11:37:02] [FRONTEND] [LOG] ✅ [ModalManager] Conteneur onglet general trouvé, peuplement du contenu
[18/07/2025 11:37:02] [FRONTEND] [LOG] ✅ [ModalManager] Fonctionnalités activées pour l'onglet general
[18/07/2025 11:37:02] [FRONTEND] [LOG] ✅ [MODAL] Paramètres généraux activés
[18/07/2025 11:37:02] [FRONTEND] [LOG] ✅ [MODAL] Modal paramètres ouvert avec succès
[18/07/2025 11:37:02] [FRONTEND] [LOG] ✅ [ModalManager] Rendu du contenu terminé
[18/07/2025 11:37:02] [FRONTEND] [LOG] 🔧 [ModalManager] Activation des fonctionnalités pour l'onglet general
[18/07/2025 11:37:02] [FRONTEND] [LOG] ✅ [ModalManager] Onglet general déjà peuplé, activation des fonctionnalités
[18/07/2025 11:37:02] [FRONTEND] [LOG] 🔍 [MODAL] Clic sur onglet: tab-vacations -> tabId: vacations
[18/07/2025 11:37:02] [FRONTEND] [LOG] ✅ [MODAL] Onglet activé et contenu affiché: vacations
[18/07/2025 11:37:02] [FRONTEND] [LOG] 🔍 [MODAL] Clic sur onglet: tab-assignments -> tabId: assignments
[18/07/2025 11:37:02] [FRONTEND] [LOG] ✅ [MODAL] Onglet activé et contenu affiché: assignments
[18/07/2025 11:37:04] [FRONTEND] [LOG] 🔍 [MODAL] Clic sur onglet: tab-employee-templates -> tabId: employee-templates
[18/07/2025 11:37:04] [FRONTEND] [LOG] ✅ [MODAL] Onglet activé et contenu affiché: employee-templates
[18/07/2025 11:37:04] [FRONTEND] [LOG] 🔍 [MODAL] Clic sur onglet: tab-employees -> tabId: employees
[18/07/2025 11:37:04] [FRONTEND] [LOG] ✅ [MODAL] Onglet activé et contenu affiché: employees
[18/07/2025 11:37:05] [FRONTEND] [LOG] 🔍 [MODAL] Clic sur onglet: tab-general -> tabId: general
[18/07/2025 11:37:05] [FRONTEND] [LOG] ✅ [MODAL] Onglet activé et contenu affiché: general
[18/07/2025 11:37:07] [FRONTEND] [LOG] ✅ [MODAL] Onglet activé et contenu affiché: assignments
[18/07/2025 11:37:07] [FRONTEND] [LOG] 🔍 [MODAL] Clic sur onglet: tab-assignments -> tabId: assignments
[18/07/2025 11:37:09] [FRONTEND] [LOG] 🔍 [MODAL] Clic sur onglet: tab-posts -> tabId: posts
[18/07/2025 11:37:09] [FRONTEND] [LOG] ✅ [MODAL] Onglet activé et contenu affiché: posts
[18/07/2025 11:37:13] [FRONTEND] [LOG] ✅ [MODAL] Modal fermé via bouton
[18/07/2025 11:37:24] [FRONTEND] [LOG] ➡️ Clic sur suivant
[18/07/2025 11:37:24] [FRONTEND] [LOG] 🧭 [navigateWeek] Direction: 1, Offset actuel: 1
[18/07/2025 11:37:24] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 86f97857-d86a-4e2a-aec3-831bf92012d4
[18/07/2025 11:37:24] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 0c74e25d-4692-46d7-b5aa-b84eee797971 -> "00:00-08:00"
[18/07/2025 11:37:24] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 86f97857-d86a-4e2a-aec3-831bf92012d4
[18/07/2025 11:37:24] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 0c74e25d-4692-46d7-b5aa-b84eee797971 -> "00:00-08:00"
[18/07/2025 11:37:24] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 6830f981-f642-4364-9b1d-0ff92e12fad2
[18/07/2025 11:37:24] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[18/07/2025 11:37:24] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[18/07/2025 11:37:24] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 0c74e25d-4692-46d7-b5aa-b84eee797971 -> "00:00-08:00"
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:37:24.587Z"
}
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 0c74e25d-4692-46d7-b5aa-b84eee797971 -> "00:00-08:00"
[18/07/2025 11:37:25] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 0c74e25d-4692-46d7-b5aa-b84eee797971 -> "00:00-08:00"
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 42c64a69-ab23-451f-a314-c5c87622675d
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 42c64a69-ab23-451f-a314-c5c87622675d
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:37:24.604Z"
}
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 70 zones trouvées
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 70 zones de drop disponibles
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:37:24.879Z"
}
[18/07/2025 11:37:25] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:37:24.700Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:37:24.903Z"
}
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:37:24.734Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:37:24.730Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:37:24.756Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:37:24.758Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:37:24.951Z"
}
[18/07/2025 11:37:25] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:37:24.952Z"
}
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:37:24.964Z"
}
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:37:24.778Z"
}
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:37:25.077Z"
}
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 11:37:25] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T15:37:24.909Z"
}
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (70 zones)
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [SETUP] Drag & drop des employés configuré (mode simplifié)
[18/07/2025 11:37:25] [FRONTEND] [WARN] ⚠️ [SETUP] Erreur lors de la destruction: Error: can't access property "Sortable1752853005226", el is null
destroy@http://localhost:5173/node_modules/.vite/deps/sortablejs.js?v=189de042:1812:5
setupDragAndDrop@http://localhost:5173/src/teamCalendarApp.ts?t=1752850722144:6142:33
attachAllEventListeners@http://localhost:5173/src/teamCalendarApp.ts?t=1752850722144:5692:10
actualRender@http://localhost:5173/src/teamCalendarApp.ts?t=1752850722144:2708:10
render/this._debouncedRender<@http://localhost:5173/src/teamCalendarApp.ts?t=1752850722144:2693:12
setTimeout handler*render@http://localhost:5173/src/teamCalendarApp.ts?t=1752850722144:2692:29
navigateWeek@http://localhost:5173/src/teamCalendarApp.ts?t=1752850722144:726:10
setupNavigationListeners/<@http://localhost:5173/src/teamCalendarApp.ts?t=1752850722144:2678:14
EventListener.handleEvent*setupNavigationListeners@http://localhost:5173/src/teamCalendarApp.ts?t=1752850722144:2676:15
init@http://localhost:5173/src/teamCalendarApp.ts?t=1752850722144:1142:10
async*initApp@http://localhost:5173/src/Agenda.tsx?t=1752850722144:40:31
setTimeout handler*AgendaFullscreen/<@http://localhost:5173/src/Agenda.tsx?t=1752850722144:50:29
react-stack-bottom-frame@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:17478:20
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
commitHookEffectListMount@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8460:122
commitHookPassiveMountEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8518:60
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10016:42
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
doubleInvokeEffectsOnFiber@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11461:207
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
recursivelyTraverseAndDoubleInvokeEffectsInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11432:78
commitDoubleInvokeEffectsInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11469:55
flushPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11309:41
node_modules/react-dom/cjs/react-dom-client.development.js/commitRoot/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11060:32
performWorkUntilDeadline@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:36:58
EventHandlerNonNull*node_modules/scheduler/cjs/scheduler.development.js/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:156:9
node_modules/scheduler/cjs/scheduler.development.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:266:7
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/scheduler/index.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:277:24
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/react-dom/cjs/react-dom-client.development.js/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:15216:23
node_modules/react-dom/cjs/react-dom-client.development.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18060:7
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/react-dom/client.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18071:24
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18075:16

[18/07/2025 11:37:25] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 70 zones trouvées
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 70 zones de drop disponibles
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (70 zones)
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[18/07/2025 11:37:25] [FRONTEND] [LOG] ⚠️ [validateGripFunctionality] Grip 86f97857-d86a-4e2a-aec3-831bf92012d4 invalide: {"hasCorrectId":true,"isDraggable":false,"hasRegularFlag":true,"hasValidDimensions":true,"isVisible":true,"hasCorrectCursor":true}
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Recréation du grip défaillant pour 86f97857-d86a-4e2a-aec3-831bf92012d4
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🔗 [attachGripEvents] Attachement événements pour 86f97857-d86a-4e2a-aec3-831bf92012d4
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [createAndAttachGrip] Grip créé et attaché pour 86f97857-d86a-4e2a-aec3-831bf92012d4
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [attachGripEvents] Événements attachés pour 86f97857-d86a-4e2a-aec3-831bf92012d4
[18/07/2025 11:37:25] [FRONTEND] [LOG] ⚠️ [validateGripFunctionality] Grip 86f97857-d86a-4e2a-aec3-831bf92012d4 invalide: {"hasCorrectId":true,"isDraggable":false,"hasRegularFlag":true,"hasValidDimensions":true,"isVisible":true,"hasCorrectCursor":true}
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🔗 [attachGripEvents] Attachement événements pour 86f97857-d86a-4e2a-aec3-831bf92012d4
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Recréation du grip défaillant pour 86f97857-d86a-4e2a-aec3-831bf92012d4
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [attachGripEvents] Événements attachés pour 86f97857-d86a-4e2a-aec3-831bf92012d4
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [createAndAttachGrip] Grip créé et attaché pour 86f97857-d86a-4e2a-aec3-831bf92012d4
[18/07/2025 11:37:25] [FRONTEND] [LOG] ⚠️ [validateGripFunctionality] Grip 6830f981-f642-4364-9b1d-0ff92e12fad2 invalide: {"hasCorrectId":true,"isDraggable":false,"hasRegularFlag":true,"hasValidDimensions":true,"isVisible":true,"hasCorrectCursor":true}
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Recréation du grip défaillant pour 6830f981-f642-4364-9b1d-0ff92e12fad2
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🔗 [attachGripEvents] Attachement événements pour 6830f981-f642-4364-9b1d-0ff92e12fad2
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [attachGripEvents] Événements attachés pour 6830f981-f642-4364-9b1d-0ff92e12fad2
[18/07/2025 11:37:25] [FRONTEND] [LOG] ✅ [createAndAttachGrip] Grip créé et attaché pour 6830f981-f642-4364-9b1d-0ff92e12fad2
[18/07/2025 11:37:25] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Recréation du grip défaillant pour 42c64a69-ab23-451f-a314-c5c87622675d
[18/07/2025 11:37:25] [FRONTEND] [LOG] ⚠️ [validateGripFunctionality] Grip 42c64a69-ab23-451f-a314-c5c87622675d invalide: {"hasCorrectId":true,"isDraggable":false,"hasRegularFlag":true,"hasValidDimensions":true,"isVisible":true,"hasCorrectCursor":true}
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🔗 [attachGripEvents] Attachement événements pour 42c64a69-ab23-451f-a314-c5c87622675d
[18/07/2025 11:37:26] [FRONTEND] [LOG] ✅ [attachGripEvents] Événements attachés pour 42c64a69-ab23-451f-a314-c5c87622675d
[18/07/2025 11:37:26] [FRONTEND] [LOG] ✅ [createAndAttachGrip] Grip créé et attaché pour 42c64a69-ab23-451f-a314-c5c87622675d
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Recréation du grip défaillant pour 42c64a69-ab23-451f-a314-c5c87622675d
[18/07/2025 11:37:26] [FRONTEND] [LOG] ⚠️ [validateGripFunctionality] Grip 42c64a69-ab23-451f-a314-c5c87622675d invalide: {"hasCorrectId":true,"isDraggable":false,"hasRegularFlag":true,"hasValidDimensions":true,"isVisible":true,"hasCorrectCursor":true}
[18/07/2025 11:37:26] [FRONTEND] [LOG] ✅ [attachGripEvents] Événements attachés pour 42c64a69-ab23-451f-a314-c5c87622675d
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🔗 [attachGripEvents] Attachement événements pour 42c64a69-ab23-451f-a314-c5c87622675d
[18/07/2025 11:37:26] [FRONTEND] [LOG] ✅ [createAndAttachGrip] Grip créé et attaché pour 42c64a69-ab23-451f-a314-c5c87622675d
[18/07/2025 11:37:26] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] Résultats: 0 ajoutés, 0 validés, 5 corrigés
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🎯 [ensureRegularAssignmentGrips] Grips réguliers maintenant disponibles pour le drag & drop
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[18/07/2025 11:37:26] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] Nettoyage des shifts réguliers existants...
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] 0 shifts réguliers supprimés
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Cache invalidé pour forcer la régénération
[18/07/2025 11:37:26] [FRONTEND] [LOG] 📋 [navigateWeek] Application des attributions pour la nouvelle semaine 2025-W30
[18/07/2025 11:37:26] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Application pour semaine 2025-W30
[18/07/2025 11:37:26] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution 86f97857-d86a-4e2a-aec3-831bf92012d4 a une intersection avec cette semaine (2025-07-28 → 2025-07-29) vs (2025-07-27 → 2025-08-02)
[18/07/2025 11:37:26] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution 6830f981-f642-4364-9b1d-0ff92e12fad2 a une intersection avec cette semaine (2025-08-01 → ∞) vs (2025-07-27 → 2025-08-02)
[18/07/2025 11:37:26] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 08b7356a-e76a-484d-b599-2d5d4a0d8b6b terminée avant cette semaine (endDate: 2025-07-23, semaine commence: 2025-07-27)
[18/07/2025 11:37:26] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution 42c64a69-ab23-451f-a314-c5c87622675d a une intersection avec cette semaine (2025-07-30 → 2025-07-31) vs (2025-07-27 → 2025-08-02)
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"42c64a69-ab23-451f-a314-c5c87622675d","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-27T04:00:00.000Z","dayDateKey":"2025-07-27","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:26] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-28 (1)
[18/07/2025 11:37:26] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-27 ignoré pour attribution 42c64a69-ab23-451f-a314-c5c87622675d
[18/07/2025 11:37:26] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-27 (0)
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"42c64a69-ab23-451f-a314-c5c87622675d","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-29T04:00:00.000Z","dayDateKey":"2025-07-29","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-29: {"assignmentId":"42c64a69-ab23-451f-a314-c5c87622675d","dayDateStr":"2025-07-29","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:37:26] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-30 (3)
[18/07/2025 11:37:26] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 5 attributions appliquées pour la semaine 2025-W30
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-08-01: {"assignmentId":"316031c4-9402-4394-970e-e598022f5118","dayDateStr":"2025-08-01","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:37:26] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-08-01 (5)
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"316031c4-9402-4394-970e-e598022f5118","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-08-01T04:00:00.000Z","dayDateKey":"2025-08-01","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Rendu demandé car 5 shifts créés
[18/07/2025 11:37:26] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-08-02 ignoré pour attribution 316031c4-9402-4394-970e-e598022f5118
[18/07/2025 11:37:26] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[18/07/2025 11:37:26] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-31 ignoré pour attribution 316031c4-9402-4394-970e-e598022f5118
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-31: {"assignmentId":"316031c4-9402-4394-970e-e598022f5118","dayDateStr":"2025-07-31","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:37:26] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-08-01 ignoré pour attribution 316031c4-9402-4394-970e-e598022f5118
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"316031c4-9402-4394-970e-e598022f5118","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-08-02T04:00:00.000Z","dayDateKey":"2025-08-02","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:26] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-08-02 (6)
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:37:26] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-31 après endDate 2025-07-27 (original: 2025-07-27) (attribution 316031c4-9402-4394-970e-e598022f5118)
[18/07/2025 11:37:26] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-08-01 après endDate 2025-07-27 (original: 2025-07-27) (attribution 316031c4-9402-4394-970e-e598022f5118)
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"316031c4-9402-4394-970e-e598022f5118","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-31T04:00:00.000Z","dayDateKey":"2025-07-31","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:26] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-29 ignoré pour attribution 42c64a69-ab23-451f-a314-c5c87622675d
[18/07/2025 11:37:26] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-29 (2)
[18/07/2025 11:37:26] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-30 ignoré pour attribution 316031c4-9402-4394-970e-e598022f5118
[18/07/2025 11:37:26] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-29 avant startDate 2025-07-30 (original: 2025-07-30)
[18/07/2025 11:37:26] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-31 (4)
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-30: {"assignmentId":"316031c4-9402-4394-970e-e598022f5118","dayDateStr":"2025-07-30","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:37:26] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-28 ignoré pour attribution 42c64a69-ab23-451f-a314-c5c87622675d
[18/07/2025 11:37:26] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-30 après endDate 2025-07-27 (original: 2025-07-27) (attribution 316031c4-9402-4394-970e-e598022f5118)
[18/07/2025 11:37:26] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-28 avant startDate 2025-07-30 (original: 2025-07-30)
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:37:26] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 5400f0bd-d560-4167-89ea-a6da77478469 terminée avant cette semaine (endDate: 2025-07-22, semaine commence: 2025-07-27)
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"316031c4-9402-4394-970e-e598022f5118","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-30T04:00:00.000Z","dayDateKey":"2025-07-30","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-28: {"assignmentId":"42c64a69-ab23-451f-a314-c5c87622675d","dayDateStr":"2025-07-28","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:37:26] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-30 (3)
[18/07/2025 11:37:26] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-29 ignoré pour attribution 316031c4-9402-4394-970e-e598022f5118
[18/07/2025 11:37:26] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution 316031c4-9402-4394-970e-e598022f5118 a une intersection avec cette semaine (2025-07-25 → 2025-07-27) vs (2025-07-27 → 2025-08-02)
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-29: {"assignmentId":"316031c4-9402-4394-970e-e598022f5118","dayDateStr":"2025-07-29","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:37:26] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution ed906322-db11-4d06-aa28-437b06c445c4 terminée avant cette semaine (endDate: 2025-07-21, semaine commence: 2025-07-27)
[18/07/2025 11:37:26] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution d546a50b-43af-4f53-afd0-4a43c1f3959a terminée avant cette semaine (endDate: 2025-07-24, semaine commence: 2025-07-27)
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:37:26] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 4 attributions actives sur 8 total
[18/07/2025 11:37:26] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-29 après endDate 2025-07-27 (original: 2025-07-27) (attribution 316031c4-9402-4394-970e-e598022f5118)
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"316031c4-9402-4394-970e-e598022f5118","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-29T04:00:00.000Z","dayDateKey":"2025-07-29","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:26] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"86f97857-d86a-4e2a-aec3-831bf92012d4","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"startDate":"2025-07-28","endDate":"2025-07-29","excludedDates":[]}
[18/07/2025 11:37:26] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-28 ignoré pour attribution 316031c4-9402-4394-970e-e598022f5118
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution 86f97857-d86a-4e2a-aec3-831bf92012d4: [{"dateKey":"2025-07-27","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-28","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-29","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-30","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-31","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-08-01","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-08-02","isWeekend":true,"dayOfWeek":6}]
[18/07/2025 11:37:26] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-27 (0)
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-28: {"assignmentId":"316031c4-9402-4394-970e-e598022f5118","dayDateStr":"2025-07-28","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"86f97857-d86a-4e2a-aec3-831bf92012d4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-27T04:00:00.000Z","dayDateKey":"2025-07-27","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:26] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-28 après endDate 2025-07-27 (original: 2025-07-27) (attribution 316031c4-9402-4394-970e-e598022f5118)
[18/07/2025 11:37:26] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-29 (2)
[18/07/2025 11:37:26] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[18/07/2025 11:37:26] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-27 ignoré pour attribution 86f97857-d86a-4e2a-aec3-831bf92012d4
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:37:26] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-28 (1)
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"316031c4-9402-4394-970e-e598022f5118","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-28T04:00:00.000Z","dayDateKey":"2025-07-28","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-28: {"assignmentId":"86f97857-d86a-4e2a-aec3-831bf92012d4","dayDateStr":"2025-07-28","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"86f97857-d86a-4e2a-aec3-831bf92012d4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-28T04:00:00.000Z","dayDateKey":"2025-07-28","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:26] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-27 ignoré pour attribution 316031c4-9402-4394-970e-e598022f5118
[18/07/2025 11:37:26] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-28 (1)
[18/07/2025 11:37:26] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"316031c4-9402-4394-970e-e598022f5118","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-27T04:00:00.000Z","dayDateKey":"2025-07-27","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:26] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-28
[18/07/2025 11:37:26] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Nuit → 2025-07-28 pour Lucas Bernard
[18/07/2025 11:37:26] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-28 validé, shifts existants: 0
[18/07/2025 11:37:26] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-27 (0)
[18/07/2025 11:37:26] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-29 (2)
[18/07/2025 11:37:26] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"316031c4-9402-4394-970e-e598022f5118","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"startDate":"2025-07-25","endDate":"2025-07-27","excludedDates":[]}
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution 316031c4-9402-4394-970e-e598022f5118: [{"dateKey":"2025-07-27","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-28","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-29","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-30","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-31","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-08-01","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-08-02","isWeekend":true,"dayOfWeek":6}]
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"86f97857-d86a-4e2a-aec3-831bf92012d4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-29T04:00:00.000Z","dayDateKey":"2025-07-29","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:26] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-08-02 ignoré pour attribution 42c64a69-ab23-451f-a314-c5c87622675d
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"42c64a69-ab23-451f-a314-c5c87622675d","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-08-02T04:00:00.000Z","dayDateKey":"2025-08-02","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:26] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-08-02 (6)
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-29: {"assignmentId":"86f97857-d86a-4e2a-aec3-831bf92012d4","dayDateStr":"2025-07-29","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:37:26] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-29
[18/07/2025 11:37:26] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[18/07/2025 11:37:26] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-29 validé, shifts existants: 0
[18/07/2025 11:37:26] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-08-01 ignoré pour attribution 42c64a69-ab23-451f-a314-c5c87622675d
[18/07/2025 11:37:26] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Nuit → 2025-07-29 pour Lucas Bernard
[18/07/2025 11:37:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-08-01: {"assignmentId":"42c64a69-ab23-451f-a314-c5c87622675d","dayDateStr":"2025-08-01","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:37:26] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-08-01 après endDate 2025-07-31 (original: 2025-07-31) (attribution 42c64a69-ab23-451f-a314-c5c87622675d)
[18/07/2025 11:37:26] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-30 (3)
[18/07/2025 11:37:27] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"86f97857-d86a-4e2a-aec3-831bf92012d4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-30T04:00:00.000Z","dayDateKey":"2025-07-30","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:27] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:37:27] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:37:27] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"42c64a69-ab23-451f-a314-c5c87622675d","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-08-01T04:00:00.000Z","dayDateKey":"2025-08-01","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:27] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-30: {"assignmentId":"86f97857-d86a-4e2a-aec3-831bf92012d4","dayDateStr":"2025-07-30","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:37:27] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-08-01 (5)
[18/07/2025 11:37:27] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-31 validé, shifts existants: 0
[18/07/2025 11:37:27] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-30 après endDate 2025-07-29 (original: 2025-07-29) (attribution 86f97857-d86a-4e2a-aec3-831bf92012d4)
[18/07/2025 11:37:27] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-30 ignoré pour attribution 86f97857-d86a-4e2a-aec3-831bf92012d4
[18/07/2025 11:37:27] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-31 (4)
[18/07/2025 11:37:27] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-31
[18/07/2025 11:37:27] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Nuit → 2025-07-31 pour Marie Martin
[18/07/2025 11:37:27] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:37:27] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:37:27] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-31: {"assignmentId":"42c64a69-ab23-451f-a314-c5c87622675d","dayDateStr":"2025-07-31","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:37:27] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"86f97857-d86a-4e2a-aec3-831bf92012d4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-31T04:00:00.000Z","dayDateKey":"2025-07-31","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:27] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-31: {"assignmentId":"86f97857-d86a-4e2a-aec3-831bf92012d4","dayDateStr":"2025-07-31","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:37:27] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-31 après endDate 2025-07-29 (original: 2025-07-29) (attribution 86f97857-d86a-4e2a-aec3-831bf92012d4)
[18/07/2025 11:37:27] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"42c64a69-ab23-451f-a314-c5c87622675d","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-31T04:00:00.000Z","dayDateKey":"2025-07-31","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:27] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Nuit → 2025-07-30 pour Marie Martin
[18/07/2025 11:37:27] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-31 (4)
[18/07/2025 11:37:27] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-31 ignoré pour attribution 86f97857-d86a-4e2a-aec3-831bf92012d4
[18/07/2025 11:37:27] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-08-01 (5)
[18/07/2025 11:37:27] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-30: {"assignmentId":"42c64a69-ab23-451f-a314-c5c87622675d","dayDateStr":"2025-07-30","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:37:27] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-08-01: {"assignmentId":"86f97857-d86a-4e2a-aec3-831bf92012d4","dayDateStr":"2025-08-01","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:37:27] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-30 validé, shifts existants: 0
[18/07/2025 11:37:27] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"86f97857-d86a-4e2a-aec3-831bf92012d4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-08-01T04:00:00.000Z","dayDateKey":"2025-08-01","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:27] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-30
[18/07/2025 11:37:27] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:37:27] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:37:27] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-08-01 après endDate 2025-07-29 (original: 2025-07-29) (attribution 86f97857-d86a-4e2a-aec3-831bf92012d4)
[18/07/2025 11:37:27] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"42c64a69-ab23-451f-a314-c5c87622675d","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-30T04:00:00.000Z","dayDateKey":"2025-07-30","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:27] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-08-01 ignoré pour attribution 86f97857-d86a-4e2a-aec3-831bf92012d4
[18/07/2025 11:37:27] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-08-02 (6)
[18/07/2025 11:37:27] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:37:27] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"42c64a69-ab23-451f-a314-c5c87622675d","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-28T04:00:00.000Z","dayDateKey":"2025-07-28","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:27] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"86f97857-d86a-4e2a-aec3-831bf92012d4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-08-02T04:00:00.000Z","dayDateKey":"2025-08-02","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:27] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[18/07/2025 11:37:27] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution 42c64a69-ab23-451f-a314-c5c87622675d: [{"dateKey":"2025-07-27","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-28","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-29","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-30","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-31","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-08-01","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-08-02","isWeekend":true,"dayOfWeek":6}]
[18/07/2025 11:37:27] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[18/07/2025 11:37:27] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-08-02 ignoré pour attribution 86f97857-d86a-4e2a-aec3-831bf92012d4
[18/07/2025 11:37:27] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"6830f981-f642-4364-9b1d-0ff92e12fad2","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"startDate":"2025-08-01","endDate":null,"excludedDates":[]}
[18/07/2025 11:37:27] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"42c64a69-ab23-451f-a314-c5c87622675d","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"startDate":"2025-07-30","endDate":"2025-07-31","excludedDates":[]}
[18/07/2025 11:37:27] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-08-02 ignoré pour attribution 6830f981-f642-4364-9b1d-0ff92e12fad2
[18/07/2025 11:37:27] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution 6830f981-f642-4364-9b1d-0ff92e12fad2: [{"dateKey":"2025-07-27","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-28","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-29","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-30","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-31","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-08-01","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-08-02","isWeekend":true,"dayOfWeek":6}]
[18/07/2025 11:37:27] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[18/07/2025 11:37:27] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"6830f981-f642-4364-9b1d-0ff92e12fad2","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-08-02T04:00:00.000Z","dayDateKey":"2025-08-02","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:27] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[18/07/2025 11:37:27] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-27 ignoré pour attribution 6830f981-f642-4364-9b1d-0ff92e12fad2
[18/07/2025 11:37:27] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Nuit → 2025-08-01 pour Lucas Bernard
[18/07/2025 11:37:27] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-08-02 (6)
[18/07/2025 11:37:27] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-27 (0)
[18/07/2025 11:37:27] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"6830f981-f642-4364-9b1d-0ff92e12fad2","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-27T04:00:00.000Z","dayDateKey":"2025-07-27","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:27] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-08-01 validé, shifts existants: 0
[18/07/2025 11:37:27] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-28 (1)
[18/07/2025 11:37:27] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-08-01
[18/07/2025 11:37:27] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"6830f981-f642-4364-9b1d-0ff92e12fad2","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-28T04:00:00.000Z","dayDateKey":"2025-07-28","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:27] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-28: {"assignmentId":"6830f981-f642-4364-9b1d-0ff92e12fad2","dayDateStr":"2025-07-28","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:37:27] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:37:27] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-08-01: {"assignmentId":"6830f981-f642-4364-9b1d-0ff92e12fad2","dayDateStr":"2025-08-01","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:37:27] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:37:27] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"6830f981-f642-4364-9b1d-0ff92e12fad2","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-08-01T04:00:00.000Z","dayDateKey":"2025-08-01","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:27] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-31 ignoré pour attribution 6830f981-f642-4364-9b1d-0ff92e12fad2
[18/07/2025 11:37:27] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-28 avant startDate 2025-08-01 (original: 2025-08-01)
[18/07/2025 11:37:27] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-28 ignoré pour attribution 6830f981-f642-4364-9b1d-0ff92e12fad2
[18/07/2025 11:37:27] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-08-01 (5)
[18/07/2025 11:37:27] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"6830f981-f642-4364-9b1d-0ff92e12fad2","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-29T04:00:00.000Z","dayDateKey":"2025-07-29","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:27] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-31 avant startDate 2025-08-01 (original: 2025-08-01)
[18/07/2025 11:37:27] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-29 (2)
[18/07/2025 11:37:27] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-31: {"assignmentId":"6830f981-f642-4364-9b1d-0ff92e12fad2","dayDateStr":"2025-07-31","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:37:27] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:37:27] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:37:27] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-29: {"assignmentId":"6830f981-f642-4364-9b1d-0ff92e12fad2","dayDateStr":"2025-07-29","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:37:27] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"6830f981-f642-4364-9b1d-0ff92e12fad2","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-31T04:00:00.000Z","dayDateKey":"2025-07-31","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:27] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-29 avant startDate 2025-08-01 (original: 2025-08-01)
[18/07/2025 11:37:27] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-29 ignoré pour attribution 6830f981-f642-4364-9b1d-0ff92e12fad2
[18/07/2025 11:37:27] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-31 (4)
[18/07/2025 11:37:27] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-30 (3)
[18/07/2025 11:37:27] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-30 ignoré pour attribution 6830f981-f642-4364-9b1d-0ff92e12fad2
[18/07/2025 11:37:27] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-30 avant startDate 2025-08-01 (original: 2025-08-01)
[18/07/2025 11:37:27] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-30: {"assignmentId":"6830f981-f642-4364-9b1d-0ff92e12fad2","dayDateStr":"2025-07-30","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 11:37:27] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"6830f981-f642-4364-9b1d-0ff92e12fad2","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-30T04:00:00.000Z","dayDateKey":"2025-07-30","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 11:37:27] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 11:37:27] [FRONTEND] [LOG] 🎯 [POST DRAG] Début drag poste: 0c74e25d-4692-46d7-b5aa-b84eee797971 (Poste Nuit)
[18/07/2025 11:37:27] [FRONTEND] [LOG] 🎯 [POST DRAG] DataTransfer configuré: {"postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","label":"Poste Nuit","types":["text/plain","application/x-post-id","postid","postlabel"]}
[18/07/2025 11:37:28] [FRONTEND] [LOG] 🎨 [enableAgendaPreviewMode] Activation preview agenda pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 11:37:28] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[18/07/2025 11:37:28] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[18/07/2025 11:37:28] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] Ajout des fantômes pour Poste Nuit
[18/07/2025 11:37:28] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] 25 fantômes créés
[18/07/2025 11:37:28] [FRONTEND] [LOG] 🖱️ [enableCellHoverTracking] Suivi activé sur 25 cellules
[18/07/2025 11:37:28] [FRONTEND] [LOG] ✅ [enableAgendaPreviewMode] Mode preview activé pour Poste Nuit
[18/07/2025 11:37:28] [FRONTEND] [LOG] 🎯 [POST DRAG] Début drag poste: 0c74e25d-4692-46d7-b5aa-b84eee797971 (Poste Nuit)
[18/07/2025 11:37:28] [FRONTEND] [LOG] 🎯 [POST DRAG] 70 zones préparées pour le drop
[18/07/2025 11:37:28] [FRONTEND] [LOG] 🎯 [POST DRAG] DataTransfer configuré: {"postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","label":"Poste Nuit","types":["text/plain","application/x-post-id","postid","postlabel"]}
[18/07/2025 11:37:28] [FRONTEND] [LOG] 🎨 [enableAgendaPreviewMode] Activation preview agenda pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 11:37:28] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[18/07/2025 11:37:28] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[18/07/2025 11:37:28] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] Ajout des fantômes pour Poste Nuit
[18/07/2025 11:37:28] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] 25 fantômes créés
[18/07/2025 11:37:28] [FRONTEND] [LOG] 🖱️ [enableCellHoverTracking] Suivi activé sur 50 cellules
[18/07/2025 11:37:28] [FRONTEND] [LOG] ✅ [enableAgendaPreviewMode] Mode preview activé pour Poste Nuit
[18/07/2025 11:37:28] [FRONTEND] [LOG] 🎯 [POST DRAG] 70 zones préparées pour le drop
[18/07/2025 11:37:28] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 18fe9cd1-e8a5-44e3-90b3-a856086ce27e, types: text/plain, application/x-post-id, postid, postlabel
[18/07/2025 11:37:28] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a, types: text/plain, application/x-post-id, postid, postlabel
[18/07/2025 11:37:28] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé cf78e945-72c7-48f3-a72d-bd0e245a284d, types: text/plain, application/x-post-id, postid, postlabel
[18/07/2025 11:37:28] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 42b53805-18ba-425e-bb00-52bb8d6ce76b, types: text/plain, application/x-post-id, postid, postlabel
[18/07/2025 11:37:28] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 42b53805-18ba-425e-bb00-52bb8d6ce76b, types: text/plain, application/x-post-id, postid, postlabel
[18/07/2025 11:37:28] [FRONTEND] [LOG] 🎯 [DROP] ✅ Drop reçu sur employé: Jean Dupont
[18/07/2025 11:37:28] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[18/07/2025 11:37:28] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[18/07/2025 11:37:28] [FRONTEND] [LOG] ✅ [DROP] PostId trouvé via application/x-post-id: "0c74e25d-4692-46d7-b5aa-b84eee797971"
[18/07/2025 11:37:28] [FRONTEND] [LOG] 📅 [openAssignmentContextModal] Date initialisée: 2025-07-18
[18/07/2025 11:37:28] [FRONTEND] [LOG] ℹ️ [openAssignmentContextModal] Option "Attribution unique (cellule)" masquée - contexte inapproprié
[18/07/2025 11:37:28] [FRONTEND] [LOG] 📦 [DROP] Nouveau poste: 0c74e25d-4692-46d7-b5aa-b84eee797971 → 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 11:37:28] [FRONTEND] [LOG] 🎯 [openAssignmentContextModal] Poste brut: 0c74e25d-4692-46d7-b5aa-b84eee797971, Employé: 42b53805-18ba-425e-bb00-52bb8d6ce76b, Contexte: null
[18/07/2025 11:37:28] [FRONTEND] [LOG] ✅ [DROP] Modal d'assignation ouvert
[18/07/2025 11:37:28] [FRONTEND] [LOG] 🎯 [POST DRAG] Fin drag poste: 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 11:37:28] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[18/07/2025 11:37:28] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[18/07/2025 11:37:28] [FRONTEND] [LOG] 🎯 [POST DRAG] Nettoyage des 70 zones
[18/07/2025 11:37:28] [FRONTEND] [LOG] 🎯 [POST DRAG] Fin drag poste: 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 11:37:28] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[18/07/2025 11:37:29] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[18/07/2025 11:37:29] [FRONTEND] [LOG] 🎯 [POST DRAG] Nettoyage des 70 zones