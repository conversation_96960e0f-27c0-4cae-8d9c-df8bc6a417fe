[14/07/2025 14:29:19] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[14/07/2025 14:29:19] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[14/07/2025 14:29:19] [FRONTEND] [LOG] [LogsPage] Fermeture de la connexion SSE.
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:29:20.120Z"
}
[14/07/2025 14:29:19] [BACKEND] [INFO] [LOGS] Client connecté: 1752517760109
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:29:20.110Z"
}
[14/07/2025 14:29:19] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:29:20.121Z"
}
[14/07/2025 14:29:19] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:29:20.251Z"
}
[14/07/2025 14:29:19] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[14/07/2025 14:29:19] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:29:20.253Z"
}
[14/07/2025 13:56:14] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Rendu demandé car 14 shifts créés
[14/07/2025 13:56:14] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: c8d557c3-bb9d-45bf-a90b-a98192f08d76 -> "00:00-12:00"
[14/07/2025 13:56:14] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-19 validé, shifts existants: 0
[14/07/2025 13:56:14] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste WE1 → 2025-07-19 pour Sophie Leblanc
[14/07/2025 13:56:14] [FRONTEND] [LOG] 🎉 [Agenda] Application fullscreen initialisée avec succès
[14/07/2025 13:56:14] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[14/07/2025 13:56:14] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[14/07/2025 13:56:14] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière e1d1ea5b-67e9-45a0-9498-ef20cefd302b
[14/07/2025 13:56:14] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: c8d557c3-bb9d-45bf-a90b-a98192f08d76 -> "00:00-12:00"
[14/07/2025 13:56:14] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 0e114dfe-053e-4c8d-ba1b-1edb6f814fcf
[14/07/2025 13:56:14] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 97a5a09a-60b7-495c-9796-bc44cfc56585 -> "08:00-16:00"
[14/07/2025 13:56:14] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière cd38f349-5704-4012-a750-93be50f87b34
[14/07/2025 13:56:14] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière e1d1ea5b-67e9-45a0-9498-ef20cefd302b
[14/07/2025 13:56:14] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière cd38f349-5704-4012-a750-93be50f87b34
[14/07/2025 13:56:14] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 97a5a09a-60b7-495c-9796-bc44cfc56585 -> "08:00-16:00"
[14/07/2025 13:56:14] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière cd38f349-5704-4012-a750-93be50f87b34
[14/07/2025 13:56:14] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 97a5a09a-60b7-495c-9796-bc44cfc56585 -> "08:00-16:00"
[14/07/2025 13:56:14] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière cd38f349-5704-4012-a750-93be50f87b34
[14/07/2025 13:56:14] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: a57ffb59-baf2-454a-b825-4e74d4ae4787 -> "12:00-24:00"
[14/07/2025 13:56:14] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 97a5a09a-60b7-495c-9796-bc44cfc56585 -> "08:00-16:00"
[14/07/2025 13:56:14] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 4d1e2a29-162d-4f25-8b37-f5fe83494a73
[14/07/2025 13:56:14] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: a57ffb59-baf2-454a-b825-4e74d4ae4787 -> "12:00-24:00"
[14/07/2025 13:56:14] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 4d1e2a29-162d-4f25-8b37-f5fe83494a73
[14/07/2025 13:56:14] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière f6cfa7cb-0f48-4753-82d7-7fc39b9cd501
[14/07/2025 13:56:14] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 4800a7d3-709b-4dc7-890f-e9733a6bfa63 -> "00:00-08:00"
[14/07/2025 13:56:14] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 4800a7d3-709b-4dc7-890f-e9733a6bfa63 -> "00:00-08:00"
[14/07/2025 13:56:14] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière f6cfa7cb-0f48-4753-82d7-7fc39b9cd501
[14/07/2025 13:56:14] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière f6cfa7cb-0f48-4753-82d7-7fc39b9cd501
[14/07/2025 13:56:14] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 4800a7d3-709b-4dc7-890f-e9733a6bfa63 -> "00:00-08:00"
[14/07/2025 13:56:14] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière f6cfa7cb-0f48-4753-82d7-7fc39b9cd501
[14/07/2025 13:56:14] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[14/07/2025 13:56:14] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[14/07/2025 13:56:14] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière f6cfa7cb-0f48-4753-82d7-7fc39b9cd501
[14/07/2025 13:56:14] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 3: {"employee":"Jean Dupont","date":"2025-07-14","assignmentId":"0e114dfe-053e-4c8d-ba1b-1edb6f814fcf","postId":"97a5a09a-60b7-495c-9796-bc44cfc56585","text":"08:00-16:00"}
[14/07/2025 13:56:14] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 4800a7d3-709b-4dc7-890f-e9733a6bfa63 -> "00:00-08:00"
[14/07/2025 13:56:14] [FRONTEND] [WARN] ⚠️ [Legacy] workingDays manquant pour Poste Nuit (4800a7d3-709b-4dc7-890f-e9733a6bfa63) - API n'a pas renvoyé les données
[14/07/2025 13:56:14] [FRONTEND] [WARN] ⚠️ [Legacy] workingDays manquant pour Poste Matin (97a5a09a-60b7-495c-9796-bc44cfc56585) - API n'a pas renvoyé les données
[14/07/2025 13:56:14] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[14/07/2025 13:56:14] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[14/07/2025 13:56:14] [FRONTEND] [WARN] ⚠️ [Legacy] workingDays manquant pour Poste WE2 (a57ffb59-baf2-454a-b825-4e74d4ae4787) - API n'a pas renvoyé les données
[14/07/2025 13:56:14] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[14/07/2025 13:56:14] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 88 zones trouvées
[14/07/2025 13:56:14] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[14/07/2025 13:56:14] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[14/07/2025 13:56:14] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[14/07/2025 13:56:14] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 97a5a09a-60b7-495c-9796-bc44cfc56585
[14/07/2025 13:56:14] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 97a5a09a-60b7-495c-9796-bc44cfc56585 configuré comme draggable
[14/07/2025 13:56:14] [FRONTEND] [WARN] ⚠️ [Legacy] workingDays manquant pour Poste Soir (ff229f24-11fe-40c9-b245-0427c3e68a7a) - API n'a pas renvoyé les données
[14/07/2025 13:56:14] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ff229f24-11fe-40c9-b245-0427c3e68a7a configuré comme draggable
[14/07/2025 13:56:14] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste c8d557c3-bb9d-45bf-a90b-a98192f08d76
[14/07/2025 13:56:14] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 4800a7d3-709b-4dc7-890f-e9733a6bfa63
[14/07/2025 13:56:14] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste c8d557c3-bb9d-45bf-a90b-a98192f08d76 configuré comme draggable
[14/07/2025 13:56:14] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a57ffb59-baf2-454a-b825-4e74d4ae4787
[14/07/2025 13:56:14] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[14/07/2025 13:56:14] [FRONTEND] [LOG] 🔄 [attachSettingsButtonIfMissing] Gestionnaire de modales initialisé
[14/07/2025 13:56:14] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 4800a7d3-709b-4dc7-890f-e9733a6bfa63 configuré comme draggable
[14/07/2025 13:56:14] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ff229f24-11fe-40c9-b245-0427c3e68a7a
[14/07/2025 13:56:14] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a57ffb59-baf2-454a-b825-4e74d4ae4787 configuré comme draggable
[14/07/2025 13:56:14] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 97a5a09a-60b7-495c-9796-bc44cfc56585
[14/07/2025 13:56:14] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ff229f24-11fe-40c9-b245-0427c3e68a7a
[14/07/2025 13:56:14] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 97a5a09a-60b7-495c-9796-bc44cfc56585 configuré comme draggable
[14/07/2025 13:56:14] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a57ffb59-baf2-454a-b825-4e74d4ae4787
[14/07/2025 13:56:14] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 4800a7d3-709b-4dc7-890f-e9733a6bfa63 configuré comme draggable
[14/07/2025 13:56:14] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ff229f24-11fe-40c9-b245-0427c3e68a7a configuré comme draggable
[14/07/2025 13:56:14] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste c8d557c3-bb9d-45bf-a90b-a98192f08d76 configuré comme draggable
[14/07/2025 13:56:14] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 4800a7d3-709b-4dc7-890f-e9733a6bfa63
[14/07/2025 13:56:14] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[14/07/2025 13:56:14] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a57ffb59-baf2-454a-b825-4e74d4ae4787 configuré comme draggable
[14/07/2025 13:56:14] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste c8d557c3-bb9d-45bf-a90b-a98192f08d76
[14/07/2025 13:56:14] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[14/07/2025 13:56:14] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[14/07/2025 13:56:14] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[14/07/2025 13:56:14] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[14/07/2025 13:56:14] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[14/07/2025 13:56:14] [FRONTEND] [LOG] 🎯 [diagnoseRegularAssignmentGrips] Grips trouvés dans le DOM: 14
[14/07/2025 13:56:14] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[14/07/2025 13:56:14] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (88 zones)
[14/07/2025 13:56:14] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 88 zones trouvées
[14/07/2025 13:56:14] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[14/07/2025 13:56:14] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[14/07/2025 13:56:15] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[14/07/2025 13:56:15] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 88 zones de drop disponibles
[14/07/2025 13:56:15] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 97a5a09a-60b7-495c-9796-bc44cfc56585
[14/07/2025 13:56:15] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 4800a7d3-709b-4dc7-890f-e9733a6bfa63 configuré comme draggable
[14/07/2025 13:56:15] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 4800a7d3-709b-4dc7-890f-e9733a6bfa63
[14/07/2025 13:56:15] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[14/07/2025 13:56:15] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste c8d557c3-bb9d-45bf-a90b-a98192f08d76 configuré comme draggable
[14/07/2025 13:56:15] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 97a5a09a-60b7-495c-9796-bc44cfc56585 configuré comme draggable
[14/07/2025 13:56:15] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a57ffb59-baf2-454a-b825-4e74d4ae4787 configuré comme draggable
[14/07/2025 13:56:15] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 97a5a09a-60b7-495c-9796-bc44cfc56585 configuré comme draggable
[14/07/2025 13:56:15] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ff229f24-11fe-40c9-b245-0427c3e68a7a
[14/07/2025 13:56:15] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a57ffb59-baf2-454a-b825-4e74d4ae4787
[14/07/2025 13:56:15] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste c8d557c3-bb9d-45bf-a90b-a98192f08d76
[14/07/2025 13:56:15] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 97a5a09a-60b7-495c-9796-bc44cfc56585
[14/07/2025 13:56:15] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ff229f24-11fe-40c9-b245-0427c3e68a7a configuré comme draggable
[14/07/2025 13:56:15] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ff229f24-11fe-40c9-b245-0427c3e68a7a
[14/07/2025 13:56:15] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 4800a7d3-709b-4dc7-890f-e9733a6bfa63 configuré comme draggable
[14/07/2025 13:56:15] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste c8d557c3-bb9d-45bf-a90b-a98192f08d76
[14/07/2025 13:56:15] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a57ffb59-baf2-454a-b825-4e74d4ae4787 configuré comme draggable
[14/07/2025 13:56:15] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[14/07/2025 13:56:15] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[14/07/2025 13:56:15] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[14/07/2025 13:56:15] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[14/07/2025 13:56:15] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 13: {"employee":"Sophie Leblanc","date":"2025-07-19","assignmentId":"e1d1ea5b-67e9-45a0-9498-ef20cefd302b","postId":"c8d557c3-bb9d-45bf-a90b-a98192f08d76","text":"00:00-12:00"}
[14/07/2025 13:56:15] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[14/07/2025 13:56:15] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[14/07/2025 13:56:15] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[14/07/2025 13:56:15] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[14/07/2025 13:56:15] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[14/07/2025 13:56:15] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[14/07/2025 13:56:15] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (88 zones)
[14/07/2025 13:56:15] [FRONTEND] [LOG] 🔍 [attachSettingsButtonIfMissing] Vérification des boutons paramètres
[14/07/2025 13:56:15] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a57ffb59-baf2-454a-b825-4e74d4ae4787
[14/07/2025 13:56:15] [FRONTEND] [LOG] 🔧 [attachSettingsButtonIfMissing] Attachement du bouton paramètres sidebar
[14/07/2025 13:56:15] [FRONTEND] [LOG] 🔧 [attachSettingsButtonIfMissing] Attachement du bouton paramètres header
[14/07/2025 13:56:15] [FRONTEND] [LOG] 🔄 [attachSettingsButtonIfMissing] Gestionnaire de modales initialisé
[14/07/2025 13:56:15] [FRONTEND] [LOG] 🔧 [ModalFunctionalitiesManager] Initialisation du gestionnaire de modales
[14/07/2025 13:56:15] [FRONTEND] [LOG] 🔍 [attachSettingsButtonIfMissing] Vérification des boutons paramètres
[14/07/2025 13:56:15] [FRONTEND] [LOG] 🔧 [attachSettingsButtonIfMissing] Attachement du bouton paramètres header
[14/07/2025 13:56:15] [FRONTEND] [LOG] 🔧 [attachSettingsButtonIfMissing] Attachement du bouton paramètres sidebar
[14/07/2025 13:56:15] [FRONTEND] [LOG] 🔧 [ModalFunctionalitiesManager] Initialisation du gestionnaire de modales
[14/07/2025 13:56:15] [FRONTEND] [LOG] 📊 [diagnoseRegularAssignmentGrips] Statistiques: {"totalShifts":14,"regularShifts":14,"regularAssignments":8}
[14/07/2025 13:56:15] [FRONTEND] [LOG] 🔍 [diagnoseRegularAssignmentGrips] Diagnostic des grips d'attributions régulières
[14/07/2025 13:56:15] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 1: {"employee":"Sophie Leblanc","date":"2025-07-13","assignmentId":"e1d1ea5b-67e9-45a0-9498-ef20cefd302b","postId":"c8d557c3-bb9d-45bf-a90b-a98192f08d76","text":"00:00-12:00"}
[14/07/2025 13:56:15] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 4: {"employee":"Lucas Bernard","date":"2025-07-14","assignmentId":"f6cfa7cb-0f48-4753-82d7-7fc39b9cd501","postId":"4800a7d3-709b-4dc7-890f-e9733a6bfa63","text":"00:00-08:00"}
[14/07/2025 13:56:15] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 2: {"employee":"Pierre Durand","date":"2025-07-13","assignmentId":"4d1e2a29-162d-4f25-8b37-f5fe83494a73","postId":"a57ffb59-baf2-454a-b825-4e74d4ae4787","text":"12:00-24:00"}
[14/07/2025 13:56:15] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 5: {"employee":"Jean Dupont","date":"2025-07-15","assignmentId":"cd38f349-5704-4012-a750-93be50f87b34","postId":"97a5a09a-60b7-495c-9796-bc44cfc56585","text":"08:00-16:00"}
[14/07/2025 13:56:15] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 8: {"employee":"Lucas Bernard","date":"2025-07-16","assignmentId":"f6cfa7cb-0f48-4753-82d7-7fc39b9cd501","postId":"4800a7d3-709b-4dc7-890f-e9733a6bfa63","text":"00:00-08:00"}
[14/07/2025 13:56:15] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 6: {"employee":"Lucas Bernard","date":"2025-07-15","assignmentId":"f6cfa7cb-0f48-4753-82d7-7fc39b9cd501","postId":"4800a7d3-709b-4dc7-890f-e9733a6bfa63","text":"00:00-08:00"}
[14/07/2025 13:56:15] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 9: {"employee":"Jean Dupont","date":"2025-07-17","assignmentId":"cd38f349-5704-4012-a750-93be50f87b34","postId":"97a5a09a-60b7-495c-9796-bc44cfc56585","text":"08:00-16:00"}
[14/07/2025 13:56:15] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 10: {"employee":"Lucas Bernard","date":"2025-07-17","assignmentId":"f6cfa7cb-0f48-4753-82d7-7fc39b9cd501","postId":"4800a7d3-709b-4dc7-890f-e9733a6bfa63","text":"00:00-08:00"}
[14/07/2025 13:56:15] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 11: {"employee":"Jean Dupont","date":"2025-07-18","assignmentId":"cd38f349-5704-4012-a750-93be50f87b34","postId":"97a5a09a-60b7-495c-9796-bc44cfc56585","text":"08:00-16:00"}
[14/07/2025 13:56:15] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 12: {"employee":"Lucas Bernard","date":"2025-07-18","assignmentId":"f6cfa7cb-0f48-4753-82d7-7fc39b9cd501","postId":"4800a7d3-709b-4dc7-890f-e9733a6bfa63","text":"00:00-08:00"}
[14/07/2025 13:56:15] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[14/07/2025 13:56:15] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[14/07/2025 13:56:15] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 14: {"employee":"Pierre Durand","date":"2025-07-19","assignmentId":"4d1e2a29-162d-4f25-8b37-f5fe83494a73","postId":"a57ffb59-baf2-454a-b825-4e74d4ae4787","text":"12:00-24:00"}
[14/07/2025 13:56:15] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] 0 grips ajoutés
[14/07/2025 13:56:15] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[14/07/2025 13:56:15] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[14/07/2025 14:07:44] [FRONTEND] [DEBUG] [vite] hot updated: /src/Agenda.tsx
[14/07/2025 14:07:44] [FRONTEND] [DEBUG] [vite] hot updated: /src/index.css
[14/07/2025 14:07:44] [FRONTEND] [DEBUG] [vite] hot updated: /src/Agenda.tsx
[14/07/2025 14:07:44] [FRONTEND] [DEBUG] [vite] hot updated: /src/index.css
[14/07/2025 14:07:44] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:07:45.118Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 14:07:44] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[14/07/2025 14:07:44] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:07:45.122Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 14:07:44] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:07:44.983Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 14:07:44] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:07:45.127Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 14:07:44] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:07:44.990Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 14:07:44] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:07:45.003Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 14:07:44] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:07:45.136Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 14:07:44] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:07:45.004Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 14:07:44] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:07:45.137Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 14:07:44] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:07:45.256Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 14:07:44] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[14/07/2025 14:08:32] [FRONTEND] [DEBUG] [vite] hot updated: /src/Agenda.tsx
[14/07/2025 14:08:32] [FRONTEND] [DEBUG] [vite] hot updated: /src/Agenda.tsx
[14/07/2025 14:08:32] [FRONTEND] [DEBUG] [vite] hot updated: /src/index.css
[14/07/2025 14:08:32] [FRONTEND] [DEBUG] [vite] hot updated: /src/index.css
[14/07/2025 14:08:32] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:08:33.586Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 14:08:32] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:08:33.473Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 14:08:32] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:08:33.458Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 14:08:32] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:08:33.592Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 14:08:32] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:08:33.601Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 14:08:32] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:08:33.459Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 14:08:32] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[14/07/2025 14:08:32] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:08:33.606Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 14:08:32] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:08:33.462Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 14:08:32] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:08:33.752Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 14:08:32] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[14/07/2025 14:08:32] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:08:33.609Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 14:29:18] [BACKEND] [INFO] [LOGS] Client déconnecté: 1752515766539
[14/07/2025 14:29:18] [FRONTEND] [ERROR] [LogsPage] Erreur SSE: {"isTrusted":true}
[14/07/2025 14:29:18] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:29:19.388Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 14:29:18] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:29:19.254Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 14:29:18] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[14/07/2025 14:29:18] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:29:19.317Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 14:29:18] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[14/07/2025 14:29:18] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:29:19.454Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 14:29:18] [FRONTEND] [DEBUG] [vite] connected.
[14/07/2025 14:29:19] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[14/07/2025 14:29:19] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[14/07/2025 14:29:19] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[14/07/2025 14:29:19] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[14/07/2025 14:29:19] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[14/07/2025 14:29:19] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[14/07/2025 14:29:19] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[14/07/2025 14:29:19] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[14/07/2025 14:29:19] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[14/07/2025 14:29:19] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[14/07/2025 14:29:19] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[14/07/2025 14:29:19] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[14/07/2025 14:29:19] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[14/07/2025 14:29:19] [FRONTEND] [LOG] [LogsPage] Fermeture de la connexion SSE.
[14/07/2025 14:29:19] [BACKEND] [INFO] [LOGS] Client connecté: 1752517760109
[14/07/2025 14:29:19] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:29:20.110Z",
  "originalArgs": [
    "[query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ]
}
[14/07/2025 14:29:19] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:29:20.120Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 14:29:19] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[14/07/2025 14:29:19] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:29:20.121Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 14:29:19] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[14/07/2025 14:29:19] [BACKEND] [INFO] ✅ Requête exécutée en 246ms: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 246ms: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 246ms:",
    "SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:29:20.356Z"
}
[14/07/2025 14:29:22] [FRONTEND] [DEBUG] [vite] connected.
[14/07/2025 14:29:22] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[14/07/2025 14:29:22] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[14/07/2025 14:29:22] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[14/07/2025 14:29:22] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[14/07/2025 14:29:22] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[14/07/2025 14:29:22] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[14/07/2025 14:29:22] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[14/07/2025 14:29:22] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[14/07/2025 14:29:22] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[14/07/2025 14:29:22] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[14/07/2025 14:29:22] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[14/07/2025 14:29:22] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[14/07/2025 14:29:22] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[14/07/2025 14:29:22] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[14/07/2025 14:29:22] [FRONTEND] [LOG] 🧹 [Agenda] Nettoyage composant Agenda
[14/07/2025 14:29:22] [FRONTEND] [LOG] 🔄 [Agenda] Réinitialisation nécessaire (employés non chargés)
[14/07/2025 14:29:22] [FRONTEND] [LOG] ✅ [clearRenderCache] Cache de rendu nettoyé
[14/07/2025 14:29:22] [FRONTEND] [LOG] 🧹 [clearRenderCache] Nettoyage du cache de rendu
[14/07/2025 14:29:22] [FRONTEND] [LOG] 🚀 [Agenda] Initialisation TeamCalendarApp...
[14/07/2025 14:29:22] [FRONTEND] [LOG] 🚀 [init] Initialisation de TeamCalendarApp...
[14/07/2025 14:29:23] [FRONTEND] [LOG] 🔌 [init] Indicateur de connexion initialisé
[14/07/2025 14:29:23] [FRONTEND] [LOG] 🔌 [ConnectionIndicator] Création de l'indicateur de connexion
[14/07/2025 14:29:23] [FRONTEND] [LOG] 🌐 [TeamCalendarApp] Chargement depuis l'API...
[14/07/2025 14:29:23] [FRONTEND] [LOG] 📂 [TeamCalendarApp] Chargement de l'état...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT e.*, et.name as template_name 
      FROM employees e 
      LEFT JOIN employee_templates et ON e.template_id = et.id 
      ORDER BY e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT e.*, et.name as template_name \n      FROM employees e \n      LEFT JOIN employee_templates et ON e.template_id = et.id \n      ORDER BY e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:29:23.788Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 45ms: 
      SELECT e.*, et.name as template_name 
      {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 45ms:",
    "\n      SELECT e.*, et.name as template_name \n     ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:29:23.832Z"
}
[14/07/2025 14:29:23] [BACKEND] [INFO] ✅ Requête exécutée en 45ms: 
      SELECT e.*, et.name as template_name 
      {}
[14/07/2025 14:29:23] [FRONTEND] [LOG] 👥 [TeamCalendarApp] 5 employés chargés
[14/07/2025 14:29:23] [FRONTEND] [LOG] 📦 [loadState] Chargement de tous les shifts depuis la base de données...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT s.*, e.name as employee_name, sp.label as post_label
      FROM shifts s
      LEFT JOIN employees e ON s.employee_id = e.id
      LEFT JOIN standard_posts sp ON s.post_id = sp.id
      ORDER BY s.date_key, e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT s.*, e.name as employee_name, sp.label as post_label\n      FROM shifts s\n      LEFT JOIN employees e ON s.employee_id = e.id\n      LEFT JOIN standard_posts sp ON s.post_id = sp.id\n      ORDER BY s.date_key, e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:29:23.861Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 36ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 36ms:",
    "\n      SELECT s.*, e.name as employee_name, sp.lab",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:29:23.898Z"
}
[14/07/2025 14:29:23] [BACKEND] [INFO] ✅ Requête exécutée en 36ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
[14/07/2025 14:29:23] [FRONTEND] [LOG] 📅 [loadState] 0 shifts chargés depuis la base de données
[14/07/2025 14:29:23] [FRONTEND] [LOG] ✅ [loadState] Shifts organisés dans this.data.schedule
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:29:23.926Z"
}
[14/07/2025 14:29:23] [BACKEND] [INFO] ✅ Requête exécutée en 50ms: SELECT * FROM standard_posts ORDER BY label {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 50ms: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 50ms:",
    "SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:29:23.975Z"
}
[14/07/2025 14:29:23] [FRONTEND] [LOG] 🏢 [TeamCalendarApp] 5 postes standards chargés
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:29:23.992Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 41ms: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 41ms:",
    "SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:29:24.033Z"
}
[14/07/2025 14:29:23] [FRONTEND] [LOG] ⚙️ [loadState] Paramètres chargés depuis API: {}
[14/07/2025 14:29:23] [BACKEND] [INFO] ✅ Requête exécutée en 41ms: SELECT * FROM app_settings ORDER BY setting_key {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT 
        ra.id,
        ra.employee_id,
        ra.post_id,
        ra.days_of_week,
        ra.start_date,
        ra.end_date,
        ra.is_active,
        ra.excluded_dates,
        e.name as employee_name,
        sp.label as post_label,
        sp.hours as post_hours,
        sp.type as post_type,
        array_length(ra.days_of_week, 1) as days_count
      FROM regular_assignments ra
      LEFT JOIN employees e ON ra.employee_id = e.id
      LEFT JOIN standard_posts sp ON ra.post_id = sp.id
      WHERE ra.is_active = true
      ORDER BY e.name, sp.label, ra.start_date
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT \n        ra.id,\n        ra.employee_id,\n        ra.post_id,\n        ra.days_of_week,\n        ra.start_date,\n        ra.end_date,\n        ra.is_active,\n        ra.excluded_dates,\n        e.name as employee_name,\n        sp.label as post_label,\n        sp.hours as post_hours,\n        sp.type as post_type,\n        array_length(ra.days_of_week, 1) as days_count\n      FROM regular_assignments ra\n      LEFT JOIN employees e ON ra.employee_id = e.id\n      LEFT JOIN standard_posts sp ON ra.post_id = sp.id\n      WHERE ra.is_active = true\n      ORDER BY e.name, sp.label, ra.start_date\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:29:24.045Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 53ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 53ms:",
    "\n      SELECT \n        ra.id,\n        ra.employee_",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:29:24.099Z"
}
[14/07/2025 14:29:23] [BACKEND] [INFO] ✅ Requête exécutée en 53ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
[14/07/2025 14:29:23] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"d6e5f672-6e83-4ee3-8327-00b3fda88c88","employee_id":"cf78e945-72c7-48f3-a72d-bd0e245a284d","post_id":"97a5a09a-60b7-495c-9796-bc44cfc56585","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-08T04:00:00.000Z","end_date":"2025-07-08T04:00:00.000Z","excluded_dates":[]}
[14/07/2025 14:29:23] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"cd38f349-5704-4012-a750-93be50f87b34","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","postId":"97a5a09a-60b7-495c-9796-bc44cfc56585","selectedDays":[1,2,3,4,5],"isLimited":false,"startDate":"2025-07-14","endDate":null,"excludedDates":[]}
[14/07/2025 14:29:23] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"cd38f349-5704-4012-a750-93be50f87b34","employee_id":"42b53805-18ba-425e-bb00-52bb8d6ce76b","post_id":"97a5a09a-60b7-495c-9796-bc44cfc56585","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-14T04:00:00.000Z","end_date":null,"excluded_dates":[]}
[14/07/2025 14:29:23] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"0e114dfe-053e-4c8d-ba1b-1edb6f814fcf","employee_id":"42b53805-18ba-425e-bb00-52bb8d6ce76b","post_id":"97a5a09a-60b7-495c-9796-bc44cfc56585","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-09T04:00:00.000Z","end_date":"2025-07-14T04:00:00.000Z","excluded_dates":[]}
[14/07/2025 14:29:23] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"0e114dfe-053e-4c8d-ba1b-1edb6f814fcf","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","postId":"97a5a09a-60b7-495c-9796-bc44cfc56585","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-09","endDate":"2025-07-14","excludedDates":[]}
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'
         {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'\n        ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:29:24.197Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📋 [GET /api/employee-order] Récupération ordre des employés
  Data: {
  "originalArgs": [
    "📋 [GET /api/employee-order] Récupération ordre des employés"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:29:24.196Z"
}
[14/07/2025 14:29:23] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"d6e5f672-6e83-4ee3-8327-00b3fda88c88","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","postId":"97a5a09a-60b7-495c-9796-bc44cfc56585","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-08","endDate":"2025-07-08","excludedDates":[]}
[14/07/2025 14:29:23] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"f6cfa7cb-0f48-4753-82d7-7fc39b9cd501","employee_id":"cf78e945-72c7-48f3-a72d-bd0e245a284d","post_id":"4800a7d3-709b-4dc7-890f-e9733a6bfa63","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-10T04:00:00.000Z","end_date":null,"excluded_dates":[]}
[14/07/2025 14:29:23] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"18cd6e1a-2b63-403e-9299-9118701441d8","employee_id":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","post_id":"97a5a09a-60b7-495c-9796-bc44cfc56585","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-09T04:00:00.000Z","end_date":"2025-07-07T04:00:00.000Z","excluded_dates":[]}
[14/07/2025 14:29:23] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"18cd6e1a-2b63-403e-9299-9118701441d8","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","postId":"97a5a09a-60b7-495c-9796-bc44cfc56585","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-09","endDate":"2025-07-07","excludedDates":[]}
[14/07/2025 14:29:23] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"f6cfa7cb-0f48-4753-82d7-7fc39b9cd501","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","postId":"4800a7d3-709b-4dc7-890f-e9733a6bfa63","selectedDays":[1,2,3,4,5],"isLimited":false,"startDate":"2025-07-10","endDate":null,"excludedDates":[]}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 50ms: 
            SELECT setting_value FROM app_setting {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 50ms:",
    "\n            SELECT setting_value FROM app_setting",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:29:24.248Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ [GET /api/employee-order] Ordre récupéré (array): [{"id":"59f5df3a-33ef-425c-bfa2-adca818cf94f","name":"Sophie Leblanc","order":0},{"id":"42b53805-18ba-425e-bb00-52bb8d6ce76b","name":"Jean Dupont","order":1},{"id":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","name":"Pierre Durand","order":2},{"id":"cf78e945-72c7-48f3-a72d-bd0e245a284d","name":"Lucas Bernard","order":3},{"id":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","name":"Marie Martin","order":4}]
  Data: {
  "originalArgs": [
    "✅ [GET /api/employee-order] Ordre récupéré (array):",
    [
      {
        "id": "59f5df3a-33ef-425c-bfa2-adca818cf94f",
        "name": "Sophie Leblanc",
        "order": 0
      },
      {
        "id": "42b53805-18ba-425e-bb00-52bb8d6ce76b",
        "name": "Jean Dupont",
        "order": 1
      },
      {
        "id": "18fe9cd1-e8a5-44e3-90b3-a856086ce27e",
        "name": "Pierre Durand",
        "order": 2
      },
      {
        "id": "cf78e945-72c7-48f3-a72d-bd0e245a284d",
        "name": "Lucas Bernard",
        "order": 3
      },
      {
        "id": "604a1d0c-2141-4543-b7f9-7fc9adfa7b9a",
        "name": "Marie Martin",
        "order": 4
      }
    ]
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:29:24.250Z"
}
[14/07/2025 14:29:23] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"bca6e806-1aec-4ec4-86f4-ea29b522fc36","employee_id":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","post_id":"97a5a09a-60b7-495c-9796-bc44cfc56585","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-15T04:00:00.000Z","end_date":"2025-07-13T04:00:00.000Z","excluded_dates":[]}
[14/07/2025 14:29:23] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"e1d1ea5b-67e9-45a0-9498-ef20cefd302b","employee_id":"59f5df3a-33ef-425c-bfa2-adca818cf94f","post_id":"c8d557c3-bb9d-45bf-a90b-a98192f08d76","days_of_week":[0,6],"selectedDays":[0,6],"start_date":"2025-07-09T04:00:00.000Z","end_date":null,"excluded_dates":[]}
[14/07/2025 14:29:23] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"4d1e2a29-162d-4f25-8b37-f5fe83494a73","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"a57ffb59-baf2-454a-b825-4e74d4ae4787","selectedDays":[0,6],"isLimited":false,"startDate":"2025-07-06","endDate":null,"excludedDates":[]}
[14/07/2025 14:29:23] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"4d1e2a29-162d-4f25-8b37-f5fe83494a73","employee_id":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","post_id":"a57ffb59-baf2-454a-b825-4e74d4ae4787","days_of_week":[0,6],"selectedDays":[0,6],"start_date":"2025-07-06T04:00:00.000Z","end_date":null,"excluded_dates":[]}
[14/07/2025 14:29:23] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"bca6e806-1aec-4ec4-86f4-ea29b522fc36","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","postId":"97a5a09a-60b7-495c-9796-bc44cfc56585","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-15","endDate":"2025-07-13","excludedDates":[]}
[14/07/2025 14:29:23] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"e1d1ea5b-67e9-45a0-9498-ef20cefd302b","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"c8d557c3-bb9d-45bf-a90b-a98192f08d76","selectedDays":[0,6],"isLimited":false,"startDate":"2025-07-09","endDate":null,"excludedDates":[]}
[14/07/2025 14:29:23] [FRONTEND] [LOG] 📋 [loadState] 8/8 assignations régulières valides chargées et normalisées
[14/07/2025 14:29:23] [FRONTEND] [LOG] 📋 [loadState] Assignations régulières chargées (application différée)
[14/07/2025 14:29:23] [BACKEND] [INFO] 📋 [GET /api/employee-order] Récupération ordre des employés
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T18:29:24.362Z"
}
[14/07/2025 14:29:23] [FRONTEND] [LOG] 🧹 [cleanupDuplicateRegularShifts] Nettoyage des doublons de shifts réguliers...
[14/07/2025 14:29:23] [FRONTEND] [LOG] ✅ [cleanupDuplicateRegularShifts] Aucun doublon détecté
[14/07/2025 14:29:23] [FRONTEND] [LOG] 🧹 [cleanupInvalidAssignmentIds] Nettoyage des assignment_id invalides...
[14/07/2025 14:29:23] [FRONTEND] [LOG] ✅ [cleanupInvalidAssignmentIds] Aucun assignment_id invalide détecté
[14/07/2025 14:29:23] [FRONTEND] [LOG] ✅ [loadState] État chargé depuis l'API avec succès
[14/07/2025 14:29:23] [FRONTEND] [LOG] ✅ [init] Icône paramètres trouvée: [Objet non sérialisable]
[14/07/2025 14:29:23] [FRONTEND] [LOG] 🔍 [init] Recherche du bouton des paramètres dans le DOM
[14/07/2025 14:29:23] [FRONTEND] [LOG] 🔍 [init] Bouton des paramètres trouvé ? true
[14/07/2025 14:29:23] [FRONTEND] [LOG] 🔍 [init] Attachement du listener click sur le bouton des paramètres
[14/07/2025 14:29:23] [FRONTEND] [LOG] 🔍 [init] Recherche du bouton des paramètres header dans le DOM
[14/07/2025 14:29:23] [FRONTEND] [LOG] 🔍 [init] Bouton des paramètres header trouvé ? true
[14/07/2025 14:29:23] [FRONTEND] [LOG] 🔍 [init] Attachement du listener click sur le bouton des paramètres header
[14/07/2025 14:29:23] [FRONTEND] [LOG] ✅ [setupNavigationListeners] Écouteurs de navigation attachés
[14/07/2025 14:29:23] [FRONTEND] [LOG] ✅ Boutons de navigation configurés
[14/07/2025 14:29:24] [FRONTEND] [LOG] 🎯 [setupViewNavigationListeners] Configuration des boutons de navigation...
[14/07/2025 14:29:24] [FRONTEND] [LOG] 🔧 [ModalManager] Configuration des gestionnaires de modal
[14/07/2025 14:29:24] [FRONTEND] [LOG] ✅ [ModalManager] Gestionnaires de modal configurés
[14/07/2025 14:29:24] [FRONTEND] [LOG] 🔧 [setupGeneralSettings] Configuration des paramètres généraux
[14/07/2025 14:29:24] [FRONTEND] [LOG] 🔧 [setupGeneralSettings] Création du conteneur de fuseau horaire
[14/07/2025 14:29:24] [FRONTEND] [LOG] ✅ [setupGlobalEscapeHandler] Gestionnaire ESC global configuré
[14/07/2025 14:29:24] [FRONTEND] [LOG] 📋 [loadEmployeeOrder] Chargement ordre des employés
[14/07/2025 14:29:24] [FRONTEND] [LOG] ✅ [loadEmployeeOrder] Ordre API récupéré: Sophie Leblanc (0), Jean Dupont (1), Pierre Durand (2), Lucas Bernard (3), Marie Martin (4)
[14/07/2025 14:29:24] [FRONTEND] [LOG] ✅ [loadEmployeeOrder] Employés réorganisés selon l'ordre api
[14/07/2025 14:29:24] [FRONTEND] [LOG] ℹ️ [setupEmployeeDragDrop] Fonction désactivée - Utiliser setupDragAndDrop
[14/07/2025 14:29:24] [FRONTEND] [LOG] 🔧 [addEmergencyFixButton] Ajout du bouton de correction...
[14/07/2025 14:29:24] [FRONTEND] [LOG] ✅ [addEmergencyFixButton] Bouton de correction ajouté
[14/07/2025 14:29:24] [FRONTEND] [LOG] 📋 [init] Application des attributions régulières après initialisation
[14/07/2025 14:29:24] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] Nettoyage des shifts réguliers existants...
[14/07/2025 14:29:24] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] 0 shifts réguliers supprimés
[14/07/2025 14:29:24] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Application pour semaine 2025-W28
[14/07/2025 14:29:24] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution 0e114dfe-053e-4c8d-ba1b-1edb6f814fcf a une intersection avec cette semaine (2025-07-09 → 2025-07-14) vs (2025-07-13 → 2025-07-19)
[14/07/2025 14:29:24] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution f6cfa7cb-0f48-4753-82d7-7fc39b9cd501 a une intersection avec cette semaine (2025-07-10 → ∞) vs (2025-07-13 → 2025-07-19)
[14/07/2025 14:29:24] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution d6e5f672-6e83-4ee3-8327-00b3fda88c88 terminée avant cette semaine (endDate: 2025-07-08, semaine commence: 2025-07-13)
[14/07/2025 14:29:24] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution cd38f349-5704-4012-a750-93be50f87b34 a une intersection avec cette semaine (2025-07-14 → ∞) vs (2025-07-13 → 2025-07-19)
[14/07/2025 14:29:24] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 18cd6e1a-2b63-403e-9299-9118701441d8 terminée avant cette semaine (endDate: 2025-07-07, semaine commence: 2025-07-13)
[14/07/2025 14:29:24] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution bca6e806-1aec-4ec4-86f4-ea29b522fc36 a une intersection avec cette semaine (2025-07-15 → 2025-07-13) vs (2025-07-13 → 2025-07-19)
[14/07/2025 14:29:24] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 6 attributions actives sur 8 total
[14/07/2025 14:29:24] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution 4d1e2a29-162d-4f25-8b37-f5fe83494a73 a une intersection avec cette semaine (2025-07-06 → ∞) vs (2025-07-13 → 2025-07-19)
[14/07/2025 14:29:24] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution e1d1ea5b-67e9-45a0-9498-ef20cefd302b a une intersection avec cette semaine (2025-07-09 → ∞) vs (2025-07-13 → 2025-07-19)
[14/07/2025 14:29:24] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"0e114dfe-053e-4c8d-ba1b-1edb6f814fcf","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","postId":"97a5a09a-60b7-495c-9796-bc44cfc56585","selectedDays":[1,2,3,4,5],"startDate":"2025-07-09","endDate":"2025-07-14","excludedDates":[]}
[14/07/2025 14:29:24] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution 0e114dfe-053e-4c8d-ba1b-1edb6f814fcf: [{"dateKey":"2025-07-13","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-14","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-15","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-16","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-17","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-07-18","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-07-19","isWeekend":true,"dayOfWeek":6}]
[14/07/2025 14:29:24] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-13 (0)
[14/07/2025 14:29:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"0e114dfe-053e-4c8d-ba1b-1edb6f814fcf","postId":"97a5a09a-60b7-495c-9796-bc44cfc56585","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-13T04:00:00.000Z","dayDateKey":"2025-07-13","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5]}
[14/07/2025 14:29:24] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[14/07/2025 14:29:24] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-13 ignoré pour attribution 0e114dfe-053e-4c8d-ba1b-1edb6f814fcf
[14/07/2025 14:29:24] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-14 (1)
[14/07/2025 14:29:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"0e114dfe-053e-4c8d-ba1b-1edb6f814fcf","postId":"97a5a09a-60b7-495c-9796-bc44cfc56585","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-14T04:00:00.000Z","dayDateKey":"2025-07-14","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5]}
[14/07/2025 14:29:24] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[14/07/2025 14:29:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-14: {"assignmentId":"0e114dfe-053e-4c8d-ba1b-1edb6f814fcf","dayDateStr":"2025-07-14","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[14/07/2025 14:29:24] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-14
[14/07/2025 14:29:24] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-14 validé, shifts existants: 0
[14/07/2025 14:29:24] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-14 pour Jean Dupont
[14/07/2025 14:29:24] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-15 (2)
[14/07/2025 14:29:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"0e114dfe-053e-4c8d-ba1b-1edb6f814fcf","postId":"97a5a09a-60b7-495c-9796-bc44cfc56585","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-15T04:00:00.000Z","dayDateKey":"2025-07-15","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5]}
[14/07/2025 14:29:24] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[14/07/2025 14:29:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-15: {"assignmentId":"0e114dfe-053e-4c8d-ba1b-1edb6f814fcf","dayDateStr":"2025-07-15","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[14/07/2025 14:29:24] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-15 après endDate 2025-07-14 (original: 2025-07-14) (attribution 0e114dfe-053e-4c8d-ba1b-1edb6f814fcf)
[14/07/2025 14:29:24] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-15 ignoré pour attribution 0e114dfe-053e-4c8d-ba1b-1edb6f814fcf
[14/07/2025 14:29:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"0e114dfe-053e-4c8d-ba1b-1edb6f814fcf","postId":"97a5a09a-60b7-495c-9796-bc44cfc56585","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-16T04:00:00.000Z","dayDateKey":"2025-07-16","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5]}
[14/07/2025 14:29:24] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-16 (3)
[14/07/2025 14:29:24] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[14/07/2025 14:29:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-16: {"assignmentId":"0e114dfe-053e-4c8d-ba1b-1edb6f814fcf","dayDateStr":"2025-07-16","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[14/07/2025 14:29:24] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-16 après endDate 2025-07-14 (original: 2025-07-14) (attribution 0e114dfe-053e-4c8d-ba1b-1edb6f814fcf)
[14/07/2025 14:29:24] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-17 (4)
[14/07/2025 14:29:24] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-16 ignoré pour attribution 0e114dfe-053e-4c8d-ba1b-1edb6f814fcf
[14/07/2025 14:29:24] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[14/07/2025 14:29:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"0e114dfe-053e-4c8d-ba1b-1edb6f814fcf","postId":"97a5a09a-60b7-495c-9796-bc44cfc56585","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-17T04:00:00.000Z","dayDateKey":"2025-07-17","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5]}
[14/07/2025 14:29:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-17: {"assignmentId":"0e114dfe-053e-4c8d-ba1b-1edb6f814fcf","dayDateStr":"2025-07-17","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[14/07/2025 14:29:24] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-17 après endDate 2025-07-14 (original: 2025-07-14) (attribution 0e114dfe-053e-4c8d-ba1b-1edb6f814fcf)
[14/07/2025 14:29:24] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-17 ignoré pour attribution 0e114dfe-053e-4c8d-ba1b-1edb6f814fcf
[14/07/2025 14:29:24] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-07-18 (5)
[14/07/2025 14:29:24] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[14/07/2025 14:29:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"0e114dfe-053e-4c8d-ba1b-1edb6f814fcf","postId":"97a5a09a-60b7-495c-9796-bc44cfc56585","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-18T04:00:00.000Z","dayDateKey":"2025-07-18","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5]}
[14/07/2025 14:29:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-18: {"assignmentId":"0e114dfe-053e-4c8d-ba1b-1edb6f814fcf","dayDateStr":"2025-07-18","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[14/07/2025 14:29:24] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-18 après endDate 2025-07-14 (original: 2025-07-14) (attribution 0e114dfe-053e-4c8d-ba1b-1edb6f814fcf)
[14/07/2025 14:29:24] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-18 ignoré pour attribution 0e114dfe-053e-4c8d-ba1b-1edb6f814fcf
[14/07/2025 14:29:24] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-07-19 (6)
[14/07/2025 14:29:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"0e114dfe-053e-4c8d-ba1b-1edb6f814fcf","postId":"97a5a09a-60b7-495c-9796-bc44cfc56585","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-19T04:00:00.000Z","dayDateKey":"2025-07-19","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5]}
[14/07/2025 14:29:24] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[14/07/2025 14:29:24] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-19 ignoré pour attribution 0e114dfe-053e-4c8d-ba1b-1edb6f814fcf
[14/07/2025 14:29:24] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"cd38f349-5704-4012-a750-93be50f87b34","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","postId":"97a5a09a-60b7-495c-9796-bc44cfc56585","selectedDays":[1,2,3,4,5],"startDate":"2025-07-14","endDate":null,"excludedDates":[]}
[14/07/2025 14:29:24] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-13 (0)
[14/07/2025 14:29:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"cd38f349-5704-4012-a750-93be50f87b34","postId":"97a5a09a-60b7-495c-9796-bc44cfc56585","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-13T04:00:00.000Z","dayDateKey":"2025-07-13","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5]}
[14/07/2025 14:29:24] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution cd38f349-5704-4012-a750-93be50f87b34: [{"dateKey":"2025-07-13","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-14","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-15","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-16","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-17","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-07-18","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-07-19","isWeekend":true,"dayOfWeek":6}]
[14/07/2025 14:29:24] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[14/07/2025 14:29:24] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-13 ignoré pour attribution cd38f349-5704-4012-a750-93be50f87b34
[14/07/2025 14:29:24] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-14 (1)
[14/07/2025 14:29:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"cd38f349-5704-4012-a750-93be50f87b34","postId":"97a5a09a-60b7-495c-9796-bc44cfc56585","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-14T04:00:00.000Z","dayDateKey":"2025-07-14","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5]}
[14/07/2025 14:29:24] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[14/07/2025 14:29:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-14: {"assignmentId":"cd38f349-5704-4012-a750-93be50f87b34","dayDateStr":"2025-07-14","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[14/07/2025 14:29:24] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-14
[14/07/2025 14:29:24] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-14 validé, shifts existants: 1
[14/07/2025 14:29:24] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Même poste déjà assigné: Poste Matin → 2025-07-14
[14/07/2025 14:29:24] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-15 (2)
[14/07/2025 14:29:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"cd38f349-5704-4012-a750-93be50f87b34","postId":"97a5a09a-60b7-495c-9796-bc44cfc56585","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-15T04:00:00.000Z","dayDateKey":"2025-07-15","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5]}
[14/07/2025 14:29:24] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[14/07/2025 14:29:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-15: {"assignmentId":"cd38f349-5704-4012-a750-93be50f87b34","dayDateStr":"2025-07-15","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[14/07/2025 14:29:24] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-15
[14/07/2025 14:29:24] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-15 validé, shifts existants: 0
[14/07/2025 14:29:24] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-15 pour Jean Dupont
[14/07/2025 14:29:24] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-16 (3)
[14/07/2025 14:29:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"cd38f349-5704-4012-a750-93be50f87b34","postId":"97a5a09a-60b7-495c-9796-bc44cfc56585","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-16T04:00:00.000Z","dayDateKey":"2025-07-16","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5]}
[14/07/2025 14:29:24] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[14/07/2025 14:29:24] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-16 validé, shifts existants: 0
[14/07/2025 14:29:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-16: {"assignmentId":"cd38f349-5704-4012-a750-93be50f87b34","dayDateStr":"2025-07-16","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[14/07/2025 14:29:24] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-16
[14/07/2025 14:29:24] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-16 pour Jean Dupont
[14/07/2025 14:29:24] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-17 (4)
[14/07/2025 14:29:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-17: {"assignmentId":"cd38f349-5704-4012-a750-93be50f87b34","dayDateStr":"2025-07-17","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[14/07/2025 14:29:24] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[14/07/2025 14:29:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"cd38f349-5704-4012-a750-93be50f87b34","postId":"97a5a09a-60b7-495c-9796-bc44cfc56585","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-17T04:00:00.000Z","dayDateKey":"2025-07-17","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5]}
[14/07/2025 14:29:24] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-17
[14/07/2025 14:29:24] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-17 validé, shifts existants: 0
[14/07/2025 14:29:24] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-17 pour Jean Dupont
[14/07/2025 14:29:24] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[14/07/2025 14:29:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"cd38f349-5704-4012-a750-93be50f87b34","postId":"97a5a09a-60b7-495c-9796-bc44cfc56585","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-18T04:00:00.000Z","dayDateKey":"2025-07-18","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5]}
[14/07/2025 14:29:24] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-07-18 (5)
[14/07/2025 14:29:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-18: {"assignmentId":"cd38f349-5704-4012-a750-93be50f87b34","dayDateStr":"2025-07-18","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[14/07/2025 14:29:24] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-18
[14/07/2025 14:29:24] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-18 validé, shifts existants: 0
[14/07/2025 14:29:24] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-07-19 (6)
[14/07/2025 14:29:24] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-18 pour Jean Dupont
[14/07/2025 14:29:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"cd38f349-5704-4012-a750-93be50f87b34","postId":"97a5a09a-60b7-495c-9796-bc44cfc56585","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-19T04:00:00.000Z","dayDateKey":"2025-07-19","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5]}
[14/07/2025 14:29:24] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[14/07/2025 14:29:24] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-19 ignoré pour attribution cd38f349-5704-4012-a750-93be50f87b34
[14/07/2025 14:29:24] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-13 (0)
[14/07/2025 14:29:24] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution f6cfa7cb-0f48-4753-82d7-7fc39b9cd501: [{"dateKey":"2025-07-13","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-14","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-15","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-16","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-17","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-07-18","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-07-19","isWeekend":true,"dayOfWeek":6}]
[14/07/2025 14:29:24] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"f6cfa7cb-0f48-4753-82d7-7fc39b9cd501","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","postId":"4800a7d3-709b-4dc7-890f-e9733a6bfa63","selectedDays":[1,2,3,4,5],"startDate":"2025-07-10","endDate":null,"excludedDates":[]}
[14/07/2025 14:29:24] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"f6cfa7cb-0f48-4753-82d7-7fc39b9cd501","postId":"4800a7d3-709b-4dc7-890f-e9733a6bfa63","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-13T04:00:00.000Z","dayDateKey":"2025-07-13","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5]}
[14/07/2025 14:29:25] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[14/07/2025 14:29:25] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[14/07/2025 14:29:25] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"f6cfa7cb-0f48-4753-82d7-7fc39b9cd501","postId":"4800a7d3-709b-4dc7-890f-e9733a6bfa63","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-14T04:00:00.000Z","dayDateKey":"2025-07-14","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5]}
[14/07/2025 14:29:25] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-14 (1)
[14/07/2025 14:29:25] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-13 ignoré pour attribution f6cfa7cb-0f48-4753-82d7-7fc39b9cd501
[14/07/2025 14:29:25] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-14: {"assignmentId":"f6cfa7cb-0f48-4753-82d7-7fc39b9cd501","dayDateStr":"2025-07-14","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[14/07/2025 14:29:25] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-14
[14/07/2025 14:29:25] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-14 validé, shifts existants: 0
[14/07/2025 14:29:25] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Nuit → 2025-07-14 pour Lucas Bernard
[14/07/2025 14:29:25] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"f6cfa7cb-0f48-4753-82d7-7fc39b9cd501","postId":"4800a7d3-709b-4dc7-890f-e9733a6bfa63","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-15T04:00:00.000Z","dayDateKey":"2025-07-15","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5]}
[14/07/2025 14:29:25] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-15 (2)
[14/07/2025 14:29:25] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[14/07/2025 14:29:25] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-15: {"assignmentId":"f6cfa7cb-0f48-4753-82d7-7fc39b9cd501","dayDateStr":"2025-07-15","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[14/07/2025 14:29:25] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-15
[14/07/2025 14:29:25] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-15 validé, shifts existants: 0
[14/07/2025 14:29:25] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Nuit → 2025-07-15 pour Lucas Bernard
[14/07/2025 14:29:25] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-16 (3)
[14/07/2025 14:29:25] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"f6cfa7cb-0f48-4753-82d7-7fc39b9cd501","postId":"4800a7d3-709b-4dc7-890f-e9733a6bfa63","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-16T04:00:00.000Z","dayDateKey":"2025-07-16","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5]}
[14/07/2025 14:29:25] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[14/07/2025 14:29:25] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-16
[14/07/2025 14:29:25] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-16: {"assignmentId":"f6cfa7cb-0f48-4753-82d7-7fc39b9cd501","dayDateStr":"2025-07-16","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[14/07/2025 14:29:25] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-16 validé, shifts existants: 0
[14/07/2025 14:29:25] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Nuit → 2025-07-16 pour Lucas Bernard
[14/07/2025 14:29:25] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-17 (4)
[14/07/2025 14:29:25] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[14/07/2025 14:29:25] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-17: {"assignmentId":"f6cfa7cb-0f48-4753-82d7-7fc39b9cd501","dayDateStr":"2025-07-17","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[14/07/2025 14:29:25] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"f6cfa7cb-0f48-4753-82d7-7fc39b9cd501","postId":"4800a7d3-709b-4dc7-890f-e9733a6bfa63","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-17T04:00:00.000Z","dayDateKey":"2025-07-17","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5]}
[14/07/2025 14:29:25] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-17
[14/07/2025 14:29:25] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-17 validé, shifts existants: 0
[14/07/2025 14:29:25] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Nuit → 2025-07-17 pour Lucas Bernard
[14/07/2025 14:29:25] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-07-18 (5)
[14/07/2025 14:29:25] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"f6cfa7cb-0f48-4753-82d7-7fc39b9cd501","postId":"4800a7d3-709b-4dc7-890f-e9733a6bfa63","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-18T04:00:00.000Z","dayDateKey":"2025-07-18","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5]}
[14/07/2025 14:29:25] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[14/07/2025 14:29:25] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-18: {"assignmentId":"f6cfa7cb-0f48-4753-82d7-7fc39b9cd501","dayDateStr":"2025-07-18","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[14/07/2025 14:29:25] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-18
[14/07/2025 14:29:25] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-18 validé, shifts existants: 0
[14/07/2025 14:29:25] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Nuit → 2025-07-18 pour Lucas Bernard
[14/07/2025 14:29:25] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-07-19 (6)
[14/07/2025 14:29:25] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"f6cfa7cb-0f48-4753-82d7-7fc39b9cd501","postId":"4800a7d3-709b-4dc7-890f-e9733a6bfa63","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-19T04:00:00.000Z","dayDateKey":"2025-07-19","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5]}
[14/07/2025 14:29:25] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[14/07/2025 14:29:25] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-19 ignoré pour attribution f6cfa7cb-0f48-4753-82d7-7fc39b9cd501
[14/07/2025 14:29:25] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"bca6e806-1aec-4ec4-86f4-ea29b522fc36","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","postId":"97a5a09a-60b7-495c-9796-bc44cfc56585","selectedDays":[1,2,3,4,5],"startDate":"2025-07-15","endDate":"2025-07-13","excludedDates":[]}
[14/07/2025 14:29:25] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution bca6e806-1aec-4ec4-86f4-ea29b522fc36: [{"dateKey":"2025-07-13","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-14","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-15","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-16","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-17","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-07-18","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-07-19","isWeekend":true,"dayOfWeek":6}]
[14/07/2025 14:29:25] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-13 (0)
[14/07/2025 14:29:25] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"bca6e806-1aec-4ec4-86f4-ea29b522fc36","postId":"97a5a09a-60b7-495c-9796-bc44cfc56585","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-13T04:00:00.000Z","dayDateKey":"2025-07-13","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5]}
[14/07/2025 14:29:25] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[14/07/2025 14:29:25] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-13 ignoré pour attribution bca6e806-1aec-4ec4-86f4-ea29b522fc36
[14/07/2025 14:29:25] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-14 (1)
[14/07/2025 14:29:25] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"bca6e806-1aec-4ec4-86f4-ea29b522fc36","postId":"97a5a09a-60b7-495c-9796-bc44cfc56585","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-14T04:00:00.000Z","dayDateKey":"2025-07-14","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5]}
[14/07/2025 14:29:25] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-14 avant startDate 2025-07-15 (original: 2025-07-15)
[14/07/2025 14:29:25] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-14: {"assignmentId":"bca6e806-1aec-4ec4-86f4-ea29b522fc36","dayDateStr":"2025-07-14","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[14/07/2025 14:29:25] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[14/07/2025 14:29:25] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-14 ignoré pour attribution bca6e806-1aec-4ec4-86f4-ea29b522fc36
[14/07/2025 14:29:25] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-15 (2)
[14/07/2025 14:29:25] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[14/07/2025 14:29:25] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"bca6e806-1aec-4ec4-86f4-ea29b522fc36","postId":"97a5a09a-60b7-495c-9796-bc44cfc56585","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-15T04:00:00.000Z","dayDateKey":"2025-07-15","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5]}
[14/07/2025 14:29:25] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-15 après endDate 2025-07-13 (original: 2025-07-13) (attribution bca6e806-1aec-4ec4-86f4-ea29b522fc36)
[14/07/2025 14:29:25] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-15: {"assignmentId":"bca6e806-1aec-4ec4-86f4-ea29b522fc36","dayDateStr":"2025-07-15","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[14/07/2025 14:29:25] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-15 ignoré pour attribution bca6e806-1aec-4ec4-86f4-ea29b522fc36
[14/07/2025 14:29:25] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-16 (3)
[14/07/2025 14:29:25] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"bca6e806-1aec-4ec4-86f4-ea29b522fc36","postId":"97a5a09a-60b7-495c-9796-bc44cfc56585","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-16T04:00:00.000Z","dayDateKey":"2025-07-16","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5]}
[14/07/2025 14:29:25] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[14/07/2025 14:29:25] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"bca6e806-1aec-4ec4-86f4-ea29b522fc36","postId":"97a5a09a-60b7-495c-9796-bc44cfc56585","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-19T04:00:00.000Z","dayDateKey":"2025-07-19","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5]}
[14/07/2025 14:29:25] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"e1d1ea5b-67e9-45a0-9498-ef20cefd302b","postId":"c8d557c3-bb9d-45bf-a90b-a98192f08d76","postLabel":"Poste WE1","postCategory":"weekend","dayDate":"2025-07-13T04:00:00.000Z","dayDateKey":"2025-07-13","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[0,6]}
[14/07/2025 14:29:25] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[14/07/2025 14:29:25] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière f6cfa7cb-0f48-4753-82d7-7fc39b9cd501
[14/07/2025 14:29:25] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste c8d557c3-bb9d-45bf-a90b-a98192f08d76
[14/07/2025 14:29:25] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste c8d557c3-bb9d-45bf-a90b-a98192f08d76 configuré comme draggable
[14/07/2025 14:29:25] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 88 zones de drop disponibles
[14/07/2025 14:29:25] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[14/07/2025 14:29:25] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 5: {"employee":"Jean Dupont","date":"2025-07-15","assignmentId":"cd38f349-5704-4012-a750-93be50f87b34","postId":"97a5a09a-60b7-495c-9796-bc44cfc56585","text":"08:00-16:00"}
[14/07/2025 14:29:25] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[14/07/2025 14:29:25] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-16: {"assignmentId":"bca6e806-1aec-4ec4-86f4-ea29b522fc36","dayDateStr":"2025-07-16","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[14/07/2025 14:29:25] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-16 après endDate 2025-07-13 (original: 2025-07-13) (attribution bca6e806-1aec-4ec4-86f4-ea29b522fc36)
[14/07/2025 14:29:25] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-16 ignoré pour attribution bca6e806-1aec-4ec4-86f4-ea29b522fc36
[14/07/2025 14:29:25] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-17 (4)
[14/07/2025 14:29:25] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[14/07/2025 14:29:25] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"bca6e806-1aec-4ec4-86f4-ea29b522fc36","postId":"97a5a09a-60b7-495c-9796-bc44cfc56585","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-17T04:00:00.000Z","dayDateKey":"2025-07-17","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5]}
[14/07/2025 14:29:25] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-17 après endDate 2025-07-13 (original: 2025-07-13) (attribution bca6e806-1aec-4ec4-86f4-ea29b522fc36)
[14/07/2025 14:29:25] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-17: {"assignmentId":"bca6e806-1aec-4ec4-86f4-ea29b522fc36","dayDateStr":"2025-07-17","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[14/07/2025 14:29:25] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-07-18 (5)
[14/07/2025 14:29:25] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-17 ignoré pour attribution bca6e806-1aec-4ec4-86f4-ea29b522fc36
[14/07/2025 14:29:25] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[14/07/2025 14:29:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"bca6e806-1aec-4ec4-86f4-ea29b522fc36","postId":"97a5a09a-60b7-495c-9796-bc44cfc56585","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-18T04:00:00.000Z","dayDateKey":"2025-07-18","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5]}
[14/07/2025 14:29:26] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-18 ignoré pour attribution bca6e806-1aec-4ec4-86f4-ea29b522fc36
[14/07/2025 14:29:26] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-18 après endDate 2025-07-13 (original: 2025-07-13) (attribution bca6e806-1aec-4ec4-86f4-ea29b522fc36)
[14/07/2025 14:29:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-18: {"assignmentId":"bca6e806-1aec-4ec4-86f4-ea29b522fc36","dayDateStr":"2025-07-18","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[14/07/2025 14:29:26] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[14/07/2025 14:29:26] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-07-19 (6)
[14/07/2025 14:29:26] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"4d1e2a29-162d-4f25-8b37-f5fe83494a73","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"a57ffb59-baf2-454a-b825-4e74d4ae4787","selectedDays":[0,6],"startDate":"2025-07-06","endDate":null,"excludedDates":[]}
[14/07/2025 14:29:26] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-19 ignoré pour attribution bca6e806-1aec-4ec4-86f4-ea29b522fc36
[14/07/2025 14:29:26] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-13 (0)
[14/07/2025 14:29:26] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution 4d1e2a29-162d-4f25-8b37-f5fe83494a73: [{"dateKey":"2025-07-13","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-14","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-15","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-16","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-17","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-07-18","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-07-19","isWeekend":true,"dayOfWeek":6}]
[14/07/2025 14:29:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-13: {"assignmentId":"4d1e2a29-162d-4f25-8b37-f5fe83494a73","dayDateStr":"2025-07-13","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[14/07/2025 14:29:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"4d1e2a29-162d-4f25-8b37-f5fe83494a73","postId":"a57ffb59-baf2-454a-b825-4e74d4ae4787","postLabel":"Poste WE2","postCategory":"weekend","dayDate":"2025-07-13T04:00:00.000Z","dayDateKey":"2025-07-13","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[0,6]}
[14/07/2025 14:29:26] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[14/07/2025 14:29:26] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste WE2 → 2025-07-13 pour Pierre Durand
[14/07/2025 14:29:26] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-13 validé, shifts existants: 0
[14/07/2025 14:29:26] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-13
[14/07/2025 14:29:26] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 1 non sélectionné dans: [0,6]
[14/07/2025 14:29:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"4d1e2a29-162d-4f25-8b37-f5fe83494a73","postId":"a57ffb59-baf2-454a-b825-4e74d4ae4787","postLabel":"Poste WE2","postCategory":"weekend","dayDate":"2025-07-14T04:00:00.000Z","dayDateKey":"2025-07-14","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[0,6]}
[14/07/2025 14:29:26] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-14 (1)
[14/07/2025 14:29:26] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-15 (2)
[14/07/2025 14:29:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"4d1e2a29-162d-4f25-8b37-f5fe83494a73","postId":"a57ffb59-baf2-454a-b825-4e74d4ae4787","postLabel":"Poste WE2","postCategory":"weekend","dayDate":"2025-07-15T04:00:00.000Z","dayDateKey":"2025-07-15","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[0,6]}
[14/07/2025 14:29:26] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-14 ignoré pour attribution 4d1e2a29-162d-4f25-8b37-f5fe83494a73
[14/07/2025 14:29:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"4d1e2a29-162d-4f25-8b37-f5fe83494a73","postId":"a57ffb59-baf2-454a-b825-4e74d4ae4787","postLabel":"Poste WE2","postCategory":"weekend","dayDate":"2025-07-16T04:00:00.000Z","dayDateKey":"2025-07-16","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[0,6]}
[14/07/2025 14:29:26] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-16 (3)
[14/07/2025 14:29:26] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-15 ignoré pour attribution 4d1e2a29-162d-4f25-8b37-f5fe83494a73
[14/07/2025 14:29:26] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 2 non sélectionné dans: [0,6]
[14/07/2025 14:29:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"4d1e2a29-162d-4f25-8b37-f5fe83494a73","postId":"a57ffb59-baf2-454a-b825-4e74d4ae4787","postLabel":"Poste WE2","postCategory":"weekend","dayDate":"2025-07-17T04:00:00.000Z","dayDateKey":"2025-07-17","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[0,6]}
[14/07/2025 14:29:26] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-17 (4)
[14/07/2025 14:29:26] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-16 ignoré pour attribution 4d1e2a29-162d-4f25-8b37-f5fe83494a73
[14/07/2025 14:29:26] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 3 non sélectionné dans: [0,6]
[14/07/2025 14:29:26] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-07-18 (5)
[14/07/2025 14:29:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"4d1e2a29-162d-4f25-8b37-f5fe83494a73","postId":"a57ffb59-baf2-454a-b825-4e74d4ae4787","postLabel":"Poste WE2","postCategory":"weekend","dayDate":"2025-07-18T04:00:00.000Z","dayDateKey":"2025-07-18","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[0,6]}
[14/07/2025 14:29:26] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-17 ignoré pour attribution 4d1e2a29-162d-4f25-8b37-f5fe83494a73
[14/07/2025 14:29:26] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 4 non sélectionné dans: [0,6]
[14/07/2025 14:29:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"4d1e2a29-162d-4f25-8b37-f5fe83494a73","postId":"a57ffb59-baf2-454a-b825-4e74d4ae4787","postLabel":"Poste WE2","postCategory":"weekend","dayDate":"2025-07-19T04:00:00.000Z","dayDateKey":"2025-07-19","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[0,6]}
[14/07/2025 14:29:26] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-07-19 (6)
[14/07/2025 14:29:26] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-18 ignoré pour attribution 4d1e2a29-162d-4f25-8b37-f5fe83494a73
[14/07/2025 14:29:26] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 5 non sélectionné dans: [0,6]
[14/07/2025 14:29:26] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-19 validé, shifts existants: 0
[14/07/2025 14:29:26] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-19
[14/07/2025 14:29:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-19: {"assignmentId":"4d1e2a29-162d-4f25-8b37-f5fe83494a73","dayDateStr":"2025-07-19","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[14/07/2025 14:29:26] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[14/07/2025 14:29:26] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-13 (0)
[14/07/2025 14:29:26] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution e1d1ea5b-67e9-45a0-9498-ef20cefd302b: [{"dateKey":"2025-07-13","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-14","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-15","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-16","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-17","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-07-18","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-07-19","isWeekend":true,"dayOfWeek":6}]
[14/07/2025 14:29:26] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste WE2 → 2025-07-19 pour Pierre Durand
[14/07/2025 14:29:26] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-13 validé, shifts existants: 0
[14/07/2025 14:29:26] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"e1d1ea5b-67e9-45a0-9498-ef20cefd302b","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"c8d557c3-bb9d-45bf-a90b-a98192f08d76","selectedDays":[0,6],"startDate":"2025-07-09","endDate":null,"excludedDates":[]}
[14/07/2025 14:29:26] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-13
[14/07/2025 14:29:26] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[14/07/2025 14:29:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-13: {"assignmentId":"e1d1ea5b-67e9-45a0-9498-ef20cefd302b","dayDateStr":"2025-07-13","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[14/07/2025 14:29:26] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 1 non sélectionné dans: [0,6]
[14/07/2025 14:29:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"e1d1ea5b-67e9-45a0-9498-ef20cefd302b","postId":"c8d557c3-bb9d-45bf-a90b-a98192f08d76","postLabel":"Poste WE1","postCategory":"weekend","dayDate":"2025-07-14T04:00:00.000Z","dayDateKey":"2025-07-14","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[0,6]}
[14/07/2025 14:29:26] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-14 (1)
[14/07/2025 14:29:26] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste WE1 → 2025-07-13 pour Sophie Leblanc
[14/07/2025 14:29:26] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 2 non sélectionné dans: [0,6]
[14/07/2025 14:29:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"e1d1ea5b-67e9-45a0-9498-ef20cefd302b","postId":"c8d557c3-bb9d-45bf-a90b-a98192f08d76","postLabel":"Poste WE1","postCategory":"weekend","dayDate":"2025-07-15T04:00:00.000Z","dayDateKey":"2025-07-15","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[0,6]}
[14/07/2025 14:29:26] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-15 (2)
[14/07/2025 14:29:26] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-14 ignoré pour attribution e1d1ea5b-67e9-45a0-9498-ef20cefd302b
[14/07/2025 14:29:26] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-16 (3)
[14/07/2025 14:29:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"e1d1ea5b-67e9-45a0-9498-ef20cefd302b","postId":"c8d557c3-bb9d-45bf-a90b-a98192f08d76","postLabel":"Poste WE1","postCategory":"weekend","dayDate":"2025-07-16T04:00:00.000Z","dayDateKey":"2025-07-16","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[0,6]}
[14/07/2025 14:29:26] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-15 ignoré pour attribution e1d1ea5b-67e9-45a0-9498-ef20cefd302b
[14/07/2025 14:29:26] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-17 (4)
[14/07/2025 14:29:26] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-16 ignoré pour attribution e1d1ea5b-67e9-45a0-9498-ef20cefd302b
[14/07/2025 14:29:26] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: a57ffb59-baf2-454a-b825-4e74d4ae4787 -> "12:00-24:00"
[14/07/2025 14:29:26] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 4800a7d3-709b-4dc7-890f-e9733a6bfa63 -> "00:00-08:00"
[14/07/2025 14:29:26] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ff229f24-11fe-40c9-b245-0427c3e68a7a configuré comme draggable
[14/07/2025 14:29:26] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste c8d557c3-bb9d-45bf-a90b-a98192f08d76 configuré comme draggable
[14/07/2025 14:29:26] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[14/07/2025 14:29:26] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 3 non sélectionné dans: [0,6]
[14/07/2025 14:29:26] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 4 non sélectionné dans: [0,6]
[14/07/2025 14:29:26] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 5 non sélectionné dans: [0,6]
[14/07/2025 14:29:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"e1d1ea5b-67e9-45a0-9498-ef20cefd302b","postId":"c8d557c3-bb9d-45bf-a90b-a98192f08d76","postLabel":"Poste WE1","postCategory":"weekend","dayDate":"2025-07-18T04:00:00.000Z","dayDateKey":"2025-07-18","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[0,6]}
[14/07/2025 14:29:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"e1d1ea5b-67e9-45a0-9498-ef20cefd302b","postId":"c8d557c3-bb9d-45bf-a90b-a98192f08d76","postLabel":"Poste WE1","postCategory":"weekend","dayDate":"2025-07-17T04:00:00.000Z","dayDateKey":"2025-07-17","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[0,6]}
[14/07/2025 14:29:26] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-17 ignoré pour attribution e1d1ea5b-67e9-45a0-9498-ef20cefd302b
[14/07/2025 14:29:26] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-07-18 (5)
[14/07/2025 14:29:26] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[14/07/2025 14:29:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"e1d1ea5b-67e9-45a0-9498-ef20cefd302b","postId":"c8d557c3-bb9d-45bf-a90b-a98192f08d76","postLabel":"Poste WE1","postCategory":"weekend","dayDate":"2025-07-19T04:00:00.000Z","dayDateKey":"2025-07-19","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[0,6]}
[14/07/2025 14:29:26] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-18 ignoré pour attribution e1d1ea5b-67e9-45a0-9498-ef20cefd302b
[14/07/2025 14:29:26] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste WE1 → 2025-07-19 pour Sophie Leblanc
[14/07/2025 14:29:26] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-07-19 (6)
[14/07/2025 14:29:26] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-19: {"assignmentId":"e1d1ea5b-67e9-45a0-9498-ef20cefd302b","dayDateStr":"2025-07-19","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[14/07/2025 14:29:26] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-19 validé, shifts existants: 0
[14/07/2025 14:29:26] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 14 attributions appliquées pour la semaine 2025-W28
[14/07/2025 14:29:26] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-19
[14/07/2025 14:29:26] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Rendu demandé car 14 shifts créés
[14/07/2025 14:29:26] [FRONTEND] [LOG] ✅ [init] Initialisation terminée avec succès
[14/07/2025 14:29:26] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[14/07/2025 14:29:26] [FRONTEND] [LOG] 🎉 [Agenda] Application fullscreen initialisée avec succès
[14/07/2025 14:29:26] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière e1d1ea5b-67e9-45a0-9498-ef20cefd302b
[14/07/2025 14:29:26] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: c8d557c3-bb9d-45bf-a90b-a98192f08d76 -> "00:00-12:00"
[14/07/2025 14:29:26] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: c8d557c3-bb9d-45bf-a90b-a98192f08d76 -> "00:00-12:00"
[14/07/2025 14:29:26] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière e1d1ea5b-67e9-45a0-9498-ef20cefd302b
[14/07/2025 14:29:26] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 97a5a09a-60b7-495c-9796-bc44cfc56585 -> "08:00-16:00"
[14/07/2025 14:29:26] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 0e114dfe-053e-4c8d-ba1b-1edb6f814fcf
[14/07/2025 14:29:26] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 97a5a09a-60b7-495c-9796-bc44cfc56585 -> "08:00-16:00"
[14/07/2025 14:29:26] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière cd38f349-5704-4012-a750-93be50f87b34
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 97a5a09a-60b7-495c-9796-bc44cfc56585 -> "08:00-16:00"
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière cd38f349-5704-4012-a750-93be50f87b34
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 4800a7d3-709b-4dc7-890f-e9733a6bfa63 -> "00:00-08:00"
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 97a5a09a-60b7-495c-9796-bc44cfc56585 -> "08:00-16:00"
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière cd38f349-5704-4012-a750-93be50f87b34
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 97a5a09a-60b7-495c-9796-bc44cfc56585 -> "08:00-16:00"
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière cd38f349-5704-4012-a750-93be50f87b34
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 4d1e2a29-162d-4f25-8b37-f5fe83494a73
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière f6cfa7cb-0f48-4753-82d7-7fc39b9cd501
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: a57ffb59-baf2-454a-b825-4e74d4ae4787 -> "12:00-24:00"
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 4d1e2a29-162d-4f25-8b37-f5fe83494a73
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière f6cfa7cb-0f48-4753-82d7-7fc39b9cd501
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 4800a7d3-709b-4dc7-890f-e9733a6bfa63 -> "00:00-08:00"
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière f6cfa7cb-0f48-4753-82d7-7fc39b9cd501
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 4800a7d3-709b-4dc7-890f-e9733a6bfa63 -> "00:00-08:00"
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 4800a7d3-709b-4dc7-890f-e9733a6bfa63 -> "00:00-08:00"
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière f6cfa7cb-0f48-4753-82d7-7fc39b9cd501
[14/07/2025 14:29:27] [FRONTEND] [WARN] ⚠️ [Legacy] workingDays manquant pour Poste Matin (97a5a09a-60b7-495c-9796-bc44cfc56585) - API n'a pas renvoyé les données
[14/07/2025 14:29:27] [FRONTEND] [WARN] ⚠️ [Legacy] workingDays manquant pour Poste Nuit (4800a7d3-709b-4dc7-890f-e9733a6bfa63) - API n'a pas renvoyé les données
[14/07/2025 14:29:27] [FRONTEND] [WARN] ⚠️ [Legacy] workingDays manquant pour Poste WE1 (c8d557c3-bb9d-45bf-a90b-a98192f08d76) - API n'a pas renvoyé les données
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[14/07/2025 14:29:27] [FRONTEND] [WARN] ⚠️ [Legacy] workingDays manquant pour Poste WE2 (a57ffb59-baf2-454a-b825-4e74d4ae4787) - API n'a pas renvoyé les données
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 88 zones trouvées
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 88 zones de drop disponibles
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 97a5a09a-60b7-495c-9796-bc44cfc56585 configuré comme draggable
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 97a5a09a-60b7-495c-9796-bc44cfc56585
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 4800a7d3-709b-4dc7-890f-e9733a6bfa63 configuré comme draggable
[14/07/2025 14:29:27] [FRONTEND] [WARN] ⚠️ [Legacy] workingDays manquant pour Poste Soir (ff229f24-11fe-40c9-b245-0427c3e68a7a) - API n'a pas renvoyé les données
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ff229f24-11fe-40c9-b245-0427c3e68a7a
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ff229f24-11fe-40c9-b245-0427c3e68a7a configuré comme draggable
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste c8d557c3-bb9d-45bf-a90b-a98192f08d76
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🔍 [attachSettingsButtonIfMissing] Vérification des boutons paramètres
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 4800a7d3-709b-4dc7-890f-e9733a6bfa63
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 97a5a09a-60b7-495c-9796-bc44cfc56585
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a57ffb59-baf2-454a-b825-4e74d4ae4787 configuré comme draggable
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste c8d557c3-bb9d-45bf-a90b-a98192f08d76 configuré comme draggable
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ff229f24-11fe-40c9-b245-0427c3e68a7a
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a57ffb59-baf2-454a-b825-4e74d4ae4787
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 97a5a09a-60b7-495c-9796-bc44cfc56585 configuré comme draggable
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a57ffb59-baf2-454a-b825-4e74d4ae4787
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 4800a7d3-709b-4dc7-890f-e9733a6bfa63
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a57ffb59-baf2-454a-b825-4e74d4ae4787 configuré comme draggable
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 4800a7d3-709b-4dc7-890f-e9733a6bfa63 configuré comme draggable
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste c8d557c3-bb9d-45bf-a90b-a98192f08d76
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🔧 [ModalFunctionalitiesManager] Initialisation du gestionnaire de modales
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (88 zones)
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 88 zones trouvées
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [SETUP] Drag & drop des employés configuré (mode simplifié)
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 97a5a09a-60b7-495c-9796-bc44cfc56585
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 97a5a09a-60b7-495c-9796-bc44cfc56585 configuré comme draggable
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ff229f24-11fe-40c9-b245-0427c3e68a7a configuré comme draggable
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 4800a7d3-709b-4dc7-890f-e9733a6bfa63 configuré comme draggable
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 4800a7d3-709b-4dc7-890f-e9733a6bfa63
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a57ffb59-baf2-454a-b825-4e74d4ae4787 configuré comme draggable
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ff229f24-11fe-40c9-b245-0427c3e68a7a
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a57ffb59-baf2-454a-b825-4e74d4ae4787
[14/07/2025 14:29:27] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 10: {"employee":"Lucas Bernard","date":"2025-07-17","assignmentId":"f6cfa7cb-0f48-4753-82d7-7fc39b9cd501","postId":"4800a7d3-709b-4dc7-890f-e9733a6bfa63","text":"00:00-08:00"}
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 97a5a09a-60b7-495c-9796-bc44cfc56585 configuré comme draggable
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ff229f24-11fe-40c9-b245-0427c3e68a7a configuré comme draggable
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 4800a7d3-709b-4dc7-890f-e9733a6bfa63 configuré comme draggable
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 97a5a09a-60b7-495c-9796-bc44cfc56585
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste c8d557c3-bb9d-45bf-a90b-a98192f08d76 configuré comme draggable
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 4800a7d3-709b-4dc7-890f-e9733a6bfa63
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ff229f24-11fe-40c9-b245-0427c3e68a7a
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste c8d557c3-bb9d-45bf-a90b-a98192f08d76
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a57ffb59-baf2-454a-b825-4e74d4ae4787
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a57ffb59-baf2-454a-b825-4e74d4ae4787 configuré comme draggable
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[14/07/2025 14:29:27] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[14/07/2025 14:29:27] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[14/07/2025 14:29:28] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[14/07/2025 14:29:28] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[14/07/2025 14:29:28] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[14/07/2025 14:29:28] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[14/07/2025 14:29:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (88 zones)
[14/07/2025 14:29:28] [FRONTEND] [LOG] 🔧 [attachSettingsButtonIfMissing] Attachement du bouton paramètres sidebar
[14/07/2025 14:29:28] [FRONTEND] [LOG] 🔧 [attachSettingsButtonIfMissing] Attachement du bouton paramètres header
[14/07/2025 14:29:28] [FRONTEND] [LOG] 🔧 [ModalFunctionalitiesManager] Initialisation du gestionnaire de modales
[14/07/2025 14:29:28] [FRONTEND] [LOG] 🔄 [attachSettingsButtonIfMissing] Gestionnaire de modales initialisé
[14/07/2025 14:29:28] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] 0 grips ajoutés
[14/07/2025 14:29:28] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[14/07/2025 14:29:28] [FRONTEND] [LOG] 🎯 [diagnoseRegularAssignmentGrips] Grips trouvés dans le DOM: 14
[14/07/2025 14:29:28] [FRONTEND] [LOG] 📊 [diagnoseRegularAssignmentGrips] Statistiques: {"totalShifts":14,"regularShifts":14,"regularAssignments":8}
[14/07/2025 14:29:28] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 1: {"employee":"Sophie Leblanc","date":"2025-07-13","assignmentId":"e1d1ea5b-67e9-45a0-9498-ef20cefd302b","postId":"c8d557c3-bb9d-45bf-a90b-a98192f08d76","text":"00:00-12:00"}
[14/07/2025 14:29:28] [FRONTEND] [LOG] 🔍 [diagnoseRegularAssignmentGrips] Diagnostic des grips d'attributions régulières
[14/07/2025 14:29:28] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 3: {"employee":"Jean Dupont","date":"2025-07-14","assignmentId":"0e114dfe-053e-4c8d-ba1b-1edb6f814fcf","postId":"97a5a09a-60b7-495c-9796-bc44cfc56585","text":"08:00-16:00"}
[14/07/2025 14:29:28] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 2: {"employee":"Pierre Durand","date":"2025-07-13","assignmentId":"4d1e2a29-162d-4f25-8b37-f5fe83494a73","postId":"a57ffb59-baf2-454a-b825-4e74d4ae4787","text":"12:00-24:00"}
[14/07/2025 14:29:28] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 4: {"employee":"Lucas Bernard","date":"2025-07-14","assignmentId":"f6cfa7cb-0f48-4753-82d7-7fc39b9cd501","postId":"4800a7d3-709b-4dc7-890f-e9733a6bfa63","text":"00:00-08:00"}
[14/07/2025 14:29:28] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 6: {"employee":"Lucas Bernard","date":"2025-07-15","assignmentId":"f6cfa7cb-0f48-4753-82d7-7fc39b9cd501","postId":"4800a7d3-709b-4dc7-890f-e9733a6bfa63","text":"00:00-08:00"}
[14/07/2025 14:29:28] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 7: {"employee":"Jean Dupont","date":"2025-07-16","assignmentId":"cd38f349-5704-4012-a750-93be50f87b34","postId":"97a5a09a-60b7-495c-9796-bc44cfc56585","text":"08:00-16:00"}
[14/07/2025 14:29:28] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 8: {"employee":"Lucas Bernard","date":"2025-07-16","assignmentId":"f6cfa7cb-0f48-4753-82d7-7fc39b9cd501","postId":"4800a7d3-709b-4dc7-890f-e9733a6bfa63","text":"00:00-08:00"}
[14/07/2025 14:29:28] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 9: {"employee":"Jean Dupont","date":"2025-07-17","assignmentId":"cd38f349-5704-4012-a750-93be50f87b34","postId":"97a5a09a-60b7-495c-9796-bc44cfc56585","text":"08:00-16:00"}
[14/07/2025 14:29:28] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 11: {"employee":"Jean Dupont","date":"2025-07-18","assignmentId":"cd38f349-5704-4012-a750-93be50f87b34","postId":"97a5a09a-60b7-495c-9796-bc44cfc56585","text":"08:00-16:00"}
[14/07/2025 14:29:28] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 12: {"employee":"Lucas Bernard","date":"2025-07-18","assignmentId":"f6cfa7cb-0f48-4753-82d7-7fc39b9cd501","postId":"4800a7d3-709b-4dc7-890f-e9733a6bfa63","text":"00:00-08:00"}
[14/07/2025 14:29:28] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 13: {"employee":"Sophie Leblanc","date":"2025-07-19","assignmentId":"e1d1ea5b-67e9-45a0-9498-ef20cefd302b","postId":"c8d557c3-bb9d-45bf-a90b-a98192f08d76","text":"00:00-12:00"}
[14/07/2025 14:29:28] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 14: {"employee":"Pierre Durand","date":"2025-07-19","assignmentId":"4d1e2a29-162d-4f25-8b37-f5fe83494a73","postId":"a57ffb59-baf2-454a-b825-4e74d4ae4787","text":"12:00-24:00"}
[14/07/2025 14:29:28] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[14/07/2025 14:29:28] [FRONTEND] [LOG] 🔍 [attachSettingsButtonIfMissing] Vérification des boutons paramètres
[14/07/2025 14:29:28] [FRONTEND] [LOG] 🔧 [attachSettingsButtonIfMissing] Attachement du bouton paramètres header
[14/07/2025 14:29:28] [FRONTEND] [LOG] 🔧 [attachSettingsButtonIfMissing] Attachement du bouton paramètres sidebar
[14/07/2025 14:29:28] [FRONTEND] [LOG] 🔄 [attachSettingsButtonIfMissing] Gestionnaire de modales initialisé