[15/07/2025 09:31:26] [FRONTEND] [LOG] - testForkModalWithDate() : Tester le modal avec date
[15/07/2025 09:31:26] [FRONTEND] [LOG] - testCompleteForkFlow() : Tester le flux complet
[15/07/2025 09:31:26] [FRONTEND] [LOG] - diagnoseForkModalIssues() : Diagnostic complet
[15/07/2025 09:31:26] [FRONTEND] [LOG] [LogsPage] Fermeture de la connexion SSE.
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:26.386Z"
}
[15/07/2025 09:31:26] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[15/07/2025 09:31:26] [BACKEND] [INFO] [LOGS] Client connecté: 1752586286385
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:26.406Z"
}
[15/07/2025 09:31:26] [FRONTEND] [LOG] 🏢 [TeamCalendarApp] 5 postes standards chargés
[15/07/2025 09:31:26] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[15/07/2025 09:30:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3 configuré comme draggable
[15/07/2025 09:30:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste cf827d15-6cde-4f62-adc8-ceb64b8f6bb3
[15/07/2025 09:30:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6a79ab18-319e-4a85-805e-f80d0de9a73f configuré comme draggable
[15/07/2025 09:30:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6a79ab18-319e-4a85-805e-f80d0de9a73f
[15/07/2025 09:30:55] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 09:30:55] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 09:30:55] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:30:55] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:30:55] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:30:55] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:30:55] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:30:55] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:30:55] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:30:55] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:30:55] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:30:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (70 zones)
[15/07/2025 09:30:55] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 09:30:55] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:30:55] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] 0 grips ajoutés
[15/07/2025 09:30:55] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 09:30:55] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 09:31:01] [BACKEND] [INFO] [LOGGER] [query] Executing query: CREATE TABLE IF NOT EXISTS logs (
  id SERIAL PRIMARY KEY,
  session_id VARCHAR(255) NOT NULL,
  timestamp TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
  source VARCHAR(50) NOT NULL,
  level VARCHAR(50) NOT NULL,
  message TEXT NOT NULL,
  data JSONB,
  priority INTEGER DEFAULT 0
) {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:00.919Z",
  "originalArgs": [
    "[query] Executing query: CREATE TABLE IF NOT EXISTS logs (\n  id SERIAL PRIMARY KEY,\n  session_id VARCHAR(255) NOT NULL,\n  timestamp TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,\n  source VARCHAR(50) NOT NULL,\n  level VARCHAR(50) NOT NULL,\n  message TEXT NOT NULL,\n  data JSONB,\n  priority INTEGER DEFAULT 0\n)",
    {}
  ]
}
[15/07/2025 09:31:01] [BACKEND] [INFO] [LOGGER] ✅ [Logger] Console patchée pour intégration unifiée
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:00.912Z",
  "originalArgs": [
    "✅ [Logger] Console patchée pour intégration unifiée"
  ]
}
[15/07/2025 09:31:01] [BACKEND] [INFO] [LOGGER] 🛡️  Protection UUID: ACTIVÉE
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:00.967Z",
  "originalArgs": [
    "🛡️  Protection UUID: ACTIVÉE"
  ]
}
[15/07/2025 09:31:01] [BACKEND] [INFO] [LOGGER] ============================================================
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:00.930Z",
  "originalArgs": [
    "============================================================"
  ]
}
[15/07/2025 09:31:01] [BACKEND] [INFO] [LOGGER] ⏰ Démarré le: 2025-07-15T13:31:00.970Z
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:00.970Z",
  "originalArgs": [
    "⏰ Démarré le: 2025-07-15T13:31:00.970Z"
  ]
}
[15/07/2025 09:31:01] [BACKEND] [INFO] [LOGGER] ============================================================
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:00.970Z",
  "originalArgs": [
    "============================================================"
  ]
}
[15/07/2025 09:31:01] [BACKEND] [INFO] [LOGGER] ⚠️  Gestion erreurs: ROBUSTE
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:00.969Z",
  "originalArgs": [
    "⚠️  Gestion erreurs: ROBUSTE"
  ]
}
[15/07/2025 09:31:01] [BACKEND] [INFO] [LOGGER] 🏥 Health Check: http://localhost:3001/health
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:00.964Z",
  "originalArgs": [
    "🏥 Health Check: http://localhost:3001/health"
  ]
}
[15/07/2025 09:31:01] [BACKEND] [INFO] [LOGGER] ============================================================
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:00.963Z",
  "originalArgs": [
    "============================================================"
  ]
}
[15/07/2025 09:31:01] [BACKEND] [INFO] [LOGGER] 📍 URL: http://localhost:3001
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:00.964Z",
  "originalArgs": [
    "📍 URL: http://localhost:3001"
  ]
}
[15/07/2025 09:31:01] [BACKEND] [INFO] [LOGGER] 🔍 Logs détaillés: ACTIVÉS
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:00.968Z",
  "originalArgs": [
    "🔍 Logs détaillés: ACTIVÉS"
  ]
}
[15/07/2025 09:31:01] [FRONTEND] [LOG] [vite] server connection lost. Polling for restart...
[15/07/2025 09:31:01] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:01.110Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 09:31:01] [BACKEND] [INFO] [LOGGER] 🚀 SERVEUR API DÉMARRÉ AVEC PROTECTION UUID
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:00.962Z",
  "originalArgs": [
    "🚀 SERVEUR API DÉMARRÉ AVEC PROTECTION UUID"
  ]
}
[15/07/2025 09:31:01] [FRONTEND] [LOG] [vite] server connection lost. Polling for restart...
[15/07/2025 09:31:01] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:01.113Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 09:31:01] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:01.113Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 09:31:01] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:01.112Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 09:31:01] [BACKEND] [INFO] ============================================================
[15/07/2025 09:31:01] [BACKEND] [INFO] [LOGGER] ✅ Serveur prêt à recevoir les requêtes
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:00.971Z",
  "originalArgs": [
    "✅ Serveur prêt à recevoir les requêtes"
  ]
}
[15/07/2025 09:31:01] [BACKEND] [INFO] [LOGGER] 
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:00.972Z",
  "originalArgs": [
    ""
  ]
}
[15/07/2025 09:31:01] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:01.142Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 09:31:01] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:01.145Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 09:31:01] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:01.144Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 09:31:01] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:01.145Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 09:31:01] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:01.146Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 09:31:01] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:01.147Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 09:31:01] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:01.149Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 09:31:01] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:01.158Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 09:31:01] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:01.159Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 09:31:01] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:01.148Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 09:31:01] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 244ms: CREATE TABLE IF NOT EXISTS logs (
  id SERIAL PRIM {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:01.162Z",
  "originalArgs": [
    "✅ Requête exécutée en 244ms:",
    "CREATE TABLE IF NOT EXISTS logs (\n  id SERIAL PRIM",
    {}
  ]
}
[15/07/2025 09:31:01] [BACKEND] [INFO] [LOGGER] [query] Executing query: ALTER TABLE logs ADD COLUMN IF NOT EXISTS priority INTEGER DEFAULT 0 {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:01.163Z",
  "originalArgs": [
    "[query] Executing query: ALTER TABLE logs ADD COLUMN IF NOT EXISTS priority INTEGER DEFAULT 0",
    {}
  ]
}
[15/07/2025 09:31:01] [FRONTEND] [ERROR] [LogsPage] Erreur SSE: {"isTrusted":true}
[15/07/2025 09:31:01] [BACKEND] [INFO] [LOGGER] ✅ [Logger] Connexion PostgreSQL (distant) validée
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:01.172Z",
  "originalArgs": [
    "✅ [Logger] Connexion PostgreSQL (distant) validée"
  ]
}
[15/07/2025 09:31:01] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:01.169Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 09:31:01] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:01.173Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 09:31:01] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 262ms: SELECT 1 FROM logs LIMIT 1 {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:01.172Z",
  "originalArgs": [
    "✅ Requête exécutée en 262ms:",
    "SELECT 1 FROM logs LIMIT 1",
    {}
  ]
}
[15/07/2025 09:31:01] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:01.178Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 09:31:01] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:01.212Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 09:31:01] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:01.179Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 09:31:01] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:01.180Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 09:31:01] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 97ms: ALTER TABLE logs ADD COLUMN IF NOT EXISTS priority {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:01.260Z",
  "originalArgs": [
    "✅ Requête exécutée en 97ms:",
    "ALTER TABLE logs ADD COLUMN IF NOT EXISTS priority",
    {}
  ]
}
[15/07/2025 09:31:01] [BACKEND] [INFO] ✅ Requête exécutée en 97ms: ALTER TABLE logs ADD COLUMN IF NOT EXISTS priority {}
[15/07/2025 09:31:17] [BACKEND] [INFO] [LOGGER] ✅ [Logger] Console patchée pour intégration unifiée
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:17.400Z",
  "originalArgs": [
    "✅ [Logger] Console patchée pour intégration unifiée"
  ]
}
[15/07/2025 09:31:17] [BACKEND] [INFO] [LOGGER] ============================================================
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:17.441Z",
  "originalArgs": [
    "============================================================"
  ]
}
[15/07/2025 09:31:17] [BACKEND] [INFO] [LOGGER] [query] Executing query: CREATE TABLE IF NOT EXISTS logs (
  id SERIAL PRIMARY KEY,
  session_id VARCHAR(255) NOT NULL,
  timestamp TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
  source VARCHAR(50) NOT NULL,
  level VARCHAR(50) NOT NULL,
  message TEXT NOT NULL,
  data JSONB,
  priority INTEGER DEFAULT 0
) {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:17.406Z",
  "originalArgs": [
    "[query] Executing query: CREATE TABLE IF NOT EXISTS logs (\n  id SERIAL PRIMARY KEY,\n  session_id VARCHAR(255) NOT NULL,\n  timestamp TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,\n  source VARCHAR(50) NOT NULL,\n  level VARCHAR(50) NOT NULL,\n  message TEXT NOT NULL,\n  data JSONB,\n  priority INTEGER DEFAULT 0\n)",
    {}
  ]
}
[15/07/2025 09:31:17] [BACKEND] [INFO] [LOGGER] 🚀 SERVEUR API DÉMARRÉ AVEC PROTECTION UUID
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:17.441Z",
  "originalArgs": [
    "🚀 SERVEUR API DÉMARRÉ AVEC PROTECTION UUID"
  ]
}
[15/07/2025 09:31:17] [BACKEND] [INFO] [LOGGER] 🛡️  Protection UUID: ACTIVÉE
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:17.444Z",
  "originalArgs": [
    "🛡️  Protection UUID: ACTIVÉE"
  ]
}
[15/07/2025 09:31:17] [BACKEND] [INFO] [LOGGER] ⏰ Démarré le: 2025-07-15T13:31:17.448Z
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:17.448Z",
  "originalArgs": [
    "⏰ Démarré le: 2025-07-15T13:31:17.448Z"
  ]
}
[15/07/2025 09:31:17] [BACKEND] [INFO] [LOGGER] 📍 URL: http://localhost:3001
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:17.442Z",
  "originalArgs": [
    "📍 URL: http://localhost:3001"
  ]
}
[15/07/2025 09:31:17] [BACKEND] [INFO] [LOGGER] ✅ Serveur prêt à recevoir les requêtes
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:17.448Z",
  "originalArgs": [
    "✅ Serveur prêt à recevoir les requêtes"
  ]
}
[15/07/2025 09:31:17] [BACKEND] [INFO] [LOGGER] ⚠️  Gestion erreurs: ROBUSTE
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:17.447Z",
  "originalArgs": [
    "⚠️  Gestion erreurs: ROBUSTE"
  ]
}
[15/07/2025 09:31:17] [BACKEND] [INFO] [LOGGER] ============================================================
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:17.415Z",
  "originalArgs": [
    "============================================================"
  ]
}
[15/07/2025 09:31:17] [BACKEND] [INFO] [LOGGER] 🏥 Health Check: http://localhost:3001/health
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:17.443Z",
  "originalArgs": [
    "🏥 Health Check: http://localhost:3001/health"
  ]
}
[15/07/2025 09:31:17] [BACKEND] [INFO] [LOGGER] 🔍 Logs détaillés: ACTIVÉS
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:17.446Z",
  "originalArgs": [
    "🔍 Logs détaillés: ACTIVÉS"
  ]
}
[15/07/2025 09:31:17] [BACKEND] [INFO] [LOGGER] 
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:17.449Z",
  "originalArgs": [
    ""
  ]
}
[15/07/2025 09:31:17] [BACKEND] [INFO] [LOGGER] ============================================================
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:17.447Z",
  "originalArgs": [
    "============================================================"
  ]
}
[15/07/2025 09:31:17] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:17.592Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 09:31:17] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:17.616Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 09:31:17] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:17.598Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 09:31:17] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:17.614Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 09:31:17] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:17.617Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 09:31:17] [BACKEND] [INFO] ============================================================
[15/07/2025 09:31:17] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:17.618Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 09:31:17] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:17.623Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 09:31:17] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:17.619Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 09:31:17] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:17.624Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 09:31:17] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:17.625Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 09:31:17] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:17.630Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 09:31:17] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:17.629Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 09:31:17] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:17.626Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 09:31:17] [BACKEND] [INFO] [LOGGER] [query] Executing query: ALTER TABLE logs ADD COLUMN IF NOT EXISTS priority INTEGER DEFAULT 0 {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:17.633Z",
  "originalArgs": [
    "[query] Executing query: ALTER TABLE logs ADD COLUMN IF NOT EXISTS priority INTEGER DEFAULT 0",
    {}
  ]
}
[15/07/2025 09:31:17] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 227ms: CREATE TABLE IF NOT EXISTS logs (
  id SERIAL PRIM {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:17.632Z",
  "originalArgs": [
    "✅ Requête exécutée en 227ms:",
    "CREATE TABLE IF NOT EXISTS logs (\n  id SERIAL PRIM",
    {}
  ]
}
[15/07/2025 09:31:17] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:17.637Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 09:31:17] [BACKEND] [INFO] [LOGGER] ✅ [Logger] Connexion PostgreSQL (distant) validée
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:17.635Z",
  "originalArgs": [
    "✅ [Logger] Connexion PostgreSQL (distant) validée"
  ]
}
[15/07/2025 09:31:17] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 237ms: SELECT 1 FROM logs LIMIT 1 {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:17.634Z",
  "originalArgs": [
    "✅ Requête exécutée en 237ms:",
    "SELECT 1 FROM logs LIMIT 1",
    {}
  ]
}
[15/07/2025 09:31:17] [BACKEND] [INFO] ✅ Requête exécutée en 87ms: ALTER TABLE logs ADD COLUMN IF NOT EXISTS priority {}
[15/07/2025 09:31:17] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 87ms: ALTER TABLE logs ADD COLUMN IF NOT EXISTS priority {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:17.721Z",
  "originalArgs": [
    "✅ Requête exécutée en 87ms:",
    "ALTER TABLE logs ADD COLUMN IF NOT EXISTS priority",
    {}
  ]
}
[15/07/2025 09:31:17] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:17.766Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 09:31:17] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:17.768Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 09:31:17] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:17.769Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 09:31:17] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:17.590Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 09:31:17] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:17.588Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 09:31:17] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[15/07/2025 09:31:17] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:17.579Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 09:31:25] [FRONTEND] [DEBUG] [vite] connected.
[15/07/2025 09:31:25] [FRONTEND] [DEBUG] [vite] connected.
[15/07/2025 09:31:25] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[15/07/2025 09:31:25] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[15/07/2025 09:31:25] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[15/07/2025 09:31:25] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[15/07/2025 09:31:25] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[15/07/2025 09:31:25] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[15/07/2025 09:31:25] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[15/07/2025 09:31:25] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[15/07/2025 09:31:25] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[15/07/2025 09:31:25] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[15/07/2025 09:31:25] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[15/07/2025 09:31:25] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[15/07/2025 09:31:25] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[15/07/2025 09:31:25] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[15/07/2025 09:31:25] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[15/07/2025 09:31:25] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[15/07/2025 09:31:25] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[15/07/2025 09:31:25] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[15/07/2025 09:31:25] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[15/07/2025 09:31:25] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[15/07/2025 09:31:25] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[15/07/2025 09:31:25] [FRONTEND] [LOG] 🧪 [TEST] Script de test de la modale drag & drop chargé
[15/07/2025 09:31:26] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[15/07/2025 09:31:26] [FRONTEND] [LOG] - testAssignmentModal() : Tester l'ouverture de la modale
[15/07/2025 09:31:26] [FRONTEND] [LOG] - diagnoseModalIssues() : Diagnostiquer les problèmes
[15/07/2025 09:31:26] [FRONTEND] [LOG] - testButtonListeners() : Tester les listeners des boutons
[15/07/2025 09:31:26] [FRONTEND] [LOG] - forceReconfigureListeners() : Reconfigurer les listeners
[15/07/2025 09:31:26] [FRONTEND] [LOG] - testButtonClicks() : Tester les clics sur les boutons
[15/07/2025 09:31:26] [FRONTEND] [LOG] 🔍 [VALIDATION] Script de validation de la correction modale chargé
[15/07/2025 09:31:26] [FRONTEND] [LOG] ✅ [VALIDATION] Fonctions de validation disponibles:
[15/07/2025 09:31:26] [FRONTEND] [LOG] - validateMainFix() : Valider la correction principale
[15/07/2025 09:31:26] [FRONTEND] [LOG] - validateDOMElements() : Valider les éléments DOM
[15/07/2025 09:31:26] [FRONTEND] [LOG] - validateListeners() : Valider les listeners
[15/07/2025 09:31:26] [FRONTEND] [LOG] - runCompleteValidation() : Validation complète
[15/07/2025 09:31:26] [FRONTEND] [LOG] - testEndToEnd() : Test end-to-end complet
[15/07/2025 09:31:26] [FRONTEND] [LOG] 🧪 [TEST] Script de test pour la correction du fork modal chargé
[15/07/2025 09:31:26] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[15/07/2025 09:31:26] [FRONTEND] [LOG] - testDropDateDetection() : Tester la détection de date
[15/07/2025 09:31:26] [FRONTEND] [LOG] - testForkModalWithDate() : Tester le modal avec date
[15/07/2025 09:31:26] [FRONTEND] [LOG] - testCompleteForkFlow() : Tester le flux complet
[15/07/2025 09:31:26] [FRONTEND] [LOG] - diagnoseForkModalIssues() : Diagnostic complet
[15/07/2025 09:31:26] [FRONTEND] [LOG] 🧹 [Agenda] Nettoyage composant Agenda
[15/07/2025 09:31:26] [FRONTEND] [LOG] 🔄 [Agenda] Réinitialisation nécessaire (employés non chargés)
[15/07/2025 09:31:26] [FRONTEND] [LOG] 🚀 [Agenda] Initialisation TeamCalendarApp...
[15/07/2025 09:31:26] [FRONTEND] [LOG] 🚀 [init] Initialisation de TeamCalendarApp...
[15/07/2025 09:31:26] [FRONTEND] [LOG] 🧹 [clearRenderCache] Nettoyage du cache de rendu
[15/07/2025 09:31:26] [FRONTEND] [LOG] ✅ [clearRenderCache] Cache de rendu nettoyé
[15/07/2025 09:31:26] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT e.*, et.name as template_name 
      FROM employees e 
      LEFT JOIN employee_templates et ON e.template_id = et.id 
      ORDER BY e.name
     {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:26.112Z",
  "originalArgs": [
    "[query] Executing query: \n      SELECT e.*, et.name as template_name \n      FROM employees e \n      LEFT JOIN employee_templates et ON e.template_id = et.id \n      ORDER BY e.name\n    ",
    {}
  ]
}
[15/07/2025 09:31:26] [FRONTEND] [LOG] 🔌 [ConnectionIndicator] Création de l'indicateur de connexion
[15/07/2025 09:31:26] [FRONTEND] [LOG] 🔌 [init] Indicateur de connexion initialisé
[15/07/2025 09:31:26] [FRONTEND] [LOG] 📂 [TeamCalendarApp] Chargement de l'état...
[15/07/2025 09:31:26] [FRONTEND] [LOG] 🌐 [TeamCalendarApp] Chargement depuis l'API...
[15/07/2025 09:31:26] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[15/07/2025 09:31:26] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[15/07/2025 09:31:26] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[15/07/2025 09:31:26] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[15/07/2025 09:31:26] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[15/07/2025 09:31:26] [BACKEND] [INFO] ✅ Requête exécutée en 51ms: 
      SELECT e.*, et.name as template_name 
      {}
[15/07/2025 09:31:26] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 51ms: 
      SELECT e.*, et.name as template_name 
      {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:26.163Z",
  "originalArgs": [
    "✅ Requête exécutée en 51ms:",
    "\n      SELECT e.*, et.name as template_name \n     ",
    {}
  ]
}
[15/07/2025 09:31:26] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[15/07/2025 09:31:26] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT s.*, e.name as employee_name, sp.label as post_label
      FROM shifts s
      LEFT JOIN employees e ON s.employee_id = e.id
      LEFT JOIN standard_posts sp ON s.post_id = sp.id
      ORDER BY s.date_key, e.name
     {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:26.196Z",
  "originalArgs": [
    "[query] Executing query: \n      SELECT s.*, e.name as employee_name, sp.label as post_label\n      FROM shifts s\n      LEFT JOIN employees e ON s.employee_id = e.id\n      LEFT JOIN standard_posts sp ON s.post_id = sp.id\n      ORDER BY s.date_key, e.name\n    ",
    {}
  ]
}
[15/07/2025 09:31:26] [FRONTEND] [LOG] 👥 [TeamCalendarApp] 5 employés chargés
[15/07/2025 09:31:26] [FRONTEND] [LOG] 📦 [loadState] Chargement de tous les shifts depuis la base de données...
[15/07/2025 09:31:26] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[15/07/2025 09:31:26] [FRONTEND] [LOG] 🧪 [TEST] Script de test de la modale drag & drop chargé
[15/07/2025 09:31:26] [FRONTEND] [LOG] - testAssignmentModal() : Tester l'ouverture de la modale
[15/07/2025 09:31:26] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[15/07/2025 09:31:26] [FRONTEND] [LOG] - testButtonListeners() : Tester les listeners des boutons
[15/07/2025 09:31:26] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 49ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:26.244Z",
  "originalArgs": [
    "✅ Requête exécutée en 49ms:",
    "\n      SELECT s.*, e.name as employee_name, sp.lab",
    {}
  ]
}
[15/07/2025 09:31:26] [BACKEND] [INFO] ✅ Requête exécutée en 49ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
[15/07/2025 09:31:26] [FRONTEND] [LOG] - diagnoseModalIssues() : Diagnostiquer les problèmes
[15/07/2025 09:31:26] [FRONTEND] [LOG] - forceReconfigureListeners() : Reconfigurer les listeners
[15/07/2025 09:31:26] [FRONTEND] [LOG] - testButtonClicks() : Tester les clics sur les boutons
[15/07/2025 09:31:26] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:26.268Z",
  "originalArgs": [
    "[query] Executing query: SELECT * FROM standard_posts ORDER BY label",
    {}
  ]
}
[15/07/2025 09:31:26] [FRONTEND] [LOG] ✅ [loadState] Shifts organisés dans this.data.schedule
[15/07/2025 09:31:26] [FRONTEND] [LOG] 📅 [loadState] 0 shifts chargés depuis la base de données
[15/07/2025 09:31:26] [FRONTEND] [LOG] ✅ [VALIDATION] Fonctions de validation disponibles:
[15/07/2025 09:31:26] [FRONTEND] [LOG] 🔍 [VALIDATION] Script de validation de la correction modale chargé
[15/07/2025 09:31:26] [FRONTEND] [LOG] - validateMainFix() : Valider la correction principale
[15/07/2025 09:31:26] [FRONTEND] [LOG] - validateDOMElements() : Valider les éléments DOM
[15/07/2025 09:31:26] [FRONTEND] [LOG] - testEndToEnd() : Test end-to-end complet
[15/07/2025 09:31:26] [FRONTEND] [LOG] - validateListeners() : Valider les listeners
[15/07/2025 09:31:26] [FRONTEND] [LOG] - runCompleteValidation() : Validation complète
[15/07/2025 09:31:26] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 53ms: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:26.321Z",
  "originalArgs": [
    "✅ Requête exécutée en 53ms:",
    "SELECT * FROM standard_posts ORDER BY label",
    {}
  ]
}
[15/07/2025 09:31:26] [FRONTEND] [LOG] 🧪 [TEST] Script de test pour la correction du fork modal chargé
[15/07/2025 09:31:26] [BACKEND] [INFO] ✅ Requête exécutée en 53ms: SELECT * FROM standard_posts ORDER BY label {}
[15/07/2025 09:31:26] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[15/07/2025 09:31:26] [FRONTEND] [LOG] - testDropDateDetection() : Tester la détection de date
[15/07/2025 09:31:26] [FRONTEND] [LOG] - testForkModalWithDate() : Tester le modal avec date
[15/07/2025 09:31:26] [FRONTEND] [LOG] - testCompleteForkFlow() : Tester le flux complet
[15/07/2025 09:31:26] [FRONTEND] [LOG] - diagnoseForkModalIssues() : Diagnostic complet
[15/07/2025 09:31:26] [BACKEND] [INFO] ✅ Requête exécutée en 47ms: SELECT * FROM app_settings ORDER BY setting_key {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 47ms: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 47ms:",
    "SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:26.454Z"
}
[15/07/2025 09:31:26] [FRONTEND] [LOG] ⚙️ [loadState] Paramètres chargés depuis API: {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT 
        ra.id,
        ra.employee_id,
        ra.post_id,
        ra.days_of_week,
        ra.start_date,
        ra.end_date,
        ra.is_active,
        ra.excluded_dates,
        e.name as employee_name,
        sp.label as post_label,
        sp.hours as post_hours,
        sp.type as post_type,
        array_length(ra.days_of_week, 1) as days_count
      FROM regular_assignments ra
      LEFT JOIN employees e ON ra.employee_id = e.id
      LEFT JOIN standard_posts sp ON ra.post_id = sp.id
      WHERE ra.is_active = true
      ORDER BY e.name, sp.label, ra.start_date
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT \n        ra.id,\n        ra.employee_id,\n        ra.post_id,\n        ra.days_of_week,\n        ra.start_date,\n        ra.end_date,\n        ra.is_active,\n        ra.excluded_dates,\n        e.name as employee_name,\n        sp.label as post_label,\n        sp.hours as post_hours,\n        sp.type as post_type,\n        array_length(ra.days_of_week, 1) as days_count\n      FROM regular_assignments ra\n      LEFT JOIN employees e ON ra.employee_id = e.id\n      LEFT JOIN standard_posts sp ON ra.post_id = sp.id\n      WHERE ra.is_active = true\n      ORDER BY e.name, sp.label, ra.start_date\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:26.488Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 115ms: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 115ms:",
    "SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:26.501Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 45ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 45ms:",
    "\n      SELECT \n        ra.id,\n        ra.employee_",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:26.532Z"
}
[15/07/2025 09:31:26] [BACKEND] [INFO] ✅ Requête exécutée en 45ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
[15/07/2025 09:31:26] [FRONTEND] [LOG] 📋 [loadState] 0/0 assignations régulières valides chargées et normalisées
[15/07/2025 09:31:26] [FRONTEND] [LOG] ✅ [fixInvalidAssignmentDates] Aucune date invalide trouvée
[15/07/2025 09:31:26] [FRONTEND] [LOG] 🔧 [fixInvalidAssignmentDates] Vérification des dates invalides...
[15/07/2025 09:31:26] [FRONTEND] [LOG] 📋 [loadState] Assignations régulières chargées (application différée)
[15/07/2025 09:31:26] [FRONTEND] [LOG] 🧹 [cleanupDuplicateRegularShifts] Nettoyage des doublons de shifts réguliers...
[15/07/2025 09:31:26] [FRONTEND] [LOG] ✅ [cleanupDuplicateRegularShifts] Aucun doublon détecté
[15/07/2025 09:31:26] [FRONTEND] [LOG] 🧹 [cleanupInvalidAssignmentIds] Nettoyage des assignment_id invalides...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'
         {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'\n        ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:26.747Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📋 [GET /api/employee-order] Récupération ordre des employés
  Data: {
  "originalArgs": [
    "📋 [GET /api/employee-order] Récupération ordre des employés"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:26.746Z"
}
[15/07/2025 09:31:26] [FRONTEND] [LOG] ✅ [loadState] État chargé depuis l'API avec succès
[15/07/2025 09:31:26] [FRONTEND] [LOG] ✅ [cleanupInvalidAssignmentIds] Aucun assignment_id invalide détecté
[15/07/2025 09:31:26] [BACKEND] [INFO] 📋 [GET /api/employee-order] Récupération ordre des employés
[15/07/2025 09:31:26] [FRONTEND] [LOG] 🔍 [init] Recherche du bouton des paramètres dans le DOM
[15/07/2025 09:31:26] [FRONTEND] [LOG] ✅ [init] Icône paramètres trouvée: [Objet non sérialisable]
[15/07/2025 09:31:26] [FRONTEND] [LOG] 🔍 [init] Bouton des paramètres trouvé ? true
[15/07/2025 09:31:26] [FRONTEND] [LOG] 🔍 [init] Attachement du listener click sur le bouton des paramètres
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📋 [GET /api/employee-order] Aucun ordre personnalisé trouvé
  Data: {
  "originalArgs": [
    "📋 [GET /api/employee-order] Aucun ordre personnalisé trouvé"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:26.798Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 51ms: 
            SELECT setting_value FROM app_setting {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 51ms:",
    "\n            SELECT setting_value FROM app_setting",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:26.798Z"
}
[15/07/2025 09:31:26] [FRONTEND] [LOG] 🔍 [init] Recherche du bouton des paramètres header dans le DOM
[15/07/2025 09:31:26] [FRONTEND] [LOG] 🔍 [init] Bouton des paramètres header trouvé ? true
[15/07/2025 09:31:26] [FRONTEND] [LOG] ✅ [setupNavigationListeners] Écouteurs de navigation attachés
[15/07/2025 09:31:26] [FRONTEND] [LOG] 🔍 [init] Attachement du listener click sur le bouton des paramètres header
[15/07/2025 09:31:26] [FRONTEND] [LOG] 🎯 [setupViewNavigationListeners] Configuration des boutons de navigation...
[15/07/2025 09:31:26] [FRONTEND] [LOG] ✅ Boutons de navigation configurés
[15/07/2025 09:31:26] [FRONTEND] [LOG] 🔧 [ModalManager] Configuration des gestionnaires de modal
[15/07/2025 09:31:26] [FRONTEND] [LOG] ✅ [ModalManager] Gestionnaires de modal configurés
[15/07/2025 09:31:26] [FRONTEND] [LOG] 🔧 [setupGeneralSettings] Configuration des paramètres généraux
[15/07/2025 09:31:26] [FRONTEND] [LOG] 🔧 [setupGeneralSettings] Création du conteneur de fuseau horaire
[15/07/2025 09:31:26] [FRONTEND] [LOG] ✅ [setupGlobalEscapeHandler] Gestionnaire ESC global configuré
[15/07/2025 09:31:26] [FRONTEND] [LOG] 📋 [loadEmployeeOrder] Chargement ordre des employés
[15/07/2025 09:31:27] [FRONTEND] [WARN] ⚠️ [loadEmployeeOrder] Réponse API sans tableau employeeOrder
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🔧 [init] Forçage du rendu initial...
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[15/07/2025 09:31:27] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[15/07/2025 09:31:27] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 09:31:27] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 60 zones trouvées
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 09:31:27] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 60 zones de drop disponibles
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Matin: [1, 2, 3, 4, 5]
[15/07/2025 09:31:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Nuit: [1, 2, 3, 4, 5]
[15/07/2025 09:31:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Soir: [1, 2, 3, 4, 5]
[15/07/2025 09:31:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE1: [0, 6]
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 09:31:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE2: [0, 6]
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 09:31:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 09:31:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 09:31:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 09:31:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 09:31:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 09:31:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 09:31:27] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:31:27] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:31:27] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:31:27] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:31:27] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:31:27] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 09:31:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (60 zones)
[15/07/2025 09:31:27] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🔧 [init] Configuration du drag & drop des postes...
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 09:31:27] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 60 zones trouvées
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 60 zones de drop disponibles
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 09:31:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 09:31:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 09:31:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 09:31:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 09:31:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 09:31:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 09:31:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 09:31:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 09:31:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 09:31:27] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:31:27] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:31:27] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:31:27] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:31:27] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:31:27] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:31:27] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:31:27] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 09:31:28] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (60 zones)
[15/07/2025 09:31:28] [FRONTEND] [LOG] ℹ️ [setupEmployeeDragDrop] Fonction désactivée - Utiliser setupDragAndDrop
[15/07/2025 09:31:28] [FRONTEND] [LOG] 🔧 [addEmergencyFixButton] Ajout du bouton de correction...
[15/07/2025 09:31:28] [FRONTEND] [LOG] ✅ [addEmergencyFixButton] Bouton de correction ajouté
[15/07/2025 09:31:28] [FRONTEND] [LOG] ✅ [init] Initialisation terminée avec succès
[15/07/2025 09:31:28] [FRONTEND] [LOG] 🎉 [Agenda] Application fullscreen initialisée avec succès
[15/07/2025 09:31:28] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] 0 grips ajoutés
[15/07/2025 09:31:28] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 09:31:28] [FRONTEND] [LOG] 🔍 [diagnoseRegularAssignmentGrips] Diagnostic des grips d'attributions régulières
[15/07/2025 09:31:28] [FRONTEND] [LOG] 📊 [diagnoseRegularAssignmentGrips] Statistiques: {"totalShifts":0,"regularShifts":0,"regularAssignments":0}
[15/07/2025 09:31:28] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 09:31:28] [FRONTEND] [LOG] ⚠️ [diagnoseRegularAssignmentGrips] Aucun shift régulier trouvé - Ré-application des attributions...
[15/07/2025 09:31:28] [FRONTEND] [LOG] 🎯 [diagnoseRegularAssignmentGrips] Grips trouvés dans le DOM: 0
[15/07/2025 09:31:28] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 09:31:28] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[15/07/2025 09:31:28] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 09:31:28] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic des problèmes de modale...
[15/07/2025 09:31:28] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Éléments DOM: {"modal":true,"closeBtn":true,"cancelBtn":true,"confirmBtn":true,"radioButtons":4}
[15/07/2025 09:31:28] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] TeamCalendarApp: {"exists":true,"hasModalManager":true,"hasOpenFunction":true,"hasHandleFunction":true}
[15/07/2025 09:31:28] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] État de la modale: {"hidden":true,"classes":["hidden","fixed","inset-0","bg-black/40","backdrop-blur-sm","z-50","flex","items-center","justify-center","p-4"],"style":""}
[15/07/2025 09:31:28] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Données contextuelles: {"employees":5,"posts":5}
[15/07/2025 09:31:28] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic des problèmes de modale...
[15/07/2025 09:31:28] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[15/07/2025 09:31:28] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Éléments DOM: {"modal":false,"closeBtn":false,"cancelBtn":false,"confirmBtn":false,"radioButtons":0}
[15/07/2025 09:31:28] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Données contextuelles: {"employees":0,"posts":0}
[15/07/2025 09:31:28] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] TeamCalendarApp: {"exists":true,"hasModalManager":true,"hasOpenFunction":true,"hasHandleFunction":true}
[15/07/2025 09:31:28] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation automatique...
[15/07/2025 09:31:28] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation complète...
[15/07/2025 09:31:28] [FRONTEND] [LOG] ✅ [VALIDATION] ModalManager.app initialisé
[15/07/2025 09:31:28] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation de la correction principale...
[15/07/2025 09:31:28] [FRONTEND] [LOG] ✅ [VALIDATION] ModalManager.setupAssignmentContextModal disponible
[15/07/2025 09:31:28] [FRONTEND] [LOG] ✅ [VALIDATION] handleAssignmentContextConfirm disponible
[15/07/2025 09:31:28] [FRONTEND] [LOG] 📋 [VALIDATION] Éléments DOM: {"modal":true,"closeBtn":true,"cancelBtn":true,"confirmBtn":true,"radioButtons":true}
[15/07/2025 09:31:28] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des listeners...
[15/07/2025 09:31:28] [FRONTEND] [LOG] ✅ [VALIDATION] Tous les éléments DOM sont présents
[15/07/2025 09:31:28] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des éléments DOM...
[15/07/2025 09:31:28] [FRONTEND] [LOG] 🔧 [setupAssignmentContextModal] Configuration du modal contextuel...
[15/07/2025 09:31:28] [FRONTEND] [LOG] ✅ [setupAssignmentContextModal] Modal contextuel configuré avec listeners nettoyés
[15/07/2025 09:31:28] [FRONTEND] [LOG] 🎉 [VALIDATION] Toutes les validations sont passées avec succès !
[15/07/2025 09:31:28] [FRONTEND] [LOG] 📊 [VALIDATION] Résultats de validation: {"mainFix":true,"domElements":true,"listeners":true}
[15/07/2025 09:31:28] [FRONTEND] [LOG] ✅ [VALIDATION] setupAssignmentContextModal exécuté sans erreur
[15/07/2025 09:31:28] [FRONTEND] [LOG] ✅ [VALIDATION] La correction de la modale devrait maintenant fonctionner
[15/07/2025 09:31:28] [FRONTEND] [LOG] 🧪 [VALIDATION] Pour tester pratiquement:
[15/07/2025 09:31:28] [FRONTEND] [LOG] 4. Ou utilisez testButtonClicks() pour un test automatique
[15/07/2025 09:31:28] [FRONTEND] [LOG] 3. Cliquez sur "Confirmer" et vérifiez les logs
[15/07/2025 09:31:28] [FRONTEND] [LOG] 2. Vérifiez que la modale s'ouvre
[15/07/2025 09:31:28] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[15/07/2025 09:31:28] [FRONTEND] [LOG] 1. Effectuez un drag & drop d'un poste vers un employé
[15/07/2025 09:31:28] [FRONTEND] [LOG] 📊 [DIAGNOSTIC] Résultats: {"teamCalendarApp":true,"detectDropDateFunction":true,"showConfirmationMenuFunction":true,"handleRegularAssignmentDropFunction":true,"regularAssignments":0,"employees":5}
[15/07/2025 09:31:28] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic complet du fork modal...
[15/07/2025 09:31:28] [FRONTEND] [LOG] ✅ [DIAGNOSTIC] Toutes les fonctions nécessaires sont disponibles
[15/07/2025 09:31:28] [FRONTEND] [LOG] ✅ [VALIDATION] ModalManager.setupAssignmentContextModal disponible
[15/07/2025 09:31:28] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation complète...
[15/07/2025 09:31:28] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation automatique...
[15/07/2025 09:31:28] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation de la correction principale...
[15/07/2025 09:31:28] [FRONTEND] [ERROR] ❌ [VALIDATION] ModalManager.app non initialisé
[15/07/2025 09:31:28] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des éléments DOM...
[15/07/2025 09:31:29] [FRONTEND] [LOG] 📋 [VALIDATION] Éléments DOM: {"modal":false,"closeBtn":false,"cancelBtn":false,"confirmBtn":false,"radioButtons":false}
[15/07/2025 09:31:29] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des listeners...
[15/07/2025 09:31:29] [FRONTEND] [ERROR] ❌ [VALIDATION] Éléments DOM manquants
[15/07/2025 09:31:29] [FRONTEND] [ERROR] ❌ [setupAssignmentContextModal] Éléments manquants: {"closeBtn":false,"cancelBtn":false,"confirmBtn":false}
[15/07/2025 09:31:29] [FRONTEND] [LOG] 🔧 [setupAssignmentContextModal] Configuration du modal contextuel...
[15/07/2025 09:31:29] [FRONTEND] [LOG] ✅ [VALIDATION] setupAssignmentContextModal exécuté sans erreur
[15/07/2025 09:31:29] [FRONTEND] [ERROR] ❌ [VALIDATION] Certaines validations ont échoué
[15/07/2025 09:31:29] [FRONTEND] [LOG] 📊 [VALIDATION] Résultats de validation: {"mainFix":false,"domElements":false,"listeners":true}
[15/07/2025 09:31:29] [FRONTEND] [LOG] - Vérifier que TeamCalendarApp et ModalManager sont correctement initialisés
[15/07/2025 09:31:29] [FRONTEND] [LOG] 🔧 [VALIDATION] Actions recommandées:
[15/07/2025 09:31:29] [FRONTEND] [LOG] - Vérifier que la modale assignment-context-modal existe dans le DOM
[15/07/2025 09:31:29] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic complet du fork modal...
[15/07/2025 09:31:29] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[15/07/2025 09:31:29] [FRONTEND] [LOG] ✅ [DIAGNOSTIC] Toutes les fonctions nécessaires sont disponibles
[15/07/2025 09:31:29] [FRONTEND] [LOG] 📊 [DIAGNOSTIC] Résultats: {"teamCalendarApp":true,"detectDropDateFunction":true,"showConfirmationMenuFunction":true,"handleRegularAssignmentDropFunction":true,"regularAssignments":0,"employees":0}
[15/07/2025 09:31:29] [FRONTEND] [LOG] 🎯 [POST DRAG] Début drag poste: ef3246ff-8398-4f11-a498-648d207563a0 (Poste Matin)
[15/07/2025 09:31:29] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 09:31:29] [FRONTEND] [LOG] 🎨 [enableAgendaPreviewMode] Activation preview agenda pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 09:31:29] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 09:31:29] [FRONTEND] [LOG] 🎯 [POST DRAG] DataTransfer configuré: {"postId":"ef3246ff-8398-4f11-a498-648d207563a0","label":"Poste Matin","types":["text/plain","application/x-post-id","postid","postlabel"]}
[15/07/2025 09:31:29] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] Ajout des fantômes pour Poste Matin
[15/07/2025 09:31:29] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] 25 fantômes créés
[15/07/2025 09:31:29] [FRONTEND] [LOG] ✅ [enableAgendaPreviewMode] Mode preview activé pour Poste Matin
[15/07/2025 09:31:29] [FRONTEND] [LOG] 🖱️ [enableCellHoverTracking] Suivi activé sur 25 cellules
[15/07/2025 09:31:29] [FRONTEND] [LOG] 🎯 [POST DRAG] 60 zones préparées pour le drop
[15/07/2025 09:31:29] [FRONTEND] [LOG] 🎯 [POST DRAG] Début drag poste: ef3246ff-8398-4f11-a498-648d207563a0 (Poste Matin)
[15/07/2025 09:31:29] [FRONTEND] [LOG] 🎯 [POST DRAG] DataTransfer configuré: {"postId":"ef3246ff-8398-4f11-a498-648d207563a0","label":"Poste Matin","types":["text/plain","application/x-post-id","postid","postlabel"]}
[15/07/2025 09:31:29] [FRONTEND] [LOG] 🎨 [enableAgendaPreviewMode] Activation preview agenda pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 09:31:29] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 09:31:29] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 09:31:29] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] Ajout des fantômes pour Poste Matin
[15/07/2025 09:31:29] [FRONTEND] [LOG] 🖱️ [enableCellHoverTracking] Suivi activé sur 50 cellules
[15/07/2025 09:31:29] [FRONTEND] [LOG] 🎯 [POST DRAG] 60 zones préparées pour le drop
[15/07/2025 09:31:29] [FRONTEND] [LOG] ✅ [enableAgendaPreviewMode] Mode preview activé pour Poste Matin
[15/07/2025 09:31:29] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] 25 fantômes créés
[15/07/2025 09:31:29] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 59f5df3a-33ef-425c-bfa2-adca818cf94f, types: text/plain, application/x-post-id, postid, postlabel
[15/07/2025 09:31:30] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 59f5df3a-33ef-425c-bfa2-adca818cf94f, types: text/plain, application/x-post-id, postid, postlabel
[15/07/2025 09:31:30] [FRONTEND] [LOG] 🎯 [DROP] ✅ Drop reçu sur employé: Sophie Leblanc
[15/07/2025 09:31:30] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 09:31:30] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 09:31:30] [FRONTEND] [LOG] ✅ [DROP] PostId trouvé via application/x-post-id: "ef3246ff-8398-4f11-a498-648d207563a0"
[15/07/2025 09:31:30] [FRONTEND] [LOG] 📦 [DROP] Nouveau poste: ef3246ff-8398-4f11-a498-648d207563a0 → 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:31:30] [FRONTEND] [LOG] 🎯 [openAssignmentContextModal] Poste brut: ef3246ff-8398-4f11-a498-648d207563a0, Employé: 59f5df3a-33ef-425c-bfa2-adca818cf94f, Contexte: null
[15/07/2025 09:31:30] [FRONTEND] [LOG] ℹ️ [openAssignmentContextModal] Option "Attribution unique (cellule)" masquée - contexte inapproprié
[15/07/2025 09:31:30] [FRONTEND] [LOG] ✅ [DROP] Modal d'assignation ouvert
[15/07/2025 09:31:30] [FRONTEND] [LOG] 📅 [openAssignmentContextModal] Date initialisée: 2025-07-15
[15/07/2025 09:31:30] [FRONTEND] [LOG] 🎯 [POST DRAG] Fin drag poste: ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 09:31:30] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 09:31:30] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 09:31:30] [FRONTEND] [LOG] 🎯 [POST DRAG] Nettoyage des 60 zones
[15/07/2025 09:31:30] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 09:31:30] [FRONTEND] [LOG] 🎯 [POST DRAG] Fin drag poste: ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 09:31:30] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 09:31:30] [FRONTEND] [LOG] 🎯 [POST DRAG] Nettoyage des 60 zones
[15/07/2025 09:31:31] [FRONTEND] [LOG] 🔄 [setupAssignmentContextModal] Type d'attribution changé: regular-indefinite
[15/07/2025 09:31:34] [FRONTEND] [LOG] ✅ [setupAssignmentContextModal] Bouton confirmer cliqué
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🔍 [setupAssignmentContextModal] État currentContextAssignment: {"postId":"ef3246ff-8398-4f11-a498-648d207563a0","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","context":null}
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🎯 [handleAssignmentContextConfirm] Fonction appelée
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🔍 [handleAssignmentContextConfirm] currentContextAssignment: {"postId":"ef3246ff-8398-4f11-a498-648d207563a0","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","context":null}
[15/07/2025 09:31:34] [FRONTEND] [LOG] ✅ [handleAssignmentContextConfirm] Type: regular-indefinite, Poste: ef3246ff-8398-4f11-a498-648d207563a0, Employé: 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:31:34] [FRONTEND] [LOG] 📦 [handleAssignmentContextConfirm] Données extraites: {"postId":"ef3246ff-8398-4f11-a498-648d207563a0","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f"}
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🔍 [handleAssignmentContextConfirm] Input trouvé: {"input":true,"value":"regular-indefinite","checked":true}
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🎯 [handleAssignmentContextConfirm] Exécution attribution régulière indéfinie
[15/07/2025 09:31:34] [FRONTEND] [LOG] 📅 [handleAssignmentContextConfirm] Date de début: 2025-07-15
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🔄 [createRegularAssignment] Création d'une assignation régulière
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🗙 [handleAssignmentContextConfirm] Fermeture du modal
[15/07/2025 09:31:34] [FRONTEND] [LOG] ✅ [handleAssignmentContextConfirm] Traitement terminé avec succès
[15/07/2025 09:31:34] [FRONTEND] [LOG] ✅ [setupAssignmentContextModal] handleAssignmentContextConfirm appelé avec succès
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:31:34] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] Pas de dates exclues pour l'attribution 51c06b21-438c-4996-83fa-429377a77e1e {"assignmentId":"51c06b21-438c-4996-83fa-429377a77e1e","hasExcludedDates":false,"isArray":false}
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"51c06b21-438c-4996-83fa-429377a77e1e","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-13T04:00:00.000Z","dayDateKey":"2025-07-13","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:31:34] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"51c06b21-438c-4996-83fa-429377a77e1e","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-14T04:00:00.000Z","dayDateKey":"2025-07-14","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:31:34] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-14 avant startDate 2025-07-15 (original: 2025-07-15)
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"51c06b21-438c-4996-83fa-429377a77e1e","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-15T04:00:00.000Z","dayDateKey":"2025-07-15","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📤 [RegularAssignments] Données reçues: {
  "employeeId": "59f5df3a-33ef-425c-bfa2-adca818cf94f",
  "postId": "ef3246ff-8398-4f11-a498-648d207563a0",
  "isLimited": false,
  "startDate": "2025-07-15",
  "endDate": null,
  "selectedDays": [
    1,
    2,
    3,
    4,
    5
  ]
}
  Data: {
  "originalArgs": [
    "📤 [RegularAssignments] Données reçues:",
    "{\n  \"employeeId\": \"59f5df3a-33ef-425c-bfa2-adca818cf94f\",\n  \"postId\": \"ef3246ff-8398-4f11-a498-648d207563a0\",\n  \"isLimited\": false,\n  \"startDate\": \"2025-07-15\",\n  \"endDate\": null,\n  \"selectedDays\": [\n    1,\n    2,\n    3,\n    4,\n    5\n  ]\n}"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:34.288Z"
}
[15/07/2025 09:31:34] [BACKEND] [INFO] ➕ [RegularAssignments] Création d'une attribution individuelle
[Invalid Date] [BACKEND] [INFO] [LOGGER] ➕ [RegularAssignments] Création d'une attribution individuelle
  Data: {
  "originalArgs": [
    "➕ [RegularAssignments] Création d'une attribution individuelle"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:34.287Z"
}
[15/07/2025 09:31:34] [FRONTEND] [LOG] 📅 [addShift] Conversion: dayIndex 2 → dateKey 2025-07-15
[15/07/2025 09:31:34] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-15
[15/07/2025 09:31:34] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] Pas de dates exclues pour l'attribution 51c06b21-438c-4996-83fa-429377a77e1e {"assignmentId":"51c06b21-438c-4996-83fa-429377a77e1e","hasExcludedDates":false,"isArray":false}
[15/07/2025 09:31:34] [FRONTEND] [LOG] 📝 [addShift] Ajout shift pour employé 59f5df3a-33ef-425c-bfa2-adca818cf94f, jour 2: {"postId":"ef3246ff-8398-4f11-a498-648d207563a0","text":"08:00-16:00","type":"standard","isRegular":true,"assignmentId":"51c06b21-438c-4996-83fa-429377a77e1e"}
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🎯 [addShiftByDateKey] AJOUT AVEC VÉRIFICATION STRICTE - dateKey 2025-07-15 pour employé 59f5df3a-33ef-425c-bfa2-adca818cf94f: {"postId":"ef3246ff-8398-4f11-a498-648d207563a0","text":"08:00-16:00","type":"standard","isRegular":true,"assignmentId":"51c06b21-438c-4996-83fa-429377a77e1e"}
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🆕 [addShiftByDateKey] Création schedule pour employé 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🆕 [addShiftByDateKey] Création jour 2025-07-15 pour employé 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:31:34] [FRONTEND] [LOG] 📋 [addShiftByDateKey] Shifts existants pour 2025-07-15: []
[15/07/2025 09:31:34] [FRONTEND] [LOG] ✅ [addShiftByDateKey] Shift ajouté avec succès. Nouveau total: [{"postId":"ef3246ff-8398-4f11-a498-648d207563a0","text":"08:00-16:00","type":"standard","isRegular":true,"assignmentId":"51c06b21-438c-4996-83fa-429377a77e1e"}]
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"51c06b21-438c-4996-83fa-429377a77e1e","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-16T04:00:00.000Z","dayDateKey":"2025-07-16","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[15/07/2025 09:31:34] [FRONTEND] [WARN] ⚠️ [renderUnifiedCalendar] Conteneur available-posts-container non trouvé, création...
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 09:31:34] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneur available-posts-container créé
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:34.445Z"
}
[15/07/2025 09:31:34] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ [validateAndConvertUuid] UUID valide détecté pour post_id: ef3246ff-8398-4f11-a498-648d207563a0
  Data: {
  "originalArgs": [
    "✅ [validateAndConvertUuid] UUID valide détecté pour post_id: ef3246ff-8398-4f11-a498-648d207563a0"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:34.292Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📝 [RegularAssignmentV2.create] Données reçues: {"employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","isLimited":false,"startDate":"2025-07-15","endDate":null,"selectedDays":[1,2,3,4,5]}
  Data: {
  "originalArgs": [
    "📝 [RegularAssignmentV2.create] Données reçues:",
    {
      "employeeId": "59f5df3a-33ef-425c-bfa2-adca818cf94f",
      "postId": "ef3246ff-8398-4f11-a498-648d207563a0",
      "isLimited": false,
      "startDate": "2025-07-15",
      "endDate": null,
      "selectedDays": [
        1,
        2,
        3,
        4,
        5
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:34.290Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:34.443Z"
}
[15/07/2025 09:31:34] [FRONTEND] [LOG] 📝 [addShift] Ajout shift pour employé 59f5df3a-33ef-425c-bfa2-adca818cf94f, jour 3: {"postId":"ef3246ff-8398-4f11-a498-648d207563a0","text":"08:00-16:00","type":"standard","isRegular":true,"assignmentId":"51c06b21-438c-4996-83fa-429377a77e1e"}
[15/07/2025 09:31:34] [FRONTEND] [LOG] 📅 [addShift] Conversion: dayIndex 3 → dateKey 2025-07-16
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:34.453Z"
}
[15/07/2025 09:31:34] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] Pas de dates exclues pour l'attribution 51c06b21-438c-4996-83fa-429377a77e1e {"assignmentId":"51c06b21-438c-4996-83fa-429377a77e1e","hasExcludedDates":false,"isArray":false}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔍 [validateAndConvertUuid] Entrée: fieldName="post_id", value="ef3246ff-8398-4f11-a498-648d207563a0", type=string
  Data: {
  "originalArgs": [
    "🔍 [validateAndConvertUuid] Entrée: fieldName=\"post_id\", value=\"ef3246ff-8398-4f11-a498-648d207563a0\", type=string"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:34.291Z"
}
[15/07/2025 09:31:34] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-16
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:34.460Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔍 [RegularAssignmentV2.create] Post ID original: ef3246ff-8398-4f11-a498-648d207563a0 Post ID validé: ef3246ff-8398-4f11-a498-648d207563a0
  Data: {
  "originalArgs": [
    "🔍 [RegularAssignmentV2.create] Post ID original:",
    "ef3246ff-8398-4f11-a498-648d207563a0",
    "Post ID validé:",
    "ef3246ff-8398-4f11-a498-648d207563a0"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:34.295Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      INSERT INTO regular_assignments (id, employee_id, post_id, days_of_week, start_date, end_date, is_active)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
     {"params":["b77227a7-67b7-4feb-b082-890cfeecab1f","59f5df3a-33ef-425c-bfa2-adca818cf94f","ef3246ff-8398-4f11-a498-648d207563a0",[1,2,3,4,5],"2025-07-15",null,true]}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      INSERT INTO regular_assignments (id, employee_id, post_id, days_of_week, start_date, end_date, is_active)\n      VALUES ($1, $2, $3, $4, $5, $6, $7)\n      RETURNING *\n    ",
    {
      "params": [
        "b77227a7-67b7-4feb-b082-890cfeecab1f",
        "59f5df3a-33ef-425c-bfa2-adca818cf94f",
        "ef3246ff-8398-4f11-a498-648d207563a0",
        [
          1,
          2,
          3,
          4,
          5
        ],
        "2025-07-15",
        null,
        true
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:34.297Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔍 [RegularAssignmentV2.create] Création attribution unique pour jours: [1, 2, 3, 4, 5]
  Data: {
  "originalArgs": [
    "🔍 [RegularAssignmentV2.create] Création attribution unique pour jours: [1, 2, 3, 4, 5]"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:34.296Z"
}
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🎯 [addShiftByDateKey] AJOUT AVEC VÉRIFICATION STRICTE - dateKey 2025-07-16 pour employé 59f5df3a-33ef-425c-bfa2-adca818cf94f: {"postId":"ef3246ff-8398-4f11-a498-648d207563a0","text":"08:00-16:00","type":"standard","isRegular":true,"assignmentId":"51c06b21-438c-4996-83fa-429377a77e1e"}
[15/07/2025 09:31:34] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🆕 [addShiftByDateKey] Création jour 2025-07-16 pour employé 59f5df3a-33ef-425c-bfa2-adca818cf94f
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ [RegularAssignments] Attribution créée avec succès: b77227a7-67b7-4feb-b082-890cfeecab1f
  Data: {
  "originalArgs": [
    "✅ [RegularAssignments] Attribution créée avec succès:",
    "b77227a7-67b7-4feb-b082-890cfeecab1f"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:34.528Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ [RegularAssignmentV2.create] Attribution créée avec 5 jours
  Data: {
  "originalArgs": [
    "✅ [RegularAssignmentV2.create] Attribution créée avec 5 jours"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:34.527Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 229ms: 
      INSERT INTO regular_assignments (id, employ {"params":["b77227a7-67b7-4feb-b082-890cfeecab1f","59f5df3a-33ef-425c-bfa2-adca818cf94f","ef3246ff-8398-4f11-a498-648d207563a0",[1,2,3,4,5],"2025-07-15",null,true]}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 229ms:",
    "\n      INSERT INTO regular_assignments (id, employ",
    {
      "params": [
        "b77227a7-67b7-4feb-b082-890cfeecab1f",
        "59f5df3a-33ef-425c-bfa2-adca818cf94f",
        "ef3246ff-8398-4f11-a498-648d207563a0",
        [
          1,
          2,
          3,
          4,
          5
        ],
        "2025-07-15",
        null,
        true
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:34.526Z"
}
[15/07/2025 09:31:34] [FRONTEND] [LOG] ✅ [addShiftByDateKey] Shift ajouté avec succès. Nouveau total: [{"postId":"ef3246ff-8398-4f11-a498-648d207563a0","text":"08:00-16:00","type":"standard","isRegular":true,"assignmentId":"51c06b21-438c-4996-83fa-429377a77e1e"}]
[15/07/2025 09:31:34] [FRONTEND] [LOG] 📋 [addShiftByDateKey] Shifts existants pour 2025-07-16: []
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"51c06b21-438c-4996-83fa-429377a77e1e","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-17T04:00:00.000Z","dayDateKey":"2025-07-17","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:31:34] [BACKEND] [INFO] ✅ Requête exécutée en 229ms: 
      INSERT INTO regular_assignments (id, employ {"params":["b77227a7-67b7-4feb-b082-890cfeecab1f","59f5df3a-33ef-425c-bfa2-adca818cf94f","ef3246ff-8398-4f11-a498-648d207563a0",[1,2,3,4,5],"2025-07-15",null,true]}
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:31:34] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] Pas de dates exclues pour l'attribution 51c06b21-438c-4996-83fa-429377a77e1e {"assignmentId":"51c06b21-438c-4996-83fa-429377a77e1e","hasExcludedDates":false,"isArray":false}
[15/07/2025 09:31:34] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-17
[15/07/2025 09:31:34] [FRONTEND] [LOG] 📅 [addShift] Conversion: dayIndex 4 → dateKey 2025-07-17
[15/07/2025 09:31:34] [FRONTEND] [LOG] 📝 [addShift] Ajout shift pour employé 59f5df3a-33ef-425c-bfa2-adca818cf94f, jour 4: {"postId":"ef3246ff-8398-4f11-a498-648d207563a0","text":"08:00-16:00","type":"standard","isRegular":true,"assignmentId":"51c06b21-438c-4996-83fa-429377a77e1e"}
[15/07/2025 09:31:34] [FRONTEND] [LOG] ✅ [updateAssignmentIdInShifts] 4 shifts mis à jour
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🔄 [updateAssignmentIdInShifts] Mise à jour 51c06b21-438c-4996-83fa-429377a77e1e → b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🆕 [addShiftByDateKey] Création jour 2025-07-17 pour employé 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🎯 [addShiftByDateKey] AJOUT AVEC VÉRIFICATION STRICTE - dateKey 2025-07-17 pour employé 59f5df3a-33ef-425c-bfa2-adca818cf94f: {"postId":"ef3246ff-8398-4f11-a498-648d207563a0","text":"08:00-16:00","type":"standard","isRegular":true,"assignmentId":"51c06b21-438c-4996-83fa-429377a77e1e"}
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🔍 [renderAssignments] Début du rendu des assignations
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:34.464Z"
}
[15/07/2025 09:31:34] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:34.642Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:34.461Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:34.655Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:34.466Z"
}
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:31:34] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] Pas de dates exclues pour l'attribution 51c06b21-438c-4996-83fa-429377a77e1e {"assignmentId":"51c06b21-438c-4996-83fa-429377a77e1e","hasExcludedDates":false,"isArray":false}
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"51c06b21-438c-4996-83fa-429377a77e1e","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-18T04:00:00.000Z","dayDateKey":"2025-07-18","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:31:34] [FRONTEND] [LOG] ✅ [addShiftByDateKey] Shift ajouté avec succès. Nouveau total: [{"postId":"ef3246ff-8398-4f11-a498-648d207563a0","text":"08:00-16:00","type":"standard","isRegular":true,"assignmentId":"51c06b21-438c-4996-83fa-429377a77e1e"}]
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:34.661Z"
}
[15/07/2025 09:31:34] [FRONTEND] [LOG] 📋 [addShiftByDateKey] Shifts existants pour 2025-07-17: []
[15/07/2025 09:31:34] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-18
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🆕 [addShiftByDateKey] Création jour 2025-07-18 pour employé 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:31:34] [FRONTEND] [LOG] 📝 [addShift] Ajout shift pour employé 59f5df3a-33ef-425c-bfa2-adca818cf94f, jour 5: {"postId":"ef3246ff-8398-4f11-a498-648d207563a0","text":"08:00-16:00","type":"standard","isRegular":true,"assignmentId":"51c06b21-438c-4996-83fa-429377a77e1e"}
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🎯 [addShiftByDateKey] AJOUT AVEC VÉRIFICATION STRICTE - dateKey 2025-07-18 pour employé 59f5df3a-33ef-425c-bfa2-adca818cf94f: {"postId":"ef3246ff-8398-4f11-a498-648d207563a0","text":"08:00-16:00","type":"standard","isRegular":true,"assignmentId":"51c06b21-438c-4996-83fa-429377a77e1e"}
[15/07/2025 09:31:34] [FRONTEND] [LOG] 📅 [addShift] Conversion: dayIndex 5 → dateKey 2025-07-18
[15/07/2025 09:31:34] [FRONTEND] [LOG] 📋 [addShiftByDateKey] Shifts existants pour 2025-07-18: []
[15/07/2025 09:31:34] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[15/07/2025 09:31:34] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"51c06b21-438c-4996-83fa-429377a77e1e","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-19T04:00:00.000Z","dayDateKey":"2025-07-19","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:31:34] [FRONTEND] [LOG] ✅ [addShiftByDateKey] Shift ajouté avec succès. Nouveau total: [{"postId":"ef3246ff-8398-4f11-a498-648d207563a0","text":"08:00-16:00","type":"standard","isRegular":true,"assignmentId":"51c06b21-438c-4996-83fa-429377a77e1e"}]
[15/07/2025 09:31:34] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 09:31:34] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 51c06b21-438c-4996-83fa-429377a77e1e
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 51c06b21-438c-4996-83fa-429377a77e1e
[15/07/2025 09:31:34] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 51c06b21-438c-4996-83fa-429377a77e1e
[15/07/2025 09:31:34] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 09:31:34] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[15/07/2025 09:31:34] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière 51c06b21-438c-4996-83fa-429377a77e1e
[15/07/2025 09:31:34] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 09:31:35] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 09:31:35] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 68 zones trouvées
[15/07/2025 09:31:35] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 68 zones de drop disponibles
[15/07/2025 09:31:35] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 09:31:35] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 09:31:35] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 09:31:35] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 09:31:35] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 09:31:35] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 09:31:35] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 09:31:35] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 09:31:35] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 09:31:35] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 09:31:35] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 09:31:35] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 09:31:35] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 09:31:35] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 09:31:35] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 09:31:35] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 09:31:35] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 09:31:35] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 09:31:35] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 09:31:35] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 09:31:35] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 09:31:35] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 09:31:35] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:31:35] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:31:35] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:31:35] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:31:35] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:31:35] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 09:31:35] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (68 zones)
[15/07/2025 09:31:35] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[15/07/2025 09:31:35] [FRONTEND] [LOG] ✅ [SETUP] Drag & drop des employés configuré (mode simplifié)
[15/07/2025 09:31:35] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[15/07/2025 09:31:35] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 09:31:35] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 09:31:35] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 68 zones trouvées
[15/07/2025 09:31:35] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 09:31:35] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 09:31:35] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 68 zones de drop disponibles
[15/07/2025 09:31:35] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 09:31:35] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 09:31:35] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 09:31:35] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 09:31:35] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 09:31:35] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 09:31:35] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 09:31:35] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 09:31:35] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 09:31:35] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 09:31:35] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 09:31:35] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 09:31:35] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 09:31:35] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 09:31:35] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:31:35] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 09:31:35] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:31:35] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 09:31:35] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:31:35] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:31:35] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:31:35] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:31:35] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:31:35] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:31:35] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (68 zones)
[15/07/2025 09:31:35] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:31:35] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:31:35] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 09:31:35] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] 0 grips ajoutés
[15/07/2025 09:31:35] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 09:31:35] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 09:31:35] [FRONTEND] [LOG] ✅ Attribution régulière créée et appliquée - 4 jour(s)
[15/07/2025 09:31:35] [FRONTEND] [LOG] ✅ [renderAssignments] Rendu des assignations terminé
[15/07/2025 09:31:37] [FRONTEND] [LOG] ➡️ Clic sur suivant
[15/07/2025 09:31:37] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-21 validé, shifts existants: 0
[15/07/2025 09:31:37] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-21T04:00:00.000Z","dayDateKey":"2025-07-21","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:31:37] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:31:37] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] Pas de dates exclues pour l'attribution b77227a7-67b7-4feb-b082-890cfeecab1f {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","hasExcludedDates":false,"isArray":false}
[15/07/2025 09:31:37] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-21
[15/07/2025 09:31:37] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-21 pour Sophie Leblanc
[15/07/2025 09:31:37] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-22 (2)
[15/07/2025 09:31:37] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-22T04:00:00.000Z","dayDateKey":"2025-07-22","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:31:37] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-23 validé, shifts existants: 0
[15/07/2025 09:31:37] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] Pas de dates exclues pour l'attribution b77227a7-67b7-4feb-b082-890cfeecab1f {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","hasExcludedDates":false,"isArray":false}
[15/07/2025 09:31:37] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-23 pour Sophie Leblanc
[15/07/2025 09:31:37] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-24 (4)
[15/07/2025 09:31:37] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-23
[15/07/2025 09:31:37] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:31:37] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:31:37] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Rendu demandé car 5 shifts créés
[15/07/2025 09:31:37] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 5 attributions appliquées pour la semaine 2025-W29
[15/07/2025 09:31:37] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] Pas de dates exclues pour l'attribution b77227a7-67b7-4feb-b082-890cfeecab1f {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","hasExcludedDates":false,"isArray":false}
[15/07/2025 09:31:37] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-24
[15/07/2025 09:31:37] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-26 ignoré pour attribution b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:31:37] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:31:37] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:31:37] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-24T04:00:00.000Z","dayDateKey":"2025-07-24","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:31:37] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-22
[15/07/2025 09:31:37] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-26T04:00:00.000Z","dayDateKey":"2025-07-26","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:31:37] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] Pas de dates exclues pour l'attribution b77227a7-67b7-4feb-b082-890cfeecab1f {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","hasExcludedDates":false,"isArray":false}
[15/07/2025 09:31:37] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-22 pour Sophie Leblanc
[15/07/2025 09:31:37] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-07-26 (6)
[15/07/2025 09:31:37] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-25 pour Sophie Leblanc
[15/07/2025 09:31:37] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-22 validé, shifts existants: 0
[15/07/2025 09:31:37] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-25 validé, shifts existants: 0
[15/07/2025 09:31:38] [FRONTEND] [LOG] 📋 [navigateWeek] Application des attributions pour la nouvelle semaine 2025-W29
[15/07/2025 09:31:38] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-23 (3)
[15/07/2025 09:31:38] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-23T04:00:00.000Z","dayDateKey":"2025-07-23","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:31:38] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-24 pour Sophie Leblanc
[15/07/2025 09:31:38] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] Pas de dates exclues pour l'attribution b77227a7-67b7-4feb-b082-890cfeecab1f {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","hasExcludedDates":false,"isArray":false}
[15/07/2025 09:31:38] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-25
[15/07/2025 09:31:38] [FRONTEND] [LOG] 🧭 [navigateWeek] Direction: 1, Offset actuel: 0
[15/07/2025 09:31:38] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-24 validé, shifts existants: 0
[15/07/2025 09:31:38] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:31:38] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-07-25 (5)
[15/07/2025 09:31:38] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 09:31:38] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 09:31:38] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-25T04:00:00.000Z","dayDateKey":"2025-07-25","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:31:38] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[15/07/2025 09:31:38] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 09:31:38] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:31:38] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 09:31:38] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 09:31:38] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:31:38] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 09:31:38] [FRONTEND] [WARN] ⚠️ [SETUP] Erreur lors de la destruction: Error: can't access property "Sortable1752586285578", el is null
destroy@http://localhost:5173/node_modules/.vite/deps/sortablejs.js?v=189de042:1812:5
setupDragAndDrop@http://localhost:5173/src/teamCalendarApp.ts:5808:33
attachAllEventListeners@http://localhost:5173/src/teamCalendarApp.ts:5358:10
actualRender@http://localhost:5173/src/teamCalendarApp.ts:2466:10
render/this._debouncedRender<@http://localhost:5173/src/teamCalendarApp.ts:2451:12
setTimeout handler*render@http://localhost:5173/src/teamCalendarApp.ts:2450:29
navigateWeek@http://localhost:5173/src/teamCalendarApp.ts:661:10
setupNavigationListeners/<@http://localhost:5173/src/teamCalendarApp.ts:2436:14
EventListener.handleEvent*setupNavigationListeners@http://localhost:5173/src/teamCalendarApp.ts:2434:15
init@http://localhost:5173/src/teamCalendarApp.ts:1047:10
async*initApp@http://localhost:5173/src/Agenda.tsx:40:31
setTimeout handler*AgendaFullscreen/<@http://localhost:5173/src/Agenda.tsx:50:29
react-stack-bottom-frame@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:17478:20
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
commitHookEffectListMount@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8460:122
commitHookPassiveMountEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8518:60
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10016:42
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
doubleInvokeEffectsOnFiber@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11461:207
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
recursivelyTraverseAndDoubleInvokeEffectsInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11432:78
commitDoubleInvokeEffectsInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11469:55
flushPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11309:41
node_modules/react-dom/cjs/react-dom-client.development.js/commitRoot/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11060:32
performWorkUntilDeadline@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:36:58
EventHandlerNonNull*node_modules/scheduler/cjs/scheduler.development.js/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:156:9
node_modules/scheduler/cjs/scheduler.development.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:266:7
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/scheduler/index.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:277:24
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/react-dom/cjs/react-dom-client.development.js/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:15216:23
node_modules/react-dom/cjs/react-dom-client.development.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18060:7
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/react-dom/client.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18071:24
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18075:16

[15/07/2025 09:31:38] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:31:38] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 09:31:38] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 09:31:38] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] 0 grips ajoutés
[15/07/2025 09:31:38] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 09:31:38] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[15/07/2025 09:31:38] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:31:38] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 09:31:38] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] Nettoyage des shifts réguliers existants...
[15/07/2025 09:31:38] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] 0 shifts réguliers supprimés
[15/07/2025 09:31:38] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-20 (0)
[15/07/2025 09:31:38] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-20T04:00:00.000Z","dayDateKey":"2025-07-20","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:31:38] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution b77227a7-67b7-4feb-b082-890cfeecab1f: [{"dateKey":"2025-07-20","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-21","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-22","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-23","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-24","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-07-25","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-07-26","isWeekend":true,"dayOfWeek":6}]
[15/07/2025 09:31:38] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Application pour semaine 2025-W29
[15/07/2025 09:31:38] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"b77227a7-67b7-4feb-b082-890cfeecab1f","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"startDate":"2025-07-15","endDate":null}
[15/07/2025 09:31:38] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution b77227a7-67b7-4feb-b082-890cfeecab1f a une intersection avec cette semaine (2025-07-15 → ∞) vs (2025-07-20 → 2025-07-26)
[15/07/2025 09:31:38] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:31:38] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-21 (1)
[15/07/2025 09:31:38] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 1 attributions actives sur 1 total
[15/07/2025 09:31:38] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-20 ignoré pour attribution b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:31:38] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:31:38] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 09:31:38] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 09:31:38] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:31:38] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:31:38] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[15/07/2025 09:31:38] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[15/07/2025 09:31:38] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[15/07/2025 09:31:38] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 09:31:38] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 09:31:38] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 70 zones de drop disponibles
[15/07/2025 09:31:38] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 70 zones trouvées
[15/07/2025 09:31:38] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 09:31:38] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 09:31:38] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 09:31:38] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 09:31:38] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 09:31:38] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 09:31:38] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 09:31:38] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 09:31:38] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 09:31:38] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 09:31:38] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 09:31:38] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 09:31:38] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 09:31:38] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 09:31:38] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 09:31:38] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 09:31:38] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 09:31:38] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 09:31:38] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 09:31:38] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 09:31:38] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 09:31:38] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 09:31:38] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 09:31:38] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:31:38] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:31:38] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:31:38] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:31:38] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:31:38] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[15/07/2025 09:31:38] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (70 zones)
[15/07/2025 09:31:38] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 09:31:38] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[15/07/2025 09:31:38] [FRONTEND] [LOG] ✅ [SETUP] Drag & drop des employés configuré (mode simplifié)
[15/07/2025 09:31:38] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 70 zones trouvées
[15/07/2025 09:31:38] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 70 zones de drop disponibles
[15/07/2025 09:31:39] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 09:31:39] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 09:31:39] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 09:31:39] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 09:31:39] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 09:31:39] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 09:31:39] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 09:31:39] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 09:31:39] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 09:31:39] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 09:31:39] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 09:31:39] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 09:31:39] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 09:31:39] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 09:31:39] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 09:31:39] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 09:31:39] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 09:31:39] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 09:31:39] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 09:31:39] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 09:31:39] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:31:39] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:31:39] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:31:39] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:31:39] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:31:39] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:31:39] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 09:31:39] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:31:39] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (70 zones)
[15/07/2025 09:31:39] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:31:42] [FRONTEND] [LOG] 🎯 [GRIP] Début drag grip pour attribution b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:31:42] [FRONTEND] [LOG] 🎨 [highlightEmployeeDropZones] Ajout surbrillance sur 70 zones
[15/07/2025 09:31:42] [FRONTEND] [LOG] 🎯 [GRIP] DataTransfer types avant: ["text/_moz_htmlcontext","text/_moz_htmlinfo","text/html","text/plain"]
[15/07/2025 09:31:42] [FRONTEND] [LOG] 🎯 [GRIP] regularAssignmentId stocké: b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:31:42] [FRONTEND] [LOG] 🎯 [GRIP] DataTransfer types après: ["text/_moz_htmlcontext","text/_moz_htmlinfo","text/html","text/plain","regularassignmentid"]
[15/07/2025 09:31:42] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 59f5df3a-33ef-425c-bfa2-adca818cf94f, types: text/_moz_htmlcontext, text/_moz_htmlinfo, text/html, text/plain, regularassignmentid
[15/07/2025 09:31:42] [FRONTEND] [LOG] 🎨 [highlightEmployeeDropZones] Zones trouvées: [{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30"},{"element":"BUTTON","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors"},{"element":"BUTTON","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors"},{"element":"BUTTON","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30"},{"element":"BUTTON","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors"},{"element":"BUTTON","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors"},{"element":"BUTTON","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30"},{"element":"BUTTON","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors"},{"element":"BUTTON","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors"},{"element":"BUTTON","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30"},{"element":"BUTTON","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors"},{"element":"BUTTON","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors"},{"element":"BUTTON","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30"},{"element":"BUTTON","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors"},{"element":"BUTTON","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors"},{"element":"BUTTON","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60"}]
[15/07/2025 09:31:42] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 18fe9cd1-e8a5-44e3-90b3-a856086ce27e, types: text/_moz_htmlcontext, text/_moz_htmlinfo, text/html, text/plain, regularassignmentid
[15/07/2025 09:31:43] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 18fe9cd1-e8a5-44e3-90b3-a856086ce27e, types: text/_moz_htmlcontext, text/_moz_htmlinfo, text/html, text/plain, regularassignmentid
[15/07/2025 09:31:43] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 18fe9cd1-e8a5-44e3-90b3-a856086ce27e, types: text/_moz_htmlcontext, text/_moz_htmlinfo, text/html, text/plain, regularassignmentid
[15/07/2025 09:31:44] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 18fe9cd1-e8a5-44e3-90b3-a856086ce27e, types: text/_moz_htmlcontext, text/_moz_htmlinfo, text/html, text/plain, regularassignmentid
[15/07/2025 09:31:44] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 59f5df3a-33ef-425c-bfa2-adca818cf94f, types: text/_moz_htmlcontext, text/_moz_htmlinfo, text/html, text/plain, regularassignmentid
[15/07/2025 09:31:44] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 59f5df3a-33ef-425c-bfa2-adca818cf94f, types: text/_moz_htmlcontext, text/_moz_htmlinfo, text/html, text/plain, regularassignmentid
[15/07/2025 09:31:45] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 59f5df3a-33ef-425c-bfa2-adca818cf94f, types: text/_moz_htmlcontext, text/_moz_htmlinfo, text/html, text/plain, regularassignmentid
[15/07/2025 09:31:45] [FRONTEND] [LOG] 🎨 [highlightEmployeeDropZones] Suppression surbrillance sur 70 zones
[15/07/2025 09:31:45] [FRONTEND] [LOG] 🎯 [GRIP] Fin drag grip pour attribution b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:31:45] [FRONTEND] [LOG] 🎨 [highlightEmployeeDropZones] Zones trouvées: [{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200 regular-assignment-drop-zone"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30 regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors regular-assignment-drop-zone"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200 regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30 regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200 regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30 regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30 regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30 regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 regular-assignment-drop-zone"}]
[15/07/2025 09:31:50] [FRONTEND] [LOG] 🎯 [GRIP] Début drag grip pour attribution b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:31:50] [FRONTEND] [LOG] 🎯 [GRIP] DataTransfer types avant: ["text/_moz_htmlcontext","text/_moz_htmlinfo","text/html","text/plain"]
[15/07/2025 09:31:50] [FRONTEND] [LOG] 🎯 [GRIP] DataTransfer types après: ["text/_moz_htmlcontext","text/_moz_htmlinfo","text/html","text/plain","regularassignmentid"]
[15/07/2025 09:31:50] [FRONTEND] [LOG] 🎨 [highlightEmployeeDropZones] Ajout surbrillance sur 70 zones
[15/07/2025 09:31:50] [FRONTEND] [LOG] 🎯 [GRIP] regularAssignmentId stocké: b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:31:50] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 18fe9cd1-e8a5-44e3-90b3-a856086ce27e, types: text/_moz_htmlcontext, text/_moz_htmlinfo, text/html, text/plain, regularassignmentid
[15/07/2025 09:31:50] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 59f5df3a-33ef-425c-bfa2-adca818cf94f, types: text/_moz_htmlcontext, text/_moz_htmlinfo, text/html, text/plain, regularassignmentid
[15/07/2025 09:31:50] [FRONTEND] [LOG] 🎨 [highlightEmployeeDropZones] Zones trouvées: [{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30"},{"element":"BUTTON","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors"},{"element":"BUTTON","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors"},{"element":"BUTTON","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30"},{"element":"BUTTON","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors"},{"element":"BUTTON","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors"},{"element":"BUTTON","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30"},{"element":"BUTTON","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors"},{"element":"BUTTON","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors"},{"element":"BUTTON","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30"},{"element":"BUTTON","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors"},{"element":"BUTTON","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors"},{"element":"BUTTON","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30"},{"element":"BUTTON","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors"},{"element":"BUTTON","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors"},{"element":"BUTTON","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60"}]
[15/07/2025 09:31:50] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 18fe9cd1-e8a5-44e3-90b3-a856086ce27e, types: text/_moz_htmlcontext, text/_moz_htmlinfo, text/html, text/plain, regularassignmentid
[15/07/2025 09:31:50] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 09:31:50] [FRONTEND] [LOG] 🎯 [DROP] ✅ Drop reçu sur employé: Pierre Durand
[15/07/2025 09:31:50] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 09:31:50] [FRONTEND] [LOG] 🔄 [DROP] Attribution régulière: b77227a7-67b7-4feb-b082-890cfeecab1f → 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:31:51] [FRONTEND] [LOG] 🔍 [detectDropDateFromPosition] Analyse position pour employé 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:31:51] [FRONTEND] [LOG] ✅ [detectDropDateFromPosition] Date trouvée via cellule: 2025-07-24
[15/07/2025 09:31:51] [FRONTEND] [LOG] 📅 [DROP] Date détectée pour le drop: 2025-07-24
[15/07/2025 09:31:51] [FRONTEND] [LOG] 📍 [detectDropDateFromPosition] Position souris: (862, 545)
[15/07/2025 09:31:51] [FRONTEND] [LOG] 📅 [handleRegularAssignmentDrop] Date cible: 2025-07-24
[15/07/2025 09:31:51] [FRONTEND] [LOG] 🎯 [handleRegularAssignmentDrop] Drop assignment b77227a7-67b7-4feb-b082-890cfeecab1f sur employé Pierre Durand
[15/07/2025 09:31:51] [FRONTEND] [LOG] ✅ [showRegularAssignmentConfirmationMenu] Date automatiquement détectée: 2025-07-24
[15/07/2025 09:31:51] [FRONTEND] [LOG] 🎯 [showRegularAssignmentConfirmationMenu] Affichage menu pour: {"assignment":"b77227a7-67b7-4feb-b082-890cfeecab1f","from":"Sophie Leblanc","to":"Pierre Durand","post":"Poste Matin","referenceDate":"2025-07-24","minDate":"2025-07-24","todayKey":"2025-07-15"}
[15/07/2025 09:31:51] [FRONTEND] [LOG] 🎯 [GRIP] Fin drag grip pour attribution b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:31:51] [FRONTEND] [LOG] 🎨 [highlightEmployeeDropZones] Suppression surbrillance sur 70 zones
[15/07/2025 09:31:51] [FRONTEND] [LOG] 🎨 [highlightEmployeeDropZones] Zones trouvées: [{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200 regular-assignment-drop-zone"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30 regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors regular-assignment-drop-zone"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200 regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30 regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200 regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30 regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30 regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"employee-row grid grid-cols-[240px_repeat(7,1fr)] border-b border-slate-200/40 min-h-[80px] hover:bg-slate-50/30 transition-colors duration-200"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"employee-info flex items-center p-4 gap-4 border-r border-slate-300/40 bg-slate-100/30 regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"edit-employee-btn material-icons-outlined text-slate-400 hover:text-green-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"purge-shifts-btn material-icons-outlined text-slate-400 hover:text-orange-600 transition-colors regular-assignment-drop-zone"},{"element":"BUTTON","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"manage-vacation-btn material-icons-outlined text-slate-400 hover:text-blue-600 transition-colors regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 has-grip regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"day-cell p-2 flex items-center justify-start gap-1 transition-all duration-200 ease-in-out hover:bg-slate-100/50 border-r border-slate-300/40 min-h-[80px] drop-zone-employee regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 regular-assignment-drop-zone"},{"element":"DIV","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","classes":"post-shift-card text-xs font-semibold text-center flex-1 min-w-0 p-2 rounded-lg transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-0.5 relative bg-sky-100/80 hover:bg-sky-200/90 text-sky-800 border border-sky-300/60 regular-assignment-drop-zone"}]
[15/07/2025 09:31:52] [FRONTEND] [LOG] 🔄 [handlePermanentRegularAssignmentChange] Changement permanent b77227a7-67b7-4feb-b082-890cfeecab1f → 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:31:52] [FRONTEND] [LOG] 🔄 [handlePermanentRegularAssignmentChange] Désactiver l'original: false
[15/07/2025 09:31:52] [FRONTEND] [LOG] 🔄 [reassignRegularAssignmentFromDate] Désactiver l'original: false
[15/07/2025 09:31:52] [FRONTEND] [LOG] 📅 [handlePermanentRegularAssignmentChange] Date minimale: 2025-07-23
[15/07/2025 09:31:52] [FRONTEND] [LOG] 🔄 [reassignRegularAssignmentFromDate] b77227a7-67b7-4feb-b082-890cfeecab1f → 18fe9cd1-e8a5-44e3-90b3-a856086ce27e à partir de 2025-07-23
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      UPDATE regular_assignments 
      SET employee_id = $1, post_id = $2, days_of_week = $3, start_date = $4, 
          end_date = $5, is_active = $6, excluded_dates = $7, updated_at = CURRENT_TIMESTAMP
      WHERE id = $8
      RETURNING *
     {"params":["59f5df3a-33ef-425c-bfa2-adca818cf94f","ef3246ff-8398-4f11-a498-648d207563a0",[1,2,3,4,5],"2025-07-15","2025-07-22",true,[],"b77227a7-67b7-4feb-b082-890cfeecab1f"]}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      UPDATE regular_assignments \n      SET employee_id = $1, post_id = $2, days_of_week = $3, start_date = $4, \n          end_date = $5, is_active = $6, excluded_dates = $7, updated_at = CURRENT_TIMESTAMP\n      WHERE id = $8\n      RETURNING *\n    ",
    {
      "params": [
        "59f5df3a-33ef-425c-bfa2-adca818cf94f",
        "ef3246ff-8398-4f11-a498-648d207563a0",
        [
          1,
          2,
          3,
          4,
          5
        ],
        "2025-07-15",
        "2025-07-22",
        true,
        [],
        "b77227a7-67b7-4feb-b082-890cfeecab1f"
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:52.980Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔍 [validateAndConvertUuid] Entrée: fieldName="post_id", value="ef3246ff-8398-4f11-a498-648d207563a0", type=string
  Data: {
  "originalArgs": [
    "🔍 [validateAndConvertUuid] Entrée: fieldName=\"post_id\", value=\"ef3246ff-8398-4f11-a498-648d207563a0\", type=string"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:52.977Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ [validateAndConvertUuid] UUID valide détecté pour post_id: ef3246ff-8398-4f11-a498-648d207563a0
  Data: {
  "originalArgs": [
    "✅ [validateAndConvertUuid] UUID valide détecté pour post_id: ef3246ff-8398-4f11-a498-648d207563a0"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:52.978Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📝 [RegularAssignmentV2.update] Données reçues: {"employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","isLimited":false,"startDate":"2025-07-15","endDate":"2025-07-22","selectedDays":[1,2,3,4,5],"id":"b77227a7-67b7-4feb-b082-890cfeecab1f","isActive":true}
  Data: {
  "originalArgs": [
    "📝 [RegularAssignmentV2.update] Données reçues:",
    {
      "employeeId": "59f5df3a-33ef-425c-bfa2-adca818cf94f",
      "postId": "ef3246ff-8398-4f11-a498-648d207563a0",
      "isLimited": false,
      "startDate": "2025-07-15",
      "endDate": "2025-07-22",
      "selectedDays": [
        1,
        2,
        3,
        4,
        5
      ],
      "id": "b77227a7-67b7-4feb-b082-890cfeecab1f",
      "isActive": true
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:52.976Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✏️ [RegularAssignments] Mise à jour attribution b77227a7-67b7-4feb-b082-890cfeecab1f
  Data: {
  "originalArgs": [
    "✏️ [RegularAssignments] Mise à jour attribution b77227a7-67b7-4feb-b082-890cfeecab1f"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:52.975Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:53.075Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:53.080Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:53.077Z"
}
[15/07/2025 09:31:53] [FRONTEND] [LOG] 🔄 [reassignRegularAssignmentFromDate] Fork créé, historique préservé
[15/07/2025 09:31:53] [FRONTEND] [LOG] ✅ [updateShiftsForDateBasedReassignment] 3 shifts mis à jour
[15/07/2025 09:31:53] [FRONTEND] [LOG] 🔄 [updateShiftsForDateBasedReassignment] Mise à jour des shifts à partir de 2025-07-23
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:53.130Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:53.132Z"
}
[15/07/2025 09:31:53] [BACKEND] [INFO] ✏️ [RegularAssignments] Mise à jour attribution b77227a7-67b7-4feb-b082-890cfeecab1f
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ [RegularAssignments] Attribution mise à jour: b77227a7-67b7-4feb-b082-890cfeecab1f
  Data: {
  "originalArgs": [
    "✅ [RegularAssignments] Attribution mise à jour:",
    "b77227a7-67b7-4feb-b082-890cfeecab1f"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:53.170Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 189ms: 
      UPDATE regular_assignments 
      SET emplo {"params":["59f5df3a-33ef-425c-bfa2-adca818cf94f","ef3246ff-8398-4f11-a498-648d207563a0",[1,2,3,4,5],"2025-07-15","2025-07-22",true,[],"b77227a7-67b7-4feb-b082-890cfeecab1f"]}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 189ms:",
    "\n      UPDATE regular_assignments \n      SET emplo",
    {
      "params": [
        "59f5df3a-33ef-425c-bfa2-adca818cf94f",
        "ef3246ff-8398-4f11-a498-648d207563a0",
        [
          1,
          2,
          3,
          4,
          5
        ],
        "2025-07-15",
        "2025-07-22",
        true,
        [],
        "b77227a7-67b7-4feb-b082-890cfeecab1f"
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:53.169Z"
}
[15/07/2025 09:31:53] [FRONTEND] [LOG] ✅ [reassignRegularAssignmentFromDate] Attribution existante mise à jour avec date de fin
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📝 [RegularAssignmentV2.create] Données reçues: {"employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"ef3246ff-8398-4f11-a498-648d207563a0","isLimited":false,"startDate":"2025-07-23","endDate":null,"selectedDays":[1,2,3,4,5],"isActive":true,"id":"1390c4ee-132c-4222-bd1d-e528fb97e1b4"}
  Data: {
  "originalArgs": [
    "📝 [RegularAssignmentV2.create] Données reçues:",
    {
      "employeeId": "18fe9cd1-e8a5-44e3-90b3-a856086ce27e",
      "postId": "ef3246ff-8398-4f11-a498-648d207563a0",
      "isLimited": false,
      "startDate": "2025-07-23",
      "endDate": null,
      "selectedDays": [
        1,
        2,
        3,
        4,
        5
      ],
      "isActive": true,
      "id": "1390c4ee-132c-4222-bd1d-e528fb97e1b4"
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:53.192Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📤 [RegularAssignments] Données reçues: {
  "employeeId": "18fe9cd1-e8a5-44e3-90b3-a856086ce27e",
  "postId": "ef3246ff-8398-4f11-a498-648d207563a0",
  "isLimited": false,
  "startDate": "2025-07-23",
  "endDate": null,
  "selectedDays": [
    1,
    2,
    3,
    4,
    5
  ],
  "isActive": true,
  "id": "1390c4ee-132c-4222-bd1d-e528fb97e1b4"
}
  Data: {
  "originalArgs": [
    "📤 [RegularAssignments] Données reçues:",
    "{\n  \"employeeId\": \"18fe9cd1-e8a5-44e3-90b3-a856086ce27e\",\n  \"postId\": \"ef3246ff-8398-4f11-a498-648d207563a0\",\n  \"isLimited\": false,\n  \"startDate\": \"2025-07-23\",\n  \"endDate\": null,\n  \"selectedDays\": [\n    1,\n    2,\n    3,\n    4,\n    5\n  ],\n  \"isActive\": true,\n  \"id\": \"1390c4ee-132c-4222-bd1d-e528fb97e1b4\"\n}"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:53.191Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔍 [validateAndConvertUuid] Entrée: fieldName="post_id", value="ef3246ff-8398-4f11-a498-648d207563a0", type=string
  Data: {
  "originalArgs": [
    "🔍 [validateAndConvertUuid] Entrée: fieldName=\"post_id\", value=\"ef3246ff-8398-4f11-a498-648d207563a0\", type=string"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:53.193Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ➕ [RegularAssignments] Création d'une attribution individuelle
  Data: {
  "originalArgs": [
    "➕ [RegularAssignments] Création d'une attribution individuelle"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:53.190Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔍 [RegularAssignmentV2.create] Création attribution unique pour jours: [1, 2, 3, 4, 5]
  Data: {
  "originalArgs": [
    "🔍 [RegularAssignmentV2.create] Création attribution unique pour jours: [1, 2, 3, 4, 5]"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:53.195Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔍 [RegularAssignmentV2.create] Post ID original: ef3246ff-8398-4f11-a498-648d207563a0 Post ID validé: ef3246ff-8398-4f11-a498-648d207563a0
  Data: {
  "originalArgs": [
    "🔍 [RegularAssignmentV2.create] Post ID original:",
    "ef3246ff-8398-4f11-a498-648d207563a0",
    "Post ID validé:",
    "ef3246ff-8398-4f11-a498-648d207563a0"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:53.195Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ [validateAndConvertUuid] UUID valide détecté pour post_id: ef3246ff-8398-4f11-a498-648d207563a0
  Data: {
  "originalArgs": [
    "✅ [validateAndConvertUuid] UUID valide détecté pour post_id: ef3246ff-8398-4f11-a498-648d207563a0"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:53.194Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:53.327Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:53.331Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      INSERT INTO regular_assignments (id, employee_id, post_id, days_of_week, start_date, end_date, is_active)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
     {"params":["fd058f0f-a569-42a7-90c2-d1d0f15bd888","18fe9cd1-e8a5-44e3-90b3-a856086ce27e","ef3246ff-8398-4f11-a498-648d207563a0",[1,2,3,4,5],"2025-07-23",null,true]}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      INSERT INTO regular_assignments (id, employee_id, post_id, days_of_week, start_date, end_date, is_active)\n      VALUES ($1, $2, $3, $4, $5, $6, $7)\n      RETURNING *\n    ",
    {
      "params": [
        "fd058f0f-a569-42a7-90c2-d1d0f15bd888",
        "18fe9cd1-e8a5-44e3-90b3-a856086ce27e",
        "ef3246ff-8398-4f11-a498-648d207563a0",
        [
          1,
          2,
          3,
          4,
          5
        ],
        "2025-07-23",
        null,
        true
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:53.196Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:53.329Z"
}
[15/07/2025 09:31:53] [BACKEND] [INFO] ➕ [RegularAssignments] Création d'une attribution individuelle
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 166ms: 
      INSERT INTO regular_assignments (id, employ {"params":["fd058f0f-a569-42a7-90c2-d1d0f15bd888","18fe9cd1-e8a5-44e3-90b3-a856086ce27e","ef3246ff-8398-4f11-a498-648d207563a0",[1,2,3,4,5],"2025-07-23",null,true]}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 166ms:",
    "\n      INSERT INTO regular_assignments (id, employ",
    {
      "params": [
        "fd058f0f-a569-42a7-90c2-d1d0f15bd888",
        "18fe9cd1-e8a5-44e3-90b3-a856086ce27e",
        "ef3246ff-8398-4f11-a498-648d207563a0",
        [
          1,
          2,
          3,
          4,
          5
        ],
        "2025-07-23",
        null,
        true
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:53.363Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ [RegularAssignmentV2.create] Attribution créée avec 5 jours
  Data: {
  "originalArgs": [
    "✅ [RegularAssignmentV2.create] Attribution créée avec 5 jours"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:53.363Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ [RegularAssignments] Attribution créée avec succès: fd058f0f-a569-42a7-90c2-d1d0f15bd888
  Data: {
  "originalArgs": [
    "✅ [RegularAssignments] Attribution créée avec succès:",
    "fd058f0f-a569-42a7-90c2-d1d0f15bd888"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:53.364Z"
}
[15/07/2025 09:31:53] [FRONTEND] [LOG] ✅ [reassignRegularAssignmentFromDate] Nouvelle attribution créée avec succès
[15/07/2025 09:31:53] [FRONTEND] [LOG] 📂 [TeamCalendarApp] Chargement de l'état...
[15/07/2025 09:31:53] [FRONTEND] [LOG] 🔄 [reassignRegularAssignmentFromDate] Rechargement complet des données...
[15/07/2025 09:31:53] [FRONTEND] [LOG] 🌐 [TeamCalendarApp] Chargement depuis l'API...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT e.*, et.name as template_name 
      FROM employees e 
      LEFT JOIN employee_templates et ON e.template_id = et.id 
      ORDER BY e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT e.*, et.name as template_name \n      FROM employees e \n      LEFT JOIN employee_templates et ON e.template_id = et.id \n      ORDER BY e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:53.438Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 42ms: 
      SELECT e.*, et.name as template_name 
      {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 42ms:",
    "\n      SELECT e.*, et.name as template_name \n     ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:53.480Z"
}
[15/07/2025 09:31:53] [BACKEND] [INFO] ✅ Requête exécutée en 42ms: 
      SELECT e.*, et.name as template_name 
      {}
[15/07/2025 09:31:53] [FRONTEND] [LOG] 👥 [TeamCalendarApp] 5 employés chargés
[15/07/2025 09:31:53] [FRONTEND] [LOG] 📦 [loadState] Chargement de tous les shifts depuis la base de données...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT s.*, e.name as employee_name, sp.label as post_label
      FROM shifts s
      LEFT JOIN employees e ON s.employee_id = e.id
      LEFT JOIN standard_posts sp ON s.post_id = sp.id
      ORDER BY s.date_key, e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT s.*, e.name as employee_name, sp.label as post_label\n      FROM shifts s\n      LEFT JOIN employees e ON s.employee_id = e.id\n      LEFT JOIN standard_posts sp ON s.post_id = sp.id\n      ORDER BY s.date_key, e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:53.530Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 40ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 40ms:",
    "\n      SELECT s.*, e.name as employee_name, sp.lab",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:53.571Z"
}
[15/07/2025 09:31:53] [BACKEND] [INFO] ✅ Requête exécutée en 40ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
[15/07/2025 09:31:53] [FRONTEND] [LOG] 📅 [loadState] 0 shifts chargés depuis la base de données
[15/07/2025 09:31:53] [FRONTEND] [LOG] ✅ [loadState] Shifts organisés dans this.data.schedule
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:53.602Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 39ms: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 39ms:",
    "SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:53.641Z"
}
[15/07/2025 09:31:53] [BACKEND] [INFO] ✅ Requête exécutée en 39ms: SELECT * FROM standard_posts ORDER BY label {}
[15/07/2025 09:31:53] [FRONTEND] [LOG] 🏢 [TeamCalendarApp] 5 postes standards chargés
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:53.669Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 43ms: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 43ms:",
    "SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:53.711Z"
}
[15/07/2025 09:31:53] [BACKEND] [INFO] ✅ Requête exécutée en 43ms: SELECT * FROM app_settings ORDER BY setting_key {}
[15/07/2025 09:31:53] [FRONTEND] [LOG] ⚙️ [loadState] Paramètres chargés depuis API: {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT 
        ra.id,
        ra.employee_id,
        ra.post_id,
        ra.days_of_week,
        ra.start_date,
        ra.end_date,
        ra.is_active,
        ra.excluded_dates,
        e.name as employee_name,
        sp.label as post_label,
        sp.hours as post_hours,
        sp.type as post_type,
        array_length(ra.days_of_week, 1) as days_count
      FROM regular_assignments ra
      LEFT JOIN employees e ON ra.employee_id = e.id
      LEFT JOIN standard_posts sp ON ra.post_id = sp.id
      WHERE ra.is_active = true
      ORDER BY e.name, sp.label, ra.start_date
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT \n        ra.id,\n        ra.employee_id,\n        ra.post_id,\n        ra.days_of_week,\n        ra.start_date,\n        ra.end_date,\n        ra.is_active,\n        ra.excluded_dates,\n        e.name as employee_name,\n        sp.label as post_label,\n        sp.hours as post_hours,\n        sp.type as post_type,\n        array_length(ra.days_of_week, 1) as days_count\n      FROM regular_assignments ra\n      LEFT JOIN employees e ON ra.employee_id = e.id\n      LEFT JOIN standard_posts sp ON ra.post_id = sp.id\n      WHERE ra.is_active = true\n      ORDER BY e.name, sp.label, ra.start_date\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:53.729Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 38ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 38ms:",
    "\n      SELECT \n        ra.id,\n        ra.employee_",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:31:53.767Z"
}
[15/07/2025 09:31:53] [BACKEND] [INFO] ✅ Requête exécutée en 38ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
[15/07/2025 09:31:54] [FRONTEND] [LOG] ✅ [fixInvalidAssignmentDates] Aucune date invalide trouvée
[15/07/2025 09:31:54] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"b77227a7-67b7-4feb-b082-890cfeecab1f","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-15","endDate":"2025-07-22","excludedDates":[]}
[15/07/2025 09:31:54] [FRONTEND] [LOG] 📋 [loadState] Assignations régulières chargées (application différée)
[15/07/2025 09:31:54] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"isLimited":false,"startDate":"2025-07-23","endDate":null,"excludedDates":[]}
[15/07/2025 09:31:54] [FRONTEND] [LOG] 📋 [loadState] 2/2 assignations régulières valides chargées et normalisées
[15/07/2025 09:31:54] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","employee_id":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","post_id":"ef3246ff-8398-4f11-a498-648d207563a0","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-23T04:00:00.000Z","end_date":null,"excluded_dates":[]}
[15/07/2025 09:31:54] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"b77227a7-67b7-4feb-b082-890cfeecab1f","employee_id":"59f5df3a-33ef-425c-bfa2-adca818cf94f","post_id":"ef3246ff-8398-4f11-a498-648d207563a0","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-15T04:00:00.000Z","end_date":"2025-07-22T04:00:00.000Z","excluded_dates":[]}
[15/07/2025 09:31:54] [FRONTEND] [LOG] 🔧 [fixInvalidAssignmentDates] Vérification des dates invalides...
[15/07/2025 09:31:54] [FRONTEND] [LOG] 🧹 [cleanupDuplicateRegularShifts] Nettoyage des doublons de shifts réguliers...
[15/07/2025 09:31:54] [FRONTEND] [LOG] ✅ [cleanupInvalidAssignmentIds] Aucun assignment_id invalide détecté
[15/07/2025 09:31:54] [FRONTEND] [LOG] 🧹 [cleanupInvalidAssignmentIds] Nettoyage des assignment_id invalides...
[15/07/2025 09:31:54] [FRONTEND] [LOG] ✅ [cleanupDuplicateRegularShifts] Aucun doublon détecté
[15/07/2025 09:31:54] [FRONTEND] [LOG] ✅ [loadState] État chargé depuis l'API avec succès
[15/07/2025 09:31:54] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] 0 grips ajoutés
[15/07/2025 09:31:54] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 09:31:54] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 09:31:54] [FRONTEND] [LOG] 🧹 [reassignRegularAssignmentFromDate] Nettoyage complet des shifts réguliers...
[15/07/2025 09:31:54] [FRONTEND] [LOG] 🗑️ [reassignRegularAssignmentFromDate] Invalidation des caches...
[15/07/2025 09:31:54] [FRONTEND] [LOG] 🔄 [reassignRegularAssignmentFromDate] Application des attributions avec la nouvelle logique...
[15/07/2025 09:31:54] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] Nettoyage des shifts réguliers existants...
[15/07/2025 09:31:54] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] 0 shifts réguliers supprimés
[15/07/2025 09:31:54] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Cache invalidé pour forcer la régénération
[15/07/2025 09:31:54] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Application pour semaine 2025-W29
[15/07/2025 09:31:54] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888 a une intersection avec cette semaine (2025-07-23 → ∞) vs (2025-07-20 → 2025-07-26)
[15/07/2025 09:31:54] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution b77227a7-67b7-4feb-b082-890cfeecab1f a une intersection avec cette semaine (2025-07-15 → 2025-07-22) vs (2025-07-20 → 2025-07-26)
[15/07/2025 09:31:54] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 2 attributions actives sur 2 total
[15/07/2025 09:31:54] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"startDate":"2025-07-23","endDate":null,"excludedDates":[]}
[15/07/2025 09:31:54] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-20 (0)
[15/07/2025 09:31:54] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888: [{"dateKey":"2025-07-20","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-21","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-22","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-23","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-24","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-07-25","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-07-26","isWeekend":true,"dayOfWeek":6}]
[15/07/2025 09:31:54] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-20T04:00:00.000Z","dayDateKey":"2025-07-20","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5]}
[15/07/2025 09:31:54] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:31:54] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-20 ignoré pour attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 09:31:54] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-21 (1)
[15/07/2025 09:31:54] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-21T04:00:00.000Z","dayDateKey":"2025-07-21","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5]}
[15/07/2025 09:31:54] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[15/07/2025 09:31:54] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-21 avant startDate 2025-07-23 (original: 2025-07-23)
[15/07/2025 09:31:54] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-21: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","dayDateStr":"2025-07-21","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:31:54] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-22 (2)
[15/07/2025 09:31:54] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-21 ignoré pour attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 09:31:54] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-22T04:00:00.000Z","dayDateKey":"2025-07-22","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5]}
[15/07/2025 09:31:54] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[15/07/2025 09:31:54] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-22: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","dayDateStr":"2025-07-22","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:31:54] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-22 avant startDate 2025-07-23 (original: 2025-07-23)
[15/07/2025 09:31:54] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-22 ignoré pour attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 09:31:54] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-23 (3)
[15/07/2025 09:31:54] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-23T04:00:00.000Z","dayDateKey":"2025-07-23","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5]}
[15/07/2025 09:31:54] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[15/07/2025 09:31:54] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-23: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","dayDateStr":"2025-07-23","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:31:54] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-23
[15/07/2025 09:31:54] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-23 validé, shifts existants: 0
[15/07/2025 09:31:54] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-23 pour Pierre Durand
[15/07/2025 09:31:54] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-24 (4)
[15/07/2025 09:31:54] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-24T04:00:00.000Z","dayDateKey":"2025-07-24","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5]}
[15/07/2025 09:31:54] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[15/07/2025 09:31:54] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-24: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","dayDateStr":"2025-07-24","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:31:54] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-24
[15/07/2025 09:31:54] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-24 validé, shifts existants: 0
[15/07/2025 09:31:54] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-24 pour Pierre Durand
[15/07/2025 09:31:54] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-07-25 (5)
[15/07/2025 09:31:54] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[15/07/2025 09:31:54] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-25T04:00:00.000Z","dayDateKey":"2025-07-25","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5]}
[15/07/2025 09:31:54] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-25: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","dayDateStr":"2025-07-25","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:31:54] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-25
[15/07/2025 09:31:54] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-25 validé, shifts existants: 0
[15/07/2025 09:31:54] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-07-26 (6)
[15/07/2025 09:31:54] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-25 pour Pierre Durand
[15/07/2025 09:31:54] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-26T04:00:00.000Z","dayDateKey":"2025-07-26","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5]}
[15/07/2025 09:31:54] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"b77227a7-67b7-4feb-b082-890cfeecab1f","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"startDate":"2025-07-15","endDate":"2025-07-22","excludedDates":[]}
[15/07/2025 09:31:54] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution b77227a7-67b7-4feb-b082-890cfeecab1f: [{"dateKey":"2025-07-20","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-21","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-22","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-23","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-24","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-07-25","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-07-26","isWeekend":true,"dayOfWeek":6}]
[15/07/2025 09:31:54] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-26 ignoré pour attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 09:31:54] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:31:54] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-20 (0)
[15/07/2025 09:31:54] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-20T04:00:00.000Z","dayDateKey":"2025-07-20","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5]}
[15/07/2025 09:31:54] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:31:54] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-21 (1)
[15/07/2025 09:31:54] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-20 ignoré pour attribution b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:31:54] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-21T04:00:00.000Z","dayDateKey":"2025-07-21","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5]}
[15/07/2025 09:31:54] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[15/07/2025 09:31:54] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-21: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","dayDateStr":"2025-07-21","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:31:54] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-21
[15/07/2025 09:31:54] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-21 validé, shifts existants: 0
[15/07/2025 09:31:54] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-21 pour Sophie Leblanc
[15/07/2025 09:31:54] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-22 (2)
[15/07/2025 09:31:54] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-22T04:00:00.000Z","dayDateKey":"2025-07-22","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5]}
[15/07/2025 09:31:54] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[15/07/2025 09:31:54] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-22: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","dayDateStr":"2025-07-22","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:31:54] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-22
[15/07/2025 09:31:54] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-22 validé, shifts existants: 0
[15/07/2025 09:31:55] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-22 pour Sophie Leblanc
[15/07/2025 09:31:55] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-23 (3)
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-23T04:00:00.000Z","dayDateKey":"2025-07-23","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5]}
[15/07/2025 09:31:55] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-23: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","dayDateStr":"2025-07-23","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:31:55] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-23 après endDate 2025-07-22 (original: 2025-07-22) (attribution b77227a7-67b7-4feb-b082-890cfeecab1f)
[15/07/2025 09:31:55] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-23 ignoré pour attribution b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:31:55] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-24 (4)
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-24T04:00:00.000Z","dayDateKey":"2025-07-24","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5]}
[15/07/2025 09:31:55] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-24: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","dayDateStr":"2025-07-24","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:31:55] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-24 après endDate 2025-07-22 (original: 2025-07-22) (attribution b77227a7-67b7-4feb-b082-890cfeecab1f)
[15/07/2025 09:31:55] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-24 ignoré pour attribution b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-25T04:00:00.000Z","dayDateKey":"2025-07-25","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5]}
[15/07/2025 09:31:55] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-07-25 (5)
[15/07/2025 09:31:55] [FRONTEND] [LOG] ⚠️ [shouldApplyAssignmentToDay] WorkingDays undefined/null, autoriser tous les jours
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-25: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","dayDateStr":"2025-07-25","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:31:55] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-25 après endDate 2025-07-22 (original: 2025-07-22) (attribution b77227a7-67b7-4feb-b082-890cfeecab1f)
[15/07/2025 09:31:55] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-25 ignoré pour attribution b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:31:55] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-07-26 (6)
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-26T04:00:00.000Z","dayDateKey":"2025-07-26","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5]}
[15/07/2025 09:31:55] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:31:55] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-26 ignoré pour attribution b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:31:55] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 5 attributions appliquées pour la semaine 2025-W29
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Rendu demandé car 5 shifts créés
[15/07/2025 09:31:55] [FRONTEND] [LOG] ✅ [reassignRegularAssignmentFromDate] Attribution divisée avec succès
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🔄 [reassignRegularAssignmentFromDate] Rendu final...
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Matin: [1, 2, 3, 4, 5]
[15/07/2025 09:31:55] [FRONTEND] [LOG] 📝 [logModification] Enregistrement: {"id":"a05dbeea-c225-4a74-80f0-56ecd8cb7714","type":"regular-assignment","title":"Attribution régulière modifiée (permanent)","description":"Poste Matin transféré de Sophie Leblanc vers Pierre Durand à partir du 22/07/2025","timestamp":"2025-07-15T13:31:53.856Z","employeeName":"Pierre Durand","postLabel":"Poste Matin","date":"2025-07-23","assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","sourceEmployeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","targetEmployeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e"}
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[15/07/2025 09:31:55] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[15/07/2025 09:31:55] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 09:31:55] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 09:31:55] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 09:31:55] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:31:55] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 09:31:55] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:31:55] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 09:31:55] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 70 zones trouvées
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 70 zones de drop disponibles
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 09:31:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Nuit: [1, 2, 3, 4, 5]
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 09:31:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Soir: [1, 2, 3, 4, 5]
[15/07/2025 09:31:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE1: [0, 6]
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 09:31:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE2: [0, 6]
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 09:31:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 09:31:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 09:31:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 09:31:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 09:31:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 09:31:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 09:31:55] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:31:55] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:31:55] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:31:55] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:31:55] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:31:55] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[15/07/2025 09:31:55] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[15/07/2025 09:31:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (70 zones)
[15/07/2025 09:31:55] [FRONTEND] [LOG] ✅ [SETUP] Drag & drop des employés configuré (mode simplifié)
[15/07/2025 09:31:55] [FRONTEND] [WARN] ⚠️ [SETUP] Erreur lors de la destruction: Error: can't access property "Sortable1752586285578", el is null
destroy@http://localhost:5173/node_modules/.vite/deps/sortablejs.js?v=189de042:1812:5
setupDragAndDrop@http://localhost:5173/src/teamCalendarApp.ts:5808:33
attachAllEventListeners@http://localhost:5173/src/teamCalendarApp.ts:5358:10
actualRender@http://localhost:5173/src/teamCalendarApp.ts:2466:10
render/this._debouncedRender<@http://localhost:5173/src/teamCalendarApp.ts:2451:12
setTimeout handler*render@http://localhost:5173/src/teamCalendarApp.ts:2450:29
reassignRegularAssignmentFromDate@http://localhost:5173/src/teamCalendarApp.ts:11542:12
async*handlePermanentRegularAssignmentChange@http://localhost:5173/src/teamCalendarApp.ts:10937:18
showRegularAssignmentConfirmationMenu/<@http://localhost:5173/src/teamCalendarApp.ts:10902:12
EventListener.handleEvent*showRegularAssignmentConfirmationMenu@http://localhost:5173/src/teamCalendarApp.ts:10896:35
handleRegularAssignmentDrop@http://localhost:5173/src/teamCalendarApp.ts:10476:10
dropHandler@http://localhost:5173/src/teamCalendarApp.ts:6172:16
EventListener.handleEvent*setupCentralizedDropZone/<@http://localhost:5173/src/teamCalendarApp.ts:6234:11
setupCentralizedDropZone@http://localhost:5173/src/teamCalendarApp.ts:6103:18
setupPostDragDrop@http://localhost:5173/src/teamCalendarApp.ts:5913:10
renderUnifiedCalendar@http://localhost:5173/src/teamCalendarApp.ts:2935:10
actualRender@http://localhost:5173/src/teamCalendarApp.ts:2465:10
render/this._debouncedRender<@http://localhost:5173/src/teamCalendarApp.ts:2451:12
setTimeout handler*render@http://localhost:5173/src/teamCalendarApp.ts:2450:29
navigateWeek@http://localhost:5173/src/teamCalendarApp.ts:661:10
setupNavigationListeners/<@http://localhost:5173/src/teamCalendarApp.ts:2436:14
EventListener.handleEvent*setupNavigationListeners@http://localhost:5173/src/teamCalendarApp.ts:2434:15
init@http://localhost:5173/src/teamCalendarApp.ts:1047:10
async*initApp@http://localhost:5173/src/Agenda.tsx:40:31
setTimeout handler*AgendaFullscreen/<@http://localhost:5173/src/Agenda.tsx:50:29
react-stack-bottom-frame@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:17478:20
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
commitHookEffectListMount@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8460:122
commitHookPassiveMountEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8518:60
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10016:42
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55

[15/07/2025 09:31:55] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 70 zones trouvées
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 70 zones de drop disponibles
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 09:31:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 09:31:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 09:31:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 09:31:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 09:31:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 09:31:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 09:31:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 09:31:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 09:31:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 09:31:55] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 09:31:55] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 09:31:56] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 09:31:56] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 09:31:56] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:31:56] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:31:56] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:31:56] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:31:56] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:31:56] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:31:56] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:31:56] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:31:56] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:31:56] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:31:56] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (70 zones)
[15/07/2025 09:31:56] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 09:32:05] [FRONTEND] [LOG] ➡️ Clic sur suivant
[15/07/2025 09:32:05] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] Nettoyage des shifts réguliers existants...
[15/07/2025 09:32:05] [FRONTEND] [LOG] 🧭 [navigateWeek] Direction: 1, Offset actuel: 1
[15/07/2025 09:32:05] [FRONTEND] [LOG] 📋 [navigateWeek] Application des attributions pour la nouvelle semaine 2025-W30
[15/07/2025 09:32:05] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] 0 shifts réguliers supprimés
[15/07/2025 09:32:05] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[15/07/2025 09:32:05] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[15/07/2025 09:32:05] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 09:32:05] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 09:32:05] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888 a une intersection avec cette semaine (2025-07-23 → ∞) vs (2025-07-27 → 2025-08-02)
[15/07/2025 09:32:06] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Application pour semaine 2025-W30
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[15/07/2025 09:32:06] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"startDate":"2025-07-23","endDate":null,"excludedDates":[]}
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Cache invalidé pour forcer la régénération
[15/07/2025 09:32:06] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-30 validé, shifts existants: 0
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-28T04:00:00.000Z","dayDateKey":"2025-07-28","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:06] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-27 (0)
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888: [{"dateKey":"2025-07-27","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-28","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-29","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-30","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-31","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-08-01","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-08-02","isWeekend":true,"dayOfWeek":6}]
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-27T04:00:00.000Z","dayDateKey":"2025-07-27","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:06] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:32:06] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-28 (1)
[15/07/2025 09:32:06] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-27 ignoré pour attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 70 zones trouvées
[15/07/2025 09:32:06] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution b77227a7-67b7-4feb-b082-890cfeecab1f terminée avant cette semaine (endDate: 2025-07-22, semaine commence: 2025-07-27)
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:32:06.199Z"
}
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-28: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","dayDateStr":"2025-07-28","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:32:06] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 1 attributions actives sur 2 total
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:32:06.198Z"
}
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 70 zones de drop disponibles
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:32:06.236Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:32:06.235Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:32:06.072Z"
}
[15/07/2025 09:32:06] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 09:32:06] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-28 validé, shifts existants: 0
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-28 pour Pierre Durand
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 09:32:06] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-29 (2)
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 09:32:06] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:32:06.385Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:32:06.407Z"
}
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-29T04:00:00.000Z","dayDateKey":"2025-07-29","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-28
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-29: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","dayDateStr":"2025-07-29","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-29
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (70 zones)
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[15/07/2025 09:32:06] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-29 validé, shifts existants: 0
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [SETUP] Drag & drop des employés configuré (mode simplifié)
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 09:32:06] [FRONTEND] [WARN] ⚠️ [SETUP] Erreur lors de la destruction: Error: can't access property "Sortable1752586285578", el is null
destroy@http://localhost:5173/node_modules/.vite/deps/sortablejs.js?v=189de042:1812:5
setupDragAndDrop@http://localhost:5173/src/teamCalendarApp.ts:5808:33
attachAllEventListeners@http://localhost:5173/src/teamCalendarApp.ts:5358:10
actualRender@http://localhost:5173/src/teamCalendarApp.ts:2466:10
render/this._debouncedRender<@http://localhost:5173/src/teamCalendarApp.ts:2451:12
setTimeout handler*render@http://localhost:5173/src/teamCalendarApp.ts:2450:29
navigateWeek@http://localhost:5173/src/teamCalendarApp.ts:661:10
setupNavigationListeners/<@http://localhost:5173/src/teamCalendarApp.ts:2436:14
EventListener.handleEvent*setupNavigationListeners@http://localhost:5173/src/teamCalendarApp.ts:2434:15
init@http://localhost:5173/src/teamCalendarApp.ts:1047:10
async*initApp@http://localhost:5173/src/Agenda.tsx:40:31
setTimeout handler*AgendaFullscreen/<@http://localhost:5173/src/Agenda.tsx:50:29
react-stack-bottom-frame@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:17478:20
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
commitHookEffectListMount@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8460:122
commitHookPassiveMountEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8518:60
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10016:42
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
doubleInvokeEffectsOnFiber@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11461:207
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
recursivelyTraverseAndDoubleInvokeEffectsInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11432:78
commitDoubleInvokeEffectsInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11469:55
flushPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11309:41
node_modules/react-dom/cjs/react-dom-client.development.js/commitRoot/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11060:32
performWorkUntilDeadline@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:36:58
EventHandlerNonNull*node_modules/scheduler/cjs/scheduler.development.js/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:156:9
node_modules/scheduler/cjs/scheduler.development.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:266:7
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/scheduler/index.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:277:24
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/react-dom/cjs/react-dom-client.development.js/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:15216:23
node_modules/react-dom/cjs/react-dom-client.development.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18060:7
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/react-dom/client.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18071:24
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18075:16

[15/07/2025 09:32:06] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 09:32:06] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-30 (3)
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-29 pour Pierre Durand
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 70 zones trouvées
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 70 zones de drop disponibles
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-30T04:00:00.000Z","dayDateKey":"2025-07-30","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-30: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","dayDateStr":"2025-07-30","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-30
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-30 pour Pierre Durand
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 09:32:06] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-31 (4)
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 09:32:06] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-31T04:00:00.000Z","dayDateKey":"2025-07-31","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:32:06] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 09:32:07] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:32:07] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:32:07] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:32:07] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:32:07] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:32:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-31: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","dayDateStr":"2025-07-31","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:32:07] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-31
[15/07/2025 09:32:07] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:32:07] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:32:07] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:32:07] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:32:07] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:32:07] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 09:32:07] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (70 zones)
[15/07/2025 09:32:07] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 09:32:07] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 09:32:07] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] 0 grips ajoutés
[15/07/2025 09:32:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-08-01T04:00:00.000Z","dayDateKey":"2025-08-01","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:07] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-08-01 (5)
[15/07/2025 09:32:07] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-31 pour Pierre Durand
[15/07/2025 09:32:07] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-31 validé, shifts existants: 0
[15/07/2025 09:32:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:32:07] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-08-02 (6)
[15/07/2025 09:32:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-08-02T04:00:00.000Z","dayDateKey":"2025-08-02","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:07] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Rendu demandé car 5 shifts créés
[15/07/2025 09:32:07] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 5 attributions appliquées pour la semaine 2025-W30
[15/07/2025 09:32:07] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-08-01 pour Pierre Durand
[15/07/2025 09:32:07] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-08-01 validé, shifts existants: 0
[15/07/2025 09:32:07] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:32:07] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-08-02 ignoré pour attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 09:32:07] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-08-01
[15/07/2025 09:32:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-08-01: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","dayDateStr":"2025-08-01","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:32:07] [FRONTEND] [LOG] ⬅️ Clic sur précédent
[15/07/2025 09:32:07] [FRONTEND] [LOG] 🧭 [navigateWeek] Direction: -1, Offset actuel: 2
[15/07/2025 09:32:07] [FRONTEND] [LOG] 📋 [navigateWeek] Application des attributions pour la nouvelle semaine 2025-W29
[15/07/2025 09:32:07] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] Nettoyage des shifts réguliers existants...
[15/07/2025 09:32:07] [FRONTEND] [LOG] 🗑️ [applyRegularAssignmentsForCurrentWeek] Suppression shift régulier: 59f5df3a-33ef-425c-bfa2-adca818cf94f - 2025-07-21 - assignment: b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:32:07] [FRONTEND] [LOG] 🗑️ [applyRegularAssignmentsForCurrentWeek] Suppression shift régulier: 18fe9cd1-e8a5-44e3-90b3-a856086ce27e - 2025-07-23 - assignment: fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 09:32:07] [FRONTEND] [LOG] 🗑️ [applyRegularAssignmentsForCurrentWeek] Suppression shift régulier: 59f5df3a-33ef-425c-bfa2-adca818cf94f - 2025-07-22 - assignment: b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:32:07] [FRONTEND] [LOG] 🗑️ [applyRegularAssignmentsForCurrentWeek] Suppression shift régulier: 18fe9cd1-e8a5-44e3-90b3-a856086ce27e - 2025-07-24 - assignment: fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 09:32:07] [FRONTEND] [LOG] 🗑️ [applyRegularAssignmentsForCurrentWeek] Suppression shift régulier: 18fe9cd1-e8a5-44e3-90b3-a856086ce27e - 2025-07-25 - assignment: fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 09:32:07] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] 5 shifts réguliers supprimés
[15/07/2025 09:32:07] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Cache invalidé pour forcer la régénération
[15/07/2025 09:32:07] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Application pour semaine 2025-W29
[15/07/2025 09:32:07] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888 a une intersection avec cette semaine (2025-07-23 → ∞) vs (2025-07-20 → 2025-07-26)
[15/07/2025 09:32:07] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution b77227a7-67b7-4feb-b082-890cfeecab1f a une intersection avec cette semaine (2025-07-15 → 2025-07-22) vs (2025-07-20 → 2025-07-26)
[15/07/2025 09:32:07] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 2 attributions actives sur 2 total
[15/07/2025 09:32:07] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"startDate":"2025-07-23","endDate":null,"excludedDates":[]}
[15/07/2025 09:32:07] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888: [{"dateKey":"2025-07-20","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-21","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-22","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-23","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-24","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-07-25","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-07-26","isWeekend":true,"dayOfWeek":6}]
[15/07/2025 09:32:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-20T04:00:00.000Z","dayDateKey":"2025-07-20","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:07] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-20 (0)
[15/07/2025 09:32:07] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:32:07] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-20 ignoré pour attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 09:32:07] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-21 (1)
[15/07/2025 09:32:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:32:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-21T04:00:00.000Z","dayDateKey":"2025-07-21","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-21: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","dayDateStr":"2025-07-21","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:32:07] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-21 avant startDate 2025-07-23 (original: 2025-07-23)
[15/07/2025 09:32:07] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-21 ignoré pour attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 09:32:07] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-22 (2)
[15/07/2025 09:32:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-22T04:00:00.000Z","dayDateKey":"2025-07-22","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:32:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-22: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","dayDateStr":"2025-07-22","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:32:07] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-22 avant startDate 2025-07-23 (original: 2025-07-23)
[15/07/2025 09:32:07] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-22 ignoré pour attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 09:32:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-23T04:00:00.000Z","dayDateKey":"2025-07-23","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:07] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-23 (3)
[15/07/2025 09:32:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:32:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-23: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","dayDateStr":"2025-07-23","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:32:07] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-23
[15/07/2025 09:32:07] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-23 validé, shifts existants: 0
[15/07/2025 09:32:07] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-23 pour Pierre Durand
[15/07/2025 09:32:07] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-24 (4)
[15/07/2025 09:32:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-24: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","dayDateStr":"2025-07-24","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:32:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-24T04:00:00.000Z","dayDateKey":"2025-07-24","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:32:07] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-24
[15/07/2025 09:32:07] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-24 validé, shifts existants: 0
[15/07/2025 09:32:07] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-24 pour Pierre Durand
[15/07/2025 09:32:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:32:07] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-07-25 (5)
[15/07/2025 09:32:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-25T04:00:00.000Z","dayDateKey":"2025-07-25","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-25: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","dayDateStr":"2025-07-25","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:32:07] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-25
[15/07/2025 09:32:07] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-25 validé, shifts existants: 0
[15/07/2025 09:32:07] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-07-26 (6)
[15/07/2025 09:32:07] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-26T04:00:00.000Z","dayDateKey":"2025-07-26","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:07] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-25 pour Pierre Durand
[15/07/2025 09:32:07] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:32:07] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-26 ignoré pour attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 09:32:08] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"b77227a7-67b7-4feb-b082-890cfeecab1f","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"startDate":"2025-07-15","endDate":"2025-07-22","excludedDates":[]}
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-21T04:00:00.000Z","dayDateKey":"2025-07-21","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution b77227a7-67b7-4feb-b082-890cfeecab1f: [{"dateKey":"2025-07-20","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-21","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-22","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-23","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-24","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-07-25","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-07-26","isWeekend":true,"dayOfWeek":6}]
[15/07/2025 09:32:08] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-20 ignoré pour attribution b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:32:08] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-20 (0)
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-20T04:00:00.000Z","dayDateKey":"2025-07-20","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:08] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-21: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","dayDateStr":"2025-07-21","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-22 pour Sophie Leblanc
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-25: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","dayDateStr":"2025-07-25","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:32:08] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-26 ignoré pour attribution b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-26T04:00:00.000Z","dayDateKey":"2025-07-26","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-25T04:00:00.000Z","dayDateKey":"2025-07-25","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:08] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 5 attributions appliquées pour la semaine 2025-W29
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:32:08] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Rendu demandé car 5 shifts créés
[15/07/2025 09:32:08] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-07-25 (5)
[15/07/2025 09:32:08] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-07-26 (6)
[15/07/2025 09:32:08] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-24 ignoré pour attribution b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:32:08] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-25 ignoré pour attribution b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-24: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","dayDateStr":"2025-07-24","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:32:08] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-25 après endDate 2025-07-22 (original: 2025-07-22) (attribution b77227a7-67b7-4feb-b082-890cfeecab1f)
[15/07/2025 09:32:08] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-24 après endDate 2025-07-22 (original: 2025-07-22) (attribution b77227a7-67b7-4feb-b082-890cfeecab1f)
[15/07/2025 09:32:08] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-22 validé, shifts existants: 0
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-22
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-24T04:00:00.000Z","dayDateKey":"2025-07-24","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-22: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","dayDateStr":"2025-07-22","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:32:08] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-24 (4)
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:32:08] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-23 après endDate 2025-07-22 (original: 2025-07-22) (attribution b77227a7-67b7-4feb-b082-890cfeecab1f)
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-22T04:00:00.000Z","dayDateKey":"2025-07-22","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:08] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-23 ignoré pour attribution b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-21 pour Sophie Leblanc
[15/07/2025 09:32:08] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-22 (2)
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-23: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","dayDateStr":"2025-07-23","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:32:08] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-21 validé, shifts existants: 0
[15/07/2025 09:32:08] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-21 (1)
[15/07/2025 09:32:08] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-23 (3)
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-23T04:00:00.000Z","dayDateKey":"2025-07-23","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-21
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] 0 grips ajoutés
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 70 zones trouvées
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 70 zones de drop disponibles
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (70 zones)
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [SETUP] Drag & drop des employés configuré (mode simplifié)
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 09:32:08] [FRONTEND] [WARN] ⚠️ [SETUP] Erreur lors de la destruction: Error: can't access property "Sortable1752586285578", el is null
destroy@http://localhost:5173/node_modules/.vite/deps/sortablejs.js?v=189de042:1812:5
setupDragAndDrop@http://localhost:5173/src/teamCalendarApp.ts:5808:33
attachAllEventListeners@http://localhost:5173/src/teamCalendarApp.ts:5358:10
actualRender@http://localhost:5173/src/teamCalendarApp.ts:2466:10
render/this._debouncedRender<@http://localhost:5173/src/teamCalendarApp.ts:2451:12
setTimeout handler*render@http://localhost:5173/src/teamCalendarApp.ts:2450:29
navigateWeek@http://localhost:5173/src/teamCalendarApp.ts:661:10
setupNavigationListeners/<@http://localhost:5173/src/teamCalendarApp.ts:2432:14
EventListener.handleEvent*setupNavigationListeners@http://localhost:5173/src/teamCalendarApp.ts:2430:15
init@http://localhost:5173/src/teamCalendarApp.ts:1047:10
async*initApp@http://localhost:5173/src/Agenda.tsx:40:31
setTimeout handler*AgendaFullscreen/<@http://localhost:5173/src/Agenda.tsx:50:29
react-stack-bottom-frame@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:17478:20
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
commitHookEffectListMount@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8460:122
commitHookPassiveMountEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8518:60
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10016:42
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
doubleInvokeEffectsOnFiber@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11461:207
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
recursivelyTraverseAndDoubleInvokeEffectsInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11432:78
commitDoubleInvokeEffectsInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11469:55
flushPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11309:41
node_modules/react-dom/cjs/react-dom-client.development.js/commitRoot/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11060:32
performWorkUntilDeadline@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:36:58
EventHandlerNonNull*node_modules/scheduler/cjs/scheduler.development.js/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:156:9
node_modules/scheduler/cjs/scheduler.development.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:266:7
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/scheduler/index.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:277:24
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/react-dom/cjs/react-dom-client.development.js/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:15216:23
node_modules/react-dom/cjs/react-dom-client.development.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18060:7
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/react-dom/client.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18071:24
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18075:16

[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 70 zones trouvées
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 70 zones de drop disponibles
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 09:32:08] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:32:08] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:32:09] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:32:09] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:32:09] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:32:09] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:32:09] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:32:09] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:32:09] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:32:09] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:32:09] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 09:32:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (70 zones)
[15/07/2025 09:32:09] [FRONTEND] [DEBUG] [vite] connected.
[15/07/2025 09:32:10] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[15/07/2025 09:32:10] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[15/07/2025 09:32:10] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[15/07/2025 09:32:10] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[15/07/2025 09:32:10] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[15/07/2025 09:32:10] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[15/07/2025 09:32:10] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[15/07/2025 09:32:10] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[15/07/2025 09:32:10] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[15/07/2025 09:32:10] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[15/07/2025 09:32:10] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[15/07/2025 09:32:10] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[15/07/2025 09:32:10] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[15/07/2025 09:32:10] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[15/07/2025 09:32:10] [FRONTEND] [LOG] - testAssignmentModal() : Tester l'ouverture de la modale
[15/07/2025 09:32:10] [FRONTEND] [LOG] - testButtonListeners() : Tester les listeners des boutons
[15/07/2025 09:32:10] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[15/07/2025 09:32:10] [FRONTEND] [LOG] 🧪 [TEST] Script de test de la modale drag & drop chargé
[15/07/2025 09:32:10] [FRONTEND] [LOG] - diagnoseModalIssues() : Diagnostiquer les problèmes
[15/07/2025 09:32:10] [FRONTEND] [LOG] - forceReconfigureListeners() : Reconfigurer les listeners
[15/07/2025 09:32:10] [FRONTEND] [LOG] ✅ [VALIDATION] Fonctions de validation disponibles:
[15/07/2025 09:32:10] [FRONTEND] [LOG] 🔍 [VALIDATION] Script de validation de la correction modale chargé
[15/07/2025 09:32:10] [FRONTEND] [LOG] - testButtonClicks() : Tester les clics sur les boutons
[15/07/2025 09:32:10] [FRONTEND] [LOG] - validateMainFix() : Valider la correction principale
[15/07/2025 09:32:10] [FRONTEND] [LOG] - validateDOMElements() : Valider les éléments DOM
[15/07/2025 09:32:10] [FRONTEND] [LOG] - validateListeners() : Valider les listeners
[15/07/2025 09:32:10] [FRONTEND] [LOG] - testEndToEnd() : Test end-to-end complet
[15/07/2025 09:32:10] [FRONTEND] [LOG] - runCompleteValidation() : Validation complète
[15/07/2025 09:32:10] [FRONTEND] [LOG] 🧪 [TEST] Script de test pour la correction du fork modal chargé
[15/07/2025 09:32:10] [FRONTEND] [LOG] 🚀 [Agenda] Initialisation TeamCalendarApp...
[15/07/2025 09:32:10] [FRONTEND] [LOG] 🧹 [clearRenderCache] Nettoyage du cache de rendu
[15/07/2025 09:32:10] [FRONTEND] [LOG] 🚀 [init] Initialisation de TeamCalendarApp...
[15/07/2025 09:32:10] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[15/07/2025 09:32:10] [FRONTEND] [LOG] - testDropDateDetection() : Tester la détection de date
[15/07/2025 09:32:10] [FRONTEND] [LOG] - testForkModalWithDate() : Tester le modal avec date
[15/07/2025 09:32:10] [FRONTEND] [LOG] - testCompleteForkFlow() : Tester le flux complet
[15/07/2025 09:32:10] [FRONTEND] [LOG] - diagnoseForkModalIssues() : Diagnostic complet
[15/07/2025 09:32:10] [FRONTEND] [LOG] 🧹 [Agenda] Nettoyage composant Agenda
[15/07/2025 09:32:10] [FRONTEND] [LOG] 🔄 [Agenda] Réinitialisation nécessaire (employés non chargés)
[15/07/2025 09:32:10] [FRONTEND] [LOG] ✅ [clearRenderCache] Cache de rendu nettoyé
[15/07/2025 09:32:10] [FRONTEND] [LOG] 📂 [TeamCalendarApp] Chargement de l'état...
[15/07/2025 09:32:10] [FRONTEND] [LOG] 🔌 [init] Indicateur de connexion initialisé
[15/07/2025 09:32:10] [FRONTEND] [LOG] 🔌 [ConnectionIndicator] Création de l'indicateur de connexion
[15/07/2025 09:32:10] [FRONTEND] [LOG] 🌐 [TeamCalendarApp] Chargement depuis l'API...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT e.*, et.name as template_name 
      FROM employees e 
      LEFT JOIN employee_templates et ON e.template_id = et.id 
      ORDER BY e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT e.*, et.name as template_name \n      FROM employees e \n      LEFT JOIN employee_templates et ON e.template_id = et.id \n      ORDER BY e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:32:10.736Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 55ms: 
      SELECT e.*, et.name as template_name 
      {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 55ms:",
    "\n      SELECT e.*, et.name as template_name \n     ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:32:10.792Z"
}
[15/07/2025 09:32:10] [BACKEND] [INFO] ✅ Requête exécutée en 55ms: 
      SELECT e.*, et.name as template_name 
      {}
[15/07/2025 09:32:10] [FRONTEND] [LOG] 👥 [TeamCalendarApp] 5 employés chargés
[15/07/2025 09:32:10] [FRONTEND] [LOG] 📦 [loadState] Chargement de tous les shifts depuis la base de données...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT s.*, e.name as employee_name, sp.label as post_label
      FROM shifts s
      LEFT JOIN employees e ON s.employee_id = e.id
      LEFT JOIN standard_posts sp ON s.post_id = sp.id
      ORDER BY s.date_key, e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT s.*, e.name as employee_name, sp.label as post_label\n      FROM shifts s\n      LEFT JOIN employees e ON s.employee_id = e.id\n      LEFT JOIN standard_posts sp ON s.post_id = sp.id\n      ORDER BY s.date_key, e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:32:10.824Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 33ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 33ms:",
    "\n      SELECT s.*, e.name as employee_name, sp.lab",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:32:10.857Z"
}
[15/07/2025 09:32:10] [BACKEND] [INFO] ✅ Requête exécutée en 33ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
[15/07/2025 09:32:11] [FRONTEND] [LOG] ✅ [loadState] Shifts organisés dans this.data.schedule
[15/07/2025 09:32:11] [FRONTEND] [LOG] 📅 [loadState] 0 shifts chargés depuis la base de données
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:32:10.999Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 30ms: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 30ms:",
    "SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:32:11.028Z"
}
[15/07/2025 09:32:11] [BACKEND] [INFO] ✅ Requête exécutée en 30ms: SELECT * FROM standard_posts ORDER BY label {}
[15/07/2025 09:32:11] [FRONTEND] [LOG] 🏢 [TeamCalendarApp] 5 postes standards chargés
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:32:11.077Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 45ms: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 45ms:",
    "SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:32:11.122Z"
}
[15/07/2025 09:32:11] [BACKEND] [INFO] ✅ Requête exécutée en 45ms: SELECT * FROM app_settings ORDER BY setting_key {}
[15/07/2025 09:32:11] [FRONTEND] [LOG] ⚙️ [loadState] Paramètres chargés depuis API: {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT 
        ra.id,
        ra.employee_id,
        ra.post_id,
        ra.days_of_week,
        ra.start_date,
        ra.end_date,
        ra.is_active,
        ra.excluded_dates,
        e.name as employee_name,
        sp.label as post_label,
        sp.hours as post_hours,
        sp.type as post_type,
        array_length(ra.days_of_week, 1) as days_count
      FROM regular_assignments ra
      LEFT JOIN employees e ON ra.employee_id = e.id
      LEFT JOIN standard_posts sp ON ra.post_id = sp.id
      WHERE ra.is_active = true
      ORDER BY e.name, sp.label, ra.start_date
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT \n        ra.id,\n        ra.employee_id,\n        ra.post_id,\n        ra.days_of_week,\n        ra.start_date,\n        ra.end_date,\n        ra.is_active,\n        ra.excluded_dates,\n        e.name as employee_name,\n        sp.label as post_label,\n        sp.hours as post_hours,\n        sp.type as post_type,\n        array_length(ra.days_of_week, 1) as days_count\n      FROM regular_assignments ra\n      LEFT JOIN employees e ON ra.employee_id = e.id\n      LEFT JOIN standard_posts sp ON ra.post_id = sp.id\n      WHERE ra.is_active = true\n      ORDER BY e.name, sp.label, ra.start_date\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:32:11.169Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 122ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 122ms:",
    "\n      SELECT \n        ra.id,\n        ra.employee_",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:32:11.290Z"
}
[15/07/2025 09:32:11] [BACKEND] [INFO] ✅ Requête exécutée en 122ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
[15/07/2025 09:32:11] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","employee_id":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","post_id":"ef3246ff-8398-4f11-a498-648d207563a0","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-23T04:00:00.000Z","end_date":null,"excluded_dates":[]}
[15/07/2025 09:32:11] [FRONTEND] [LOG] ✅ [fixInvalidAssignmentDates] Aucune date invalide trouvée
[15/07/2025 09:32:11] [FRONTEND] [LOG] 📋 [loadState] 2/2 assignations régulières valides chargées et normalisées
[15/07/2025 09:32:11] [FRONTEND] [LOG] 📋 [loadState] Assignations régulières chargées (application différée)
[15/07/2025 09:32:11] [FRONTEND] [LOG] 🔧 [fixInvalidAssignmentDates] Vérification des dates invalides...
[15/07/2025 09:32:11] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"b77227a7-67b7-4feb-b082-890cfeecab1f","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-15","endDate":"2025-07-22","excludedDates":[]}
[15/07/2025 09:32:11] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"isLimited":false,"startDate":"2025-07-23","endDate":null,"excludedDates":[]}
[15/07/2025 09:32:11] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"b77227a7-67b7-4feb-b082-890cfeecab1f","employee_id":"59f5df3a-33ef-425c-bfa2-adca818cf94f","post_id":"ef3246ff-8398-4f11-a498-648d207563a0","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-15T04:00:00.000Z","end_date":"2025-07-22T04:00:00.000Z","excluded_dates":[]}
[15/07/2025 09:32:11] [FRONTEND] [LOG] ✅ [init] Icône paramètres trouvée: [Objet non sérialisable]
[15/07/2025 09:32:11] [BACKEND] [INFO] 📋 [GET /api/employee-order] Récupération ordre des employés
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'
         {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'\n        ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:32:11.400Z"
}
[15/07/2025 09:32:11] [FRONTEND] [LOG] ✅ [cleanupInvalidAssignmentIds] Aucun assignment_id invalide détecté
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📋 [GET /api/employee-order] Récupération ordre des employés
  Data: {
  "originalArgs": [
    "📋 [GET /api/employee-order] Récupération ordre des employés"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:32:11.400Z"
}
[15/07/2025 09:32:11] [FRONTEND] [LOG] 🧹 [cleanupInvalidAssignmentIds] Nettoyage des assignment_id invalides...
[15/07/2025 09:32:11] [FRONTEND] [LOG] ✅ [cleanupDuplicateRegularShifts] Aucun doublon détecté
[15/07/2025 09:32:11] [FRONTEND] [LOG] 🧹 [cleanupDuplicateRegularShifts] Nettoyage des doublons de shifts réguliers...
[15/07/2025 09:32:11] [FRONTEND] [LOG] ✅ [loadState] État chargé depuis l'API avec succès
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 42ms: 
            SELECT setting_value FROM app_setting {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 42ms:",
    "\n            SELECT setting_value FROM app_setting",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:32:11.443Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📋 [GET /api/employee-order] Aucun ordre personnalisé trouvé
  Data: {
  "originalArgs": [
    "📋 [GET /api/employee-order] Aucun ordre personnalisé trouvé"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T13:32:11.443Z"
}
[15/07/2025 09:32:11] [FRONTEND] [LOG] 🔍 [init] Recherche du bouton des paramètres dans le DOM
[15/07/2025 09:32:11] [FRONTEND] [LOG] 🔍 [init] Recherche du bouton des paramètres header dans le DOM
[15/07/2025 09:32:11] [FRONTEND] [LOG] 🔍 [init] Attachement du listener click sur le bouton des paramètres
[15/07/2025 09:32:11] [FRONTEND] [LOG] 🔍 [init] Bouton des paramètres header trouvé ? true
[15/07/2025 09:32:11] [FRONTEND] [LOG] 🔍 [init] Bouton des paramètres trouvé ? true
[15/07/2025 09:32:11] [FRONTEND] [LOG] 🔍 [init] Attachement du listener click sur le bouton des paramètres header
[15/07/2025 09:32:11] [FRONTEND] [LOG] ✅ Boutons de navigation configurés
[15/07/2025 09:32:11] [FRONTEND] [LOG] 🎯 [setupViewNavigationListeners] Configuration des boutons de navigation...
[15/07/2025 09:32:11] [FRONTEND] [LOG] ✅ [setupNavigationListeners] Écouteurs de navigation attachés
[15/07/2025 09:32:11] [FRONTEND] [LOG] 🔧 [ModalManager] Configuration des gestionnaires de modal
[15/07/2025 09:32:11] [FRONTEND] [LOG] ✅ [ModalManager] Gestionnaires de modal configurés
[15/07/2025 09:32:11] [FRONTEND] [LOG] 🔧 [setupGeneralSettings] Configuration des paramètres généraux
[15/07/2025 09:32:11] [FRONTEND] [LOG] 🔧 [setupGeneralSettings] Création du conteneur de fuseau horaire
[15/07/2025 09:32:11] [FRONTEND] [LOG] 📋 [loadEmployeeOrder] Chargement ordre des employés
[15/07/2025 09:32:11] [FRONTEND] [LOG] ✅ [setupGlobalEscapeHandler] Gestionnaire ESC global configuré
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[15/07/2025 09:32:12] [FRONTEND] [WARN] ⚠️ [loadEmployeeOrder] Réponse API sans tableau employeeOrder
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🔧 [init] Forçage du rendu initial...
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Matin: [1, 2, 3, 4, 5]
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 60 zones trouvées
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 60 zones de drop disponibles
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Nuit: [1, 2, 3, 4, 5]
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Soir: [1, 2, 3, 4, 5]
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE1: [0, 6]
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE2: [0, 6]
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (60 zones)
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🔧 [init] Configuration du drag & drop des postes...
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 60 zones trouvées
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 60 zones de drop disponibles
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneur available-posts-container créé
[15/07/2025 09:32:12] [FRONTEND] [WARN] ⚠️ [renderUnifiedCalendar] Conteneur available-posts-container non trouvé, création...
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (60 zones)
[15/07/2025 09:32:12] [FRONTEND] [LOG] ℹ️ [setupEmployeeDragDrop] Fonction désactivée - Utiliser setupDragAndDrop
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🔍 [diagnoseRegularAssignmentGrips] Diagnostic des grips d'attributions régulières
[15/07/2025 09:32:12] [FRONTEND] [LOG] 📊 [diagnoseRegularAssignmentGrips] Statistiques: {"totalShifts":4,"regularShifts":4,"regularAssignments":2}
[15/07/2025 09:32:12] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] État de la modale: {"hidden":true,"classes":["hidden","fixed","inset-0","bg-black/40","backdrop-blur-sm","z-50","flex","items-center","justify-center","p-4"],"style":""}
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🎯 [diagnoseRegularAssignmentGrips] Grips trouvés dans le DOM: 4
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [addEmergencyFixButton] Bouton de correction ajouté
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🔧 [addEmergencyFixButton] Ajout du bouton de correction...
[15/07/2025 09:32:12] [FRONTEND] [LOG] 📋 [init] Application des attributions régulières après initialisation
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] Nettoyage des shifts réguliers existants...
[15/07/2025 09:32:12] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] 0 shifts réguliers supprimés
[15/07/2025 09:32:12] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Application pour semaine 2025-W28
[15/07/2025 09:32:12] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888 commence après cette semaine (startDate: 2025-07-23, semaine se termine: 2025-07-19)
[15/07/2025 09:32:12] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution b77227a7-67b7-4feb-b082-890cfeecab1f a une intersection avec cette semaine (2025-07-15 → 2025-07-22) vs (2025-07-13 → 2025-07-19)
[15/07/2025 09:32:13] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 1 attributions actives sur 2 total
[15/07/2025 09:32:13] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"b77227a7-67b7-4feb-b082-890cfeecab1f","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"startDate":"2025-07-15","endDate":"2025-07-22","excludedDates":[]}
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution b77227a7-67b7-4feb-b082-890cfeecab1f: [{"dateKey":"2025-07-13","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-14","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-15","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-16","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-17","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-07-18","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-07-19","isWeekend":true,"dayOfWeek":6}]
[15/07/2025 09:32:13] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-13 (0)
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-13T04:00:00.000Z","dayDateKey":"2025-07-13","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:13] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:32:13] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-13 ignoré pour attribution b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:32:13] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-14 (1)
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-14T04:00:00.000Z","dayDateKey":"2025-07-14","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-14: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","dayDateStr":"2025-07-14","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:32:13] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-14 avant startDate 2025-07-15 (original: 2025-07-15)
[15/07/2025 09:32:13] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-14 ignoré pour attribution b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:32:13] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-15 (2)
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-15T04:00:00.000Z","dayDateKey":"2025-07-15","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-15: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","dayDateStr":"2025-07-15","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:32:13] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-15 validé, shifts existants: 0
[15/07/2025 09:32:13] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-15
[15/07/2025 09:32:13] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-16 (3)
[15/07/2025 09:32:13] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-15 pour Sophie Leblanc
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-16T04:00:00.000Z","dayDateKey":"2025-07-16","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-16: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","dayDateStr":"2025-07-16","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:32:13] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-16
[15/07/2025 09:32:13] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-16 validé, shifts existants: 0
[15/07/2025 09:32:13] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-16 pour Sophie Leblanc
[15/07/2025 09:32:13] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-17 (4)
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-17T04:00:00.000Z","dayDateKey":"2025-07-17","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-17: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","dayDateStr":"2025-07-17","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:32:13] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-17
[15/07/2025 09:32:13] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-17 validé, shifts existants: 0
[15/07/2025 09:32:13] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-17 pour Sophie Leblanc
[15/07/2025 09:32:13] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-07-18 (5)
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-18T04:00:00.000Z","dayDateKey":"2025-07-18","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation automatique...
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation complète...
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation de la correction principale...
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-18: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","dayDateStr":"2025-07-18","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:32:13] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-18
[15/07/2025 09:32:13] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-18 pour Sophie Leblanc
[15/07/2025 09:32:13] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-18 validé, shifts existants: 0
[15/07/2025 09:32:13] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-07-19 (6)
[15/07/2025 09:32:13] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-19T04:00:00.000Z","dayDateKey":"2025-07-19","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Rendu demandé car 4 shifts créés
[15/07/2025 09:32:13] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-19 ignoré pour attribution b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:32:13] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 4 attributions appliquées pour la semaine 2025-W28
[15/07/2025 09:32:13] [FRONTEND] [LOG] ✅ [init] Initialisation terminée avec succès
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🎉 [Agenda] Application fullscreen initialisée avec succès
[15/07/2025 09:32:13] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] 0 grips ajoutés
[15/07/2025 09:32:13] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:32:13] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[15/07/2025 09:32:13] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 09:32:13] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 09:32:13] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 09:32:13] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:32:13] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 09:32:13] [FRONTEND] [LOG] ✅ [SETUP] Drag & drop des employés configuré (mode simplifié)
[15/07/2025 09:32:13] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 09:32:13] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (68 zones)
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 68 zones de drop disponibles
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 09:32:13] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 09:32:13] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 09:32:13] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 09:32:13] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 09:32:13] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:32:13] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:32:13] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (68 zones)
[15/07/2025 09:32:13] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 4: {"employee":"Sophie Leblanc","date":"2025-07-18","assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","text":"08:00-16:00"}
[15/07/2025 09:32:13] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 3: {"employee":"Sophie Leblanc","date":"2025-07-17","assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","text":"08:00-16:00"}
[15/07/2025 09:32:13] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 2: {"employee":"Sophie Leblanc","date":"2025-07-16","assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","text":"08:00-16:00"}
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 09:32:13] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 1: {"employee":"Sophie Leblanc","date":"2025-07-15","assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","text":"08:00-16:00"}
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic des problèmes de modale...
[15/07/2025 09:32:13] [FRONTEND] [LOG] ✅ [VALIDATION] ModalManager.app initialisé
[15/07/2025 09:32:13] [FRONTEND] [LOG] ✅ [VALIDATION] ModalManager.setupAssignmentContextModal disponible
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des éléments DOM...
[15/07/2025 09:32:13] [FRONTEND] [LOG] ✅ [VALIDATION] handleAssignmentContextConfirm disponible
[15/07/2025 09:32:13] [FRONTEND] [LOG] 📋 [VALIDATION] Éléments DOM: {"modal":true,"closeBtn":true,"cancelBtn":true,"confirmBtn":true,"radioButtons":true}
[15/07/2025 09:32:13] [FRONTEND] [LOG] ✅ [VALIDATION] Tous les éléments DOM sont présents
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des listeners...
[15/07/2025 09:32:13] [FRONTEND] [LOG] ✅ [VALIDATION] setupAssignmentContextModal exécuté sans erreur
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🔧 [setupAssignmentContextModal] Configuration du modal contextuel...
[15/07/2025 09:32:13] [FRONTEND] [LOG] ✅ [setupAssignmentContextModal] Modal contextuel configuré avec listeners nettoyés
[15/07/2025 09:32:13] [FRONTEND] [LOG] ➡️ Clic sur suivant
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🧭 [navigateWeek] Direction: 1, Offset actuel: 0
[15/07/2025 09:32:13] [FRONTEND] [LOG] 📊 [VALIDATION] Résultats de validation: {"mainFix":true,"domElements":true,"listeners":true}
[15/07/2025 09:32:13] [FRONTEND] [LOG] ✅ [VALIDATION] La correction de la modale devrait maintenant fonctionner
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🎉 [VALIDATION] Toutes les validations sont passées avec succès !
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🧪 [VALIDATION] Pour tester pratiquement:
[15/07/2025 09:32:13] [FRONTEND] [LOG] 1. Effectuez un drag & drop d'un poste vers un employé
[15/07/2025 09:32:13] [FRONTEND] [LOG] 2. Vérifiez que la modale s'ouvre
[15/07/2025 09:32:13] [FRONTEND] [LOG] 3. Cliquez sur "Confirmer" et vérifiez les logs
[15/07/2025 09:32:13] [FRONTEND] [LOG] 4. Ou utilisez testButtonClicks() pour un test automatique
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[15/07/2025 09:32:13] [FRONTEND] [LOG] 📋 [navigateWeek] Application des attributions pour la nouvelle semaine 2025-W29
[15/07/2025 09:32:13] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic complet du fork modal...
[15/07/2025 09:32:13] [FRONTEND] [LOG] 📊 [DIAGNOSTIC] Résultats: {"teamCalendarApp":true,"detectDropDateFunction":true,"showConfirmationMenuFunction":true,"handleRegularAssignmentDropFunction":true,"regularAssignments":2,"employees":5}
[15/07/2025 09:32:14] [FRONTEND] [LOG] ✅ [DIAGNOSTIC] Toutes les fonctions nécessaires sont disponibles
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] Nettoyage des shifts réguliers existants...
[15/07/2025 09:32:14] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:32:14] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] 0 shifts réguliers supprimés
[15/07/2025 09:32:14] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Cache invalidé pour forcer la régénération
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[15/07/2025 09:32:14] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 09:32:14] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 68 zones trouvées
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 09:32:14] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Application pour semaine 2025-W29
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 68 zones de drop disponibles
[15/07/2025 09:32:14] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888 a une intersection avec cette semaine (2025-07-23 → ∞) vs (2025-07-20 → 2025-07-26)
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 09:32:14] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 09:32:14] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 09:32:14] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution b77227a7-67b7-4feb-b082-890cfeecab1f a une intersection avec cette semaine (2025-07-15 → 2025-07-22) vs (2025-07-20 → 2025-07-26)
[15/07/2025 09:32:14] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 2 attributions actives sur 2 total
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 09:32:14] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"startDate":"2025-07-23","endDate":null,"excludedDates":[]}
[15/07/2025 09:32:14] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888: [{"dateKey":"2025-07-20","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-21","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-22","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-23","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-24","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-07-25","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-07-26","isWeekend":true,"dayOfWeek":6}]
[15/07/2025 09:32:14] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 09:32:14] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 09:32:14] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-20 (0)
[15/07/2025 09:32:14] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-20T04:00:00.000Z","dayDateKey":"2025-07-20","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:14] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:32:14] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:32:14] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:32:14] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 09:32:14] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:32:14] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:32:14] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-20 ignoré pour attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 09:32:14] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-21 (1)
[15/07/2025 09:32:14] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:32:14] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-21T04:00:00.000Z","dayDateKey":"2025-07-21","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-21: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","dayDateStr":"2025-07-21","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:32:14] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 09:32:14] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 68 zones trouvées
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 09:32:14] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-21 avant startDate 2025-07-23 (original: 2025-07-23)
[15/07/2025 09:32:14] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-21 ignoré pour attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-22T04:00:00.000Z","dayDateKey":"2025-07-22","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:14] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-22 (2)
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:32:14] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-22 ignoré pour attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 09:32:14] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-22 avant startDate 2025-07-23 (original: 2025-07-23)
[15/07/2025 09:32:14] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-23 (3)
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-22: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","dayDateStr":"2025-07-22","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-23T04:00:00.000Z","dayDateKey":"2025-07-23","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-23: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","dayDateStr":"2025-07-23","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 09:32:14] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-23
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 09:32:14] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-23 validé, shifts existants: 0
[15/07/2025 09:32:14] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-23 pour Pierre Durand
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-24T04:00:00.000Z","dayDateKey":"2025-07-24","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 09:32:14] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-24 (4)
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-24: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","dayDateStr":"2025-07-24","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:32:14] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-24
[15/07/2025 09:32:14] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-24 validé, shifts existants: 0
[15/07/2025 09:32:14] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-24 pour Pierre Durand
[15/07/2025 09:32:14] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-07-25 (5)
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-25T04:00:00.000Z","dayDateKey":"2025-07-25","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:14] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-25: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","dayDateStr":"2025-07-25","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:32:14] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-25
[15/07/2025 09:32:14] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-25 validé, shifts existants: 0
[15/07/2025 09:32:14] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-25 pour Pierre Durand
[15/07/2025 09:32:14] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-07-26 (6)
[15/07/2025 09:32:14] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-26T04:00:00.000Z","dayDateKey":"2025-07-26","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:14] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-26 ignoré pour attribution fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution b77227a7-67b7-4feb-b082-890cfeecab1f: [{"dateKey":"2025-07-20","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-21","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-22","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-23","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-24","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-07-25","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-07-26","isWeekend":true,"dayOfWeek":6}]
[15/07/2025 09:32:14] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-20 (0)
[15/07/2025 09:32:14] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"b77227a7-67b7-4feb-b082-890cfeecab1f","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"startDate":"2025-07-15","endDate":"2025-07-22","excludedDates":[]}
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-20T04:00:00.000Z","dayDateKey":"2025-07-20","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:14] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-20 ignoré pour attribution b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:32:14] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:32:14] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-21 (1)
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-21T04:00:00.000Z","dayDateKey":"2025-07-21","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:14] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-21 validé, shifts existants: 0
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-21: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","dayDateStr":"2025-07-21","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:32:14] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-21
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:32:14] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-22 (2)
[15/07/2025 09:32:14] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-21 pour Sophie Leblanc
[15/07/2025 09:32:14] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-22 validé, shifts existants: 0
[15/07/2025 09:32:14] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-07-25 (5)
[15/07/2025 09:32:14] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-22
[15/07/2025 09:32:14] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-24 ignoré pour attribution b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:32:14] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-24 après endDate 2025-07-22 (original: 2025-07-22) (attribution b77227a7-67b7-4feb-b082-890cfeecab1f)
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-25T04:00:00.000Z","dayDateKey":"2025-07-25","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-22: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","dayDateStr":"2025-07-22","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-24: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","dayDateStr":"2025-07-24","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-25: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","dayDateStr":"2025-07-25","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:32:14] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-07-26 (6)
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-22T04:00:00.000Z","dayDateKey":"2025-07-22","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:14] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Matin → 2025-07-22 pour Sophie Leblanc
[15/07/2025 09:32:14] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-26 ignoré pour attribution b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:32:14] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-26T04:00:00.000Z","dayDateKey":"2025-07-26","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-23T04:00:00.000Z","dayDateKey":"2025-07-23","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:14] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-25 ignoré pour attribution b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:32:14] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-23 (3)
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Rendu demandé car 5 shifts créés
[15/07/2025 09:32:14] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 5 attributions appliquées pour la semaine 2025-W29
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:32:14] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-25 après endDate 2025-07-22 (original: 2025-07-22) (attribution b77227a7-67b7-4feb-b082-890cfeecab1f)
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[15/07/2025 09:32:14] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-24 (4)
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-23: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","dayDateStr":"2025-07-23","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"b77227a7-67b7-4feb-b082-890cfeecab1f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","postLabel":"Poste Matin","postCategory":null,"dayDate":"2025-07-24T04:00:00.000Z","dayDateKey":"2025-07-24","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[15/07/2025 09:32:14] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-23 après endDate 2025-07-22 (original: 2025-07-22) (attribution b77227a7-67b7-4feb-b082-890cfeecab1f)
[15/07/2025 09:32:14] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-23 ignoré pour attribution b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 09:32:14] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 09:32:14] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:32:14] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 70 zones de drop disponibles
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 70 zones trouvées
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] 0 grips ajoutés
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] 0 grips ajoutés
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[15/07/2025 09:32:15] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] TeamCalendarApp: {"exists":true,"hasModalManager":true,"hasOpenFunction":true,"hasHandleFunction":true}
[15/07/2025 09:32:15] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Éléments DOM: {"modal":true,"closeBtn":true,"cancelBtn":true,"confirmBtn":true,"radioButtons":4}
[15/07/2025 09:32:15] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Données contextuelles: {"employees":5,"posts":5}
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière fd058f0f-a569-42a7-90c2-d1d0f15bd888
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: ef3246ff-8398-4f11-a498-648d207563a0 -> "08:00-16:00"
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière b77227a7-67b7-4feb-b082-890cfeecab1f
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (70 zones)
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [SETUP] Drag & drop des employés configuré (mode simplifié)
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 09:32:15] [FRONTEND] [WARN] ⚠️ [SETUP] Erreur lors de la destruction: Error: can't access property "Sortable1752586330146", el is null
destroy@http://localhost:5173/node_modules/.vite/deps/sortablejs.js?v=189de042:1812:5
setupDragAndDrop@http://localhost:5173/src/teamCalendarApp.ts:5808:33
attachAllEventListeners@http://localhost:5173/src/teamCalendarApp.ts:5358:10
actualRender@http://localhost:5173/src/teamCalendarApp.ts:2466:10
render/this._debouncedRender<@http://localhost:5173/src/teamCalendarApp.ts:2451:12
setTimeout handler*render@http://localhost:5173/src/teamCalendarApp.ts:2450:29
navigateWeek@http://localhost:5173/src/teamCalendarApp.ts:661:10
setupNavigationListeners/<@http://localhost:5173/src/teamCalendarApp.ts:2436:14
EventListener.handleEvent*setupNavigationListeners@http://localhost:5173/src/teamCalendarApp.ts:2434:15
init@http://localhost:5173/src/teamCalendarApp.ts:1047:10
async*initApp@http://localhost:5173/src/Agenda.tsx:40:31
setTimeout handler*AgendaFullscreen/<@http://localhost:5173/src/Agenda.tsx:50:29
react-stack-bottom-frame@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:17478:20
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
commitHookEffectListMount@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8460:122
commitHookPassiveMountEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8518:60
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10016:42
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
doubleInvokeEffectsOnFiber@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11461:207
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
recursivelyTraverseAndDoubleInvokeEffectsInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11432:78
commitDoubleInvokeEffectsInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11469:55
flushPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11309:41
node_modules/react-dom/cjs/react-dom-client.development.js/commitRoot/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11060:32
performWorkUntilDeadline@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:36:58
EventHandlerNonNull*node_modules/scheduler/cjs/scheduler.development.js/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:156:9
node_modules/scheduler/cjs/scheduler.development.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:266:7
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/scheduler/index.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:277:24
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/react-dom/cjs/react-dom-client.development.js/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:15216:23
node_modules/react-dom/cjs/react-dom-client.development.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18060:7
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/react-dom/client.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18071:24
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18075:16

[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 70 zones trouvées
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 70 zones de drop disponibles
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b configuré comme draggable
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste d097722d-b282-4ff6-8795-4cbdb4f2f39b
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste f109c8e2-a9b0-4dbe-8078-4ae84177df4b
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste ef3246ff-8398-4f11-a498-648d207563a0 configuré comme draggable
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste ef3246ff-8398-4f11-a498-648d207563a0
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 30d2db36-35da-4e70-a638-958e1f7fb3db configuré comme draggable
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 30d2db36-35da-4e70-a638-958e1f7fb3db
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c configuré comme draggable
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste a7b9f370-ffc1-4a3d-9525-e25754c83e3c
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste d097722d-b282-4ff6-8795-4cbdb4f2f39b configuré comme draggable
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (70 zones)
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 09:32:15] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 09:32:15] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées