[14/07/2025 15:21:44] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[14/07/2025 15:21:44] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[14/07/2025 15:21:44] [FRONTEND] [LOG] [LogsPage] Fermeture de la connexion SSE.
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:45.336Z"
}
[14/07/2025 15:21:44] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[14/07/2025 15:21:44] [BACKEND] [INFO] [LOGS] Client connecté: 1752520905334
[14/07/2025 15:15:30] [FRONTEND] [LOG] 🔍 [diagnoseRegularAssignmentGrips] Diagnostic des grips d'attributions régulières
[14/07/2025 15:15:30] [FRONTEND] [LOG] ⚠️ [diagnoseRegularAssignmentGrips] Aucun shift régulier trouvé - Ré-application des attributions...
[14/07/2025 15:15:30] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[14/07/2025 15:15:30] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[14/07/2025 15:15:34] [FRONTEND] [LOG] ➡️ Clic sur suivant
[14/07/2025 15:15:34] [FRONTEND] [LOG] 🧭 [navigateWeek] Direction: 1, Offset actuel: 0
[14/07/2025 15:15:34] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[14/07/2025 15:15:34] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[14/07/2025 15:15:34] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[14/07/2025 15:15:34] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[14/07/2025 15:15:34] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[14/07/2025 15:15:34] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[14/07/2025 15:15:34] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[14/07/2025 15:15:34] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 60 zones trouvées
[14/07/2025 15:15:34] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 60 zones de drop disponibles
[14/07/2025 15:15:34] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Matin: [1, 2, 3, 4, 5]
[14/07/2025 15:15:34] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste e0332f5d-23e2-40fa-bf2d-d9271f03100a configuré comme draggable
[14/07/2025 15:15:34] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[14/07/2025 15:15:34] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste e0332f5d-23e2-40fa-bf2d-d9271f03100a
[14/07/2025 15:15:35] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Nuit: [1, 2, 3, 4, 5]
[14/07/2025 15:15:35] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Soir: [1, 2, 3, 4, 5]
[14/07/2025 15:15:35] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 9ebe6752-7cf6-47de-bf62-8eb912bd439d configuré comme draggable
[14/07/2025 15:15:35] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 9ebe6752-7cf6-47de-bf62-8eb912bd439d
[14/07/2025 15:15:35] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7224fea3-1bc9-4ff2-ab34-35cb0cf19f3e configuré comme draggable
[14/07/2025 15:15:35] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7224fea3-1bc9-4ff2-ab34-35cb0cf19f3e
[14/07/2025 15:15:35] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE1: [0, 6]
[14/07/2025 15:15:35] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 5480db79-77a5-4f23-aa37-614936e8514b configuré comme draggable
[14/07/2025 15:15:35] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 5480db79-77a5-4f23-aa37-614936e8514b
[14/07/2025 15:15:35] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE2: [0, 6]
[14/07/2025 15:15:35] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 1ed512ff-9b54-4a63-90da-790b0594198b configuré comme draggable
[14/07/2025 15:15:35] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 1ed512ff-9b54-4a63-90da-790b0594198b
[14/07/2025 15:15:35] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste e0332f5d-23e2-40fa-bf2d-d9271f03100a configuré comme draggable
[14/07/2025 15:15:35] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 9ebe6752-7cf6-47de-bf62-8eb912bd439d configuré comme draggable
[14/07/2025 15:15:35] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste e0332f5d-23e2-40fa-bf2d-d9271f03100a
[14/07/2025 15:15:35] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 9ebe6752-7cf6-47de-bf62-8eb912bd439d
[14/07/2025 15:15:35] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7224fea3-1bc9-4ff2-ab34-35cb0cf19f3e configuré comme draggable
[14/07/2025 15:15:35] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 5480db79-77a5-4f23-aa37-614936e8514b configuré comme draggable
[14/07/2025 15:15:35] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 5480db79-77a5-4f23-aa37-614936e8514b
[14/07/2025 15:15:35] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7224fea3-1bc9-4ff2-ab34-35cb0cf19f3e
[14/07/2025 15:15:35] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 1ed512ff-9b54-4a63-90da-790b0594198b configuré comme draggable
[14/07/2025 15:15:35] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 1ed512ff-9b54-4a63-90da-790b0594198b
[14/07/2025 15:15:35] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[14/07/2025 15:15:35] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[14/07/2025 15:15:35] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[14/07/2025 15:15:35] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[14/07/2025 15:15:35] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[14/07/2025 15:15:35] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[14/07/2025 15:15:35] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[14/07/2025 15:15:35] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[14/07/2025 15:15:35] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (60 zones)
[14/07/2025 15:15:35] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[14/07/2025 15:15:35] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[14/07/2025 15:15:35] [FRONTEND] [LOG] ⚠️ [setupPostDragDrop] Drag & drop déjà initialisé, ignoré
[14/07/2025 15:15:35] [FRONTEND] [LOG] ✅ [SETUP] Drag & drop des employés configuré (mode simplifié)
[14/07/2025 15:15:35] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] 0 grips ajoutés
[14/07/2025 15:15:35] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[14/07/2025 15:15:35] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[14/07/2025 15:15:36] [FRONTEND] [LOG] ⬅️ Clic sur précédent
[14/07/2025 15:15:36] [FRONTEND] [LOG] 🧭 [navigateWeek] Direction: -1, Offset actuel: 1
[14/07/2025 15:15:37] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[14/07/2025 15:15:37] [FRONTEND] [WARN] ⚠️ [renderUnifiedCalendar] Conteneur available-posts-container non trouvé, création...
[14/07/2025 15:15:37] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[14/07/2025 15:15:37] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneur available-posts-container créé
[14/07/2025 15:15:37] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[14/07/2025 15:15:37] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[14/07/2025 15:15:37] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[14/07/2025 15:15:37] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[14/07/2025 15:15:37] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[14/07/2025 15:15:37] [FRONTEND] [LOG] ⚠️ [setupPostDragDrop] Drag & drop déjà initialisé, ignoré
[14/07/2025 15:15:37] [FRONTEND] [WARN] ⚠️ [SETUP] Erreur lors de la destruction: Error: can't access property "Sortable1752520529943", el is null
destroy@http://localhost:5173/node_modules/.vite/deps/sortablejs.js?v=189de042:1812:5
setupDragAndDrop@http://localhost:5173/src/teamCalendarApp.ts:5805:33
attachAllEventListeners@http://localhost:5173/src/teamCalendarApp.ts:5355:10
actualRender@http://localhost:5173/src/teamCalendarApp.ts:2463:10
render/this._debouncedRender<@http://localhost:5173/src/teamCalendarApp.ts:2448:12

[14/07/2025 15:15:37] [FRONTEND] [LOG] ✅ [SETUP] Drag & drop des employés configuré (mode simplifié)
[14/07/2025 15:15:37] [FRONTEND] [LOG] ⚠️ [setupPostDragDrop] Drag & drop déjà initialisé, ignoré
[14/07/2025 15:15:37] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[14/07/2025 15:15:37] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] 0 grips ajoutés
[14/07/2025 15:15:37] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[14/07/2025 15:17:14] [FRONTEND] [DEBUG] [vite] hot updated: /src/Agenda.tsx
[14/07/2025 15:17:14] [FRONTEND] [DEBUG] [vite] hot updated: /src/index.css
[14/07/2025 15:17:14] [FRONTEND] [DEBUG] [vite] hot updated: /src/index.css
[14/07/2025 15:17:14] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:17:15.346Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:17:14] [FRONTEND] [DEBUG] [vite] hot updated: /src/Agenda.tsx
[14/07/2025 15:17:14] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:17:15.447Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:17:14] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:17:15.445Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:17:14] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:17:15.448Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:17:14] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[14/07/2025 15:17:14] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:17:15.305Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:17:14] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:17:15.449Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:17:14] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:17:15.291Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:17:14] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:17:15.318Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:17:33] [FRONTEND] [DEBUG] [vite] hot updated: /src/index.css
[14/07/2025 15:17:33] [FRONTEND] [DEBUG] [vite] hot updated: /src/Agenda.tsx
[14/07/2025 15:17:33] [FRONTEND] [DEBUG] [vite] hot updated: /src/Agenda.tsx
[14/07/2025 15:17:33] [FRONTEND] [DEBUG] [vite] hot updated: /src/index.css
[14/07/2025 15:17:33] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:17:34.538Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:17:33] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:17:34.489Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:17:33] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:17:34.634Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:17:33] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:17:34.636Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:17:33] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:17:34.487Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:17:33] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:17:34.637Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:17:33] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[14/07/2025 15:17:33] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:17:34.655Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:17:33] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:17:34.492Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:17:42] [FRONTEND] [DEBUG] [vite] hot updated: /src/index.css
[14/07/2025 15:17:42] [FRONTEND] [DEBUG] [vite] hot updated: /src/Agenda.tsx
[14/07/2025 15:17:42] [FRONTEND] [DEBUG] [vite] hot updated: /src/index.css
[14/07/2025 15:17:42] [FRONTEND] [DEBUG] [vite] hot updated: /src/Agenda.tsx
[14/07/2025 15:17:50] [FRONTEND] [DEBUG] [vite] hot updated: /src/Agenda.tsx
[14/07/2025 15:17:50] [FRONTEND] [DEBUG] [vite] hot updated: /src/index.css
[14/07/2025 15:17:50] [FRONTEND] [DEBUG] [vite] hot updated: /src/index.css
[14/07/2025 15:17:50] [FRONTEND] [DEBUG] [vite] hot updated: /src/Agenda.tsx
[14/07/2025 15:18:03] [FRONTEND] [DEBUG] [vite] hot updated: /src/index.css
[14/07/2025 15:18:03] [FRONTEND] [DEBUG] [vite] hot updated: /src/Agenda.tsx
[14/07/2025 15:18:03] [FRONTEND] [DEBUG] [vite] hot updated: /src/index.css
[14/07/2025 15:18:03] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:18:03.979Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:18:03] [FRONTEND] [DEBUG] [vite] hot updated: /src/Agenda.tsx
[14/07/2025 15:18:03] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:18:04.072Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:18:03] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:18:03.925Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:18:03] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:18:04.089Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:18:03] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:18:04.082Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:18:03] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:18:03.928Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:18:03] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[14/07/2025 15:18:03] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:18:04.117Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:18:03] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:18:03.980Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:19:12] [FRONTEND] [DEBUG] [vite] (message répété, suppression des suivants) hot updated: /src/Agenda.tsx
[14/07/2025 15:19:12] [FRONTEND] [DEBUG] [vite] (message répété, suppression des suivants) hot updated: /src/index.css
[14/07/2025 15:19:12] [FRONTEND] [DEBUG] [vite] (message répété, suppression des suivants) hot updated: /src/Agenda.tsx
[14/07/2025 15:19:12] [FRONTEND] [DEBUG] [vite] (message répété, suppression des suivants) hot updated: /src/index.css
[14/07/2025 15:19:12] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:19:13.075Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:19:12] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:19:12.938Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:19:12] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:19:12.952Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:19:12] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[14/07/2025 15:19:12] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:19:12.945Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:19:12] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:19:13.088Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:19:12] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:19:13.089Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:19:12] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:19:13.089Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:19:12] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:19:12.965Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:19:12] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:19:13.094Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:19:12] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[14/07/2025 15:19:12] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:19:13.216Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:21:38] [BACKEND] [INFO] [LOGGER] [query] Executing query: CREATE TABLE IF NOT EXISTS logs (
  id SERIAL PRIMARY KEY,
  session_id VARCHAR(255) NOT NULL,
  timestamp TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
  source VARCHAR(50) NOT NULL,
  level VARCHAR(50) NOT NULL,
  message TEXT NOT NULL,
  data JSONB,
  priority INTEGER DEFAULT 0
) {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:38.846Z",
  "originalArgs": [
    "[query] Executing query: CREATE TABLE IF NOT EXISTS logs (\n  id SERIAL PRIMARY KEY,\n  session_id VARCHAR(255) NOT NULL,\n  timestamp TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,\n  source VARCHAR(50) NOT NULL,\n  level VARCHAR(50) NOT NULL,\n  message TEXT NOT NULL,\n  data JSONB,\n  priority INTEGER DEFAULT 0\n)",
    {}
  ]
}
[14/07/2025 15:21:38] [BACKEND] [INFO] [LOGGER] ✅ [Logger] Console patchée pour intégration unifiée
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:38.841Z",
  "originalArgs": [
    "✅ [Logger] Console patchée pour intégration unifiée"
  ]
}
[14/07/2025 15:21:38] [BACKEND] [INFO] [LOGGER] 📍 URL: http://localhost:3001
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:38.885Z",
  "originalArgs": [
    "📍 URL: http://localhost:3001"
  ]
}
[14/07/2025 15:21:38] [BACKEND] [INFO] [LOGGER] ============================================================
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:38.890Z",
  "originalArgs": [
    "============================================================"
  ]
}
[14/07/2025 15:21:38] [BACKEND] [INFO] [LOGGER] ============================================================
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:38.857Z",
  "originalArgs": [
    "============================================================"
  ]
}
[14/07/2025 15:21:38] [BACKEND] [INFO] [LOGGER] 🛡️  Protection UUID: ACTIVÉE
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:38.888Z",
  "originalArgs": [
    "🛡️  Protection UUID: ACTIVÉE"
  ]
}
[14/07/2025 15:21:38] [BACKEND] [INFO] [LOGGER] ⏰ Démarré le: 2025-07-14T19:21:38.890Z
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:38.891Z",
  "originalArgs": [
    "⏰ Démarré le: 2025-07-14T19:21:38.890Z"
  ]
}
[14/07/2025 15:21:38] [BACKEND] [INFO] [LOGGER] 🔍 Logs détaillés: ACTIVÉS
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:38.889Z",
  "originalArgs": [
    "🔍 Logs détaillés: ACTIVÉS"
  ]
}
[14/07/2025 15:21:38] [BACKEND] [INFO] [LOGGER] ⚠️  Gestion erreurs: ROBUSTE
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:38.889Z",
  "originalArgs": [
    "⚠️  Gestion erreurs: ROBUSTE"
  ]
}
[14/07/2025 15:21:38] [BACKEND] [INFO] [LOGGER] 🚀 SERVEUR API DÉMARRÉ AVEC PROTECTION UUID
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:38.882Z",
  "originalArgs": [
    "🚀 SERVEUR API DÉMARRÉ AVEC PROTECTION UUID"
  ]
}
[14/07/2025 15:21:38] [BACKEND] [INFO] [LOGGER] 
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:38.891Z",
  "originalArgs": [
    ""
  ]
}
[14/07/2025 15:21:38] [BACKEND] [INFO] [LOGGER] ============================================================
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:38.884Z",
  "originalArgs": [
    "============================================================"
  ]
}
[14/07/2025 15:21:38] [BACKEND] [INFO] [LOGGER] ✅ Serveur prêt à recevoir les requêtes
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:38.891Z",
  "originalArgs": [
    "✅ Serveur prêt à recevoir les requêtes"
  ]
}
[14/07/2025 15:21:38] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:39.026Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:21:38] [BACKEND] [INFO] [LOGGER] 🏥 Health Check: http://localhost:3001/health
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:38.887Z",
  "originalArgs": [
    "🏥 Health Check: http://localhost:3001/health"
  ]
}
[14/07/2025 15:21:38] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:39.029Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:21:38] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:39.027Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:21:38] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:39.028Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:21:38] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:39.042Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:21:38] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:39.044Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:21:38] [FRONTEND] [ERROR] [LogsPage] Erreur SSE: {"isTrusted":true}
[14/07/2025 15:21:38] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:39.042Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:21:38] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:39.049Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:21:38] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:39.071Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:21:38] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:39.050Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:21:38] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:39.072Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:21:38] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:39.071Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:21:38] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:39.081Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:21:38] [FRONTEND] [LOG] [vite] server connection lost. Polling for restart...
[14/07/2025 15:21:38] [FRONTEND] [LOG] [vite] server connection lost. Polling for restart...
[14/07/2025 15:21:38] [BACKEND] [INFO] ============================================================
[14/07/2025 15:21:38] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:39.094Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:21:38] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:39.095Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:21:38] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 251ms: CREATE TABLE IF NOT EXISTS logs (
  id SERIAL PRIM {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:39.097Z",
  "originalArgs": [
    "✅ Requête exécutée en 251ms:",
    "CREATE TABLE IF NOT EXISTS logs (\n  id SERIAL PRIM",
    {}
  ]
}
[14/07/2025 15:21:38] [BACKEND] [INFO] [LOGGER] [query] Executing query: ALTER TABLE logs ADD COLUMN IF NOT EXISTS priority INTEGER DEFAULT 0 {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:39.098Z",
  "originalArgs": [
    "[query] Executing query: ALTER TABLE logs ADD COLUMN IF NOT EXISTS priority INTEGER DEFAULT 0",
    {}
  ]
}
[14/07/2025 15:21:38] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 261ms: SELECT 1 FROM logs LIMIT 1 {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:39.099Z",
  "originalArgs": [
    "✅ Requête exécutée en 261ms:",
    "SELECT 1 FROM logs LIMIT 1",
    {}
  ]
}
[14/07/2025 15:21:38] [BACKEND] [INFO] [LOGGER] ✅ [Logger] Connexion PostgreSQL (distant) validée
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:39.099Z",
  "originalArgs": [
    "✅ [Logger] Connexion PostgreSQL (distant) validée"
  ]
}
[14/07/2025 15:21:38] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:39.101Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:21:38] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:39.100Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:21:38] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:39.107Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:21:38] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:39.125Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:21:38] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:39.109Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[14/07/2025 15:21:38] [BACKEND] [INFO] ✅ Requête exécutée en 90ms: ALTER TABLE logs ADD COLUMN IF NOT EXISTS priority {}
[14/07/2025 15:21:38] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 90ms: ALTER TABLE logs ADD COLUMN IF NOT EXISTS priority {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:39.187Z",
  "originalArgs": [
    "✅ Requête exécutée en 90ms:",
    "ALTER TABLE logs ADD COLUMN IF NOT EXISTS priority",
    {}
  ]
}
[14/07/2025 15:21:43] [FRONTEND] [DEBUG] [vite] connected.
[14/07/2025 15:21:44] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[14/07/2025 15:21:44] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[14/07/2025 15:21:44] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[14/07/2025 15:21:44] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[14/07/2025 15:21:44] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[14/07/2025 15:21:44] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[14/07/2025 15:21:44] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[14/07/2025 15:21:44] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[14/07/2025 15:21:44] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[14/07/2025 15:21:44] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[14/07/2025 15:21:44] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[14/07/2025 15:21:44] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[14/07/2025 15:21:44] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[14/07/2025 15:21:44] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[14/07/2025 15:21:44] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[14/07/2025 15:21:44] [FRONTEND] [LOG] [LogsPage] Fermeture de la connexion SSE.
[14/07/2025 15:21:44] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:45.336Z",
  "originalArgs": [
    "[query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ]
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 107ms: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 107ms:",
    "SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:45.442Z"
}
[14/07/2025 15:21:44] [BACKEND] [INFO] ✅ Requête exécutée en 107ms: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
[14/07/2025 15:21:45] [FRONTEND] [DEBUG] [vite] connected.
[14/07/2025 15:21:45] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[14/07/2025 15:21:45] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[14/07/2025 15:21:45] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[14/07/2025 15:21:45] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[14/07/2025 15:21:45] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[14/07/2025 15:21:45] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[14/07/2025 15:21:45] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[14/07/2025 15:21:45] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[14/07/2025 15:21:45] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[14/07/2025 15:21:45] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[14/07/2025 15:21:45] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[14/07/2025 15:21:45] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[14/07/2025 15:21:45] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[14/07/2025 15:21:45] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[14/07/2025 15:21:45] [FRONTEND] [LOG] 🧹 [Agenda] Nettoyage composant Agenda
[14/07/2025 15:21:45] [FRONTEND] [LOG] 🔄 [Agenda] Réinitialisation nécessaire (employés non chargés)
[14/07/2025 15:21:45] [FRONTEND] [LOG] 🚀 [Agenda] Initialisation TeamCalendarApp...
[14/07/2025 15:21:45] [FRONTEND] [LOG] 🚀 [init] Initialisation de TeamCalendarApp...
[14/07/2025 15:21:45] [FRONTEND] [LOG] ✅ [clearRenderCache] Cache de rendu nettoyé
[14/07/2025 15:21:45] [FRONTEND] [LOG] 🧹 [clearRenderCache] Nettoyage du cache de rendu
[14/07/2025 15:21:46] [FRONTEND] [LOG] 🔌 [init] Indicateur de connexion initialisé
[14/07/2025 15:21:46] [FRONTEND] [LOG] 🔌 [ConnectionIndicator] Création de l'indicateur de connexion
[14/07/2025 15:21:46] [FRONTEND] [LOG] 📂 [TeamCalendarApp] Chargement de l'état...
[14/07/2025 15:21:46] [FRONTEND] [LOG] 🌐 [TeamCalendarApp] Chargement depuis l'API...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT e.*, et.name as template_name 
      FROM employees e 
      LEFT JOIN employee_templates et ON e.template_id = et.id 
      ORDER BY e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT e.*, et.name as template_name \n      FROM employees e \n      LEFT JOIN employee_templates et ON e.template_id = et.id \n      ORDER BY e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:46.830Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 44ms: 
      SELECT e.*, et.name as template_name 
      {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 44ms:",
    "\n      SELECT e.*, et.name as template_name \n     ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:46.874Z"
}
[14/07/2025 15:21:46] [BACKEND] [INFO] ✅ Requête exécutée en 44ms: 
      SELECT e.*, et.name as template_name 
      {}
[14/07/2025 15:21:46] [FRONTEND] [LOG] 📦 [loadState] Chargement de tous les shifts depuis la base de données...
[14/07/2025 15:21:46] [FRONTEND] [LOG] 👥 [TeamCalendarApp] 5 employés chargés
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT s.*, e.name as employee_name, sp.label as post_label
      FROM shifts s
      LEFT JOIN employees e ON s.employee_id = e.id
      LEFT JOIN standard_posts sp ON s.post_id = sp.id
      ORDER BY s.date_key, e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT s.*, e.name as employee_name, sp.label as post_label\n      FROM shifts s\n      LEFT JOIN employees e ON s.employee_id = e.id\n      LEFT JOIN standard_posts sp ON s.post_id = sp.id\n      ORDER BY s.date_key, e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:46.910Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 40ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 40ms:",
    "\n      SELECT s.*, e.name as employee_name, sp.lab",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:46.950Z"
}
[14/07/2025 15:21:46] [BACKEND] [INFO] ✅ Requête exécutée en 40ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
[14/07/2025 15:21:46] [FRONTEND] [LOG] ✅ [loadState] Shifts organisés dans this.data.schedule
[14/07/2025 15:21:46] [FRONTEND] [LOG] 📅 [loadState] 0 shifts chargés depuis la base de données
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:46.994Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 38ms: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 38ms:",
    "SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:47.032Z"
}
[14/07/2025 15:21:46] [BACKEND] [INFO] ✅ Requête exécutée en 38ms: SELECT * FROM standard_posts ORDER BY label {}
[14/07/2025 15:21:46] [FRONTEND] [LOG] 🏢 [TeamCalendarApp] 5 postes standards chargés
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:47.097Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 42ms: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 42ms:",
    "SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:47.140Z"
}
[14/07/2025 15:21:46] [BACKEND] [INFO] ✅ Requête exécutée en 42ms: SELECT * FROM app_settings ORDER BY setting_key {}
[14/07/2025 15:21:46] [FRONTEND] [LOG] ⚙️ [loadState] Paramètres chargés depuis API: {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT 
        ra.id,
        ra.employee_id,
        ra.post_id,
        ra.days_of_week,
        ra.start_date,
        ra.end_date,
        ra.is_active,
        ra.excluded_dates,
        e.name as employee_name,
        sp.label as post_label,
        sp.hours as post_hours,
        sp.type as post_type,
        array_length(ra.days_of_week, 1) as days_count
      FROM regular_assignments ra
      LEFT JOIN employees e ON ra.employee_id = e.id
      LEFT JOIN standard_posts sp ON ra.post_id = sp.id
      WHERE ra.is_active = true
      ORDER BY e.name, sp.label, ra.start_date
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT \n        ra.id,\n        ra.employee_id,\n        ra.post_id,\n        ra.days_of_week,\n        ra.start_date,\n        ra.end_date,\n        ra.is_active,\n        ra.excluded_dates,\n        e.name as employee_name,\n        sp.label as post_label,\n        sp.hours as post_hours,\n        sp.type as post_type,\n        array_length(ra.days_of_week, 1) as days_count\n      FROM regular_assignments ra\n      LEFT JOIN employees e ON ra.employee_id = e.id\n      LEFT JOIN standard_posts sp ON ra.post_id = sp.id\n      WHERE ra.is_active = true\n      ORDER BY e.name, sp.label, ra.start_date\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:47.160Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 46ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 46ms:",
    "\n      SELECT \n        ra.id,\n        ra.employee_",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:47.206Z"
}
[14/07/2025 15:21:46] [BACKEND] [INFO] ✅ Requête exécutée en 46ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
[14/07/2025 15:21:46] [FRONTEND] [LOG] 📋 [loadState] 0/0 assignations régulières valides chargées et normalisées
[14/07/2025 15:21:46] [FRONTEND] [LOG] 🔧 [fixInvalidAssignmentDates] Vérification des dates invalides...
[14/07/2025 15:21:46] [FRONTEND] [LOG] 📋 [loadState] Assignations régulières chargées (application différée)
[14/07/2025 15:21:46] [FRONTEND] [LOG] ✅ [fixInvalidAssignmentDates] Aucune date invalide trouvée
[14/07/2025 15:21:46] [FRONTEND] [LOG] 🧹 [cleanupDuplicateRegularShifts] Nettoyage des doublons de shifts réguliers...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'
         {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'\n        ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:47.334Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📋 [GET /api/employee-order] Récupération ordre des employés
  Data: {
  "originalArgs": [
    "📋 [GET /api/employee-order] Récupération ordre des employés"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:47.332Z"
}
[14/07/2025 15:21:46] [BACKEND] [INFO] 📋 [GET /api/employee-order] Récupération ordre des employés
[14/07/2025 15:21:46] [FRONTEND] [LOG] ✅ [cleanupDuplicateRegularShifts] Aucun doublon détecté
[14/07/2025 15:21:46] [FRONTEND] [LOG] 🧹 [cleanupInvalidAssignmentIds] Nettoyage des assignment_id invalides...
[14/07/2025 15:21:46] [FRONTEND] [LOG] ✅ [loadState] État chargé depuis l'API avec succès
[14/07/2025 15:21:46] [FRONTEND] [LOG] ✅ [cleanupInvalidAssignmentIds] Aucun assignment_id invalide détecté
[14/07/2025 15:21:46] [FRONTEND] [LOG] ✅ [init] Icône paramètres trouvée: [Objet non sérialisable]
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📋 [GET /api/employee-order] Aucun ordre personnalisé trouvé
  Data: {
  "originalArgs": [
    "📋 [GET /api/employee-order] Aucun ordre personnalisé trouvé"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:47.384Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 48ms: 
            SELECT setting_value FROM app_setting {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 48ms:",
    "\n            SELECT setting_value FROM app_setting",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:47.383Z"
}
[14/07/2025 15:21:46] [FRONTEND] [LOG] 🔍 [init] Recherche du bouton des paramètres dans le DOM
[14/07/2025 15:21:46] [FRONTEND] [LOG] 🔍 [init] Attachement du listener click sur le bouton des paramètres
[14/07/2025 15:21:46] [FRONTEND] [LOG] 🔍 [init] Recherche du bouton des paramètres header dans le DOM
[14/07/2025 15:21:46] [FRONTEND] [LOG] 🔍 [init] Bouton des paramètres trouvé ? true
[14/07/2025 15:21:46] [FRONTEND] [LOG] 🔍 [init] Bouton des paramètres header trouvé ? true
[14/07/2025 15:21:46] [FRONTEND] [LOG] 🔍 [init] Attachement du listener click sur le bouton des paramètres header
[14/07/2025 15:21:46] [FRONTEND] [LOG] ✅ [setupNavigationListeners] Écouteurs de navigation attachés
[14/07/2025 15:21:46] [FRONTEND] [LOG] 🎯 [setupViewNavigationListeners] Configuration des boutons de navigation...
[14/07/2025 15:21:46] [FRONTEND] [LOG] ✅ Boutons de navigation configurés
[14/07/2025 15:21:46] [FRONTEND] [LOG] 🔧 [ModalManager] Configuration des gestionnaires de modal
[14/07/2025 15:21:46] [FRONTEND] [LOG] ✅ [ModalManager] Gestionnaires de modal configurés
[14/07/2025 15:21:46] [FRONTEND] [LOG] 🔧 [setupGeneralSettings] Création du conteneur de fuseau horaire
[14/07/2025 15:21:46] [FRONTEND] [LOG] 🔧 [setupGeneralSettings] Configuration des paramètres généraux
[14/07/2025 15:21:46] [FRONTEND] [LOG] ✅ [setupGlobalEscapeHandler] Gestionnaire ESC global configuré
[14/07/2025 15:21:46] [FRONTEND] [LOG] 📋 [loadEmployeeOrder] Chargement ordre des employés
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🔧 [init] Forçage du rendu initial...
[14/07/2025 15:21:47] [FRONTEND] [WARN] ⚠️ [loadEmployeeOrder] Réponse API sans tableau employeeOrder
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 60 zones trouvées
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 60 zones de drop disponibles
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste e0332f5d-23e2-40fa-bf2d-d9271f03100a configuré comme draggable
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Matin: [1, 2, 3, 4, 5]
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste e0332f5d-23e2-40fa-bf2d-d9271f03100a
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Nuit: [1, 2, 3, 4, 5]
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 9ebe6752-7cf6-47de-bf62-8eb912bd439d configuré comme draggable
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Soir: [1, 2, 3, 4, 5]
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 9ebe6752-7cf6-47de-bf62-8eb912bd439d
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7224fea3-1bc9-4ff2-ab34-35cb0cf19f3e configuré comme draggable
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7224fea3-1bc9-4ff2-ab34-35cb0cf19f3e
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 5480db79-77a5-4f23-aa37-614936e8514b configuré comme draggable
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE2: [0, 6]
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 5480db79-77a5-4f23-aa37-614936e8514b
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE1: [0, 6]
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 1ed512ff-9b54-4a63-90da-790b0594198b configuré comme draggable
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 1ed512ff-9b54-4a63-90da-790b0594198b
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste e0332f5d-23e2-40fa-bf2d-d9271f03100a configuré comme draggable
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste e0332f5d-23e2-40fa-bf2d-d9271f03100a
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 9ebe6752-7cf6-47de-bf62-8eb912bd439d configuré comme draggable
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 9ebe6752-7cf6-47de-bf62-8eb912bd439d
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7224fea3-1bc9-4ff2-ab34-35cb0cf19f3e configuré comme draggable
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7224fea3-1bc9-4ff2-ab34-35cb0cf19f3e
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 5480db79-77a5-4f23-aa37-614936e8514b configuré comme draggable
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 1ed512ff-9b54-4a63-90da-790b0594198b configuré comme draggable
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 5480db79-77a5-4f23-aa37-614936e8514b
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 1ed512ff-9b54-4a63-90da-790b0594198b
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (60 zones)
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🔧 [init] Configuration du drag & drop des postes...
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 60 zones de drop disponibles
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 60 zones trouvées
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste e0332f5d-23e2-40fa-bf2d-d9271f03100a configuré comme draggable
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste e0332f5d-23e2-40fa-bf2d-d9271f03100a
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 9ebe6752-7cf6-47de-bf62-8eb912bd439d
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 9ebe6752-7cf6-47de-bf62-8eb912bd439d configuré comme draggable
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7224fea3-1bc9-4ff2-ab34-35cb0cf19f3e configuré comme draggable
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 5480db79-77a5-4f23-aa37-614936e8514b configuré comme draggable
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7224fea3-1bc9-4ff2-ab34-35cb0cf19f3e
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 5480db79-77a5-4f23-aa37-614936e8514b
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 1ed512ff-9b54-4a63-90da-790b0594198b configuré comme draggable
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste e0332f5d-23e2-40fa-bf2d-d9271f03100a configuré comme draggable
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 1ed512ff-9b54-4a63-90da-790b0594198b
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 9ebe6752-7cf6-47de-bf62-8eb912bd439d configuré comme draggable
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste e0332f5d-23e2-40fa-bf2d-d9271f03100a
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 9ebe6752-7cf6-47de-bf62-8eb912bd439d
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7224fea3-1bc9-4ff2-ab34-35cb0cf19f3e configuré comme draggable
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7224fea3-1bc9-4ff2-ab34-35cb0cf19f3e
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 5480db79-77a5-4f23-aa37-614936e8514b configuré comme draggable
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 5480db79-77a5-4f23-aa37-614936e8514b
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 1ed512ff-9b54-4a63-90da-790b0594198b configuré comme draggable
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 1ed512ff-9b54-4a63-90da-790b0594198b
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (60 zones)
[14/07/2025 15:21:47] [FRONTEND] [LOG] ℹ️ [setupEmployeeDragDrop] Fonction désactivée - Utiliser setupDragAndDrop
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🔧 [addEmergencyFixButton] Ajout du bouton de correction...
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🎉 [Agenda] Application fullscreen initialisée avec succès
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [init] Initialisation terminée avec succès
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [addEmergencyFixButton] Bouton de correction ajouté
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] 0 grips ajoutés
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🔍 [diagnoseRegularAssignmentGrips] Diagnostic des grips d'attributions régulières
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[14/07/2025 15:21:47] [FRONTEND] [LOG] 📊 [diagnoseRegularAssignmentGrips] Statistiques: {"totalShifts":0,"regularShifts":0,"regularAssignments":0}
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🎯 [diagnoseRegularAssignmentGrips] Grips trouvés dans le DOM: 0
[14/07/2025 15:21:47] [FRONTEND] [LOG] ⚠️ [diagnoseRegularAssignmentGrips] Aucun shift régulier trouvé - Ré-application des attributions...
[14/07/2025 15:21:47] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[14/07/2025 15:21:47] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[14/07/2025 15:21:57] [FRONTEND] [LOG] 🎯 [POST DRAG] Début drag poste: e0332f5d-23e2-40fa-bf2d-d9271f03100a (Poste Matin)
[14/07/2025 15:21:57] [FRONTEND] [LOG] 🎯 [POST DRAG] DataTransfer configuré: {"postId":"e0332f5d-23e2-40fa-bf2d-d9271f03100a","label":"Poste Matin","types":["text/plain","application/x-post-id","postid","postlabel"]}
[14/07/2025 15:21:57] [FRONTEND] [LOG] 🎨 [enableAgendaPreviewMode] Activation preview agenda pour poste e0332f5d-23e2-40fa-bf2d-d9271f03100a
[14/07/2025 15:21:58] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[14/07/2025 15:21:58] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] Ajout des fantômes pour Poste Matin
[14/07/2025 15:21:58] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[14/07/2025 15:21:58] [FRONTEND] [LOG] ✅ [enableAgendaPreviewMode] Mode preview activé pour Poste Matin
[14/07/2025 15:21:58] [FRONTEND] [LOG] 🎯 [POST DRAG] 60 zones préparées pour le drop
[14/07/2025 15:21:58] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 59f5df3a-33ef-425c-bfa2-adca818cf94f, types: text/plain, application/x-post-id, postid, postlabel
[14/07/2025 15:21:58] [FRONTEND] [LOG] 🎯 [POST DRAG] DataTransfer configuré: {"postId":"e0332f5d-23e2-40fa-bf2d-d9271f03100a","label":"Poste Matin","types":["text/plain","application/x-post-id","postid","postlabel"]}
[14/07/2025 15:21:58] [FRONTEND] [LOG] 🎯 [POST DRAG] Début drag poste: e0332f5d-23e2-40fa-bf2d-d9271f03100a (Poste Matin)
[14/07/2025 15:21:58] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] 25 fantômes créés
[14/07/2025 15:21:58] [FRONTEND] [LOG] 🎨 [enableAgendaPreviewMode] Activation preview agenda pour poste e0332f5d-23e2-40fa-bf2d-d9271f03100a
[14/07/2025 15:21:58] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[14/07/2025 15:21:58] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[14/07/2025 15:21:58] [FRONTEND] [LOG] 🖱️ [enableCellHoverTracking] Suivi activé sur 25 cellules
[14/07/2025 15:21:58] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] Ajout des fantômes pour Poste Matin
[14/07/2025 15:21:58] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] 25 fantômes créés
[14/07/2025 15:21:58] [FRONTEND] [LOG] 🖱️ [enableCellHoverTracking] Suivi activé sur 50 cellules
[14/07/2025 15:21:58] [FRONTEND] [LOG] 🎯 [POST DRAG] 60 zones préparées pour le drop
[14/07/2025 15:21:58] [FRONTEND] [LOG] ✅ [enableAgendaPreviewMode] Mode preview activé pour Poste Matin
[14/07/2025 15:21:58] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:58.990Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:58.842Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:58.996Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:58.995Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-14T19:21:58.857Z"
}
[14/07/2025 15:21:58] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 59f5df3a-33ef-425c-bfa2-adca818cf94f, types: text/plain, application/x-post-id, postid, postlabel
[14/07/2025 15:21:59] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 59f5df3a-33ef-425c-bfa2-adca818cf94f, types: text/plain, application/x-post-id, postid, postlabel
[14/07/2025 15:21:59] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 59f5df3a-33ef-425c-bfa2-adca818cf94f, types: text/plain, application/x-post-id, postid, postlabel
[14/07/2025 15:22:00] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 59f5df3a-33ef-425c-bfa2-adca818cf94f, types: text/plain, application/x-post-id, postid, postlabel
[14/07/2025 15:22:00] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 18fe9cd1-e8a5-44e3-90b3-a856086ce27e, types: text/plain, application/x-post-id, postid, postlabel
[14/07/2025 15:22:00] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 18fe9cd1-e8a5-44e3-90b3-a856086ce27e, types: text/plain, application/x-post-id, postid, postlabel
[14/07/2025 15:22:01] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 18fe9cd1-e8a5-44e3-90b3-a856086ce27e, types: text/plain, application/x-post-id, postid, postlabel
[14/07/2025 15:22:01] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 18fe9cd1-e8a5-44e3-90b3-a856086ce27e, types: text/plain, application/x-post-id, postid, postlabel
[14/07/2025 15:22:02] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 18fe9cd1-e8a5-44e3-90b3-a856086ce27e, types: text/plain, application/x-post-id, postid, postlabel
[14/07/2025 15:22:02] [FRONTEND] [LOG] 🎯 [DROP] ✅ Drop reçu sur employé: Pierre Durand
[14/07/2025 15:22:02] [FRONTEND] [LOG] ✅ [DROP] PostId trouvé via application/x-post-id: "e0332f5d-23e2-40fa-bf2d-d9271f03100a"
[14/07/2025 15:22:02] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[14/07/2025 15:22:02] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[14/07/2025 15:22:02] [FRONTEND] [LOG] 📦 [DROP] Nouveau poste: e0332f5d-23e2-40fa-bf2d-d9271f03100a → 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[14/07/2025 15:22:02] [FRONTEND] [LOG] 🎯 [openAssignmentContextModal] Poste brut: e0332f5d-23e2-40fa-bf2d-d9271f03100a, Employé: 18fe9cd1-e8a5-44e3-90b3-a856086ce27e, Contexte: null
[14/07/2025 15:22:02] [FRONTEND] [LOG] ℹ️ [openAssignmentContextModal] Option "Attribution unique (cellule)" masquée - contexte inapproprié
[14/07/2025 15:22:02] [FRONTEND] [LOG] 📅 [openAssignmentContextModal] Date initialisée: 2025-07-14
[14/07/2025 15:22:02] [FRONTEND] [LOG] 🎯 [POST DRAG] Fin drag poste: e0332f5d-23e2-40fa-bf2d-d9271f03100a
[14/07/2025 15:22:02] [FRONTEND] [LOG] ✅ [DROP] Modal d'assignation ouvert
[14/07/2025 15:22:02] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[14/07/2025 15:22:02] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[14/07/2025 15:22:02] [FRONTEND] [LOG] 🎯 [POST DRAG] Nettoyage des 60 zones
[14/07/2025 15:22:02] [FRONTEND] [LOG] 🎯 [POST DRAG] Fin drag poste: e0332f5d-23e2-40fa-bf2d-d9271f03100a
[14/07/2025 15:22:02] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[14/07/2025 15:22:02] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[14/07/2025 15:22:02] [FRONTEND] [LOG] 🎯 [POST DRAG] Nettoyage des 60 zones