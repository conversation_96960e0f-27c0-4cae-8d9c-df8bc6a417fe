[{
	"resource": "/c:/Users/<USER>/Desktop/interface/interface-3/interface - 1 Backup/backups/backup-2025-07-07T16-46-28-613Z/tsconfig.json",
	"owner": "typescript",
	"severity": 8,
	"message": "File 'c:/Users/<USER>/Desktop/interface/interface-3/interface - 1 Backup/backups/backup-2025-07-07T16-46-28-613Z/tsconfig.node.json' not found.",
	"source": "ts",
	"startLineNumber": 36,
	"startColumn": 18,
	"endLineNumber": 36,
	"endColumn": 52,
	"origin": "extHost1"
}]
[15/07/2025 13:52:03] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[15/07/2025 13:52:03] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[15/07/2025 13:52:03] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[15/07/2025 13:52:03] [FRONTEND] [LOG] [LogsPage] Fermeture de la connexion SSE.
[15/07/2025 13:52:03] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:52:03.019Z"
}
[15/07/2025 13:52:03] [BACKEND] [INFO] [LOGS] Client connecté: 1752601923018
[15/07/2025 13:51:46] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 87ms: ALTER TABLE logs ADD COLUMN IF NOT EXISTS priority {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:51:46.605Z",
  "originalArgs": [
    "✅ Requête exécutée en 87ms:",
    "ALTER TABLE logs ADD COLUMN IF NOT EXISTS priority",
    {}
  ]
}
[15/07/2025 13:51:46] [BACKEND] [INFO] ✅ Requête exécutée en 87ms: ALTER TABLE logs ADD COLUMN IF NOT EXISTS priority {}
[15/07/2025 13:51:46] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:51:46.649Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 13:51:46] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:51:46.461Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 13:51:46] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[15/07/2025 13:51:46] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:51:46.664Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 13:51:46] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:51:46.458Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 13:51:46] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:51:46.665Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 13:51:46] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:51:46.462Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 13:51:52] [FRONTEND] [DEBUG] [vite] connected.
[15/07/2025 13:51:52] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[15/07/2025 13:51:52] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[15/07/2025 13:51:52] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[15/07/2025 13:51:52] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[15/07/2025 13:51:52] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[15/07/2025 13:51:52] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[15/07/2025 13:51:52] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[15/07/2025 13:51:52] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[15/07/2025 13:51:52] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[15/07/2025 13:51:52] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[15/07/2025 13:51:52] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[15/07/2025 13:51:52] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[15/07/2025 13:51:52] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[15/07/2025 13:51:52] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[15/07/2025 13:51:52] [FRONTEND] [LOG] 🧹 [Agenda] Nettoyage composant Agenda
[15/07/2025 13:51:52] [FRONTEND] [LOG] 🔄 [Agenda] Réinitialisation nécessaire (employés non chargés)
[15/07/2025 13:51:53] [FRONTEND] [LOG] 🚀 [init] Initialisation de TeamCalendarApp...
[15/07/2025 13:51:53] [FRONTEND] [LOG] 🧹 [clearRenderCache] Nettoyage du cache de rendu
[15/07/2025 13:51:53] [FRONTEND] [LOG] 🚀 [Agenda] Initialisation TeamCalendarApp...
[15/07/2025 13:51:53] [FRONTEND] [LOG] ✅ [clearRenderCache] Cache de rendu nettoyé
[15/07/2025 13:51:53] [FRONTEND] [LOG] 🔌 [init] Indicateur de connexion initialisé
[15/07/2025 13:51:53] [FRONTEND] [LOG] 🔌 [ConnectionIndicator] Création de l'indicateur de connexion
[15/07/2025 13:51:53] [FRONTEND] [LOG] 📂 [TeamCalendarApp] Chargement de l'état...
[15/07/2025 13:51:53] [FRONTEND] [LOG] 🌐 [TeamCalendarApp] Chargement depuis l'API...
[15/07/2025 13:51:53] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT e.*, et.name as template_name 
      FROM employees e 
      LEFT JOIN employee_templates et ON e.template_id = et.id 
      ORDER BY e.name
     {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:51:53.111Z",
  "originalArgs": [
    "[query] Executing query: \n      SELECT e.*, et.name as template_name \n      FROM employees e \n      LEFT JOIN employee_templates et ON e.template_id = et.id \n      ORDER BY e.name\n    ",
    {}
  ]
}
[15/07/2025 13:51:53] [BACKEND] [INFO] ✅ Requête exécutée en 60ms: 
      SELECT e.*, et.name as template_name 
      {}
[15/07/2025 13:51:53] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 60ms: 
      SELECT e.*, et.name as template_name 
      {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:51:53.170Z",
  "originalArgs": [
    "✅ Requête exécutée en 60ms:",
    "\n      SELECT e.*, et.name as template_name \n     ",
    {}
  ]
}
[15/07/2025 13:51:53] [FRONTEND] [LOG] 👥 [TeamCalendarApp] 5 employés chargés
[15/07/2025 13:51:53] [FRONTEND] [LOG] 📦 [loadState] Chargement de tous les shifts depuis la base de données...
[15/07/2025 13:51:53] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT s.*, e.name as employee_name, sp.label as post_label
      FROM shifts s
      LEFT JOIN employees e ON s.employee_id = e.id
      LEFT JOIN standard_posts sp ON s.post_id = sp.id
      ORDER BY s.date_key, e.name
     {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:51:53.190Z",
  "originalArgs": [
    "[query] Executing query: \n      SELECT s.*, e.name as employee_name, sp.label as post_label\n      FROM shifts s\n      LEFT JOIN employees e ON s.employee_id = e.id\n      LEFT JOIN standard_posts sp ON s.post_id = sp.id\n      ORDER BY s.date_key, e.name\n    ",
    {}
  ]
}
[15/07/2025 13:51:53] [BACKEND] [INFO] ✅ Requête exécutée en 50ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
[15/07/2025 13:51:53] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 50ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:51:53.239Z",
  "originalArgs": [
    "✅ Requête exécutée en 50ms:",
    "\n      SELECT s.*, e.name as employee_name, sp.lab",
    {}
  ]
}
[15/07/2025 13:51:53] [FRONTEND] [LOG] 📅 [loadState] 0 shifts chargés depuis la base de données
[15/07/2025 13:51:53] [FRONTEND] [LOG] ✅ [loadState] Shifts organisés dans this.data.schedule
[15/07/2025 13:51:53] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:51:53.260Z",
  "originalArgs": [
    "[query] Executing query: SELECT * FROM standard_posts ORDER BY label",
    {}
  ]
}
[15/07/2025 13:51:53] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 53ms: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:51:53.314Z",
  "originalArgs": [
    "✅ Requête exécutée en 53ms:",
    "SELECT * FROM standard_posts ORDER BY label",
    {}
  ]
}
[15/07/2025 13:51:53] [BACKEND] [INFO] ✅ Requête exécutée en 53ms: SELECT * FROM standard_posts ORDER BY label {}
[15/07/2025 13:51:53] [FRONTEND] [LOG] 🏢 [TeamCalendarApp] 5 postes standards chargés
[15/07/2025 13:51:53] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:51:53.330Z",
  "originalArgs": [
    "[query] Executing query: SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ]
}
[15/07/2025 13:51:53] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 46ms: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:51:53.375Z",
  "originalArgs": [
    "✅ Requête exécutée en 46ms:",
    "SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ]
}
[15/07/2025 13:51:53] [BACKEND] [INFO] ✅ Requête exécutée en 46ms: SELECT * FROM app_settings ORDER BY setting_key {}
[15/07/2025 13:51:53] [FRONTEND] [LOG] ⚙️ [loadState] Paramètres chargés depuis API: {}
[15/07/2025 13:51:53] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT 
        ra.id,
        ra.employee_id,
        ra.post_id,
        ra.days_of_week,
        ra.start_date,
        ra.end_date,
        ra.is_active,
        ra.excluded_dates,
        e.name as employee_name,
        sp.label as post_label,
        sp.hours as post_hours,
        sp.type as post_type,
        array_length(ra.days_of_week, 1) as days_count
      FROM regular_assignments ra
      LEFT JOIN employees e ON ra.employee_id = e.id
      LEFT JOIN standard_posts sp ON ra.post_id = sp.id
      WHERE ra.is_active = true
      ORDER BY e.name, sp.label, ra.start_date
     {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:51:53.399Z",
  "originalArgs": [
    "[query] Executing query: \n      SELECT \n        ra.id,\n        ra.employee_id,\n        ra.post_id,\n        ra.days_of_week,\n        ra.start_date,\n        ra.end_date,\n        ra.is_active,\n        ra.excluded_dates,\n        e.name as employee_name,\n        sp.label as post_label,\n        sp.hours as post_hours,\n        sp.type as post_type,\n        array_length(ra.days_of_week, 1) as days_count\n      FROM regular_assignments ra\n      LEFT JOIN employees e ON ra.employee_id = e.id\n      LEFT JOIN standard_posts sp ON ra.post_id = sp.id\n      WHERE ra.is_active = true\n      ORDER BY e.name, sp.label, ra.start_date\n    ",
    {}
  ]
}
[15/07/2025 13:51:53] [BACKEND] [INFO] ✅ Requête exécutée en 64ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
[15/07/2025 13:51:53] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 64ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:51:53.462Z",
  "originalArgs": [
    "✅ Requête exécutée en 64ms:",
    "\n      SELECT \n        ra.id,\n        ra.employee_",
    {}
  ]
}
[15/07/2025 13:51:53] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"2a584d12-574b-4310-9d38-865829631d79","employee_id":"cf78e945-72c7-48f3-a72d-bd0e245a284d","post_id":"ef3246ff-8398-4f11-a498-648d207563a0","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-25T04:00:00.000Z","end_date":"2025-07-29T04:00:00.000Z","excluded_dates":[]}
[15/07/2025 13:51:53] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"2a584d12-574b-4310-9d38-865829631d79","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-25","endDate":"2025-07-29","excludedDates":[]}
[15/07/2025 13:51:53] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"7233f36d-6d3c-4085-824a-9e693eea2715","employee_id":"42b53805-18ba-425e-bb00-52bb8d6ce76b","post_id":"ef3246ff-8398-4f11-a498-648d207563a0","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-08-19T04:00:00.000Z","end_date":null,"excluded_dates":[]}
[15/07/2025 13:51:53] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"7233f36d-6d3c-4085-824a-9e693eea2715","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"isLimited":false,"startDate":"2025-08-19","endDate":null,"excludedDates":[]}
[15/07/2025 13:51:53] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"f2215562-53ed-43f8-839f-62cf07621ab3","employee_id":"cf78e945-72c7-48f3-a72d-bd0e245a284d","post_id":"ef3246ff-8398-4f11-a498-648d207563a0","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-08-14T04:00:00.000Z","end_date":"2025-08-18T04:00:00.000Z","excluded_dates":[]}
[15/07/2025 13:51:53] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"f2215562-53ed-43f8-839f-62cf07621ab3","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-08-14","endDate":"2025-08-18","excludedDates":[]}
[15/07/2025 13:51:53] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"a16c8b97-bb1b-41f0-a10f-91adc459eb80","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-24","endDate":"2025-07-24","excludedDates":[]}
[15/07/2025 13:51:53] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"a16c8b97-bb1b-41f0-a10f-91adc459eb80","employee_id":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","post_id":"ef3246ff-8398-4f11-a498-648d207563a0","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-24T04:00:00.000Z","end_date":"2025-07-24T04:00:00.000Z","excluded_dates":[]}
[15/07/2025 13:51:53] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"a563e948-18d8-4540-bfbc-7853d6506c05","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-30","endDate":"2025-08-04","excludedDates":[]}
[15/07/2025 13:51:53] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"a563e948-18d8-4540-bfbc-7853d6506c05","employee_id":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","post_id":"ef3246ff-8398-4f11-a498-648d207563a0","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-30T04:00:00.000Z","end_date":"2025-08-04T04:00:00.000Z","excluded_dates":[]}
[15/07/2025 13:51:53] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"87109327-e3af-4564-9b6c-402c760b130d","employee_id":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","post_id":"ef3246ff-8398-4f11-a498-648d207563a0","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-08-12T04:00:00.000Z","end_date":"2025-08-13T04:00:00.000Z","excluded_dates":[]}
[15/07/2025 13:51:53] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"87109327-e3af-4564-9b6c-402c760b130d","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-08-12","endDate":"2025-08-13","excludedDates":[]}
[15/07/2025 13:51:53] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"255ae26a-03c7-499f-ba7d-10568e4a8be1","employee_id":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","post_id":"ef3246ff-8398-4f11-a498-648d207563a0","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-08-05T04:00:00.000Z","end_date":"2025-08-11T04:00:00.000Z","excluded_dates":[]}
[15/07/2025 13:51:53] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","employee_id":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","post_id":"ef3246ff-8398-4f11-a498-648d207563a0","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-23T04:00:00.000Z","end_date":"2025-07-23T04:00:00.000Z","excluded_dates":[]}
[15/07/2025 13:51:53] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-23","endDate":"2025-07-23","excludedDates":[]}
[15/07/2025 13:51:53] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"255ae26a-03c7-499f-ba7d-10568e4a8be1","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-08-05","endDate":"2025-08-11","excludedDates":[]}
[15/07/2025 13:51:53] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"b77227a7-67b7-4feb-b082-890cfeecab1f","employee_id":"59f5df3a-33ef-425c-bfa2-adca818cf94f","post_id":"ef3246ff-8398-4f11-a498-648d207563a0","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-15T04:00:00.000Z","end_date":"2025-07-22T04:00:00.000Z","excluded_dates":[]}
[15/07/2025 13:51:53] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"b77227a7-67b7-4feb-b082-890cfeecab1f","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-15","endDate":"2025-07-22","excludedDates":[]}
[15/07/2025 13:51:53] [FRONTEND] [LOG] ✅ [fixInvalidAssignmentDates] Aucune date invalide trouvée
[15/07/2025 13:51:53] [FRONTEND] [LOG] 🔧 [fixInvalidAssignmentDates] Vérification des dates invalides...
[15/07/2025 13:51:53] [FRONTEND] [LOG] 📋 [loadState] 9/9 assignations régulières valides chargées et normalisées
[15/07/2025 13:51:53] [FRONTEND] [LOG] 📋 [loadState] Assignations régulières chargées (application différée)
[15/07/2025 13:51:53] [FRONTEND] [LOG] 🧹 [cleanupDuplicateRegularShifts] Nettoyage des doublons de shifts réguliers...
[15/07/2025 13:51:53] [FRONTEND] [LOG] ✅ [cleanupDuplicateRegularShifts] Aucun doublon détecté
[15/07/2025 13:51:53] [FRONTEND] [LOG] ✅ [cleanupInvalidAssignmentIds] Aucun assignment_id invalide détecté
[15/07/2025 13:51:53] [FRONTEND] [LOG] 🧹 [cleanupInvalidAssignmentIds] Nettoyage des assignment_id invalides...
[15/07/2025 13:51:53] [FRONTEND] [LOG] ✅ [loadState] État chargé depuis l'API avec succès
[15/07/2025 13:51:54] [FRONTEND] [ERROR] TeamCalendarApp: Un ou plusieurs éléments DOM requis n'ont pas été trouvés. Le rendu est annulé.
[15/07/2025 13:51:54] [FRONTEND] [LOG] 🎉 [Agenda] Application fullscreen initialisée avec succès
[15/07/2025 13:51:56] [FRONTEND] [DEBUG] [vite] connected.
[15/07/2025 13:51:57] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[15/07/2025 13:51:57] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[15/07/2025 13:51:57] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[15/07/2025 13:51:57] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[15/07/2025 13:51:57] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[15/07/2025 13:51:57] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[15/07/2025 13:51:57] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[15/07/2025 13:51:57] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[15/07/2025 13:51:57] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[15/07/2025 13:51:57] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[15/07/2025 13:51:57] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[15/07/2025 13:51:57] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[15/07/2025 13:51:57] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[15/07/2025 13:51:57] [FRONTEND] [LOG] 🧹 [Agenda] Nettoyage composant Agenda
[15/07/2025 13:51:57] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[15/07/2025 13:51:57] [FRONTEND] [LOG] 🔄 [Agenda] Réinitialisation nécessaire (employés non chargés)
[15/07/2025 13:51:57] [FRONTEND] [LOG] 🚀 [init] Initialisation de TeamCalendarApp...
[15/07/2025 13:51:57] [FRONTEND] [LOG] 🚀 [Agenda] Initialisation TeamCalendarApp...
[15/07/2025 13:51:57] [FRONTEND] [LOG] 🧹 [clearRenderCache] Nettoyage du cache de rendu
[15/07/2025 13:51:57] [FRONTEND] [LOG] ✅ [clearRenderCache] Cache de rendu nettoyé
[15/07/2025 13:51:57] [FRONTEND] [LOG] 🔌 [ConnectionIndicator] Création de l'indicateur de connexion
[15/07/2025 13:51:57] [FRONTEND] [LOG] 🔌 [init] Indicateur de connexion initialisé
[15/07/2025 13:51:57] [FRONTEND] [LOG] 📂 [TeamCalendarApp] Chargement de l'état...
[15/07/2025 13:51:57] [FRONTEND] [LOG] 🌐 [TeamCalendarApp] Chargement depuis l'API...
[15/07/2025 13:51:57] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT e.*, et.name as template_name 
      FROM employees e 
      LEFT JOIN employee_templates et ON e.template_id = et.id 
      ORDER BY e.name
     {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:51:57.552Z",
  "originalArgs": [
    "[query] Executing query: \n      SELECT e.*, et.name as template_name \n      FROM employees e \n      LEFT JOIN employee_templates et ON e.template_id = et.id \n      ORDER BY e.name\n    ",
    {}
  ]
}
[15/07/2025 13:51:57] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 54ms: 
      SELECT e.*, et.name as template_name 
      {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:51:57.605Z",
  "originalArgs": [
    "✅ Requête exécutée en 54ms:",
    "\n      SELECT e.*, et.name as template_name \n     ",
    {}
  ]
}
[15/07/2025 13:51:57] [BACKEND] [INFO] ✅ Requête exécutée en 54ms: 
      SELECT e.*, et.name as template_name 
      {}
[15/07/2025 13:51:57] [FRONTEND] [LOG] 👥 [TeamCalendarApp] 5 employés chargés
[15/07/2025 13:51:57] [FRONTEND] [LOG] 📦 [loadState] Chargement de tous les shifts depuis la base de données...
[15/07/2025 13:51:57] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT s.*, e.name as employee_name, sp.label as post_label
      FROM shifts s
      LEFT JOIN employees e ON s.employee_id = e.id
      LEFT JOIN standard_posts sp ON s.post_id = sp.id
      ORDER BY s.date_key, e.name
     {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:51:57.672Z",
  "originalArgs": [
    "[query] Executing query: \n      SELECT s.*, e.name as employee_name, sp.label as post_label\n      FROM shifts s\n      LEFT JOIN employees e ON s.employee_id = e.id\n      LEFT JOIN standard_posts sp ON s.post_id = sp.id\n      ORDER BY s.date_key, e.name\n    ",
    {}
  ]
}
[15/07/2025 13:51:57] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 56ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:51:57.728Z",
  "originalArgs": [
    "✅ Requête exécutée en 56ms:",
    "\n      SELECT s.*, e.name as employee_name, sp.lab",
    {}
  ]
}
[15/07/2025 13:51:58] [BACKEND] [INFO] ✅ Requête exécutée en 56ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
[15/07/2025 13:51:58] [FRONTEND] [LOG] 📅 [loadState] 0 shifts chargés depuis la base de données
[15/07/2025 13:51:58] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:51:57.818Z",
  "originalArgs": [
    "[query] Executing query: SELECT * FROM standard_posts ORDER BY label",
    {}
  ]
}
[15/07/2025 13:51:58] [FRONTEND] [LOG] ✅ [loadState] Shifts organisés dans this.data.schedule
[15/07/2025 13:51:58] [BACKEND] [INFO] ✅ Requête exécutée en 51ms: SELECT * FROM standard_posts ORDER BY label {}
[15/07/2025 13:51:58] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 51ms: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:51:57.869Z",
  "originalArgs": [
    "✅ Requête exécutée en 51ms:",
    "SELECT * FROM standard_posts ORDER BY label",
    {}
  ]
}
[15/07/2025 13:51:58] [FRONTEND] [LOG] 🏢 [TeamCalendarApp] 5 postes standards chargés
[15/07/2025 13:51:58] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:51:57.896Z",
  "originalArgs": [
    "[query] Executing query: SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ]
}
[15/07/2025 13:51:58] [BACKEND] [INFO] ✅ Requête exécutée en 56ms: SELECT * FROM app_settings ORDER BY setting_key {}
[15/07/2025 13:51:58] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 56ms: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:51:57.952Z",
  "originalArgs": [
    "✅ Requête exécutée en 56ms:",
    "SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ]
}
[15/07/2025 13:51:58] [FRONTEND] [LOG] ⚙️ [loadState] Paramètres chargés depuis API: {}
[15/07/2025 13:51:58] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT 
        ra.id,
        ra.employee_id,
        ra.post_id,
        ra.days_of_week,
        ra.start_date,
        ra.end_date,
        ra.is_active,
        ra.excluded_dates,
        e.name as employee_name,
        sp.label as post_label,
        sp.hours as post_hours,
        sp.type as post_type,
        array_length(ra.days_of_week, 1) as days_count
      FROM regular_assignments ra
      LEFT JOIN employees e ON ra.employee_id = e.id
      LEFT JOIN standard_posts sp ON ra.post_id = sp.id
      WHERE ra.is_active = true
      ORDER BY e.name, sp.label, ra.start_date
     {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:51:58.116Z",
  "originalArgs": [
    "[query] Executing query: \n      SELECT \n        ra.id,\n        ra.employee_id,\n        ra.post_id,\n        ra.days_of_week,\n        ra.start_date,\n        ra.end_date,\n        ra.is_active,\n        ra.excluded_dates,\n        e.name as employee_name,\n        sp.label as post_label,\n        sp.hours as post_hours,\n        sp.type as post_type,\n        array_length(ra.days_of_week, 1) as days_count\n      FROM regular_assignments ra\n      LEFT JOIN employees e ON ra.employee_id = e.id\n      LEFT JOIN standard_posts sp ON ra.post_id = sp.id\n      WHERE ra.is_active = true\n      ORDER BY e.name, sp.label, ra.start_date\n    ",
    {}
  ]
}
[15/07/2025 13:51:58] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 59ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:51:58.174Z",
  "originalArgs": [
    "✅ Requête exécutée en 59ms:",
    "\n      SELECT \n        ra.id,\n        ra.employee_",
    {}
  ]
}
[15/07/2025 13:51:58] [BACKEND] [INFO] ✅ Requête exécutée en 59ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
[15/07/2025 13:51:58] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"2a584d12-574b-4310-9d38-865829631d79","employee_id":"cf78e945-72c7-48f3-a72d-bd0e245a284d","post_id":"ef3246ff-8398-4f11-a498-648d207563a0","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-25T04:00:00.000Z","end_date":"2025-07-29T04:00:00.000Z","excluded_dates":[]}
[15/07/2025 13:51:58] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"7233f36d-6d3c-4085-824a-9e693eea2715","employee_id":"42b53805-18ba-425e-bb00-52bb8d6ce76b","post_id":"ef3246ff-8398-4f11-a498-648d207563a0","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-08-19T04:00:00.000Z","end_date":null,"excluded_dates":[]}
[15/07/2025 13:51:58] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"7233f36d-6d3c-4085-824a-9e693eea2715","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"isLimited":false,"startDate":"2025-08-19","endDate":null,"excludedDates":[]}
[15/07/2025 13:51:58] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"2a584d12-574b-4310-9d38-865829631d79","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-25","endDate":"2025-07-29","excludedDates":[]}
[15/07/2025 13:51:58] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"f2215562-53ed-43f8-839f-62cf07621ab3","employee_id":"cf78e945-72c7-48f3-a72d-bd0e245a284d","post_id":"ef3246ff-8398-4f11-a498-648d207563a0","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-08-14T04:00:00.000Z","end_date":"2025-08-18T04:00:00.000Z","excluded_dates":[]}
[15/07/2025 13:51:58] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"f2215562-53ed-43f8-839f-62cf07621ab3","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-08-14","endDate":"2025-08-18","excludedDates":[]}
[15/07/2025 13:51:58] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"a16c8b97-bb1b-41f0-a10f-91adc459eb80","employee_id":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","post_id":"ef3246ff-8398-4f11-a498-648d207563a0","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-24T04:00:00.000Z","end_date":"2025-07-24T04:00:00.000Z","excluded_dates":[]}
[15/07/2025 13:51:58] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","employee_id":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","post_id":"ef3246ff-8398-4f11-a498-648d207563a0","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-23T04:00:00.000Z","end_date":"2025-07-23T04:00:00.000Z","excluded_dates":[]}
[15/07/2025 13:51:58] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"87109327-e3af-4564-9b6c-402c760b130d","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-08-12","endDate":"2025-08-13","excludedDates":[]}
[15/07/2025 13:51:58] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"a563e948-18d8-4540-bfbc-7853d6506c05","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-30","endDate":"2025-08-04","excludedDates":[]}
[15/07/2025 13:51:58] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"a563e948-18d8-4540-bfbc-7853d6506c05","employee_id":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","post_id":"ef3246ff-8398-4f11-a498-648d207563a0","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-30T04:00:00.000Z","end_date":"2025-08-04T04:00:00.000Z","excluded_dates":[]}
[15/07/2025 13:51:58] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"a16c8b97-bb1b-41f0-a10f-91adc459eb80","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-24","endDate":"2025-07-24","excludedDates":[]}
[15/07/2025 13:51:58] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"87109327-e3af-4564-9b6c-402c760b130d","employee_id":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","post_id":"ef3246ff-8398-4f11-a498-648d207563a0","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-08-12T04:00:00.000Z","end_date":"2025-08-13T04:00:00.000Z","excluded_dates":[]}
[15/07/2025 13:51:58] [FRONTEND] [DEBUG] [vite] connected.
[15/07/2025 13:51:58] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-23","endDate":"2025-07-23","excludedDates":[]}
[15/07/2025 13:51:58] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"255ae26a-03c7-499f-ba7d-10568e4a8be1","employee_id":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","post_id":"ef3246ff-8398-4f11-a498-648d207563a0","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-08-05T04:00:00.000Z","end_date":"2025-08-11T04:00:00.000Z","excluded_dates":[]}
[15/07/2025 13:51:58] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"255ae26a-03c7-499f-ba7d-10568e4a8be1","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-08-05","endDate":"2025-08-11","excludedDates":[]}
[15/07/2025 13:51:58] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"b77227a7-67b7-4feb-b082-890cfeecab1f","employee_id":"59f5df3a-33ef-425c-bfa2-adca818cf94f","post_id":"ef3246ff-8398-4f11-a498-648d207563a0","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-15T04:00:00.000Z","end_date":"2025-07-22T04:00:00.000Z","excluded_dates":[]}
[15/07/2025 13:51:58] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"b77227a7-67b7-4feb-b082-890cfeecab1f","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-15","endDate":"2025-07-22","excludedDates":[]}
[15/07/2025 13:51:58] [FRONTEND] [LOG] 📋 [loadState] 9/9 assignations régulières valides chargées et normalisées
[15/07/2025 13:51:58] [FRONTEND] [LOG] 🔧 [fixInvalidAssignmentDates] Vérification des dates invalides...
[15/07/2025 13:51:58] [FRONTEND] [LOG] ✅ [fixInvalidAssignmentDates] Aucune date invalide trouvée
[15/07/2025 13:51:58] [FRONTEND] [LOG] 📋 [loadState] Assignations régulières chargées (application différée)
[15/07/2025 13:51:58] [FRONTEND] [LOG] 🧹 [cleanupDuplicateRegularShifts] Nettoyage des doublons de shifts réguliers...
[15/07/2025 13:51:58] [FRONTEND] [LOG] ✅ [cleanupDuplicateRegularShifts] Aucun doublon détecté
[15/07/2025 13:51:58] [FRONTEND] [LOG] 🧹 [cleanupInvalidAssignmentIds] Nettoyage des assignment_id invalides...
[15/07/2025 13:51:58] [FRONTEND] [LOG] ✅ [cleanupInvalidAssignmentIds] Aucun assignment_id invalide détecté
[15/07/2025 13:51:58] [FRONTEND] [LOG] ✅ [loadState] État chargé depuis l'API avec succès
[15/07/2025 13:51:58] [FRONTEND] [ERROR] TeamCalendarApp: Un ou plusieurs éléments DOM requis n'ont pas été trouvés. Le rendu est annulé.
[15/07/2025 13:51:58] [FRONTEND] [LOG] 🎉 [Agenda] Application fullscreen initialisée avec succès
[15/07/2025 13:51:59] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[15/07/2025 13:51:59] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[15/07/2025 13:51:59] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[15/07/2025 13:51:59] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[15/07/2025 13:51:59] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[15/07/2025 13:51:59] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[15/07/2025 13:51:59] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[15/07/2025 13:51:59] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[15/07/2025 13:51:59] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[15/07/2025 13:51:59] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[15/07/2025 13:51:59] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[15/07/2025 13:51:59] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[15/07/2025 13:51:59] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[15/07/2025 13:51:59] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[15/07/2025 13:51:59] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[15/07/2025 13:51:59] [FRONTEND] [LOG] [LogsPage] Fermeture de la connexion SSE.
[15/07/2025 13:51:59] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:51:59.029Z",
  "originalArgs": [
    "[query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ]
}
[15/07/2025 13:51:59] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[15/07/2025 13:51:59] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 120ms: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:51:59.149Z",
  "originalArgs": [
    "✅ Requête exécutée en 120ms:",
    "SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ]
}
[15/07/2025 13:51:59] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:51:59.199Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 13:51:59] [BACKEND] [INFO] [LOGS] Client connecté: 1752601919028
[15/07/2025 13:52:02] [BACKEND] [INFO] [LOGS] Client déconnecté: 1752601919028
[15/07/2025 13:52:02] [FRONTEND] [ERROR] [LogsPage] Erreur SSE: {"isTrusted":true}
[15/07/2025 13:52:02] [FRONTEND] [DEBUG] [vite] connected.
[15/07/2025 13:52:03] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[15/07/2025 13:52:03] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[15/07/2025 13:52:03] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[15/07/2025 13:52:03] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[15/07/2025 13:52:03] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[15/07/2025 13:52:03] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[15/07/2025 13:52:03] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[15/07/2025 13:52:03] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[15/07/2025 13:52:03] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[15/07/2025 13:52:03] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[15/07/2025 13:52:03] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[15/07/2025 13:52:03] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[15/07/2025 13:52:03] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[15/07/2025 13:52:03] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[15/07/2025 13:52:03] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[15/07/2025 13:52:03] [FRONTEND] [LOG] [LogsPage] Fermeture de la connexion SSE.
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 124ms: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 124ms:",
    "SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:52:03.142Z"
}
[15/07/2025 13:52:03] [BACKEND] [INFO] ✅ Requête exécutée en 124ms: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
[15/07/2025 13:52:09] [FRONTEND] [DEBUG] [vite] connected.
[15/07/2025 13:52:10] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[15/07/2025 13:52:10] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[15/07/2025 13:52:10] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[15/07/2025 13:52:10] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[15/07/2025 13:52:10] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[15/07/2025 13:52:10] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[15/07/2025 13:52:10] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[15/07/2025 13:52:10] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[15/07/2025 13:52:10] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[15/07/2025 13:52:10] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[15/07/2025 13:52:10] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[15/07/2025 13:52:10] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[15/07/2025 13:52:10] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[15/07/2025 13:52:10] [FRONTEND] [LOG] 🧹 [Agenda] Nettoyage composant Agenda
[15/07/2025 13:52:10] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[15/07/2025 13:52:10] [FRONTEND] [LOG] 🔄 [Agenda] Réinitialisation nécessaire (employés non chargés)
[15/07/2025 13:52:10] [FRONTEND] [LOG] 🚀 [Agenda] Initialisation TeamCalendarApp...
[15/07/2025 13:52:10] [FRONTEND] [LOG] 🚀 [init] Initialisation de TeamCalendarApp...
[15/07/2025 13:52:10] [FRONTEND] [LOG] 🧹 [clearRenderCache] Nettoyage du cache de rendu
[15/07/2025 13:52:10] [FRONTEND] [LOG] ✅ [clearRenderCache] Cache de rendu nettoyé
[15/07/2025 13:52:10] [FRONTEND] [LOG] 🔌 [ConnectionIndicator] Création de l'indicateur de connexion
[15/07/2025 13:52:10] [FRONTEND] [LOG] 🔌 [init] Indicateur de connexion initialisé
[15/07/2025 13:52:10] [FRONTEND] [LOG] 📂 [TeamCalendarApp] Chargement de l'état...
[15/07/2025 13:52:10] [FRONTEND] [LOG] 🌐 [TeamCalendarApp] Chargement depuis l'API...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT e.*, et.name as template_name 
      FROM employees e 
      LEFT JOIN employee_templates et ON e.template_id = et.id 
      ORDER BY e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT e.*, et.name as template_name \n      FROM employees e \n      LEFT JOIN employee_templates et ON e.template_id = et.id \n      ORDER BY e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:52:10.668Z"
}
[15/07/2025 13:52:10] [BACKEND] [INFO] ✅ Requête exécutée en 48ms: 
      SELECT e.*, et.name as template_name 
      {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 48ms: 
      SELECT e.*, et.name as template_name 
      {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 48ms:",
    "\n      SELECT e.*, et.name as template_name \n     ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:52:10.716Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT s.*, e.name as employee_name, sp.label as post_label
      FROM shifts s
      LEFT JOIN employees e ON s.employee_id = e.id
      LEFT JOIN standard_posts sp ON s.post_id = sp.id
      ORDER BY s.date_key, e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT s.*, e.name as employee_name, sp.label as post_label\n      FROM shifts s\n      LEFT JOIN employees e ON s.employee_id = e.id\n      LEFT JOIN standard_posts sp ON s.post_id = sp.id\n      ORDER BY s.date_key, e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:52:10.867Z"
}
[15/07/2025 13:52:11] [FRONTEND] [LOG] 👥 [TeamCalendarApp] 5 employés chargés
[15/07/2025 13:52:11] [FRONTEND] [LOG] 📦 [loadState] Chargement de tous les shifts depuis la base de données...
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 60ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 60ms:",
    "\n      SELECT s.*, e.name as employee_name, sp.lab",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:52:10.927Z"
}
[15/07/2025 13:52:11] [BACKEND] [INFO] ✅ Requête exécutée en 60ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
[15/07/2025 13:52:11] [FRONTEND] [LOG] 📅 [loadState] 0 shifts chargés depuis la base de données
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:52:11.024Z"
}
[15/07/2025 13:52:11] [FRONTEND] [LOG] ✅ [loadState] Shifts organisés dans this.data.schedule
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 52ms: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 52ms:",
    "SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:52:11.076Z"
}
[15/07/2025 13:52:11] [BACKEND] [INFO] ✅ Requête exécutée en 52ms: SELECT * FROM standard_posts ORDER BY label {}
[15/07/2025 13:52:11] [FRONTEND] [LOG] 🏢 [TeamCalendarApp] 5 postes standards chargés
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:52:11.096Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 48ms: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 48ms:",
    "SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:52:11.145Z"
}
[15/07/2025 13:52:11] [BACKEND] [INFO] ✅ Requête exécutée en 48ms: SELECT * FROM app_settings ORDER BY setting_key {}
[15/07/2025 13:52:11] [FRONTEND] [LOG] ⚙️ [loadState] Paramètres chargés depuis API: {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT 
        ra.id,
        ra.employee_id,
        ra.post_id,
        ra.days_of_week,
        ra.start_date,
        ra.end_date,
        ra.is_active,
        ra.excluded_dates,
        e.name as employee_name,
        sp.label as post_label,
        sp.hours as post_hours,
        sp.type as post_type,
        array_length(ra.days_of_week, 1) as days_count
      FROM regular_assignments ra
      LEFT JOIN employees e ON ra.employee_id = e.id
      LEFT JOIN standard_posts sp ON ra.post_id = sp.id
      WHERE ra.is_active = true
      ORDER BY e.name, sp.label, ra.start_date
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT \n        ra.id,\n        ra.employee_id,\n        ra.post_id,\n        ra.days_of_week,\n        ra.start_date,\n        ra.end_date,\n        ra.is_active,\n        ra.excluded_dates,\n        e.name as employee_name,\n        sp.label as post_label,\n        sp.hours as post_hours,\n        sp.type as post_type,\n        array_length(ra.days_of_week, 1) as days_count\n      FROM regular_assignments ra\n      LEFT JOIN employees e ON ra.employee_id = e.id\n      LEFT JOIN standard_posts sp ON ra.post_id = sp.id\n      WHERE ra.is_active = true\n      ORDER BY e.name, sp.label, ra.start_date\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:52:11.177Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 50ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 50ms:",
    "\n      SELECT \n        ra.id,\n        ra.employee_",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T17:52:11.226Z"
}
[15/07/2025 13:52:11] [BACKEND] [INFO] ✅ Requête exécutée en 50ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
[15/07/2025 13:52:11] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"2a584d12-574b-4310-9d38-865829631d79","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-25","endDate":"2025-07-29","excludedDates":[]}
[15/07/2025 13:52:11] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"7233f36d-6d3c-4085-824a-9e693eea2715","employee_id":"42b53805-18ba-425e-bb00-52bb8d6ce76b","post_id":"ef3246ff-8398-4f11-a498-648d207563a0","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-08-19T04:00:00.000Z","end_date":null,"excluded_dates":[]}
[15/07/2025 13:52:11] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"7233f36d-6d3c-4085-824a-9e693eea2715","employeeId":"42b53805-18ba-425e-bb00-52bb8d6ce76b","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"isLimited":false,"startDate":"2025-08-19","endDate":null,"excludedDates":[]}
[15/07/2025 13:52:11] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"2a584d12-574b-4310-9d38-865829631d79","employee_id":"cf78e945-72c7-48f3-a72d-bd0e245a284d","post_id":"ef3246ff-8398-4f11-a498-648d207563a0","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-25T04:00:00.000Z","end_date":"2025-07-29T04:00:00.000Z","excluded_dates":[]}
[15/07/2025 13:52:11] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"f2215562-53ed-43f8-839f-62cf07621ab3","employee_id":"cf78e945-72c7-48f3-a72d-bd0e245a284d","post_id":"ef3246ff-8398-4f11-a498-648d207563a0","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-08-14T04:00:00.000Z","end_date":"2025-08-18T04:00:00.000Z","excluded_dates":[]}
[15/07/2025 13:52:11] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"f2215562-53ed-43f8-839f-62cf07621ab3","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-08-14","endDate":"2025-08-18","excludedDates":[]}
[15/07/2025 13:52:11] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"a16c8b97-bb1b-41f0-a10f-91adc459eb80","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-24","endDate":"2025-07-24","excludedDates":[]}
[15/07/2025 13:52:11] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"a563e948-18d8-4540-bfbc-7853d6506c05","employee_id":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","post_id":"ef3246ff-8398-4f11-a498-648d207563a0","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-30T04:00:00.000Z","end_date":"2025-08-04T04:00:00.000Z","excluded_dates":[]}
[15/07/2025 13:52:11] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"a563e948-18d8-4540-bfbc-7853d6506c05","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-30","endDate":"2025-08-04","excludedDates":[]}
[15/07/2025 13:52:11] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"a16c8b97-bb1b-41f0-a10f-91adc459eb80","employee_id":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","post_id":"ef3246ff-8398-4f11-a498-648d207563a0","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-24T04:00:00.000Z","end_date":"2025-07-24T04:00:00.000Z","excluded_dates":[]}
[15/07/2025 13:52:11] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"87109327-e3af-4564-9b6c-402c760b130d","employee_id":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","post_id":"ef3246ff-8398-4f11-a498-648d207563a0","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-08-12T04:00:00.000Z","end_date":"2025-08-13T04:00:00.000Z","excluded_dates":[]}
[15/07/2025 13:52:11] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"87109327-e3af-4564-9b6c-402c760b130d","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-08-12","endDate":"2025-08-13","excludedDates":[]}
[15/07/2025 13:52:11] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-23","endDate":"2025-07-23","excludedDates":[]}
[15/07/2025 13:52:11] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"255ae26a-03c7-499f-ba7d-10568e4a8be1","employee_id":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","post_id":"ef3246ff-8398-4f11-a498-648d207563a0","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-08-05T04:00:00.000Z","end_date":"2025-08-11T04:00:00.000Z","excluded_dates":[]}
[15/07/2025 13:52:11] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"255ae26a-03c7-499f-ba7d-10568e4a8be1","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-08-05","endDate":"2025-08-11","excludedDates":[]}
[15/07/2025 13:52:11] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"fd058f0f-a569-42a7-90c2-d1d0f15bd888","employee_id":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","post_id":"ef3246ff-8398-4f11-a498-648d207563a0","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-23T04:00:00.000Z","end_date":"2025-07-23T04:00:00.000Z","excluded_dates":[]}
[15/07/2025 13:52:11] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"b77227a7-67b7-4feb-b082-890cfeecab1f","employee_id":"59f5df3a-33ef-425c-bfa2-adca818cf94f","post_id":"ef3246ff-8398-4f11-a498-648d207563a0","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-15T04:00:00.000Z","end_date":"2025-07-22T04:00:00.000Z","excluded_dates":[]}
[15/07/2025 13:52:11] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"b77227a7-67b7-4feb-b082-890cfeecab1f","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"ef3246ff-8398-4f11-a498-648d207563a0","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-15","endDate":"2025-07-22","excludedDates":[]}
[15/07/2025 13:52:11] [FRONTEND] [LOG] 📋 [loadState] 9/9 assignations régulières valides chargées et normalisées
[15/07/2025 13:52:11] [FRONTEND] [LOG] 📋 [loadState] Assignations régulières chargées (application différée)
[15/07/2025 13:52:11] [FRONTEND] [LOG] ✅ [fixInvalidAssignmentDates] Aucune date invalide trouvée
[15/07/2025 13:52:11] [FRONTEND] [LOG] 🔧 [fixInvalidAssignmentDates] Vérification des dates invalides...
[15/07/2025 13:52:11] [FRONTEND] [LOG] 🧹 [cleanupDuplicateRegularShifts] Nettoyage des doublons de shifts réguliers...
[15/07/2025 13:52:11] [FRONTEND] [LOG] ✅ [cleanupDuplicateRegularShifts] Aucun doublon détecté
[15/07/2025 13:52:11] [FRONTEND] [LOG] 🧹 [cleanupInvalidAssignmentIds] Nettoyage des assignment_id invalides...
[15/07/2025 13:52:11] [FRONTEND] [LOG] ✅ [cleanupInvalidAssignmentIds] Aucun assignment_id invalide détecté
[15/07/2025 13:52:11] [FRONTEND] [LOG] ✅ [loadState] État chargé depuis l'API avec succès
[15/07/2025 13:52:11] [FRONTEND] [LOG] 🎉 [Agenda] Application fullscreen initialisée avec succès
[15/07/2025 13:52:11] [FRONTEND] [ERROR] TeamCalendarApp: Un ou plusieurs éléments DOM requis n'ont pas été trouvés. Le rendu est annulé.