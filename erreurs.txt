[15/07/2025 14:46:25] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[15/07/2025 14:46:25] [FRONTEND] [LOG] [LogsPage] Fermeture de la connexion SSE.
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:46:25.373Z"
}
[15/07/2025 14:46:25] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:46:25.393Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:46:25.395Z"
}
[15/07/2025 14:46:25] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[15/07/2025 14:46:08] [FRONTEND] [LOG] 🎯 [setupViewNavigationListeners] Configuration des boutons de navigation...
[15/07/2025 14:46:08] [FRONTEND] [LOG] ✅ Boutons de navigation configurés
[15/07/2025 14:46:08] [BACKEND] [INFO] 📋 [GET /api/employee-order] Récupération ordre des employés
[15/07/2025 14:46:08] [BACKEND] [INFO] [LOGGER] 📋 [GET /api/employee-order] Récupération ordre des employés
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:46:08.643Z",
  "originalArgs": [
    "📋 [GET /api/employee-order] Récupération ordre des employés"
  ]
}
[15/07/2025 14:46:08] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'
         {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:46:08.645Z",
  "originalArgs": [
    "[query] Executing query: \n            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'\n        ",
    {}
  ]
}
[15/07/2025 14:46:08] [FRONTEND] [LOG] ✅ [ModalManager] Gestionnaires de modal configurés
[15/07/2025 14:46:08] [FRONTEND] [LOG] 🔧 [setupGeneralSettings] Configuration des paramètres généraux
[15/07/2025 14:46:08] [FRONTEND] [LOG] 🔧 [ModalManager] Configuration des gestionnaires de modal
[15/07/2025 14:46:08] [FRONTEND] [LOG] 🔧 [setupGeneralSettings] Création du conteneur de fuseau horaire
[15/07/2025 14:46:08] [FRONTEND] [LOG] ✅ [setupGlobalEscapeHandler] Gestionnaire ESC global configuré
[15/07/2025 14:46:09] [BACKEND] [INFO] [LOGGER] 📋 [GET /api/employee-order] Aucun ordre personnalisé trouvé
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:46:08.697Z",
  "originalArgs": [
    "📋 [GET /api/employee-order] Aucun ordre personnalisé trouvé"
  ]
}
[15/07/2025 14:46:09] [FRONTEND] [LOG] 📋 [loadEmployeeOrder] Chargement ordre des employés
[15/07/2025 14:46:09] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 52ms: 
            SELECT setting_value FROM app_setting {}
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:46:08.697Z",
  "originalArgs": [
    "✅ Requête exécutée en 52ms:",
    "\n            SELECT setting_value FROM app_setting",
    {}
  ]
}
[15/07/2025 14:46:09] [FRONTEND] [WARN] ⚠️ [loadEmployeeOrder] Réponse API sans tableau employeeOrder
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🔧 [init] Forçage du rendu initial...
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[15/07/2025 14:46:09] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 8 postes
[15/07/2025 14:46:09] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[15/07/2025 14:46:09] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 8 postes configurés dans available-posts-container (masqué)
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 14:46:09] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 60 zones trouvées
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 60 zones de drop disponibles
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 16 postes (8 cachés + 8 visibles)
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Matin: [1, 2, 3, 4, 5]
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 56baba19-67ac-484a-afa8-69e1fa1aadd9 configuré comme draggable
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Nuit: [1, 2, 3, 4, 5]
[15/07/2025 14:46:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 56baba19-67ac-484a-afa8-69e1fa1aadd9
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7fdd688d-3210-41a5-a539-8367043ef297 configuré comme draggable
[15/07/2025 14:46:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7fdd688d-3210-41a5-a539-8367043ef297
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 112bb0e0-50c9-4104-b6e3-6ca24c7fefc1 configuré comme draggable
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Nuit: [1, 2, 3, 4, 5]
[15/07/2025 14:46:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 112bb0e0-50c9-4104-b6e3-6ca24c7fefc1
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Soir: [1, 2, 3, 4, 5]
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 45db4c3d-1ef0-4c9c-b6fa-f47e6d42d4d8 configuré comme draggable
[15/07/2025 14:46:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 45db4c3d-1ef0-4c9c-b6fa-f47e6d42d4d8
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE1: [0, 6]
[15/07/2025 14:46:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 11689109-b303-4783-9e0d-57b01816f83e
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 11689109-b303-4783-9e0d-57b01816f83e configuré comme draggable
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE1: [0, 6]
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 10e1ce3e-2ac2-4d51-a1dc-f25b9f940cff configuré comme draggable
[15/07/2025 14:46:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 10e1ce3e-2ac2-4d51-a1dc-f25b9f940cff
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE2: [0, 6]
[15/07/2025 14:46:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6b34d172-8ecf-42ce-b20a-bc719a87b1d1
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE2: [0, 6]
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6b34d172-8ecf-42ce-b20a-bc719a87b1d1 configuré comme draggable
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 9080691a-54b8-48ff-a7fd-c57e469f6c48 configuré comme draggable
[15/07/2025 14:46:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 9080691a-54b8-48ff-a7fd-c57e469f6c48
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 56baba19-67ac-484a-afa8-69e1fa1aadd9 configuré comme draggable
[15/07/2025 14:46:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 56baba19-67ac-484a-afa8-69e1fa1aadd9
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7fdd688d-3210-41a5-a539-8367043ef297 configuré comme draggable
[15/07/2025 14:46:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7fdd688d-3210-41a5-a539-8367043ef297
[15/07/2025 14:46:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 112bb0e0-50c9-4104-b6e3-6ca24c7fefc1
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 112bb0e0-50c9-4104-b6e3-6ca24c7fefc1 configuré comme draggable
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 45db4c3d-1ef0-4c9c-b6fa-f47e6d42d4d8 configuré comme draggable
[15/07/2025 14:46:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 45db4c3d-1ef0-4c9c-b6fa-f47e6d42d4d8
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 11689109-b303-4783-9e0d-57b01816f83e configuré comme draggable
[15/07/2025 14:46:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 11689109-b303-4783-9e0d-57b01816f83e
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 10e1ce3e-2ac2-4d51-a1dc-f25b9f940cff configuré comme draggable
[15/07/2025 14:46:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 10e1ce3e-2ac2-4d51-a1dc-f25b9f940cff
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6b34d172-8ecf-42ce-b20a-bc719a87b1d1 configuré comme draggable
[15/07/2025 14:46:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6b34d172-8ecf-42ce-b20a-bc719a87b1d1
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 9080691a-54b8-48ff-a7fd-c57e469f6c48 configuré comme draggable
[15/07/2025 14:46:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 9080691a-54b8-48ff-a7fd-c57e469f6c48
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 14:46:09] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 14:46:09] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 14:46:09] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 14:46:09] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 14:46:09] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 14:46:09] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 14:46:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (60 zones)
[15/07/2025 14:46:09] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🔧 [init] Configuration du drag & drop des postes...
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 14:46:09] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 60 zones trouvées
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 56baba19-67ac-484a-afa8-69e1fa1aadd9 configuré comme draggable
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 16 postes (8 cachés + 8 visibles)
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 60 zones de drop disponibles
[15/07/2025 14:46:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 56baba19-67ac-484a-afa8-69e1fa1aadd9
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7fdd688d-3210-41a5-a539-8367043ef297 configuré comme draggable
[15/07/2025 14:46:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7fdd688d-3210-41a5-a539-8367043ef297
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 112bb0e0-50c9-4104-b6e3-6ca24c7fefc1 configuré comme draggable
[15/07/2025 14:46:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 112bb0e0-50c9-4104-b6e3-6ca24c7fefc1
[15/07/2025 14:46:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 45db4c3d-1ef0-4c9c-b6fa-f47e6d42d4d8
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 45db4c3d-1ef0-4c9c-b6fa-f47e6d42d4d8 configuré comme draggable
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 11689109-b303-4783-9e0d-57b01816f83e configuré comme draggable
[15/07/2025 14:46:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 11689109-b303-4783-9e0d-57b01816f83e
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 10e1ce3e-2ac2-4d51-a1dc-f25b9f940cff configuré comme draggable
[15/07/2025 14:46:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 10e1ce3e-2ac2-4d51-a1dc-f25b9f940cff
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6b34d172-8ecf-42ce-b20a-bc719a87b1d1 configuré comme draggable
[15/07/2025 14:46:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6b34d172-8ecf-42ce-b20a-bc719a87b1d1
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 9080691a-54b8-48ff-a7fd-c57e469f6c48 configuré comme draggable
[15/07/2025 14:46:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 9080691a-54b8-48ff-a7fd-c57e469f6c48
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 56baba19-67ac-484a-afa8-69e1fa1aadd9 configuré comme draggable
[15/07/2025 14:46:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 56baba19-67ac-484a-afa8-69e1fa1aadd9
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7fdd688d-3210-41a5-a539-8367043ef297 configuré comme draggable
[15/07/2025 14:46:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7fdd688d-3210-41a5-a539-8367043ef297
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 112bb0e0-50c9-4104-b6e3-6ca24c7fefc1 configuré comme draggable
[15/07/2025 14:46:09] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 45db4c3d-1ef0-4c9c-b6fa-f47e6d42d4d8 configuré comme draggable
[15/07/2025 14:46:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 112bb0e0-50c9-4104-b6e3-6ca24c7fefc1
[15/07/2025 14:46:09] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 45db4c3d-1ef0-4c9c-b6fa-f47e6d42d4d8
[15/07/2025 14:46:10] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 11689109-b303-4783-9e0d-57b01816f83e configuré comme draggable
[15/07/2025 14:46:10] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 11689109-b303-4783-9e0d-57b01816f83e
[15/07/2025 14:46:10] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 10e1ce3e-2ac2-4d51-a1dc-f25b9f940cff configuré comme draggable
[15/07/2025 14:46:10] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 10e1ce3e-2ac2-4d51-a1dc-f25b9f940cff
[15/07/2025 14:46:10] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6b34d172-8ecf-42ce-b20a-bc719a87b1d1 configuré comme draggable
[15/07/2025 14:46:10] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6b34d172-8ecf-42ce-b20a-bc719a87b1d1
[15/07/2025 14:46:10] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 9080691a-54b8-48ff-a7fd-c57e469f6c48 configuré comme draggable
[15/07/2025 14:46:10] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 9080691a-54b8-48ff-a7fd-c57e469f6c48
[15/07/2025 14:46:10] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 14:46:10] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 14:46:10] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 14:46:10] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 14:46:10] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 14:46:10] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 14:46:10] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 14:46:10] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 14:46:10] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 14:46:10] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 14:46:10] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 14:46:10] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 14:46:10] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 14:46:10] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (60 zones)
[15/07/2025 14:46:10] [FRONTEND] [LOG] ℹ️ [setupEmployeeDragDrop] Fonction désactivée - Utiliser setupDragAndDrop
[15/07/2025 14:46:10] [FRONTEND] [LOG] 🎉 [Agenda] Application fullscreen initialisée avec succès
[15/07/2025 14:46:10] [FRONTEND] [LOG] ✅ [init] Initialisation terminée avec succès
[15/07/2025 14:46:10] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] Résultats: 0 ajoutés, 0 validés, 0 corrigés
[15/07/2025 14:46:10] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 14:46:10] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 14:46:10] [FRONTEND] [LOG] 🔍 [diagnoseRegularAssignmentGrips] Diagnostic des grips d'attributions régulières
[15/07/2025 14:46:10] [FRONTEND] [LOG] 📊 [diagnoseRegularAssignmentGrips] Statistiques: {"totalShifts":0,"regularShifts":0,"regularAssignments":0}
[15/07/2025 14:46:10] [FRONTEND] [LOG] 🎯 [diagnoseRegularAssignmentGrips] Grips trouvés dans le DOM: 0
[15/07/2025 14:46:10] [FRONTEND] [LOG] ⚠️ [diagnoseRegularAssignmentGrips] Aucun shift régulier trouvé - Ré-application des attributions...
[15/07/2025 14:46:10] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 14:46:10] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 14:46:13] [FRONTEND] [LOG] 🎯 [POST DRAG] Début drag poste: 112bb0e0-50c9-4104-b6e3-6ca24c7fefc1 (Poste Nuit)
[15/07/2025 14:46:13] [FRONTEND] [LOG] 🎯 [POST DRAG] DataTransfer configuré: {"postId":"112bb0e0-50c9-4104-b6e3-6ca24c7fefc1","label":"Poste Nuit","types":["text/plain","application/x-post-id","postid","postlabel"]}
[15/07/2025 14:46:13] [FRONTEND] [LOG] 🎨 [enableAgendaPreviewMode] Activation preview agenda pour poste 112bb0e0-50c9-4104-b6e3-6ca24c7fefc1
[15/07/2025 14:46:13] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 14:46:13] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 14:46:13] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] Ajout des fantômes pour Poste Nuit
[15/07/2025 14:46:13] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] 25 fantômes créés
[15/07/2025 14:46:13] [FRONTEND] [LOG] 🖱️ [enableCellHoverTracking] Suivi activé sur 25 cellules
[15/07/2025 14:46:13] [FRONTEND] [LOG] ✅ [enableAgendaPreviewMode] Mode preview activé pour Poste Nuit
[15/07/2025 14:46:13] [FRONTEND] [LOG] 🎯 [POST DRAG] 60 zones préparées pour le drop
[15/07/2025 14:46:13] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 59f5df3a-33ef-425c-bfa2-adca818cf94f, types: text/plain, application/x-post-id, postid, postlabel
[15/07/2025 14:46:14] [FRONTEND] [LOG] 🎯 [POST DRAG] Début drag poste: 112bb0e0-50c9-4104-b6e3-6ca24c7fefc1 (Poste Nuit)
[15/07/2025 14:46:14] [FRONTEND] [LOG] 🎨 [enableAgendaPreviewMode] Activation preview agenda pour poste 112bb0e0-50c9-4104-b6e3-6ca24c7fefc1
[15/07/2025 14:46:14] [FRONTEND] [LOG] 🎯 [POST DRAG] DataTransfer configuré: {"postId":"112bb0e0-50c9-4104-b6e3-6ca24c7fefc1","label":"Poste Nuit","types":["text/plain","application/x-post-id","postid","postlabel"]}
[15/07/2025 14:46:14] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 14:46:14] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] Ajout des fantômes pour Poste Nuit
[15/07/2025 14:46:14] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 14:46:14] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] 25 fantômes créés
[15/07/2025 14:46:14] [FRONTEND] [LOG] 🖱️ [enableCellHoverTracking] Suivi activé sur 50 cellules
[15/07/2025 14:46:14] [FRONTEND] [LOG] ✅ [enableAgendaPreviewMode] Mode preview activé pour Poste Nuit
[15/07/2025 14:46:14] [FRONTEND] [LOG] 🎯 [POST DRAG] 60 zones préparées pour le drop
[15/07/2025 14:46:14] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 59f5df3a-33ef-425c-bfa2-adca818cf94f, types: text/plain, application/x-post-id, postid, postlabel
[15/07/2025 14:46:14] [FRONTEND] [LOG] 🎯 [DROP] ✅ Drop reçu sur employé: Sophie Leblanc
[15/07/2025 14:46:14] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 14:46:14] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 14:46:14] [FRONTEND] [LOG] ✅ [DROP] PostId trouvé via application/x-post-id: "112bb0e0-50c9-4104-b6e3-6ca24c7fefc1"
[15/07/2025 14:46:14] [FRONTEND] [LOG] 🎯 [openAssignmentContextModal] Poste brut: 112bb0e0-50c9-4104-b6e3-6ca24c7fefc1, Employé: 59f5df3a-33ef-425c-bfa2-adca818cf94f, Contexte: null
[15/07/2025 14:46:14] [FRONTEND] [LOG] 📦 [DROP] Nouveau poste: 112bb0e0-50c9-4104-b6e3-6ca24c7fefc1 → 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 14:46:14] [FRONTEND] [LOG] ℹ️ [openAssignmentContextModal] Option "Attribution unique (cellule)" masquée - contexte inapproprié
[15/07/2025 14:46:14] [FRONTEND] [LOG] 📅 [openAssignmentContextModal] Date initialisée: 2025-07-15
[15/07/2025 14:46:14] [FRONTEND] [LOG] ✅ [DROP] Modal d'assignation ouvert
[15/07/2025 14:46:14] [FRONTEND] [LOG] 🎯 [POST DRAG] Fin drag poste: 112bb0e0-50c9-4104-b6e3-6ca24c7fefc1
[15/07/2025 14:46:14] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 14:46:14] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 14:46:14] [FRONTEND] [LOG] 🎯 [POST DRAG] Nettoyage des 60 zones
[15/07/2025 14:46:14] [FRONTEND] [LOG] 🎯 [POST DRAG] Fin drag poste: 112bb0e0-50c9-4104-b6e3-6ca24c7fefc1
[15/07/2025 14:46:14] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 14:46:14] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 14:46:14] [FRONTEND] [LOG] 🎯 [POST DRAG] Nettoyage des 60 zones
[15/07/2025 14:46:25] [FRONTEND] [DEBUG] [vite] connected.
[15/07/2025 14:46:25] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:46:24.842Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 14:46:25] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:46:24.961Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 14:46:25] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[15/07/2025 14:46:25] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:46:25.083Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 14:46:25] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:46:24.962Z",
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ]
}
[15/07/2025 14:46:25] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[15/07/2025 14:46:25] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[15/07/2025 14:46:25] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[15/07/2025 14:46:25] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[15/07/2025 14:46:25] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[15/07/2025 14:46:25] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[15/07/2025 14:46:25] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[15/07/2025 14:46:25] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[15/07/2025 14:46:25] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[15/07/2025 14:46:25] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[15/07/2025 14:46:25] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[15/07/2025 14:46:25] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[15/07/2025 14:46:25] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[15/07/2025 14:46:25] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[15/07/2025 14:46:25] [FRONTEND] [LOG] [LogsPage] Fermeture de la connexion SSE.
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 105ms: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 105ms:",
    "SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:46:25.478Z"
}
[15/07/2025 14:46:25] [BACKEND] [INFO] [LOGS] Client connecté: 1752605185371
[15/07/2025 14:46:25] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:46:25.531Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:46:25.531Z"
}
[15/07/2025 14:46:28] [FRONTEND] [DEBUG] [vite] connected.
[15/07/2025 14:46:29] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[15/07/2025 14:46:29] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[15/07/2025 14:46:29] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[15/07/2025 14:46:29] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[15/07/2025 14:46:29] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[15/07/2025 14:46:29] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[15/07/2025 14:46:29] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[15/07/2025 14:46:29] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[15/07/2025 14:46:29] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[15/07/2025 14:46:29] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[15/07/2025 14:46:29] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[15/07/2025 14:46:29] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[15/07/2025 14:46:29] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[15/07/2025 14:46:29] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[15/07/2025 14:46:29] [FRONTEND] [LOG] 🧹 [Agenda] Nettoyage composant Agenda
[15/07/2025 14:46:29] [FRONTEND] [LOG] 🔄 [Agenda] Réinitialisation nécessaire (employés non chargés)
[15/07/2025 14:46:29] [FRONTEND] [LOG] 🚀 [Agenda] Initialisation TeamCalendarApp...
[15/07/2025 14:46:29] [FRONTEND] [LOG] 🚀 [init] Initialisation de TeamCalendarApp...
[15/07/2025 14:46:29] [FRONTEND] [LOG] ✅ [clearRenderCache] Cache de rendu nettoyé
[15/07/2025 14:46:29] [FRONTEND] [LOG] 🧹 [clearRenderCache] Nettoyage du cache de rendu
[15/07/2025 14:46:29] [FRONTEND] [LOG] 🔌 [ConnectionIndicator] Création de l'indicateur de connexion
[15/07/2025 14:46:29] [FRONTEND] [LOG] 🔌 [init] Indicateur de connexion initialisé
[15/07/2025 14:46:29] [FRONTEND] [LOG] 📂 [TeamCalendarApp] Chargement de l'état...
[15/07/2025 14:46:29] [FRONTEND] [LOG] 🌐 [TeamCalendarApp] Chargement depuis l'API...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT e.*, et.name as template_name 
      FROM employees e 
      LEFT JOIN employee_templates et ON e.template_id = et.id 
      ORDER BY e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT e.*, et.name as template_name \n      FROM employees e \n      LEFT JOIN employee_templates et ON e.template_id = et.id \n      ORDER BY e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:46:29.362Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 58ms: 
      SELECT e.*, et.name as template_name 
      {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 58ms:",
    "\n      SELECT e.*, et.name as template_name \n     ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:46:29.419Z"
}
[15/07/2025 14:46:29] [BACKEND] [INFO] ✅ Requête exécutée en 58ms: 
      SELECT e.*, et.name as template_name 
      {}
[15/07/2025 14:46:29] [FRONTEND] [LOG] 👥 [TeamCalendarApp] 5 employés chargés
[15/07/2025 14:46:29] [FRONTEND] [LOG] 📦 [loadState] Chargement de tous les shifts depuis la base de données...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT s.*, e.name as employee_name, sp.label as post_label
      FROM shifts s
      LEFT JOIN employees e ON s.employee_id = e.id
      LEFT JOIN standard_posts sp ON s.post_id = sp.id
      ORDER BY s.date_key, e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT s.*, e.name as employee_name, sp.label as post_label\n      FROM shifts s\n      LEFT JOIN employees e ON s.employee_id = e.id\n      LEFT JOIN standard_posts sp ON s.post_id = sp.id\n      ORDER BY s.date_key, e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:46:29.453Z"
}
[15/07/2025 14:46:29] [BACKEND] [INFO] ✅ Requête exécutée en 47ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 47ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 47ms:",
    "\n      SELECT s.*, e.name as employee_name, sp.lab",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:46:29.500Z"
}
[15/07/2025 14:46:29] [FRONTEND] [LOG] 📅 [loadState] 0 shifts chargés depuis la base de données
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:46:29.553Z"
}
[15/07/2025 14:46:29] [FRONTEND] [LOG] ✅ [loadState] Shifts organisés dans this.data.schedule
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 39ms: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 39ms:",
    "SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:46:29.592Z"
}
[15/07/2025 14:46:29] [BACKEND] [INFO] ✅ Requête exécutée en 39ms: SELECT * FROM standard_posts ORDER BY label {}
[15/07/2025 14:46:30] [FRONTEND] [LOG] 🏢 [TeamCalendarApp] 8 postes standards chargés
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:46:29.711Z"
}
[15/07/2025 14:46:30] [BACKEND] [INFO] ✅ Requête exécutée en 50ms: SELECT * FROM app_settings ORDER BY setting_key {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 50ms: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 50ms:",
    "SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:46:29.761Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT 
        ra.id,
        ra.employee_id,
        ra.post_id,
        ra.days_of_week,
        ra.start_date,
        ra.end_date,
        ra.is_active,
        ra.excluded_dates,
        e.name as employee_name,
        sp.label as post_label,
        sp.hours as post_hours,
        sp.type as post_type,
        array_length(ra.days_of_week, 1) as days_count
      FROM regular_assignments ra
      LEFT JOIN employees e ON ra.employee_id = e.id
      LEFT JOIN standard_posts sp ON ra.post_id = sp.id
      WHERE ra.is_active = true
      ORDER BY e.name, sp.label, ra.start_date
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT \n        ra.id,\n        ra.employee_id,\n        ra.post_id,\n        ra.days_of_week,\n        ra.start_date,\n        ra.end_date,\n        ra.is_active,\n        ra.excluded_dates,\n        e.name as employee_name,\n        sp.label as post_label,\n        sp.hours as post_hours,\n        sp.type as post_type,\n        array_length(ra.days_of_week, 1) as days_count\n      FROM regular_assignments ra\n      LEFT JOIN employees e ON ra.employee_id = e.id\n      LEFT JOIN standard_posts sp ON ra.post_id = sp.id\n      WHERE ra.is_active = true\n      ORDER BY e.name, sp.label, ra.start_date\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:46:29.812Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 48ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 48ms:",
    "\n      SELECT \n        ra.id,\n        ra.employee_",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:46:29.861Z"
}
[15/07/2025 14:46:30] [BACKEND] [INFO] ✅ Requête exécutée en 48ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
[15/07/2025 14:46:30] [FRONTEND] [LOG] ⚙️ [loadState] Paramètres chargés depuis API: {}
[15/07/2025 14:46:30] [FRONTEND] [LOG] 📋 [loadState] 0/0 assignations régulières valides chargées et normalisées
[15/07/2025 14:46:30] [FRONTEND] [LOG] ✅ [fixInvalidAssignmentDates] Aucune date invalide trouvée
[15/07/2025 14:46:30] [FRONTEND] [LOG] 🧹 [cleanupDuplicateRegularShifts] Nettoyage des doublons de shifts réguliers...
[15/07/2025 14:46:30] [FRONTEND] [LOG] 🔧 [fixInvalidAssignmentDates] Vérification des dates invalides...
[15/07/2025 14:46:30] [FRONTEND] [LOG] 📋 [loadState] Assignations régulières chargées (application différée)
[15/07/2025 14:46:30] [FRONTEND] [LOG] ✅ [cleanupDuplicateRegularShifts] Aucun doublon détecté
[15/07/2025 14:46:30] [FRONTEND] [LOG] 🧹 [cleanupInvalidAssignmentIds] Nettoyage des assignment_id invalides...
[15/07/2025 14:46:30] [FRONTEND] [LOG] ✅ [cleanupInvalidAssignmentIds] Aucun assignment_id invalide détecté
[15/07/2025 14:46:30] [FRONTEND] [LOG] ✅ [loadState] État chargé depuis l'API avec succès
[15/07/2025 14:46:30] [FRONTEND] [LOG] ✅ [init] Icône paramètres trouvée: [Objet non sérialisable]
[15/07/2025 14:46:30] [FRONTEND] [LOG] 🔍 [init] Recherche du bouton des paramètres dans le DOM
[15/07/2025 14:46:30] [FRONTEND] [LOG] ✅ [setupNavigationListeners] Écouteurs de navigation attachés
[15/07/2025 14:46:30] [FRONTEND] [LOG] 🔍 [init] Bouton des paramètres trouvé ? true
[15/07/2025 14:46:30] [FRONTEND] [LOG] 🔍 [init] Attachement du listener click sur le bouton des paramètres
[15/07/2025 14:46:30] [FRONTEND] [LOG] 🎯 [setupViewNavigationListeners] Configuration des boutons de navigation...
[15/07/2025 14:46:30] [FRONTEND] [LOG] ✅ Boutons de navigation configurés
[15/07/2025 14:46:30] [FRONTEND] [LOG] 🔧 [ModalManager] Configuration des gestionnaires de modal
[15/07/2025 14:46:30] [FRONTEND] [LOG] 🔧 [setupGeneralSettings] Configuration des paramètres généraux
[15/07/2025 14:46:30] [FRONTEND] [LOG] ✅ [ModalManager] Gestionnaires de modal configurés
[15/07/2025 14:46:30] [FRONTEND] [LOG] 🔧 [setupGeneralSettings] Création du conteneur de fuseau horaire
[15/07/2025 14:46:30] [FRONTEND] [LOG] ✅ [setupGlobalEscapeHandler] Gestionnaire ESC global configuré
[15/07/2025 14:46:30] [FRONTEND] [LOG] 📋 [loadEmployeeOrder] Chargement ordre des employés
[15/07/2025 14:46:30] [BACKEND] [INFO] 📋 [GET /api/employee-order] Récupération ordre des employés
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📋 [GET /api/employee-order] Récupération ordre des employés
  Data: {
  "originalArgs": [
    "📋 [GET /api/employee-order] Récupération ordre des employés"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:46:30.181Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'
         {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'\n        ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:46:30.183Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📋 [GET /api/employee-order] Aucun ordre personnalisé trouvé
  Data: {
  "originalArgs": [
    "📋 [GET /api/employee-order] Aucun ordre personnalisé trouvé"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:46:30.221Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 37ms: 
            SELECT setting_value FROM app_setting {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 37ms:",
    "\n            SELECT setting_value FROM app_setting",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-15T18:46:30.221Z"
}
[15/07/2025 14:46:30] [FRONTEND] [WARN] ⚠️ [loadEmployeeOrder] Réponse API sans tableau employeeOrder
[15/07/2025 14:46:30] [FRONTEND] [LOG] 🔧 [init] Forçage du rendu initial...
[15/07/2025 14:46:30] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[15/07/2025 14:46:30] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[15/07/2025 14:46:30] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 8 postes
[15/07/2025 14:46:30] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[15/07/2025 14:46:30] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 8 postes configurés dans available-posts-container (masqué)
[15/07/2025 14:46:30] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 14:46:30] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 14:46:30] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 60 zones trouvées
[15/07/2025 14:46:30] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 60 zones de drop disponibles
[15/07/2025 14:46:30] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 16 postes (8 cachés + 8 visibles)
[15/07/2025 14:46:30] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Matin: [1, 2, 3, 4, 5]
[15/07/2025 14:46:30] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 56baba19-67ac-484a-afa8-69e1fa1aadd9 configuré comme draggable
[15/07/2025 14:46:30] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 56baba19-67ac-484a-afa8-69e1fa1aadd9
[15/07/2025 14:46:30] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Nuit: [1, 2, 3, 4, 5]
[15/07/2025 14:46:30] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7fdd688d-3210-41a5-a539-8367043ef297 configuré comme draggable
[15/07/2025 14:46:31] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 45db4c3d-1ef0-4c9c-b6fa-f47e6d42d4d8
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 11689109-b303-4783-9e0d-57b01816f83e configuré comme draggable
[15/07/2025 14:46:31] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 11689109-b303-4783-9e0d-57b01816f83e
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 14:46:31] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7fdd688d-3210-41a5-a539-8367043ef297
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Nuit: [1, 2, 3, 4, 5]
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 112bb0e0-50c9-4104-b6e3-6ca24c7fefc1 configuré comme draggable
[15/07/2025 14:46:31] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 112bb0e0-50c9-4104-b6e3-6ca24c7fefc1
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 45db4c3d-1ef0-4c9c-b6fa-f47e6d42d4d8 configuré comme draggable
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Soir: [1, 2, 3, 4, 5]
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE1: [0, 6]
[15/07/2025 14:46:31] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 45db4c3d-1ef0-4c9c-b6fa-f47e6d42d4d8
[15/07/2025 14:46:31] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 11689109-b303-4783-9e0d-57b01816f83e
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 11689109-b303-4783-9e0d-57b01816f83e configuré comme draggable
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE1: [0, 6]
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 10e1ce3e-2ac2-4d51-a1dc-f25b9f940cff configuré comme draggable
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE2: [0, 6]
[15/07/2025 14:46:31] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 10e1ce3e-2ac2-4d51-a1dc-f25b9f940cff
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE2: [0, 6]
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6b34d172-8ecf-42ce-b20a-bc719a87b1d1 configuré comme draggable
[15/07/2025 14:46:31] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6b34d172-8ecf-42ce-b20a-bc719a87b1d1
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 56baba19-67ac-484a-afa8-69e1fa1aadd9 configuré comme draggable
[15/07/2025 14:46:31] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 9080691a-54b8-48ff-a7fd-c57e469f6c48
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 9080691a-54b8-48ff-a7fd-c57e469f6c48 configuré comme draggable
[15/07/2025 14:46:31] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7fdd688d-3210-41a5-a539-8367043ef297
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7fdd688d-3210-41a5-a539-8367043ef297 configuré comme draggable
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 45db4c3d-1ef0-4c9c-b6fa-f47e6d42d4d8 configuré comme draggable
[15/07/2025 14:46:31] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 56baba19-67ac-484a-afa8-69e1fa1aadd9
[15/07/2025 14:46:31] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 112bb0e0-50c9-4104-b6e3-6ca24c7fefc1
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 112bb0e0-50c9-4104-b6e3-6ca24c7fefc1 configuré comme draggable
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 10e1ce3e-2ac2-4d51-a1dc-f25b9f940cff configuré comme draggable
[15/07/2025 14:46:31] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 11689109-b303-4783-9e0d-57b01816f83e
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 11689109-b303-4783-9e0d-57b01816f83e configuré comme draggable
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6b34d172-8ecf-42ce-b20a-bc719a87b1d1 configuré comme draggable
[15/07/2025 14:46:31] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 10e1ce3e-2ac2-4d51-a1dc-f25b9f940cff
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 9080691a-54b8-48ff-a7fd-c57e469f6c48 configuré comme draggable
[15/07/2025 14:46:31] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 10e1ce3e-2ac2-4d51-a1dc-f25b9f940cff
[15/07/2025 14:46:31] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 9080691a-54b8-48ff-a7fd-c57e469f6c48
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🔍 [diagnoseRegularAssignmentGrips] Diagnostic des grips d'attributions régulières
[15/07/2025 14:46:31] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6b34d172-8ecf-42ce-b20a-bc719a87b1d1
[15/07/2025 14:46:31] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 9080691a-54b8-48ff-a7fd-c57e469f6c48
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 14:46:31] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 14:46:31] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 14:46:31] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 14:46:31] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 14:46:31] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 14:46:31] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[15/07/2025 14:46:31] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (60 zones)
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[15/07/2025 14:46:31] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🔧 [init] Configuration du drag & drop des postes...
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 60 zones de drop disponibles
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 16 postes (8 cachés + 8 visibles)
[15/07/2025 14:46:31] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 56baba19-67ac-484a-afa8-69e1fa1aadd9
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7fdd688d-3210-41a5-a539-8367043ef297 configuré comme draggable
[15/07/2025 14:46:31] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 60 zones trouvées
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 112bb0e0-50c9-4104-b6e3-6ca24c7fefc1 configuré comme draggable
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 56baba19-67ac-484a-afa8-69e1fa1aadd9 configuré comme draggable
[15/07/2025 14:46:31] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7fdd688d-3210-41a5-a539-8367043ef297
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 14:46:31] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 112bb0e0-50c9-4104-b6e3-6ca24c7fefc1
[15/07/2025 14:46:31] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 45db4c3d-1ef0-4c9c-b6fa-f47e6d42d4d8
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 45db4c3d-1ef0-4c9c-b6fa-f47e6d42d4d8 configuré comme draggable
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6b34d172-8ecf-42ce-b20a-bc719a87b1d1 configuré comme draggable
[15/07/2025 14:46:31] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 11689109-b303-4783-9e0d-57b01816f83e
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 10e1ce3e-2ac2-4d51-a1dc-f25b9f940cff configuré comme draggable
[15/07/2025 14:46:31] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6b34d172-8ecf-42ce-b20a-bc719a87b1d1
[15/07/2025 14:46:31] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 56baba19-67ac-484a-afa8-69e1fa1aadd9
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 9080691a-54b8-48ff-a7fd-c57e469f6c48 configuré comme draggable
[15/07/2025 14:46:31] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 9080691a-54b8-48ff-a7fd-c57e469f6c48
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 56baba19-67ac-484a-afa8-69e1fa1aadd9 configuré comme draggable
[15/07/2025 14:46:31] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7fdd688d-3210-41a5-a539-8367043ef297
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7fdd688d-3210-41a5-a539-8367043ef297 configuré comme draggable
[15/07/2025 14:46:31] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 112bb0e0-50c9-4104-b6e3-6ca24c7fefc1
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 112bb0e0-50c9-4104-b6e3-6ca24c7fefc1 configuré comme draggable
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 45db4c3d-1ef0-4c9c-b6fa-f47e6d42d4d8 configuré comme draggable
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 11689109-b303-4783-9e0d-57b01816f83e configuré comme draggable
[15/07/2025 14:46:31] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 45db4c3d-1ef0-4c9c-b6fa-f47e6d42d4d8
[15/07/2025 14:46:31] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 10e1ce3e-2ac2-4d51-a1dc-f25b9f940cff
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 10e1ce3e-2ac2-4d51-a1dc-f25b9f940cff configuré comme draggable
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 6b34d172-8ecf-42ce-b20a-bc719a87b1d1 configuré comme draggable
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 9080691a-54b8-48ff-a7fd-c57e469f6c48 configuré comme draggable
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[15/07/2025 14:46:31] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 6b34d172-8ecf-42ce-b20a-bc719a87b1d1
[15/07/2025 14:46:31] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[15/07/2025 14:46:32] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[15/07/2025 14:46:32] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 14:46:32] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 14:46:32] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[15/07/2025 14:46:32] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[15/07/2025 14:46:32] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[15/07/2025 14:46:32] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 14:46:32] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 14:46:32] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[15/07/2025 14:46:32] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (60 zones)
[15/07/2025 14:46:32] [FRONTEND] [LOG] ℹ️ [setupEmployeeDragDrop] Fonction désactivée - Utiliser setupDragAndDrop
[15/07/2025 14:46:32] [FRONTEND] [LOG] ✅ [init] Initialisation terminée avec succès
[15/07/2025 14:46:32] [FRONTEND] [LOG] 🎉 [Agenda] Application fullscreen initialisée avec succès
[15/07/2025 14:46:32] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] Résultats: 0 ajoutés, 0 validés, 0 corrigés
[15/07/2025 14:46:32] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 14:46:32] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 14:46:32] [FRONTEND] [LOG] 📊 [diagnoseRegularAssignmentGrips] Statistiques: {"totalShifts":0,"regularShifts":0,"regularAssignments":0}
[15/07/2025 14:46:32] [FRONTEND] [LOG] 🎯 [diagnoseRegularAssignmentGrips] Grips trouvés dans le DOM: 0
[15/07/2025 14:46:32] [FRONTEND] [LOG] ⚠️ [diagnoseRegularAssignmentGrips] Aucun shift régulier trouvé - Ré-application des attributions...
[15/07/2025 14:46:32] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[15/07/2025 14:46:32] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[15/07/2025 14:46:32] [FRONTEND] [LOG] 🎯 [POST DRAG] Début drag poste: 112bb0e0-50c9-4104-b6e3-6ca24c7fefc1 (Poste Nuit)
[15/07/2025 14:46:32] [FRONTEND] [LOG] 🎯 [POST DRAG] DataTransfer configuré: {"postId":"112bb0e0-50c9-4104-b6e3-6ca24c7fefc1","label":"Poste Nuit","types":["text/plain","application/x-post-id","postid","postlabel"]}
[15/07/2025 14:46:32] [FRONTEND] [LOG] 🎨 [enableAgendaPreviewMode] Activation preview agenda pour poste 112bb0e0-50c9-4104-b6e3-6ca24c7fefc1
[15/07/2025 14:46:32] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 14:46:32] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 14:46:32] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] Ajout des fantômes pour Poste Nuit
[15/07/2025 14:46:32] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] 25 fantômes créés
[15/07/2025 14:46:32] [FRONTEND] [LOG] 🖱️ [enableCellHoverTracking] Suivi activé sur 25 cellules
[15/07/2025 14:46:33] [FRONTEND] [LOG] ✅ [enableAgendaPreviewMode] Mode preview activé pour Poste Nuit
[15/07/2025 14:46:33] [FRONTEND] [LOG] 🎯 [POST DRAG] 60 zones préparées pour le drop
[15/07/2025 14:46:33] [FRONTEND] [LOG] 🎯 [POST DRAG] Début drag poste: 112bb0e0-50c9-4104-b6e3-6ca24c7fefc1 (Poste Nuit)
[15/07/2025 14:46:33] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 14:46:33] [FRONTEND] [LOG] 🎨 [enableAgendaPreviewMode] Activation preview agenda pour poste 112bb0e0-50c9-4104-b6e3-6ca24c7fefc1
[15/07/2025 14:46:33] [FRONTEND] [LOG] 🎯 [POST DRAG] DataTransfer configuré: {"postId":"112bb0e0-50c9-4104-b6e3-6ca24c7fefc1","label":"Poste Nuit","types":["text/plain","application/x-post-id","postid","postlabel"]}
[15/07/2025 14:46:33] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 14:46:33] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] Ajout des fantômes pour Poste Nuit
[15/07/2025 14:46:33] [FRONTEND] [LOG] 👻 [addGhostPreviewsToCompatibleCells] 25 fantômes créés
[15/07/2025 14:46:33] [FRONTEND] [LOG] ✅ [enableAgendaPreviewMode] Mode preview activé pour Poste Nuit
[15/07/2025 14:46:33] [FRONTEND] [LOG] 🖱️ [enableCellHoverTracking] Suivi activé sur 50 cellules
[15/07/2025 14:46:33] [FRONTEND] [LOG] 🎯 [POST DRAG] 60 zones préparées pour le drop
[15/07/2025 14:46:33] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 59f5df3a-33ef-425c-bfa2-adca818cf94f, types: text/plain, application/x-post-id, postid, postlabel
[15/07/2025 14:46:33] [FRONTEND] [LOG] 🎯 [DRAGOVER] Sur employé 59f5df3a-33ef-425c-bfa2-adca818cf94f, types: text/plain, application/x-post-id, postid, postlabel
[15/07/2025 14:46:33] [FRONTEND] [LOG] 🎯 [DROP] ✅ Drop reçu sur employé: Sophie Leblanc
[15/07/2025 14:46:33] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 14:46:33] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 14:46:33] [FRONTEND] [LOG] ✅ [DROP] PostId trouvé via application/x-post-id: "112bb0e0-50c9-4104-b6e3-6ca24c7fefc1"
[15/07/2025 14:46:33] [FRONTEND] [LOG] 📦 [DROP] Nouveau poste: 112bb0e0-50c9-4104-b6e3-6ca24c7fefc1 → 59f5df3a-33ef-425c-bfa2-adca818cf94f
[15/07/2025 14:46:33] [FRONTEND] [LOG] 🎯 [openAssignmentContextModal] Poste brut: 112bb0e0-50c9-4104-b6e3-6ca24c7fefc1, Employé: 59f5df3a-33ef-425c-bfa2-adca818cf94f, Contexte: null
[15/07/2025 14:46:33] [FRONTEND] [LOG] ℹ️ [openAssignmentContextModal] Option "Attribution unique (cellule)" masquée - contexte inapproprié
[15/07/2025 14:46:33] [FRONTEND] [LOG] 📅 [openAssignmentContextModal] Date initialisée: 2025-07-15
[15/07/2025 14:46:33] [FRONTEND] [LOG] ✅ [DROP] Modal d'assignation ouvert
[15/07/2025 14:46:33] [FRONTEND] [LOG] 🎯 [POST DRAG] Fin drag poste: 112bb0e0-50c9-4104-b6e3-6ca24c7fefc1
[15/07/2025 14:46:33] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 14:46:33] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 14:46:33] [FRONTEND] [LOG] 🎯 [POST DRAG] Nettoyage des 60 zones
[15/07/2025 14:46:33] [FRONTEND] [LOG] 🎯 [POST DRAG] Fin drag poste: 112bb0e0-50c9-4104-b6e3-6ca24c7fefc1
[15/07/2025 14:46:33] [FRONTEND] [LOG] 🗑️ [disableAgendaPreviewMode] Désactivation du mode preview
[15/07/2025 14:46:33] [FRONTEND] [LOG] ✅ [disableAgendaPreviewMode] Mode preview désactivé
[15/07/2025 14:46:33] [FRONTEND] [LOG] 🎯 [POST DRAG] Nettoyage des 60 zones