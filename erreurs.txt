🎨 [FRONTEND] Démarrage du serveur de développement...

> tempapp@0.0.0 dev
> vite


  VITE v6.3.5  ready in 386 ms

  ➜  Local:   http://localhost:5173/
  ➜  Network: use --host to expose
  ➜  press h + enter to show help
14 h 11 min 18 s [vite] (client) warning: Duplicate key "detectDropDateFromPosition" in object literal
12031|  
12032|      // ✅ CORRECTION CRITIQUE : Fonction manquante detectDropDateFromPosition
12033|      detectDropDateFromPosition: function(x: number, y: number): string | null {
   |      ^
12034|          console.log(`🎯 [detectDropDateFromPosition] Détection de date à la position (${x}, ${y})`);
12035|  

  Plugin: vite:esbuild
  File: C:/Users/<USER>/Desktop/interface/interface-3/interface - 1 Backup/src/teamCalendarApp.ts
[{
	"resource": "/c:/Users/<USER>/Desktop/interface/interface-3/interface - 1 Backup/backups/backup-2025-07-07T16-46-28-613Z/tsconfig.json",
	"owner": "typescript",
	"severity": 8,
	"message": "File 'c:/Users/<USER>/Desktop/interface/interface-3/interface - 1 Backup/backups/backup-2025-07-07T16-46-28-613Z/tsconfig.node.json' not found.",
	"source": "ts",
	"startLineNumber": 36,
	"startColumn": 18,
	"endLineNumber": 36,
	"endColumn": 52,
	"origin": "extHost1"
}]
[{
	"resource": "/c:/Users/<USER>/Desktop/interface/interface-3/interface - 1 Backup/src/modalFunctionalities.ts",
	"owner": "typescript",
	"code": "2687",
	"severity": 8,
	"message": "All declarations of 'TeamCalendarApp' must have identical modifiers.",
	"source": "ts",
	"startLineNumber": 13,
	"startColumn": 5,
	"endLineNumber": 13,
	"endColumn": 20,
	"origin": "extHost1"
}]
[{
	"resource": "/c:/Users/<USER>/Desktop/interface/interface-3/interface - 1 Backup/src/teamCalendarApp.ts",
	"owner": "typescript",
	"code": "2687",
	"severity": 8,
	"message": "All declarations of 'TeamCalendarApp' must have identical modifiers.",
	"source": "ts",
	"startLineNumber": 20,
	"startColumn": 5,
	"endLineNumber": 20,
	"endColumn": 20,
	"origin": "extHost1"
},{
	"resource": "/c:/Users/<USER>/Desktop/interface/interface-3/interface - 1 Backup/src/teamCalendarApp.ts",
	"owner": "typescript",
	"code": "1117",
	"severity": 8,
	"message": "An object literal cannot have multiple properties with the same name.",
	"source": "ts",
	"startLineNumber": 12033,
	"startColumn": 5,
	"endLineNumber": 12033,
	"endColumn": 31,
	"origin": "extHost1"
}]
[18/07/2025 14:12:07] [FRONTEND] [LOG] 🧪 [TEST-DRAG-DROP] Démarrage des tests complets de drag & drop...
[18/07/2025 14:12:07] [FRONTEND] [LOG] ✅ [TEST-DRAG-DROP] Script chargé. Utilisez runComprehensiveDragDropTests() pour tester.
[18/07/2025 14:12:07] [FRONTEND] [LOG] [LogsPage] Fermeture de la connexion SSE.
[18/07/2025 14:12:07] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[18/07/2025 14:12:07] [BACKEND] [INFO] [LOGS] Client connecté: 1752862327583
[18/07/2025 14:12:07] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:12:07.585Z"
}
[18/07/2025 14:12:04] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[18/07/2025 14:12:04] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[18/07/2025 14:12:04] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[18/07/2025 14:12:04] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[18/07/2025 14:12:04] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 62 zones trouvées
[18/07/2025 14:12:04] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 62 zones de drop disponibles
[18/07/2025 14:12:04] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[18/07/2025 14:12:04] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 14:12:04] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 14:12:04] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 14:12:04] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 14:12:04] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 14:12:04] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 14:12:04] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 14:12:04] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 14:12:04] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 14:12:04] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 14:12:04] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 14:12:04] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 14:12:04] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 14:12:04] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 14:12:04] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 14:12:04] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 14:12:04] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 14:12:04] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 14:12:04] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 14:12:04] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[18/07/2025 14:12:04] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[18/07/2025 14:12:04] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 14:12:04] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 14:12:04] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 14:12:04] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 14:12:04] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 14:12:04] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[18/07/2025 14:12:04] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (62 zones)
[18/07/2025 14:12:04] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 14:12:04] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[18/07/2025 14:12:04] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[18/07/2025 14:12:04] [FRONTEND] [LOG] ✅ [SETUP] Drag & drop des employés configuré (mode simplifié)
[18/07/2025 14:12:04] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[18/07/2025 14:12:04] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[18/07/2025 14:12:04] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 62 zones trouvées
[18/07/2025 14:12:04] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 62 zones de drop disponibles
[18/07/2025 14:12:04] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[18/07/2025 14:12:04] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 14:12:04] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 14:12:04] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 14:12:04] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 14:12:04] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 14:12:04] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 14:12:04] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 14:12:04] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 14:12:05] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 14:12:05] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 14:12:05] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 14:12:05] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 14:12:05] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 14:12:05] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 14:12:05] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 14:12:05] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 14:12:05] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 14:12:05] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 14:12:05] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 14:12:05] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 14:12:05] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[18/07/2025 14:12:05] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 14:12:05] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[18/07/2025 14:12:05] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 14:12:05] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 14:12:05] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 14:12:05] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 14:12:05] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 14:12:05] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 14:12:05] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 14:12:05] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 14:12:05] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 14:12:05] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[18/07/2025 14:12:05] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (62 zones)
[18/07/2025 14:12:05] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[18/07/2025 14:12:05] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic des problèmes de modale...
[18/07/2025 14:12:05] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Éléments DOM: {"modal":true,"closeBtn":true,"cancelBtn":true,"confirmBtn":true,"radioButtons":4}
[18/07/2025 14:12:05] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] TeamCalendarApp: {"exists":true,"hasModalManager":false,"hasOpenFunction":false,"hasHandleFunction":true}
[18/07/2025 14:12:05] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] État de la modale: {"hidden":true,"classes":["hidden","fixed","inset-0","bg-black/40","backdrop-blur-sm","z-50","flex","items-center","justify-center","p-4"],"style":""}
[18/07/2025 14:12:05] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Données contextuelles: {"employees":5,"posts":5}
[18/07/2025 14:12:05] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] Résultats: 0 ajoutés, 1 validés, 0 corrigés
[18/07/2025 14:12:05] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[18/07/2025 14:12:05] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[18/07/2025 14:12:05] [FRONTEND] [LOG] 🔍 [diagnoseRegularAssignmentGrips] Diagnostic des grips d'attributions régulières
[18/07/2025 14:12:05] [FRONTEND] [LOG] 📊 [diagnoseRegularAssignmentGrips] Statistiques: {"totalShifts":1,"regularShifts":1,"regularAssignments":8}
[18/07/2025 14:12:05] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 1: {"employee":"Sophie Leblanc","date":"2025-07-18","assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","text":"00:00-08:00"}
[18/07/2025 14:12:05] [FRONTEND] [LOG] 🎯 [diagnoseRegularAssignmentGrips] Grips trouvés dans le DOM: 1
[18/07/2025 14:12:05] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[18/07/2025 14:12:05] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[18/07/2025 14:12:05] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation automatique...
[18/07/2025 14:12:05] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation complète...
[18/07/2025 14:12:05] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation de la correction principale...
[18/07/2025 14:12:05] [FRONTEND] [ERROR] ❌ [VALIDATION] ModalManager non disponible
[18/07/2025 14:12:05] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des éléments DOM...
[18/07/2025 14:12:05] [FRONTEND] [LOG] 📋 [VALIDATION] Éléments DOM: {"modal":true,"closeBtn":true,"cancelBtn":true,"confirmBtn":true,"radioButtons":true}
[18/07/2025 14:12:05] [FRONTEND] [LOG] ✅ [VALIDATION] Tous les éléments DOM sont présents
[18/07/2025 14:12:05] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des listeners...
[18/07/2025 14:12:05] [FRONTEND] [ERROR] ❌ [VALIDATION] Erreur lors de setupAssignmentContextModal: Error: can't access property "setupAssignmentContextModal", window.TeamCalendarApp.ModalManager is undefined
validateListeners@http://localhost:5173/validate-modal-fix.js:87:9
runCompleteValidation@http://localhost:5173/validate-modal-fix.js:103:20
@http://localhost:5173/validate-modal-fix.js:186:5
setTimeout handler*@http://localhost:5173/validate-modal-fix.js:184:11

[18/07/2025 14:12:05] [FRONTEND] [LOG] 📊 [VALIDATION] Résultats de validation: {"mainFix":false,"domElements":true,"listeners":false}
[18/07/2025 14:12:05] [FRONTEND] [ERROR] ❌ [VALIDATION] Certaines validations ont échoué
[18/07/2025 14:12:05] [FRONTEND] [LOG] - Vérifier que TeamCalendarApp et ModalManager sont correctement initialisés
[18/07/2025 14:12:05] [FRONTEND] [LOG] 🔧 [VALIDATION] Actions recommandées:
[18/07/2025 14:12:05] [FRONTEND] [LOG] - Vérifier les erreurs dans setupAssignmentContextModal
[18/07/2025 14:12:05] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[18/07/2025 14:12:05] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic complet du fork modal...
[18/07/2025 14:12:05] [FRONTEND] [LOG] ⏳ [TEST-DRAG-DROP] En attente du chargement de l'application...
[18/07/2025 14:12:05] [FRONTEND] [ERROR] ❌ [DIAGNOSTIC] Fonctions manquantes détectées
[18/07/2025 14:12:05] [FRONTEND] [LOG] 📊 [DIAGNOSTIC] Résultats: {"teamCalendarApp":true,"detectDropDateFunction":true,"showConfirmationMenuFunction":false,"handleRegularAssignmentDropFunction":true,"regularAssignments":8,"employees":5}
[18/07/2025 14:12:05] [FRONTEND] [LOG] 🚀 [DATE-TEST] Lancement de la validation automatique des dates...
[18/07/2025 14:12:05] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Logique de fork avec dates
[18/07/2025 14:12:05] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la conversion de date sécurisée...
[18/07/2025 14:12:05] [FRONTEND] [LOG] 🚀 [DATE-TEST] Lancement de la suite de validation des dates...
[18/07/2025 14:12:05] [FRONTEND] [ERROR] ❌ [DATE-TEST] formatDateToKey non disponible
[18/07/2025 14:12:05] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Conversion de date sécurisée
[18/07/2025 14:12:05] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la logique de fork avec dates...
[18/07/2025 14:12:05] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test avec date: 2025-07-25
[18/07/2025 14:12:05] [FRONTEND] [LOG] 🧪 [DATE-TEST] Simulation du changement permanent...
[18/07/2025 14:12:05] [FRONTEND] [LOG] - De: Lucas Bernard
[18/07/2025 14:12:05] [FRONTEND] [LOG] - Vers: Marie Martin
[18/07/2025 14:12:05] [FRONTEND] [LOG] - Date: 2025-07-25
[18/07/2025 14:12:05] [FRONTEND] [LOG] - Assignment: 86f97857-d86a-4e2a-aec3-831bf92012d4
[18/07/2025 14:12:05] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Améliorations timezone
[18/07/2025 14:12:05] [FRONTEND] [ERROR] ❌ [DATE-TEST] Erreur lors du test de fork: Error: window.TeamCalendarApp.showRegularAssignmentConfirmationMenu is not a function
testForkDateLogic@http://localhost:5173/test-date-fix-validation.js:100:32
runDateValidationSuite/<@http://localhost:5173/test-date-fix-validation.js:250:39
runDateValidationSuite@http://localhost:5173/test-date-fix-validation.js:247:11
@http://localhost:5173/test-date-fix-validation.js:286:5
setTimeout handler*@http://localhost:5173/test-date-fix-validation.js:284:11

[18/07/2025 14:12:05] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test des améliorations de timezone...
[18/07/2025 14:12:05] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Détection de date avec timezone
[18/07/2025 14:12:05] [FRONTEND] [WARN] ⚠️ [DATE-TEST] Sélecteur de timezone non trouvé (normal si modal fermé)
[18/07/2025 14:12:05] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la détection de date avec timezone...
[18/07/2025 14:12:05] [FRONTEND] [ERROR] ❌ [detectDropDateFromPosition] Erreur: Error: Document.elementFromPoint: Argument 1 is not a finite floating-point value.
detectDropDateFromPosition@http://localhost:5173/src/teamCalendarApp.ts:8838:43
testDropDateDetectionWithTimezone@http://localhost:5173/test-date-fix-validation.js:204:53
runDateValidationSuite/<@http://localhost:5173/test-date-fix-validation.js:250:39
runDateValidationSuite@http://localhost:5173/test-date-fix-validation.js:247:11
@http://localhost:5173/test-date-fix-validation.js:286:5
setTimeout handler*@http://localhost:5173/test-date-fix-validation.js:284:11

[18/07/2025 14:12:05] [FRONTEND] [LOG] ✅ [DATE-TEST] Date détectée: null
[18/07/2025 14:12:05] [FRONTEND] [LOG] 🎯 [detectDropDateFromPosition] Détection de date à la position ([object Object], test-employee)
[18/07/2025 14:12:05] [FRONTEND] [ERROR] ❌ [DATE-TEST] Format de date invalide: null
[18/07/2025 14:12:05] [FRONTEND] [LOG] 
📊 [DATE-TEST] Résultats de la validation:
[18/07/2025 14:12:05] [FRONTEND] [LOG] ❌ Conversion de date sécurisée: ÉCHOUÉ
[18/07/2025 14:12:05] [FRONTEND] [LOG] ❌ Logique de fork avec dates: ÉCHOUÉ
[18/07/2025 14:12:05] [FRONTEND] [LOG] ❌ Détection de date avec timezone: ÉCHOUÉ
[18/07/2025 14:12:05] [FRONTEND] [LOG] ✅ Améliorations timezone: PASSÉ
[18/07/2025 14:12:05] [FRONTEND] [LOG] 
⚠️ [DATE-TEST] Certaines validations ont échoué
[18/07/2025 14:12:05] [FRONTEND] [LOG] 🔧 [DATE-TEST] Vérifiez les logs ci-dessus pour les détails
[18/07/2025 14:12:06] [FRONTEND] [LOG] 🚀 [GRIP-FIX] Lancement du diagnostic automatique...
[18/07/2025 14:12:06] [FRONTEND] [LOG] 📊 [GRIP-FIX] Total shifts trouvés: 1
[18/07/2025 14:12:06] [FRONTEND] [LOG] 🔍 [GRIP-FIX] Diagnostic complet des GRIP handles...
[18/07/2025 14:12:06] [FRONTEND] [LOG] 🔍 [GRIP-FIX] Test responsivité ed906322-db11-4d06-aa28-437b06c445c4: {"hasMouseEvents":false,"isVisible":true,"hasCorrectCursor":true,"hasValidDimensions":true,"rect":{"width":130,"height":6}}
[18/07/2025 14:12:06] [FRONTEND] [LOG] 📊 [GRIP-FIX] Résultats du diagnostic: {"totalShifts":1,"regularShifts":1,"gripsInDOM":1,"gripsWithEvents":0,"gripsResponsive":1,"issues":[]}
[18/07/2025 14:12:06] [FRONTEND] [DEBUG] [vite] connected.
[18/07/2025 14:12:07] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[18/07/2025 14:12:07] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[18/07/2025 14:12:07] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[18/07/2025 14:12:07] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[18/07/2025 14:12:07] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[18/07/2025 14:12:07] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[18/07/2025 14:12:07] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[18/07/2025 14:12:07] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[18/07/2025 14:12:07] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[18/07/2025 14:12:07] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[18/07/2025 14:12:07] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[18/07/2025 14:12:07] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[18/07/2025 14:12:07] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[18/07/2025 14:12:07] [FRONTEND] [LOG] 🧪 [TEST] Script de test de la modale drag & drop chargé
[18/07/2025 14:12:07] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[18/07/2025 14:12:07] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[18/07/2025 14:12:07] [FRONTEND] [LOG] - testAssignmentModal() : Tester l'ouverture de la modale
[18/07/2025 14:12:07] [FRONTEND] [LOG] - testButtonListeners() : Tester les listeners des boutons
[18/07/2025 14:12:07] [FRONTEND] [LOG] - diagnoseModalIssues() : Diagnostiquer les problèmes
[18/07/2025 14:12:07] [FRONTEND] [LOG] - forceReconfigureListeners() : Reconfigurer les listeners
[18/07/2025 14:12:07] [FRONTEND] [LOG] - testButtonClicks() : Tester les clics sur les boutons
[18/07/2025 14:12:07] [FRONTEND] [LOG] 🔍 [VALIDATION] Script de validation de la correction modale chargé
[18/07/2025 14:12:07] [FRONTEND] [LOG] ✅ [VALIDATION] Fonctions de validation disponibles:
[18/07/2025 14:12:07] [FRONTEND] [LOG] - validateDOMElements() : Valider les éléments DOM
[18/07/2025 14:12:07] [FRONTEND] [LOG] - validateMainFix() : Valider la correction principale
[18/07/2025 14:12:07] [FRONTEND] [LOG] - validateListeners() : Valider les listeners
[18/07/2025 14:12:07] [FRONTEND] [LOG] - runCompleteValidation() : Validation complète
[18/07/2025 14:12:07] [FRONTEND] [LOG] - testEndToEnd() : Test end-to-end complet
[18/07/2025 14:12:07] [FRONTEND] [LOG] 🧪 [TEST] Script de test pour la correction du fork modal chargé
[18/07/2025 14:12:07] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[18/07/2025 14:12:07] [FRONTEND] [LOG] - testDropDateDetection() : Tester la détection de date
[18/07/2025 14:12:07] [FRONTEND] [LOG] - testForkModalWithDate() : Tester le modal avec date
[18/07/2025 14:12:07] [FRONTEND] [LOG] - testCompleteForkFlow() : Tester le flux complet
[18/07/2025 14:12:07] [FRONTEND] [LOG] - diagnoseForkModalIssues() : Diagnostic complet
[18/07/2025 14:12:07] [FRONTEND] [LOG] 🧪 [DATE-TEST] Script de validation des corrections de date chargé
[18/07/2025 14:12:07] [FRONTEND] [LOG] - testSafeDateConversion() : Tester la conversion sécurisée
[18/07/2025 14:12:07] [FRONTEND] [LOG] ✅ [DATE-TEST] Fonctions de validation des dates disponibles:
[18/07/2025 14:12:07] [FRONTEND] [LOG] - testForkDateLogic() : Tester la logique de fork
[18/07/2025 14:12:07] [FRONTEND] [LOG] - testTimezoneEnhancements() : Tester les améliorations timezone
[18/07/2025 14:12:07] [FRONTEND] [LOG] - testDropDateDetectionWithTimezone() : Tester la détection avec timezone
[18/07/2025 14:12:07] [FRONTEND] [LOG] - runDateValidationSuite() : Suite complète de validation
[18/07/2025 14:12:07] [FRONTEND] [LOG] 🔧 [GRIP-FIX] Script de diagnostic et correction des GRIP handles chargé
[18/07/2025 14:12:07] [FRONTEND] [LOG] ✅ [GRIP-FIX] Fonctions de diagnostic et correction disponibles:
[18/07/2025 14:12:07] [FRONTEND] [LOG] - testSpecificGrip(assignmentId) : Test spécifique
[18/07/2025 14:12:07] [FRONTEND] [LOG] - forceRecreateGripHandles() : Recréation forcée
[18/07/2025 14:12:07] [FRONTEND] [LOG] - diagnoseGripHandles() : Diagnostic complet
[18/07/2025 14:12:07] [FRONTEND] [LOG] - autoFixGripIssues() : Correction automatique
[18/07/2025 14:12:07] [FRONTEND] [LOG] - startGripMonitoring() : Surveillance continue
[18/07/2025 14:12:07] [FRONTEND] [LOG] 🧪 [TEST-DRAG-DROP] Démarrage des tests complets de drag & drop...
[18/07/2025 14:12:07] [FRONTEND] [LOG] ✅ [TEST-DRAG-DROP] Script chargé. Utilisez runComprehensiveDragDropTests() pour tester.
[18/07/2025 14:12:07] [FRONTEND] [LOG] [LogsPage] Connexion au flux /api/logs/stream...
[18/07/2025 14:12:07] [FRONTEND] [LOG] [LogsPage] Fermeture de la connexion SSE.
[18/07/2025 14:12:08] [BACKEND] [INFO] ✅ Requête exécutée en 123ms: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 123ms: SELECT * FROM logs ORDER BY ts DESC LIMIT $1 {"params":[200]}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 123ms:",
    "SELECT * FROM logs ORDER BY ts DESC LIMIT $1",
    {
      "params": [
        200
      ]
    }
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:12:07.708Z"
}
[18/07/2025 14:12:09] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[18/07/2025 14:12:09] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] TeamCalendarApp: {"exists":false,"hasModalManager":false,"hasOpenFunction":false,"hasHandleFunction":false}
[18/07/2025 14:12:09] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Éléments DOM: {"modal":false,"closeBtn":false,"cancelBtn":false,"confirmBtn":false,"radioButtons":0}
[18/07/2025 14:12:09] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic des problèmes de modale...
[18/07/2025 14:12:10] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation de la correction principale...
[18/07/2025 14:12:10] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des éléments DOM...
[18/07/2025 14:12:10] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation complète...
[18/07/2025 14:12:10] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation automatique...
[18/07/2025 14:12:10] [FRONTEND] [ERROR] ❌ [VALIDATION] TeamCalendarApp non disponible
[18/07/2025 14:12:10] [FRONTEND] [LOG] 📋 [VALIDATION] Éléments DOM: {"modal":false,"closeBtn":false,"cancelBtn":false,"confirmBtn":false,"radioButtons":false}
[18/07/2025 14:12:10] [FRONTEND] [ERROR] ❌ [VALIDATION] Éléments DOM manquants
[18/07/2025 14:12:10] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des listeners...
[18/07/2025 14:12:10] [FRONTEND] [LOG] 📊 [VALIDATION] Résultats de validation: {"mainFix":false,"domElements":false,"listeners":false}
[18/07/2025 14:12:10] [FRONTEND] [ERROR] ❌ [VALIDATION] Erreur lors de setupAssignmentContextModal: Error: can't access property "ModalManager", window.TeamCalendarApp is undefined
validateListeners@http://localhost:5173/validate-modal-fix.js:87:9
runCompleteValidation@http://localhost:5173/validate-modal-fix.js:103:20
@http://localhost:5173/validate-modal-fix.js:186:5

[18/07/2025 14:12:10] [FRONTEND] [ERROR] ❌ [VALIDATION] Certaines validations ont échoué
[18/07/2025 14:12:10] [FRONTEND] [LOG] 🔧 [VALIDATION] Actions recommandées:
[18/07/2025 14:12:10] [FRONTEND] [LOG] - Vérifier que TeamCalendarApp et ModalManager sont correctement initialisés
[18/07/2025 14:12:10] [FRONTEND] [LOG] - Vérifier que la modale assignment-context-modal existe dans le DOM
[18/07/2025 14:12:10] [FRONTEND] [LOG] - Vérifier les erreurs dans setupAssignmentContextModal
[18/07/2025 14:12:10] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[18/07/2025 14:12:10] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic complet du fork modal...
[18/07/2025 14:12:10] [FRONTEND] [LOG] 📊 [DIAGNOSTIC] Résultats: {"teamCalendarApp":false,"detectDropDateFunction":false,"showConfirmationMenuFunction":false,"handleRegularAssignmentDropFunction":false,"regularAssignments":0,"employees":0}
[18/07/2025 14:12:10] [FRONTEND] [ERROR] ❌ [DIAGNOSTIC] Fonctions manquantes détectées
[18/07/2025 14:12:10] [FRONTEND] [LOG] ⏳ [TEST-DRAG-DROP] En attente du chargement de l'application...
[18/07/2025 14:12:12] [FRONTEND] [LOG] 🚀 [DATE-TEST] Lancement de la suite de validation des dates...
[18/07/2025 14:12:12] [FRONTEND] [LOG] 🚀 [DATE-TEST] Lancement de la validation automatique des dates...
[18/07/2025 14:12:12] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Conversion de date sécurisée
[18/07/2025 14:12:12] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la conversion de date sécurisée...
[18/07/2025 14:12:12] [FRONTEND] [ERROR] ❌ [DATE-TEST] formatDateToKey non disponible
[18/07/2025 14:12:12] [FRONTEND] [ERROR] ❌ [DATE-TEST] Données insuffisantes pour le test
[18/07/2025 14:12:12] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la logique de fork avec dates...
[18/07/2025 14:12:12] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Logique de fork avec dates
[18/07/2025 14:12:12] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Améliorations timezone
[18/07/2025 14:12:12] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test des améliorations de timezone...
[18/07/2025 14:12:12] [FRONTEND] [WARN] ⚠️ [DATE-TEST] Sélecteur de timezone non trouvé (normal si modal fermé)
[18/07/2025 14:12:12] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Détection de date avec timezone
[18/07/2025 14:12:12] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la détection de date avec timezone...
[18/07/2025 14:12:12] [FRONTEND] [ERROR] ❌ [DATE-TEST] detectDropDateFromPosition non disponible
[18/07/2025 14:12:12] [FRONTEND] [LOG] 
📊 [DATE-TEST] Résultats de la validation:
[18/07/2025 14:12:12] [FRONTEND] [LOG] ❌ Conversion de date sécurisée: ÉCHOUÉ
[18/07/2025 14:12:12] [FRONTEND] [LOG] ❌ Logique de fork avec dates: ÉCHOUÉ
[18/07/2025 14:12:12] [FRONTEND] [LOG] ✅ Améliorations timezone: PASSÉ
[18/07/2025 14:12:12] [FRONTEND] [LOG] ❌ Détection de date avec timezone: ÉCHOUÉ
[18/07/2025 14:12:12] [FRONTEND] [LOG] 
⚠️ [DATE-TEST] Certaines validations ont échoué
[18/07/2025 14:12:12] [FRONTEND] [LOG] 🔧 [DATE-TEST] Vérifiez les logs ci-dessus pour les détails
[18/07/2025 14:12:12] [FRONTEND] [DEBUG] [vite] connected.
[18/07/2025 14:12:12] [FRONTEND] [INFO] %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[18/07/2025 14:12:12] [FRONTEND] [LOG] 🔧 [MODAL] Initialisation automatique désactivée - activation à la demande
[18/07/2025 14:12:12] [FRONTEND] [LOG] 🔧 [MODAL] Surveillance TeamCalendarApp désactivée - activation à la demande
[18/07/2025 14:12:13] [FRONTEND] [LOG] 🚀 [INIT] Initialisation du nettoyage des logs...
[18/07/2025 14:12:13] [FRONTEND] [LOG] 🧹 [CLEANUP] Nettoyage des anciens logs localStorage...
[18/07/2025 14:12:13] [FRONTEND] [LOG] 🧹 [CLEANUP] 0 clés nettoyées du localStorage
[18/07/2025 14:12:13] [FRONTEND] [LOG] 🔧 [SETUP] Application de l'optimisation console (dev mode)
[18/07/2025 14:12:13] [FRONTEND] [LOG] ⚙️ [SETUP] Configuration de l'optimisation console...
[18/07/2025 14:12:13] [FRONTEND] [LOG] ✅ [SETUP] Optimisation console configurée
[18/07/2025 14:12:13] [FRONTEND] [LOG] ✅ [INIT] Nettoyage et optimisation terminés avec succès
[18/07/2025 14:12:13] [FRONTEND] [LOG] 🔧 Contrôle des logs chargé. Niveau par défaut: WARN
[18/07/2025 14:12:13] [FRONTEND] [LOG] 💡 Pour voir plus de détails: setLogLevel("INFO") ou setLogLevel("DEBUG")
[18/07/2025 14:12:13] [FRONTEND] [LOG] 📖 Aide complète: showLogHelp()
[18/07/2025 14:12:13] [FRONTEND] [LOG] 🎓 [Tutorial] Système de tutoriel interactif V2 initialisé
[18/07/2025 14:12:13] [FRONTEND] [LOG] 🧪 [TEST] Script de test de la modale drag & drop chargé
[18/07/2025 14:12:13] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[18/07/2025 14:12:13] [FRONTEND] [LOG] - testAssignmentModal() : Tester l'ouverture de la modale
[18/07/2025 14:12:13] [FRONTEND] [LOG] - testButtonListeners() : Tester les listeners des boutons
[18/07/2025 14:12:13] [FRONTEND] [LOG] - diagnoseModalIssues() : Diagnostiquer les problèmes
[18/07/2025 14:12:13] [FRONTEND] [LOG] - forceReconfigureListeners() : Reconfigurer les listeners
[18/07/2025 14:12:13] [FRONTEND] [LOG] - testButtonClicks() : Tester les clics sur les boutons
[18/07/2025 14:12:13] [FRONTEND] [LOG] 🔍 [VALIDATION] Script de validation de la correction modale chargé
[18/07/2025 14:12:13] [FRONTEND] [LOG] ✅ [VALIDATION] Fonctions de validation disponibles:
[18/07/2025 14:12:13] [FRONTEND] [LOG] - validateMainFix() : Valider la correction principale
[18/07/2025 14:12:13] [FRONTEND] [LOG] - validateDOMElements() : Valider les éléments DOM
[18/07/2025 14:12:13] [FRONTEND] [LOG] - validateListeners() : Valider les listeners
[18/07/2025 14:12:13] [FRONTEND] [LOG] - runCompleteValidation() : Validation complète
[18/07/2025 14:12:13] [FRONTEND] [LOG] - testEndToEnd() : Test end-to-end complet
[18/07/2025 14:12:13] [FRONTEND] [LOG] 🧪 [TEST] Script de test pour la correction du fork modal chargé
[18/07/2025 14:12:13] [FRONTEND] [LOG] ✅ [TEST] Fonctions de test disponibles:
[18/07/2025 14:12:13] [FRONTEND] [LOG] - testDropDateDetection() : Tester la détection de date
[18/07/2025 14:12:13] [FRONTEND] [LOG] - testForkModalWithDate() : Tester le modal avec date
[18/07/2025 14:12:13] [FRONTEND] [LOG] - testCompleteForkFlow() : Tester le flux complet
[18/07/2025 14:12:13] [FRONTEND] [LOG] - diagnoseForkModalIssues() : Diagnostic complet
[18/07/2025 14:12:13] [FRONTEND] [LOG] 🧪 [DATE-TEST] Script de validation des corrections de date chargé
[18/07/2025 14:12:13] [FRONTEND] [LOG] ✅ [DATE-TEST] Fonctions de validation des dates disponibles:
[18/07/2025 14:12:13] [FRONTEND] [LOG] - testSafeDateConversion() : Tester la conversion sécurisée
[18/07/2025 14:12:13] [FRONTEND] [LOG] - testForkDateLogic() : Tester la logique de fork
[18/07/2025 14:12:13] [FRONTEND] [LOG] - testTimezoneEnhancements() : Tester les améliorations timezone
[18/07/2025 14:12:13] [FRONTEND] [LOG] - testDropDateDetectionWithTimezone() : Tester la détection avec timezone
[18/07/2025 14:12:13] [FRONTEND] [LOG] - runDateValidationSuite() : Suite complète de validation
[18/07/2025 14:12:13] [FRONTEND] [LOG] 🔧 [GRIP-FIX] Script de diagnostic et correction des GRIP handles chargé
[18/07/2025 14:12:13] [FRONTEND] [LOG] ✅ [GRIP-FIX] Fonctions de diagnostic et correction disponibles:
[18/07/2025 14:12:13] [FRONTEND] [LOG] - diagnoseGripHandles() : Diagnostic complet
[18/07/2025 14:12:13] [FRONTEND] [LOG] - forceRecreateGripHandles() : Recréation forcée
[18/07/2025 14:12:13] [FRONTEND] [LOG] - testSpecificGrip(assignmentId) : Test spécifique
[18/07/2025 14:12:13] [FRONTEND] [LOG] - autoFixGripIssues() : Correction automatique
[18/07/2025 14:12:13] [FRONTEND] [LOG] - startGripMonitoring() : Surveillance continue
[18/07/2025 14:12:13] [FRONTEND] [LOG] 🧪 [TEST-DRAG-DROP] Démarrage des tests complets de drag & drop...
[18/07/2025 14:12:13] [FRONTEND] [LOG] ✅ [TEST-DRAG-DROP] Script chargé. Utilisez runComprehensiveDragDropTests() pour tester.
[18/07/2025 14:12:13] [FRONTEND] [LOG] 🧹 [Agenda] Nettoyage composant Agenda
[18/07/2025 14:12:13] [FRONTEND] [LOG] 🚀 [Agenda] Initialisation TeamCalendarApp...
[18/07/2025 14:12:13] [FRONTEND] [LOG] 🔄 [Agenda] Réinitialisation nécessaire (employés non chargés)
[18/07/2025 14:12:13] [FRONTEND] [LOG] 🚀 [init] Initialisation de TeamCalendarApp...
[18/07/2025 14:12:13] [FRONTEND] [LOG] 🧹 [clearRenderCache] Nettoyage du cache de rendu
[18/07/2025 14:12:13] [FRONTEND] [LOG] ✅ [clearRenderCache] Cache de rendu nettoyé
[18/07/2025 14:12:13] [FRONTEND] [LOG] 🔍 [GRIP-FIX] Diagnostic complet des GRIP handles...
[18/07/2025 14:12:13] [FRONTEND] [LOG] 🚀 [GRIP-FIX] Lancement du diagnostic automatique...
[18/07/2025 14:12:13] [FRONTEND] [LOG] 📊 [GRIP-FIX] Total shifts trouvés: 0
[18/07/2025 14:12:13] [FRONTEND] [LOG] 📊 [GRIP-FIX] Résultats du diagnostic: {"totalShifts":0,"regularShifts":0,"gripsInDOM":0,"gripsWithEvents":0,"gripsResponsive":0,"issues":[]}
[18/07/2025 14:12:13] [FRONTEND] [LOG] 🔌 [ConnectionIndicator] Création de l'indicateur de connexion
[18/07/2025 14:12:13] [FRONTEND] [LOG] 🔌 [init] Indicateur de connexion initialisé
[18/07/2025 14:12:13] [FRONTEND] [LOG] 📂 [TeamCalendarApp] Chargement de l'état...
[18/07/2025 14:12:13] [FRONTEND] [LOG] 🌐 [TeamCalendarApp] Chargement depuis l'API...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT e.*, et.name as template_name 
      FROM employees e 
      LEFT JOIN employee_templates et ON e.template_id = et.id 
      ORDER BY e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT e.*, et.name as template_name \n      FROM employees e \n      LEFT JOIN employee_templates et ON e.template_id = et.id \n      ORDER BY e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:12:13.348Z"
}
[18/07/2025 14:12:13] [BACKEND] [INFO] ✅ Requête exécutée en 48ms: 
      SELECT e.*, et.name as template_name 
      {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 48ms: 
      SELECT e.*, et.name as template_name 
      {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 48ms:",
    "\n      SELECT e.*, et.name as template_name \n     ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:12:13.398Z"
}
[18/07/2025 14:12:13] [FRONTEND] [LOG] 👥 [TeamCalendarApp] 5 employés chargés
[18/07/2025 14:12:13] [FRONTEND] [LOG] 📦 [loadState] Chargement de tous les shifts depuis la base de données...
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT s.*, e.name as employee_name, sp.label as post_label
      FROM shifts s
      LEFT JOIN employees e ON s.employee_id = e.id
      LEFT JOIN standard_posts sp ON s.post_id = sp.id
      ORDER BY s.date_key, e.name
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT s.*, e.name as employee_name, sp.label as post_label\n      FROM shifts s\n      LEFT JOIN employees e ON s.employee_id = e.id\n      LEFT JOIN standard_posts sp ON s.post_id = sp.id\n      ORDER BY s.date_key, e.name\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:12:13.474Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 52ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 52ms:",
    "\n      SELECT s.*, e.name as employee_name, sp.lab",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:12:13.525Z"
}
[18/07/2025 14:12:13] [BACKEND] [INFO] ✅ Requête exécutée en 52ms: 
      SELECT s.*, e.name as employee_name, sp.lab {}
[18/07/2025 14:12:13] [FRONTEND] [LOG] 📅 [loadState] 0 shifts chargés depuis la base de données
[18/07/2025 14:12:13] [FRONTEND] [LOG] ✅ [loadState] Shifts organisés dans this.data.schedule
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:12:13.556Z"
}
[18/07/2025 14:12:13] [BACKEND] [INFO] ✅ Requête exécutée en 59ms: SELECT * FROM standard_posts ORDER BY label {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 59ms: SELECT * FROM standard_posts ORDER BY label {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 59ms:",
    "SELECT * FROM standard_posts ORDER BY label",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:12:13.615Z"
}
[18/07/2025 14:12:14] [FRONTEND] [LOG] 🏢 [TeamCalendarApp] 5 postes standards chargés
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "[query] Executing query: SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:12:13.639Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 55ms: SELECT * FROM app_settings ORDER BY setting_key {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 55ms:",
    "SELECT * FROM app_settings ORDER BY setting_key",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:12:13.695Z"
}
[18/07/2025 14:12:14] [BACKEND] [INFO] ✅ Requête exécutée en 55ms: SELECT * FROM app_settings ORDER BY setting_key {}
[18/07/2025 14:12:14] [FRONTEND] [LOG] ⚙️ [loadState] Paramètres chargés depuis API: {}
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
      SELECT 
        ra.id,
        ra.employee_id,
        ra.post_id,
        ra.days_of_week,
        ra.start_date,
        ra.end_date,
        ra.is_active,
        ra.excluded_dates,
        e.name as employee_name,
        sp.label as post_label,
        sp.hours as post_hours,
        sp.type as post_type,
        array_length(ra.days_of_week, 1) as days_count
      FROM regular_assignments ra
      LEFT JOIN employees e ON ra.employee_id = e.id
      LEFT JOIN standard_posts sp ON ra.post_id = sp.id
      WHERE ra.is_active = true
      ORDER BY e.name, sp.label, ra.start_date
     {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n      SELECT \n        ra.id,\n        ra.employee_id,\n        ra.post_id,\n        ra.days_of_week,\n        ra.start_date,\n        ra.end_date,\n        ra.is_active,\n        ra.excluded_dates,\n        e.name as employee_name,\n        sp.label as post_label,\n        sp.hours as post_hours,\n        sp.type as post_type,\n        array_length(ra.days_of_week, 1) as days_count\n      FROM regular_assignments ra\n      LEFT JOIN employees e ON ra.employee_id = e.id\n      LEFT JOIN standard_posts sp ON ra.post_id = sp.id\n      WHERE ra.is_active = true\n      ORDER BY e.name, sp.label, ra.start_date\n    ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:12:13.817Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 57ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 57ms:",
    "\n      SELECT \n        ra.id,\n        ra.employee_",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:12:13.874Z"
}
[18/07/2025 14:12:14] [BACKEND] [INFO] ✅ Requête exécutée en 57ms: 
      SELECT 
        ra.id,
        ra.employee_ {}
[18/07/2025 14:12:14] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"86f97857-d86a-4e2a-aec3-831bf92012d4","employee_id":"cf78e945-72c7-48f3-a72d-bd0e245a284d","post_id":"0c74e25d-4692-46d7-b5aa-b84eee797971","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-28T04:00:00.000Z","end_date":"2025-07-29T04:00:00.000Z","excluded_dates":[]}
[18/07/2025 14:12:14] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"86f97857-d86a-4e2a-aec3-831bf92012d4","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-28","endDate":"2025-07-29","excludedDates":[]}
[18/07/2025 14:12:14] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"6830f981-f642-4364-9b1d-0ff92e12fad2","employee_id":"cf78e945-72c7-48f3-a72d-bd0e245a284d","post_id":"0c74e25d-4692-46d7-b5aa-b84eee797971","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-08-01T04:00:00.000Z","end_date":null,"excluded_dates":[]}
[18/07/2025 14:12:14] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"6830f981-f642-4364-9b1d-0ff92e12fad2","employeeId":"cf78e945-72c7-48f3-a72d-bd0e245a284d","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"isLimited":false,"startDate":"2025-08-01","endDate":null,"excludedDates":[]}
[18/07/2025 14:12:14] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"08b7356a-e76a-484d-b599-2d5d4a0d8b6b","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-23","endDate":"2025-07-23","excludedDates":[]}
[18/07/2025 14:12:14] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"08b7356a-e76a-484d-b599-2d5d4a0d8b6b","employee_id":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","post_id":"0c74e25d-4692-46d7-b5aa-b84eee797971","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-23T04:00:00.000Z","end_date":"2025-07-23T04:00:00.000Z","excluded_dates":[]}
[18/07/2025 14:12:14] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"42c64a69-ab23-451f-a314-c5c87622675d","employeeId":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-30","endDate":"2025-07-31","excludedDates":[]}
[18/07/2025 14:12:14] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"42c64a69-ab23-451f-a314-c5c87622675d","employee_id":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","post_id":"0c74e25d-4692-46d7-b5aa-b84eee797971","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-30T04:00:00.000Z","end_date":"2025-07-31T04:00:00.000Z","excluded_dates":[]}
[18/07/2025 14:12:14] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"5400f0bd-d560-4167-89ea-a6da77478469","employee_id":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","post_id":"0c74e25d-4692-46d7-b5aa-b84eee797971","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-22T04:00:00.000Z","end_date":"2025-07-22T04:00:00.000Z","excluded_dates":[]}
[18/07/2025 14:12:14] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"316031c4-9402-4394-970e-e598022f5118","employee_id":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","post_id":"0c74e25d-4692-46d7-b5aa-b84eee797971","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-25T04:00:00.000Z","end_date":"2025-07-27T04:00:00.000Z","excluded_dates":[]}
[18/07/2025 14:12:14] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"5400f0bd-d560-4167-89ea-a6da77478469","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-22","endDate":"2025-07-22","excludedDates":[]}
[18/07/2025 14:12:14] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"ed906322-db11-4d06-aa28-437b06c445c4","employee_id":"59f5df3a-33ef-425c-bfa2-adca818cf94f","post_id":"0c74e25d-4692-46d7-b5aa-b84eee797971","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-18T04:00:00.000Z","end_date":"2025-07-21T04:00:00.000Z","excluded_dates":[]}
[18/07/2025 14:12:14] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"316031c4-9402-4394-970e-e598022f5118","employeeId":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-25","endDate":"2025-07-27","excludedDates":[]}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 📋 [GET /api/employee-order] Récupération ordre des employés
  Data: {
  "originalArgs": [
    "📋 [GET /api/employee-order] Récupération ordre des employés"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:12:14.356Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] [query] Executing query: 
            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'
         {}
  Data: {
  "originalArgs": [
    "[query] Executing query: \n            SELECT setting_value FROM app_settings WHERE setting_key = 'employee_order'\n        ",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:12:14.358Z"
}
[18/07/2025 14:12:14] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"ed906322-db11-4d06-aa28-437b06c445c4","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-18","endDate":"2025-07-21","excludedDates":[]}
[18/07/2025 14:12:14] [FRONTEND] [LOG] 🔄 [normalizeAssignment] Normalisation (nouveau format): {"id":"d546a50b-43af-4f53-afd0-4a43c1f3959a","employee_id":"59f5df3a-33ef-425c-bfa2-adca818cf94f","post_id":"0c74e25d-4692-46d7-b5aa-b84eee797971","days_of_week":[1,2,3,4,5],"selectedDays":[1,2,3,4,5],"start_date":"2025-07-24T04:00:00.000Z","end_date":"2025-07-24T04:00:00.000Z","excluded_dates":[]}
[18/07/2025 14:12:14] [FRONTEND] [LOG] ✅ [normalizeAssignment] Normalisé (V2) avec dates corrigées: {"id":"d546a50b-43af-4f53-afd0-4a43c1f3959a","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"isLimited":true,"startDate":"2025-07-24","endDate":"2025-07-24","excludedDates":[]}
[18/07/2025 14:12:14] [FRONTEND] [LOG] 🔧 [fixInvalidAssignmentDates] Vérification des dates invalides...
[18/07/2025 14:12:14] [FRONTEND] [LOG] 📋 [loadState] 8/8 assignations régulières valides chargées et normalisées
[18/07/2025 14:12:14] [FRONTEND] [LOG] ✅ [fixInvalidAssignmentDates] Aucune date invalide trouvée
[18/07/2025 14:12:14] [FRONTEND] [LOG] ✅ [cleanupDuplicateRegularShifts] 0 doublons supprimés
[18/07/2025 14:12:14] [FRONTEND] [LOG] 📋 [loadState] Assignations régulières chargées (application différée)
[18/07/2025 14:12:14] [FRONTEND] [LOG] ✅ [setupEmployeeNameDisplay] Styles CSS injectés pour l'affichage des noms
[18/07/2025 14:12:14] [FRONTEND] [LOG] ⚙️ [loadStateFromLocalStorage] Aucun paramètre sauvegardé.
[18/07/2025 14:12:14] [FRONTEND] [ERROR] ❌ [TeamCalendarApp] Erreur lors du chargement: Error: this.cleanupInvalidAssignmentIds is not a function
loadState@http://localhost:5173/src/teamCalendarApp.ts:978:40
async*init@http://localhost:5173/src/teamCalendarApp.ts:1052:16
async*initApp@http://localhost:5173/src/Agenda.tsx:42:31
setTimeout handler*AgendaFullscreen/<@http://localhost:5173/src/Agenda.tsx:52:29
react-stack-bottom-frame@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:17478:20
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
commitHookEffectListMount@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8460:122
commitHookPassiveMountEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:8518:60
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10016:42
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10009:55
recursivelyTraverseReconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:9995:34
reconnectPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:10054:55
doubleInvokeEffectsOnFiber@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11461:207
runWithFiberInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:1487:15
recursivelyTraverseAndDoubleInvokeEffectsInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11432:78
commitDoubleInvokeEffectsInDEV@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11469:55
flushPassiveEffects@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11309:41
node_modules/react-dom/cjs/react-dom-client.development.js/commitRoot/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:11060:32
performWorkUntilDeadline@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:36:58
EventHandlerNonNull*node_modules/scheduler/cjs/scheduler.development.js/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:156:9
node_modules/scheduler/cjs/scheduler.development.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:266:7
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/scheduler/index.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:277:24
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/react-dom/cjs/react-dom-client.development.js/<@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:15216:23
node_modules/react-dom/cjs/react-dom-client.development.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18060:7
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
node_modules/react-dom/client.js@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18071:24
__require@http://localhost:5173/node_modules/.vite/deps/chunk-4MBMRILA.js?v=189de042:11:50
@http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=189de042:18075:16

[18/07/2025 14:12:14] [FRONTEND] [LOG] ✅ [init] Icône paramètres trouvée: [Objet non sérialisable]
[18/07/2025 14:12:14] [FRONTEND] [LOG] 🔍 [init] Recherche du bouton des paramètres dans le DOM
[18/07/2025 14:12:14] [FRONTEND] [LOG] 🔍 [init] Bouton des paramètres trouvé ? true
[18/07/2025 14:12:14] [FRONTEND] [LOG] 🔍 [init] Attachement du listener click sur le bouton des paramètres
[18/07/2025 14:12:14] [FRONTEND] [LOG] 🔍 [init] Recherche du bouton des paramètres header dans le DOM
[18/07/2025 14:12:14] [FRONTEND] [LOG] 🔍 [init] Bouton des paramètres header trouvé ? true
[18/07/2025 14:12:14] [FRONTEND] [LOG] 🔍 [init] Attachement du listener click sur le bouton des paramètres header
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:12:14.569Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:12:14.570Z"
}
[18/07/2025 14:12:14] [BACKEND] [INFO] 📋 [GET /api/employee-order] Récupération ordre des employés
[18/07/2025 14:12:14] [FRONTEND] [LOG] ✅ [setupNavigationListeners] Écouteurs de navigation attachés
[18/07/2025 14:12:14] [FRONTEND] [LOG] 🎯 [setupViewNavigationListeners] Configuration des boutons de navigation...
[18/07/2025 14:12:14] [FRONTEND] [LOG] ✅ Boutons de navigation configurés
[18/07/2025 14:12:14] [FRONTEND] [LOG] 🔧 [ModalFunctionalitiesManager] Initialisation du gestionnaire de modales
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ Requête exécutée en 247ms: 
            SELECT setting_value FROM app_setting {}
  Data: {
  "originalArgs": [
    "✅ Requête exécutée en 247ms:",
    "\n            SELECT setting_value FROM app_setting",
    {}
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:12:14.605Z"
}
[18/07/2025 14:12:14] [FRONTEND] [LOG] ✅ [setupGlobalEscapeHandler] Gestionnaire ESC global configuré
[18/07/2025 14:12:14] [FRONTEND] [LOG] 📋 [loadEmployeeOrder] Chargement ordre des employés
[18/07/2025 14:12:15] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[18/07/2025 14:12:15] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic des problèmes de modale...
[18/07/2025 14:12:15] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Éléments DOM: {"modal":true,"closeBtn":true,"cancelBtn":true,"confirmBtn":true,"radioButtons":4}
[18/07/2025 14:12:15] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] TeamCalendarApp: {"exists":true,"hasModalManager":false,"hasOpenFunction":false,"hasHandleFunction":true}
[18/07/2025 14:12:15] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] État de la modale: {"hidden":true,"classes":["hidden","fixed","inset-0","bg-black/40","backdrop-blur-sm","z-50","flex","items-center","justify-center","p-4"],"style":""}
[18/07/2025 14:12:15] [BACKEND] [INFO] 🔗 Connexion PostgreSQL établie avec succès
[Invalid Date] [BACKEND] [INFO] [LOGGER] 🔗 Connexion PostgreSQL établie avec succès
  Data: {
  "originalArgs": [
    "🔗 Connexion PostgreSQL établie avec succès"
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:12:14.770Z"
}
[Invalid Date] [BACKEND] [INFO] [LOGGER] ✅ [GET /api/employee-order] Ordre récupéré (array): [{"id":"604a1d0c-2141-4543-b7f9-7fc9adfa7b9a","name":"Marie Martin","order":0},{"id":"cf78e945-72c7-48f3-a72d-bd0e245a284d","name":"Lucas Bernard","order":1},{"id":"18fe9cd1-e8a5-44e3-90b3-a856086ce27e","name":"Pierre Durand","order":2},{"id":"59f5df3a-33ef-425c-bfa2-adca818cf94f","name":"Sophie Leblanc","order":3},{"id":"42b53805-18ba-425e-bb00-52bb8d6ce76b","name":"Jean Dupont","order":4}]
  Data: {
  "originalArgs": [
    "✅ [GET /api/employee-order] Ordre récupéré (array):",
    [
      {
        "id": "604a1d0c-2141-4543-b7f9-7fc9adfa7b9a",
        "name": "Marie Martin",
        "order": 0
      },
      {
        "id": "cf78e945-72c7-48f3-a72d-bd0e245a284d",
        "name": "Lucas Bernard",
        "order": 1
      },
      {
        "id": "18fe9cd1-e8a5-44e3-90b3-a856086ce27e",
        "name": "Pierre Durand",
        "order": 2
      },
      {
        "id": "59f5df3a-33ef-425c-bfa2-adca818cf94f",
        "name": "Sophie Leblanc",
        "order": 3
      },
      {
        "id": "42b53805-18ba-425e-bb00-52bb8d6ce76b",
        "name": "Jean Dupont",
        "order": 4
      }
    ]
  ],
  "source": "logger-backend",
  "timestamp": "2025-07-18T18:12:14.607Z"
}
[18/07/2025 14:12:15] [FRONTEND] [LOG] 📋 [DIAGNOSTIC] Données contextuelles: {"employees":5,"posts":5}
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [loadEmployeeOrder] Ordre API récupéré: Marie Martin (0), Lucas Bernard (1), Pierre Durand (2), Sophie Leblanc (3), Jean Dupont (4)
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [loadEmployeeOrder] Employés réorganisés selon l'ordre api
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🔧 [init] Forçage du rendu initial...
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 60 zones trouvées
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 60 zones de drop disponibles
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Matin: [1, 2, 3, 4, 5]
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Nuit: [1, 2, 3, 4, 5]
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste Soir: [1, 2, 3, 4, 5]
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE1: [0, 6]
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🔧 [getPostById] workingDays par défaut ajouté pour Poste WE2: [0, 6]
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (60 zones)
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🔧 [init] Configuration du drag & drop des postes...
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 60 zones trouvées
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 60 zones de drop disponibles
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (60 zones)
[18/07/2025 14:12:16] [FRONTEND] [LOG] ℹ️ [setupEmployeeDragDrop] Fonction désactivée - Utiliser setupDragAndDrop
[18/07/2025 14:12:16] [FRONTEND] [LOG] ✅ [addEmergencyFixButton] Boutons ajoutés (emergency fix buttons supprimés)
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🔧 [addEmergencyFixButton] Ajout du bouton d'historique...
[18/07/2025 14:12:16] [FRONTEND] [LOG] 📋 [init] Application des attributions régulières après initialisation
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] Nettoyage des shifts réguliers existants...
[18/07/2025 14:12:16] [FRONTEND] [LOG] 🧹 [applyRegularAssignmentsForCurrentWeek] 0 shifts réguliers supprimés
[18/07/2025 14:12:17] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Application pour semaine 2025-W28
[18/07/2025 14:12:17] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 86f97857-d86a-4e2a-aec3-831bf92012d4 commence après cette semaine (startDate: 2025-07-28, semaine se termine: 2025-07-19)
[18/07/2025 14:12:17] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 6830f981-f642-4364-9b1d-0ff92e12fad2 commence après cette semaine (startDate: 2025-08-01, semaine se termine: 2025-07-19)
[18/07/2025 14:12:17] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 08b7356a-e76a-484d-b599-2d5d4a0d8b6b commence après cette semaine (startDate: 2025-07-23, semaine se termine: 2025-07-19)
[18/07/2025 14:12:17] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 42c64a69-ab23-451f-a314-c5c87622675d commence après cette semaine (startDate: 2025-07-30, semaine se termine: 2025-07-19)
[18/07/2025 14:12:17] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 5400f0bd-d560-4167-89ea-a6da77478469 commence après cette semaine (startDate: 2025-07-22, semaine se termine: 2025-07-19)
[18/07/2025 14:12:17] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Attribution ed906322-db11-4d06-aa28-437b06c445c4 a une intersection avec cette semaine (2025-07-18 → 2025-07-21) vs (2025-07-13 → 2025-07-19)
[18/07/2025 14:12:17] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution 316031c4-9402-4394-970e-e598022f5118 commence après cette semaine (startDate: 2025-07-25, semaine se termine: 2025-07-19)
[18/07/2025 14:12:17] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Attribution d546a50b-43af-4f53-afd0-4a43c1f3959a commence après cette semaine (startDate: 2025-07-24, semaine se termine: 2025-07-19)
[18/07/2025 14:12:17] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 1 attributions actives sur 8 total
[18/07/2025 14:12:17] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Traitement attribution active: {"id":"ed906322-db11-4d06-aa28-437b06c445c4","employeeId":"59f5df3a-33ef-425c-bfa2-adca818cf94f","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","selectedDays":[1,2,3,4,5],"startDate":"2025-07-18","endDate":"2025-07-21","excludedDates":[]}
[18/07/2025 14:12:17] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Traitement des jours pour attribution ed906322-db11-4d06-aa28-437b06c445c4: [{"dateKey":"2025-07-13","isWeekend":true,"dayOfWeek":0},{"dateKey":"2025-07-14","isWeekend":false,"dayOfWeek":1},{"dateKey":"2025-07-15","isWeekend":false,"dayOfWeek":2},{"dateKey":"2025-07-16","isWeekend":false,"dayOfWeek":3},{"dateKey":"2025-07-17","isWeekend":false,"dayOfWeek":4},{"dateKey":"2025-07-18","isWeekend":false,"dayOfWeek":5},{"dateKey":"2025-07-19","isWeekend":true,"dayOfWeek":6}]
[18/07/2025 14:12:17] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 0: 2025-07-13 (0)
[18/07/2025 14:12:17] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-13T04:00:00.000Z","dayDateKey":"2025-07-13","dayIsWeekend":true,"dayOfWeek":0,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 14:12:17] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-13 ignoré pour attribution ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 14:12:17] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 0 non sélectionné dans: [1,2,3,4,5]
[18/07/2025 14:12:17] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-14T04:00:00.000Z","dayDateKey":"2025-07-14","dayIsWeekend":false,"dayOfWeek":1,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 14:12:17] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 1: 2025-07-14 (1)
[18/07/2025 14:12:17] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 14:12:17] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-14: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","dayDateStr":"2025-07-14","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 14:12:17] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-14 avant startDate 2025-07-18 (original: 2025-07-18)
[18/07/2025 14:12:17] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-14 ignoré pour attribution ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 14:12:17] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 2: 2025-07-15 (2)
[18/07/2025 14:12:17] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-15T04:00:00.000Z","dayDateKey":"2025-07-15","dayIsWeekend":false,"dayOfWeek":2,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 14:12:17] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 14:12:17] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-15 ignoré pour attribution ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 14:12:17] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-15: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","dayDateStr":"2025-07-15","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 14:12:17] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-15 avant startDate 2025-07-18 (original: 2025-07-18)
[18/07/2025 14:12:17] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 3: 2025-07-16 (3)
[18/07/2025 14:12:17] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-16T04:00:00.000Z","dayDateKey":"2025-07-16","dayIsWeekend":false,"dayOfWeek":3,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 14:12:17] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 14:12:17] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-16: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","dayDateStr":"2025-07-16","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 14:12:17] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-16 avant startDate 2025-07-18 (original: 2025-07-18)
[18/07/2025 14:12:17] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-16 ignoré pour attribution ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 14:12:17] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-17T04:00:00.000Z","dayDateKey":"2025-07-17","dayIsWeekend":false,"dayOfWeek":4,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 14:12:17] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 4: 2025-07-17 (4)
[18/07/2025 14:12:17] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 14:12:17] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-17: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","dayDateStr":"2025-07-17","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 14:12:17] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Date 2025-07-17 avant startDate 2025-07-18 (original: 2025-07-18)
[18/07/2025 14:12:17] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-17 ignoré pour attribution ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 14:12:17] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 5: 2025-07-18 (5)
[18/07/2025 14:12:17] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-18T04:00:00.000Z","dayDateKey":"2025-07-18","dayIsWeekend":false,"dayOfWeek":5,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 14:12:17] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification exclusions pour 2025-07-18: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","dayDateStr":"2025-07-18","excludedDates":[],"excludedDatesLength":0,"normalizedExcludedDates":[]}
[18/07/2025 14:12:17] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] WorkingDays définis: [1,2,3,4,5]
[18/07/2025 14:12:17] [FRONTEND] [LOG] 📋 [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-18 validé, shifts existants: 0
[18/07/2025 14:12:17] [FRONTEND] [LOG] ✅ [shouldApplyAssignmentToDay] Attribution autorisée pour 2025-07-18
[18/07/2025 14:12:17] [FRONTEND] [LOG] ✅ [applyRegularAssignmentsForCurrentWeek] Shift créé: Poste Nuit → 2025-07-18 pour Sophie Leblanc
[18/07/2025 14:12:17] [FRONTEND] [LOG] 📅 [applyRegularAssignmentsForCurrentWeek] Vérification jour 6: 2025-07-19 (6)
[18/07/2025 14:12:17] [FRONTEND] [LOG] 🔍 [shouldApplyAssignmentToDay] Vérification pour: {"assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","postLabel":"Poste Nuit","postCategory":"night","dayDate":"2025-07-19T04:00:00.000Z","dayDateKey":"2025-07-19","dayIsWeekend":true,"dayOfWeek":6,"selectedDays":[1,2,3,4,5],"postWorkingDays":[1,2,3,4,5]}
[18/07/2025 14:12:17] [FRONTEND] [LOG] ⏭️ [applyRegularAssignmentsForCurrentWeek] Jour 2025-07-19 ignoré pour attribution ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 14:12:17] [FRONTEND] [LOG] ❌ [shouldApplyAssignmentToDay] Jour 6 non sélectionné dans: [1,2,3,4,5]
[18/07/2025 14:12:17] [FRONTEND] [LOG] 📊 [applyRegularAssignmentsForCurrentWeek] 1 attributions appliquées pour la semaine 2025-W28
[18/07/2025 14:12:17] [FRONTEND] [LOG] 🔄 [applyRegularAssignmentsForCurrentWeek] Rendu demandé car 1 shifts créés
[18/07/2025 14:12:17] [FRONTEND] [LOG] ✅ [init] Initialisation terminée avec succès
[18/07/2025 14:12:17] [FRONTEND] [LOG] 🎉 [Agenda] Application fullscreen initialisée avec succès
[18/07/2025 14:12:17] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] Résultats: 0 ajoutés, 0 validés, 0 corrigés
[18/07/2025 14:12:17] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[18/07/2025 14:12:18] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[18/07/2025 14:12:18] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille unifiée employés + jours
[18/07/2025 14:12:18] [FRONTEND] [WARN] ⚠️ [renderUnifiedCalendar] Conteneur available-posts-container non trouvé, création...
[18/07/2025 14:12:18] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneur available-posts-container créé
[18/07/2025 14:12:18] [FRONTEND] [LOG] 🎯 [createShiftElement] Ajout du grip pour l'attribution régulière ed906322-db11-4d06-aa28-437b06c445c4
[18/07/2025 14:12:18] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Conteneurs trouvés: {"employeeRows":true,"availablePosts":true}
[18/07/2025 14:12:18] [FRONTEND] [LOG] ✅ [createShiftElement] Poste trouvé: 0c74e25d-4692-46d7-b5aa-b84eee797971 -> "00:00-08:00"
[18/07/2025 14:12:18] [FRONTEND] [LOG] 🔧 [renderUnifiedCalendar] Génération de la grille des postes avec 5 postes
[18/07/2025 14:12:18] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Grille unifiée générée avec succès
[18/07/2025 14:12:18] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] 5 postes configurés dans available-posts-container (masqué)
[18/07/2025 14:12:18] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 62 zones trouvées
[18/07/2025 14:12:18] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[18/07/2025 14:12:18] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[18/07/2025 14:12:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 62 zones de drop disponibles
[18/07/2025 14:12:18] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[18/07/2025 14:12:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 14:12:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 14:12:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 14:12:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 14:12:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 14:12:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 14:12:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 14:12:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 14:12:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 14:12:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 14:12:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 14:12:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 14:12:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 14:12:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 14:12:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 14:12:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 14:12:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 14:12:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 14:12:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 14:12:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 14:12:18] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[18/07/2025 14:12:18] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 14:12:18] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[18/07/2025 14:12:18] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 14:12:18] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 14:12:18] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 14:12:18] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[18/07/2025 14:12:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (62 zones)
[18/07/2025 14:12:18] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 14:12:18] [FRONTEND] [LOG] ✅ [renderUnifiedCalendar] Drag & drop reconfiguré pour la nouvelle grille
[18/07/2025 14:12:18] [FRONTEND] [LOG] 🔧 [ensureRegularAssignmentGrips] Vérification et recréation des grips
[18/07/2025 14:12:18] [FRONTEND] [LOG] ✅ [SETUP] Drag & drop des employés configuré (mode simplifié)
[18/07/2025 14:12:18] [FRONTEND] [LOG] 🔍 [ensureDropZonesExist] Vérification des zones de drop...
[18/07/2025 14:12:18] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Initialisation drag & drop avec délégation d'événements
[18/07/2025 14:12:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] 62 zones de drop disponibles
[18/07/2025 14:12:18] [FRONTEND] [LOG] ✅ [ensureDropZonesExist] 62 zones trouvées
[18/07/2025 14:12:18] [FRONTEND] [LOG] 🔧 [setupPostDragDrop] Configuration drag pour 10 postes (5 cachés + 5 visibles)
[18/07/2025 14:12:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 14:12:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 14:12:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 14:12:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 14:12:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 14:12:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 14:12:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 14:12:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 14:12:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 14:12:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 14:12:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 7674f144-fe6f-4f77-b054-34524d7603b8 configuré comme draggable
[18/07/2025 14:12:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 7674f144-fe6f-4f77-b054-34524d7603b8
[18/07/2025 14:12:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste eb5aea9f-a9be-458c-8d1f-aaa618092c84 configuré comme draggable
[18/07/2025 14:12:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 0c74e25d-4692-46d7-b5aa-b84eee797971
[18/07/2025 14:12:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste eb5aea9f-a9be-458c-8d1f-aaa618092c84
[18/07/2025 14:12:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 0c74e25d-4692-46d7-b5aa-b84eee797971 configuré comme draggable
[18/07/2025 14:12:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 62711965-1ead-4e5a-a923-b724a0daf7a3 configuré comme draggable
[18/07/2025 14:12:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 62711965-1ead-4e5a-a923-b724a0daf7a3
[18/07/2025 14:12:18] [FRONTEND] [LOG] 🎯 [setupPostDragDrop] Poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9 configuré comme draggable
[18/07/2025 14:12:18] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Listeners attachés pour poste 3ee2a701-d9a3-4462-aee8-ce1bbe5a49d9
[18/07/2025 14:12:18] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 14:12:18] [FRONTEND] [LOG] 🎯 [setupCentralizedDropZone] Configuration de 5 lignes d'employés (via employee-rows-container)
[18/07/2025 14:12:18] [FRONTEND] [LOG] 🔧 [setupCentralizedDropZone] Configuration simple des zones de drop
[18/07/2025 14:12:18] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 604a1d0c-2141-4543-b7f9-7fc9adfa7b9a
[18/07/2025 14:12:18] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 14:12:18] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour cf78e945-72c7-48f3-a72d-bd0e245a284d
[18/07/2025 14:12:18] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 14:12:18] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 18fe9cd1-e8a5-44e3-90b3-a856086ce27e
[18/07/2025 14:12:18] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 14:12:19] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 59f5df3a-33ef-425c-bfa2-adca818cf94f
[18/07/2025 14:12:19] [FRONTEND] [LOG] 🧹 [setupCentralizedDropZone] Anciens listeners nettoyés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 14:12:19] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] Listeners configurés pour 42b53805-18ba-425e-bb00-52bb8d6ce76b
[18/07/2025 14:12:19] [FRONTEND] [LOG] ✅ [setupCentralizedDropZone] 5 zones de drop configurées (via employee-rows-container)
[18/07/2025 14:12:19] [FRONTEND] [LOG] ✅ [setupPostDragDrop] Drag & drop configuré avec délégation centralisée (62 zones)
[18/07/2025 14:12:19] [FRONTEND] [LOG] ✅ [ensureRegularAssignmentGrips] Résultats: 0 ajoutés, 1 validés, 0 corrigés
[18/07/2025 14:12:19] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[18/07/2025 14:12:19] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[18/07/2025 14:12:19] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation automatique...
[18/07/2025 14:12:19] [FRONTEND] [LOG] 🚀 [VALIDATION] Lancement de la validation complète...
[18/07/2025 14:12:19] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation de la correction principale...
[18/07/2025 14:12:19] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des éléments DOM...
[18/07/2025 14:12:19] [FRONTEND] [ERROR] ❌ [VALIDATION] ModalManager non disponible
[18/07/2025 14:12:19] [FRONTEND] [LOG] 📋 [VALIDATION] Éléments DOM: {"modal":true,"closeBtn":true,"cancelBtn":true,"confirmBtn":true,"radioButtons":true}
[18/07/2025 14:12:19] [FRONTEND] [LOG] ✅ [VALIDATION] Tous les éléments DOM sont présents
[18/07/2025 14:12:19] [FRONTEND] [LOG] 🔍 [VALIDATION] Validation des listeners...
[18/07/2025 14:12:19] [FRONTEND] [LOG] 📊 [VALIDATION] Résultats de validation: {"mainFix":false,"domElements":true,"listeners":false}
[18/07/2025 14:12:19] [FRONTEND] [ERROR] ❌ [VALIDATION] Erreur lors de setupAssignmentContextModal: Error: can't access property "setupAssignmentContextModal", window.TeamCalendarApp.ModalManager is undefined
validateListeners@http://localhost:5173/validate-modal-fix.js:87:9
runCompleteValidation@http://localhost:5173/validate-modal-fix.js:103:20
@http://localhost:5173/validate-modal-fix.js:186:5
setTimeout handler*@http://localhost:5173/validate-modal-fix.js:184:11

[18/07/2025 14:12:19] [FRONTEND] [ERROR] ❌ [VALIDATION] Certaines validations ont échoué
[18/07/2025 14:12:19] [FRONTEND] [LOG] 🔧 [VALIDATION] Actions recommandées:
[18/07/2025 14:12:19] [FRONTEND] [LOG] - Vérifier que TeamCalendarApp et ModalManager sont correctement initialisés
[18/07/2025 14:12:19] [FRONTEND] [LOG] - Vérifier les erreurs dans setupAssignmentContextModal
[18/07/2025 14:12:19] [FRONTEND] [LOG] 📊 [DIAGNOSTIC] Résultats: {"teamCalendarApp":true,"detectDropDateFunction":true,"showConfirmationMenuFunction":false,"handleRegularAssignmentDropFunction":true,"regularAssignments":8,"employees":5}
[18/07/2025 14:12:19] [FRONTEND] [LOG] 🚀 [TEST] Lancement du diagnostic automatique...
[18/07/2025 14:12:19] [FRONTEND] [LOG] 🔍 [DIAGNOSTIC] Diagnostic complet du fork modal...
[18/07/2025 14:12:19] [FRONTEND] [ERROR] ❌ [DIAGNOSTIC] Fonctions manquantes détectées
[18/07/2025 14:12:19] [FRONTEND] [LOG] ⏳ [TEST-DRAG-DROP] En attente du chargement de l'application...
[18/07/2025 14:12:19] [FRONTEND] [LOG] 🎯 [diagnoseRegularAssignmentGrips] Grips trouvés dans le DOM: 1
[18/07/2025 14:12:19] [FRONTEND] [LOG] 📊 [diagnoseRegularAssignmentGrips] Statistiques: {"totalShifts":1,"regularShifts":1,"regularAssignments":8}
[18/07/2025 14:12:19] [FRONTEND] [LOG] 🔍 [diagnoseRegularAssignmentGrips] Diagnostic des grips d'attributions régulières
[18/07/2025 14:12:19] [FRONTEND] [LOG] 📋 [diagnoseRegularAssignmentGrips] Shift régulier 1: {"employee":"Sophie Leblanc","date":"2025-07-18","assignmentId":"ed906322-db11-4d06-aa28-437b06c445c4","postId":"0c74e25d-4692-46d7-b5aa-b84eee797971","text":"00:00-08:00"}
[18/07/2025 14:12:19] [FRONTEND] [LOG] 🔄 [addSwapIconsToEmployees] Ajout des icônes de swap dans les fiches employés
[18/07/2025 14:12:19] [FRONTEND] [LOG] ✅ [addSwapIconsToEmployees] Icônes de swap ajoutées
[18/07/2025 14:12:19] [FRONTEND] [LOG] 🚀 [DATE-TEST] Lancement de la validation automatique des dates...
[18/07/2025 14:12:19] [FRONTEND] [ERROR] ❌ [DATE-TEST] formatDateToKey non disponible
[18/07/2025 14:12:19] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la conversion de date sécurisée...
[18/07/2025 14:12:19] [FRONTEND] [LOG] 🚀 [DATE-TEST] Lancement de la suite de validation des dates...
[18/07/2025 14:12:19] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Conversion de date sécurisée
[18/07/2025 14:12:19] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Logique de fork avec dates
[18/07/2025 14:12:19] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la logique de fork avec dates...
[18/07/2025 14:12:19] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test avec date: 2025-07-25
[18/07/2025 14:12:19] [FRONTEND] [LOG] - Assignment: 86f97857-d86a-4e2a-aec3-831bf92012d4
[18/07/2025 14:12:19] [FRONTEND] [LOG] - De: Lucas Bernard
[18/07/2025 14:12:19] [FRONTEND] [LOG] 🧪 [DATE-TEST] Simulation du changement permanent...
[18/07/2025 14:12:19] [FRONTEND] [LOG] - Vers: Marie Martin
[18/07/2025 14:12:19] [FRONTEND] [ERROR] ❌ [DATE-TEST] Erreur lors du test de fork: Error: window.TeamCalendarApp.showRegularAssignmentConfirmationMenu is not a function
testForkDateLogic@http://localhost:5173/test-date-fix-validation.js:100:32
runDateValidationSuite/<@http://localhost:5173/test-date-fix-validation.js:250:39
runDateValidationSuite@http://localhost:5173/test-date-fix-validation.js:247:11
@http://localhost:5173/test-date-fix-validation.js:286:5
setTimeout handler*@http://localhost:5173/test-date-fix-validation.js:284:11

[18/07/2025 14:12:19] [FRONTEND] [LOG] - Date: 2025-07-25
[18/07/2025 14:12:19] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Améliorations timezone
[18/07/2025 14:12:19] [FRONTEND] [WARN] ⚠️ [DATE-TEST] Sélecteur de timezone non trouvé (normal si modal fermé)
[18/07/2025 14:12:19] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test des améliorations de timezone...
[18/07/2025 14:12:19] [FRONTEND] [LOG] 
🧪 [DATE-TEST] Exécution: Détection de date avec timezone
[18/07/2025 14:12:19] [FRONTEND] [LOG] 🎯 [detectDropDateFromPosition] Détection de date à la position ([object Object], test-employee)
[18/07/2025 14:12:19] [FRONTEND] [LOG] ✅ [DATE-TEST] Date détectée: null
[18/07/2025 14:12:19] [FRONTEND] [LOG] 🧪 [DATE-TEST] Test de la détection de date avec timezone...
[18/07/2025 14:12:19] [FRONTEND] [ERROR] ❌ [detectDropDateFromPosition] Erreur: Error: Document.elementFromPoint: Argument 1 is not a finite floating-point value.
detectDropDateFromPosition@http://localhost:5173/src/teamCalendarApp.ts:8838:43
testDropDateDetectionWithTimezone@http://localhost:5173/test-date-fix-validation.js:204:53
runDateValidationSuite/<@http://localhost:5173/test-date-fix-validation.js:250:39
runDateValidationSuite@http://localhost:5173/test-date-fix-validation.js:247:11
@http://localhost:5173/test-date-fix-validation.js:286:5
setTimeout handler*@http://localhost:5173/test-date-fix-validation.js:284:11

[18/07/2025 14:12:19] [FRONTEND] [LOG] 
📊 [DATE-TEST] Résultats de la validation:
[18/07/2025 14:12:19] [FRONTEND] [ERROR] ❌ [DATE-TEST] Format de date invalide: null
[18/07/2025 14:12:19] [FRONTEND] [LOG] ✅ Améliorations timezone: PASSÉ
[18/07/2025 14:12:19] [FRONTEND] [LOG] ❌ Conversion de date sécurisée: ÉCHOUÉ
[18/07/2025 14:12:19] [FRONTEND] [LOG] ❌ Logique de fork avec dates: ÉCHOUÉ
[18/07/2025 14:12:19] [FRONTEND] [LOG] 
⚠️ [DATE-TEST] Certaines validations ont échoué
[18/07/2025 14:12:19] [FRONTEND] [LOG] ❌ Détection de date avec timezone: ÉCHOUÉ
[18/07/2025 14:12:19] [FRONTEND] [LOG] 🚀 [GRIP-FIX] Lancement du diagnostic automatique...
[18/07/2025 14:12:19] [FRONTEND] [LOG] 🔧 [DATE-TEST] Vérifiez les logs ci-dessus pour les détails
[18/07/2025 14:12:19] [FRONTEND] [LOG] 🔍 [GRIP-FIX] Diagnostic complet des GRIP handles...
[18/07/2025 14:12:19] [FRONTEND] [LOG] 📊 [GRIP-FIX] Total shifts trouvés: 1
[18/07/2025 14:12:19] [FRONTEND] [LOG] 🔍 [GRIP-FIX] Test responsivité ed906322-db11-4d06-aa28-437b06c445c4: {"hasMouseEvents":false,"isVisible":true,"hasCorrectCursor":true,"hasValidDimensions":true,"rect":{"width":130,"height":6}}
[18/07/2025 14:12:19] [FRONTEND] [LOG] 📊 [GRIP-FIX] Résultats du diagnostic: {"totalShifts":1,"regularShifts":1,"gripsInDOM":1,"gripsWithEvents":0,"gripsResponsive":1,"issues":[]}