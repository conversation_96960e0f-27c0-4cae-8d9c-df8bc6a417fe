// Utilitaires simplifiés pour la gestion des dates

export interface DayData {
    date: Date;
    short: string;
    long: string;
    number: number;
    isToday: boolean;
    isWeekend: boolean;
    dateKey: string; // Format YYYY-MM-DD
}

export class DateUtils {
    
    /**
     * Génère les données pour une semaine donnée
     * @param weekOffset Décalage en semaines par rapport à aujourd'hui (0 = semaine courante)
     * @param weekStartDay Jour de début de semaine (0=dimanche, 1=lundi, etc.)
     */
    static generateWeekDays(weekOffset: number = 0, weekStartDay: number = 0): DayData[] {
        const now = new Date();
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        // Calculer le début de la semaine
        const currentDay = now.getDay();
        const daysToSubtract = (currentDay - weekStartDay + 7) % 7;
        const startOfWeek = new Date(now.getTime());
        startOfWeek.setDate(now.getDate() - daysToSubtract + (weekOffset * 7));
        
        // Générer les 7 jours
        const days: DayData[] = [];
        for (let i = 0; i < 7; i++) {
            const date = new Date(startOfWeek);
            date.setDate(date.getDate() + i);
            date.setHours(0, 0, 0, 0);
            
            days.push({
                date: new Date(date),
                short: date.toLocaleDateString('fr-FR', { weekday: 'short' }).toUpperCase().substring(0, 3),
                long: date.toLocaleDateString('fr-FR', { weekday: 'long' }),
                number: date.getDate(),
                isToday: date.getTime() === today.getTime(),
                isWeekend: date.getDay() === 0 || date.getDay() === 6,
                dateKey: this.formatDateKey(date)
            });
        }
        
        return days;
    }
    
    /**
     * Formate une date en clé unique (YYYY-MM-DD)
     */
    static formatDateKey(date: Date): string {
        return date.toISOString().split('T')[0];
    }
    
    /**
     * Parse une clé de date en objet Date
     */
    static parseDateKey(dateKey: string): Date {
        return new Date(dateKey + 'T00:00:00');
    }
    
    /**
     * Génère le texte d'affichage pour une semaine (format concis)
     */
    static generateWeekDisplayText(days: DayData[]): string {
        if (days.length === 0) return '';

        const firstDay = days[0].date;
        const lastDay = days[6].date;
        const weekNumber = this.getWeekNumber(firstDay);
        const year = firstDay.getFullYear();

        // Format concis : "Week 42, 2024: Oct 13-19"
        const firstDayFormatted = firstDay.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric'
        });
        const lastDayFormatted = lastDay.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric'
        });

        // Si même mois, afficher "Oct 13-19", sinon "Oct 13-Nov 2"
        const sameMonth = firstDay.getMonth() === lastDay.getMonth();
        const dateRange = sameMonth
            ? `${firstDay.toLocaleDateString('en-US', { month: 'short' })} ${firstDay.getDate()}-${lastDay.getDate()}`
            : `${firstDayFormatted}-${lastDayFormatted}`;

        return `Week ${weekNumber}, ${year}: ${dateRange}`;
    }
    
    /**
     * Calcule le numéro de semaine ISO 8601
     */
    static getWeekNumber(date: Date): number {
        const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
        const dayNum = d.getUTCDay() || 7;
        d.setUTCDate(d.getUTCDate() + 4 - dayNum);
        const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
        return Math.ceil((((d.getTime() - yearStart.getTime()) / 86400000) + 1) / 7);
    }
    
    /**
     * Génère une clé unique pour une semaine
     */
    static generateWeekKey(date: Date = new Date()): string {
        const year = date.getFullYear();
        const weekNumber = this.getWeekNumber(date);
        return `${year}-W${weekNumber.toString().padStart(2, '0')}`;
    }
    
    /**
     * Vérifie si deux dates sont le même jour
     */
    static isSameDay(date1: Date, date2: Date): boolean {
        return this.formatDateKey(date1) === this.formatDateKey(date2);
    }
    
    /**
     * Vérifie si une date est aujourd'hui
     */
    static isToday(date: Date): boolean {
        return this.isSameDay(date, new Date());
    }
    
    /**
     * Vérifie si une date est un week-end
     */
    static isWeekend(date: Date): boolean {
        const day = date.getDay();
        return day === 0 || day === 6;
    }
    
    /**
     * Ajoute des jours à une date
     */
    static addDays(date: Date, days: number): Date {
        const result = new Date(date);
        result.setDate(result.getDate() + days);
        return result;
    }
    
    /**
     * Ajoute des semaines à une date
     */
    static addWeeks(date: Date, weeks: number): Date {
        return this.addDays(date, weeks * 7);
    }
    
    /**
     * Obtient le début de la semaine pour une date donnée
     */
    static getWeekStart(date: Date, weekStartDay: number = 0): Date {
        const currentDay = date.getDay();
        const daysToSubtract = (currentDay - weekStartDay + 7) % 7;
        const startOfWeek = new Date(date);
        startOfWeek.setDate(date.getDate() - daysToSubtract);
        startOfWeek.setHours(0, 0, 0, 0);
        return startOfWeek;
    }
    
    /**
     * Obtient la fin de la semaine pour une date donnée
     */
    static getWeekEnd(date: Date, weekStartDay: number = 0): Date {
        const weekStart = this.getWeekStart(date, weekStartDay);
        return this.addDays(weekStart, 6);
    }
    
    /**
     * Formate une date pour l'affichage
     */
    static formatDisplayDate(date: Date, options: Intl.DateTimeFormatOptions = {}): string {
        const defaultOptions: Intl.DateTimeFormatOptions = {
            day: 'numeric',
            month: 'long',
            year: 'numeric'
        };
        
        return date.toLocaleDateString('fr-FR', { ...defaultOptions, ...options });
    }
    
    /**
     * Parse une date depuis différents formats
     */
    static parseDate(input: string | Date): Date {
        if (input instanceof Date) {
            return new Date(input);
        }
        
        // Essayer le format ISO (YYYY-MM-DD)
        if (/^\d{4}-\d{2}-\d{2}$/.test(input)) {
            return new Date(input + 'T00:00:00');
        }
        
        // Essayer le format standard
        return new Date(input);
    }
    
    /**
     * Valide qu'une chaîne est une date valide
     */
    static isValidDate(input: string | Date): boolean {
        try {
            const date = this.parseDate(input);
            return !isNaN(date.getTime());
        } catch {
            return false;
        }
    }
    
    /**
     * Obtient la plage de dates pour une semaine donnée
     */
    static getWeekRange(weekOffset: number = 0, weekStartDay: number = 0): { start: Date; end: Date } {
        const days = this.generateWeekDays(weekOffset, weekStartDay);
        return {
            start: days[0].date,
            end: days[6].date
        };
    }
}
