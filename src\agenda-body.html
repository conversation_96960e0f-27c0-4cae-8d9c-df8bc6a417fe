<body class="bg-gradient-to-br from-slate-100 to-sky-100 text-slate-900 overflow-hidden" style='font-family: Inter, "Noto Sans", sans-serif;'>
<div class="relative flex size-full min-h-screen flex-col group/design-root">
    <div class="layout-container flex h-screen grow flex-col">
        <header class="flex items-center justify-between whitespace-nowrap border-b border-solid border-slate-300/60 px-6 py-4 bg-white/85 backdrop-blur-lg shadow-md fixed top-0 left-0 right-0 z-50">
             <!-- Header content ... (inchangé) -->
            <div class="flex items-center gap-3 text-slate-900">
                <div class="size-8 text-blue-600"><svg fill="none" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg"><path d="M44 4H30.6666V17.3334H17.3334V30.6666H4V44H44V4Z" fill="currentColor"></path></svg></div>
                <h2 class="text-slate-900 text-xl font-semibold leading-tight tracking-tight">Calendrier d'Équipe</h2>
            </div>
            <div class="flex flex-1 justify-end items-center gap-4 md:gap-6">
                <nav class="hidden md:flex items-center gap-2">
                    <button class="text-slate-600 hover:text-blue-600 text-sm font-medium leading-normal px-3 py-2 rounded-lg hover:bg-slate-200/70 transition-colors" type="button">Aujourd'hui</button>
                    <button class="text-slate-600 hover:text-blue-600 text-sm font-medium leading-normal px-3 py-2 rounded-lg hover:bg-slate-200/70 transition-colors" type="button">Mois</button>
                    <button class="text-blue-600 bg-blue-100/80 text-sm font-semibold leading-normal px-3 py-2 rounded-lg transition-colors hover:bg-blue-200/80 shadow-sm" type="button">Semaine</button>
                    <button class="text-slate-600 hover:text-blue-600 text-sm font-medium leading-normal px-3 py-2 rounded-lg hover:bg-slate-200/70 transition-colors" type="button">Jour</button>
                </nav>
                <button class="flex items-center justify-center rounded-lg h-10 w-10 text-slate-500 hover:bg-slate-200/70 hover:text-slate-700 transition-all duration-200 transform hover:scale-110"><span class="material-icons-outlined text-2xl">filter_list</span></button>
                <button class="flex items-center justify-center rounded-lg h-10 w-10 text-slate-500 hover:bg-slate-200/70 hover:text-slate-700 transition-all duration-200 transform hover:scale-110"><span class="material-icons-outlined text-2xl">settings</span></button>
                <div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10 border-2 border-slate-200 shadow-md transform hover:scale-110 transition-transform duration-200" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuDSJThJUPty7Wl9A9rU2FQdRSYJpb2_hVYDOh8hLy1MG9FnqJzZM95qmEx90D1r-0hdyCnJQPzfZHmRMt2AwALJ4o5xD9_eeeyvz_KMBOd_lDYlwQ8kvybXW3vXqnD5x4bex_arARqREWRBzAlLc6u_eqJXwjXYrWLiQp74vfLqvcbKX8THk6ct0QTj2jvsPk8cdhJMv1voPKXnQ6hW7ANU73a7FoWwR4i1uEkogdReG5KtHuxIejDGKuxm6-NzoSsSGKSinmGajvXM");'></div>
                <button class="md:hidden flex items-center justify-center rounded-lg h-10 w-10 text-slate-500 hover:bg-slate-200/70 hover:text-slate-700 transition-all duration-200 transform hover:scale-110"><span class="material-icons-outlined text-2xl">menu</span></button>
            </div>
        </header>
        <main class="flex flex-1 flex-col px-6 py-8 overflow-hidden pt-24">
            <div class="flex items-center justify-between mb-8">
                <!-- Date navigation and action buttons ... (inchangé) -->
                <div class="flex items-center gap-2">
                    <button class="p-2.5 rounded-full hover:bg-slate-200/80 text-slate-500 hover:text-slate-700 transition-all duration-200 transform hover:scale-110"><span class="material-icons-outlined text-2xl">chevron_left</span></button>
                    <h2 id="current-week-display" class="text-slate-900 text-xl sm:text-2xl font-semibold leading-tight tracking-tight w-80 min-w-80 max-w-80 text-center overflow-hidden text-ellipsis whitespace-nowrap">Chargement...</h2>
                    <button class="p-2.5 rounded-full hover:bg-slate-200/80 text-slate-500 hover:text-slate-700 transition-all duration-200 transform hover:scale-110"><span class="material-icons-outlined text-2xl">chevron_right</span></button>
                </div>
                <div class="flex items-center gap-3">
                    <button class="flex items-center justify-center gap-2 text-slate-600 bg-white/90 hover:bg-slate-50/90 border border-slate-300/80 font-medium py-2.5 px-4 rounded-xl transition-all duration-200 text-sm shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"><span class="material-icons-outlined text-xl">upload</span> Exporter</button>
                    <button id="add-shift-button" class="flex items-center justify-center gap-2 text-white bg-blue-600 hover:bg-blue-700 font-medium py-2.5 px-4 rounded-xl transition-all duration-200 text-sm shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 active:bg-blue-800"><span class="material-icons-outlined text-xl">add</span> Ajouter un shift</button>
                </div>
            </div>
            <div class="flex-1 border border-slate-300/40 rounded-xl shadow-2xl bg-white/70 overflow-hidden relative schedule-scroll-indicator schedule-container-glass" id="schedule-container">
                <div class="grid grid-cols-[220px_1fr] h-full overflow-y-auto schedule-grid-container" id="schedule-grid-scrollable-content">
                    <!-- COLONNE 1: EMPLOYÉS - Nouvelle structure pour le scroll synchronisé -->
                    <div class="bg-slate-100/60 border-r border-slate-300/40 shadow-sm"> {/* Pas de sticky ici pour la colonne entière */}
                        <div class="h-[calc(5.5rem-1px)] flex items-center justify-start pl-6 border-b border-slate-300/40 employee-info-header sticky-header-bg sticky top-0 z-30">
                            <p class="text-slate-700 text-sm font-semibold leading-normal tracking-wider">Employé(e)</p>
                        </div>
                        <div id="employee-list-container" class="divide-y divide-slate-200/70">
                            <!-- Les lignes d'employés (avec leur propre min-height dynamique) seront injectées ici par JS -->
                        </div>
                    </div>
                    <!-- COLONNE 2: GRILLE DES JOURS -->
                    <div class="grid grid-cols-7 flex-1" id="schedule-grid-content">
                        <!-- Les 7 colonnes de jours (avec en-têtes sticky et cellules de shifts) seront injectées ici par JS -->
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

