// ========================================
// SCRIPT DE TEST POUR LA CORRECTION DU FORK MODAL
// ========================================

console.log('🧪 [TEST] Script de test pour la correction du fork modal chargé');

// Fonction pour tester la détection de date de drop
function testDropDateDetection() {
    console.log('🧪 [TEST] Test de la détection de date de drop...');
    
    if (!window.TeamCalendarApp || !window.TeamCalendarApp.detectDropDateFromPosition) {
        console.error('❌ [TEST] detectDropDateFromPosition non disponible');
        return false;
    }
    
    // Simuler un événement de drop
    const mockEvent = {
        clientX: 500,
        clientY: 300,
        preventDefault: () => {},
        stopPropagation: () => {}
    };
    
    try {
        const detectedDate = window.TeamCalendarApp.detectDropDateFromPosition(mockEvent, 'test-employee-id');
        console.log('✅ [TEST] Date détectée:', detectedDate);
        
        // Vérifier que la date est au format YYYY-MM-DD
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (dateRegex.test(detectedDate)) {
            console.log('✅ [TEST] Format de date valide');
            return true;
        } else {
            console.error('❌ [TEST] Format de date invalide:', detectedDate);
            return false;
        }
    } catch (error) {
        console.error('❌ [TEST] Erreur lors de la détection de date:', error);
        return false;
    }
}

// Fonction pour tester l'ouverture du modal de fork avec date
function testForkModalWithDate() {
    console.log('🧪 [TEST] Test du modal de fork avec date...');
    
    if (!window.TeamCalendarApp || !window.TeamCalendarApp.showRegularAssignmentConfirmationMenu) {
        console.error('❌ [TEST] showRegularAssignmentConfirmationMenu non disponible');
        return false;
    }
    
    // Vérifier qu'il y a des attributions régulières et des employés
    if (!window.TeamCalendarApp.data?.regularAssignments?.length) {
        console.error('❌ [TEST] Aucune attribution régulière trouvée');
        return false;
    }
    
    if (!window.TeamCalendarApp.data?.employees?.length || window.TeamCalendarApp.data.employees.length < 2) {
        console.error('❌ [TEST] Pas assez d\'employés pour tester le fork');
        return false;
    }
    
    const assignment = window.TeamCalendarApp.data.regularAssignments[0];
    const sourceEmployee = window.TeamCalendarApp.data.employees.find(e => e.id === assignment.employeeId);
    const targetEmployee = window.TeamCalendarApp.data.employees.find(e => e.id !== assignment.employeeId);
    
    if (!sourceEmployee || !targetEmployee) {
        console.error('❌ [TEST] Impossible de trouver les employés source et cible');
        return false;
    }
    
    // Date de test (demain)
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const testDate = `${tomorrow.getFullYear()}-${(tomorrow.getMonth() + 1).toString().padStart(2, '0')}-${tomorrow.getDate().toString().padStart(2, '0')}`;
    
    console.log('🧪 [TEST] Ouverture du modal avec:', {
        assignmentId: assignment.id,
        sourceEmployee: sourceEmployee.name,
        targetEmployee: targetEmployee.name,
        testDate: testDate
    });
    
    try {
        window.TeamCalendarApp.showRegularAssignmentConfirmationMenu(
            assignment.id,
            targetEmployee.id,
            testDate
        );
        
        // Vérifier que le modal est ouvert
        setTimeout(() => {
            const modal = document.getElementById('regular-assignment-confirmation-modal');
            if (modal) {
                console.log('✅ [TEST] Modal ouvert avec succès');
                
                // Vérifier que la date est pré-remplie
                const dateInput = modal.querySelector('#modification-start-date');
                if (dateInput && dateInput.value === testDate) {
                    console.log('✅ [TEST] Date automatiquement pré-remplie:', dateInput.value);
                } else {
                    console.warn('⚠️ [TEST] Date non pré-remplie ou incorrecte:', dateInput?.value);
                }
                
                // Vérifier le message d'information
                const infoText = modal.querySelector('.text-amber-700');
                if (infoText && infoText.textContent.includes('automatiquement détectée')) {
                    console.log('✅ [TEST] Message de détection automatique affiché');
                } else {
                    console.warn('⚠️ [TEST] Message de détection automatique non trouvé');
                }
                
                return true;
            } else {
                console.error('❌ [TEST] Modal non trouvé dans le DOM');
                return false;
            }
        }, 100);
        
        return true;
    } catch (error) {
        console.error('❌ [TEST] Erreur lors de l\'ouverture du modal:', error);
        return false;
    }
}

// Fonction pour tester le fork complet avec notification
function testCompleteForkFlow() {
    console.log('🧪 [TEST] Test du flux complet de fork...');
    
    // Fermer tout modal existant
    const existingModal = document.getElementById('regular-assignment-confirmation-modal');
    if (existingModal) {
        existingModal.remove();
    }
    
    if (!window.TeamCalendarApp?.data?.regularAssignments?.length || !window.TeamCalendarApp?.data?.employees?.length) {
        console.error('❌ [TEST] Données insuffisantes pour le test');
        return false;
    }
    
    const assignment = window.TeamCalendarApp.data.regularAssignments[0];
    const sourceEmployee = window.TeamCalendarApp.data.employees.find(e => e.id === assignment.employeeId);
    const targetEmployee = window.TeamCalendarApp.data.employees.find(e => e.id !== assignment.employeeId);
    
    if (!sourceEmployee || !targetEmployee) {
        console.error('❌ [TEST] Employés non trouvés');
        return false;
    }
    
    // Date de test (dans 3 jours)
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + 3);
    const testDate = `${futureDate.getFullYear()}-${(futureDate.getMonth() + 1).toString().padStart(2, '0')}-${futureDate.getDate().toString().padStart(2, '0')}`;
    
    console.log('🧪 [TEST] Simulation du fork:', {
        assignment: assignment.id,
        from: sourceEmployee.name,
        to: targetEmployee.name,
        date: testDate
    });
    
    // Ouvrir le modal
    window.TeamCalendarApp.showRegularAssignmentConfirmationMenu(
        assignment.id,
        targetEmployee.id,
        testDate
    );
    
    // Attendre que le modal soit ouvert puis simuler le clic
    setTimeout(() => {
        const modal = document.getElementById('regular-assignment-confirmation-modal');
        if (!modal) {
            console.error('❌ [TEST] Modal non ouvert');
            return;
        }
        
        // Vérifier que la date est correcte
        const dateInput = modal.querySelector('#modification-start-date');
        if (dateInput) {
            dateInput.value = testDate;
            console.log('✅ [TEST] Date configurée:', testDate);
        }
        
        // Simuler le clic sur "Changement Permanent"
        const permanentBtn = modal.querySelector('#regular-assignment-permanent');
        if (permanentBtn) {
            console.log('🧪 [TEST] Simulation du clic sur "Changement Permanent"...');
            
            // Intercepter les toasts pour vérifier la notification
            const originalToastSuccess = window.toastSystem?.success;
            if (originalToastSuccess) {
                window.toastSystem.success = function(message, options) {
                    console.log('✅ [TEST] Toast de succès intercepté:', message);
                    
                    // Vérifier que le message contient les bonnes dates
                    if (message.includes('fork a été créé') && message.includes(futureDate.toLocaleDateString('fr-FR'))) {
                        console.log('✅ [TEST] Notification contient la date correcte');
                    } else {
                        console.warn('⚠️ [TEST] Notification ne contient pas la date attendue');
                    }
                    
                    // Restaurer la fonction originale
                    window.toastSystem.success = originalToastSuccess;
                    
                    // Appeler la fonction originale
                    return originalToastSuccess.call(this, message, options);
                };
            }
            
            permanentBtn.click();
            console.log('✅ [TEST] Clic simulé sur le bouton permanent');
        } else {
            console.error('❌ [TEST] Bouton permanent non trouvé');
        }
    }, 200);
    
    return true;
}

// Fonction de diagnostic complet
function diagnoseForkModalIssues() {
    console.log('🔍 [DIAGNOSTIC] Diagnostic complet du fork modal...');
    
    const results = {
        teamCalendarApp: !!window.TeamCalendarApp,
        detectDropDateFunction: !!(window.TeamCalendarApp?.detectDropDateFromPosition),
        showConfirmationMenuFunction: !!(window.TeamCalendarApp?.showRegularAssignmentConfirmationMenu),
        handleRegularAssignmentDropFunction: !!(window.TeamCalendarApp?.handleRegularAssignmentDrop),
        regularAssignments: window.TeamCalendarApp?.data?.regularAssignments?.length || 0,
        employees: window.TeamCalendarApp?.data?.employees?.length || 0
    };
    
    console.log('📊 [DIAGNOSTIC] Résultats:', results);
    
    if (results.teamCalendarApp && results.detectDropDateFunction && results.showConfirmationMenuFunction) {
        console.log('✅ [DIAGNOSTIC] Toutes les fonctions nécessaires sont disponibles');
        return true;
    } else {
        console.error('❌ [DIAGNOSTIC] Fonctions manquantes détectées');
        return false;
    }
}

// Exposer les fonctions globalement
window.testDropDateDetection = testDropDateDetection;
window.testForkModalWithDate = testForkModalWithDate;
window.testCompleteForkFlow = testCompleteForkFlow;
window.diagnoseForkModalIssues = diagnoseForkModalIssues;

// Auto-diagnostic au chargement
setTimeout(() => {
    console.log('🚀 [TEST] Lancement du diagnostic automatique...');
    diagnoseForkModalIssues();
}, 3000);

console.log('✅ [TEST] Fonctions de test disponibles:');
console.log('- testDropDateDetection() : Tester la détection de date');
console.log('- testForkModalWithDate() : Tester le modal avec date');
console.log('- testCompleteForkFlow() : Tester le flux complet');
console.log('- diagnoseForkModalIssues() : Diagnostic complet');
